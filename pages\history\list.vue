<template>
	<view class="container" :class="{ 'elderly-mode': isElderlyMode }">
		<!-- 自定义导航栏 -->
		<view class="custom-navbar" :class="{ 'elderly-mode': isElderlyMode }">
			<view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
			<view class="navbar-content">
				<view class="navbar-left" @click="goBack">
					<Icon
						name="arrow-left-line"
						:size="isElderlyMode ? '40rpx' : '36rpx'"
						color="#333"
					></Icon>
					<text class="back-text">返回</text>
				</view>
				<view class="navbar-center">
					<text class="navbar-title">浏览历史</text>
				</view>
				<view class="navbar-right"></view>
			</view>
		</view>

		<!-- 操作栏 -->
		<view class="action-bar">
			<view class="date-filter">
				<picker mode="date" :value="selectedDate" @change="onDateChange">
					<view class="date-picker">
						<Icon name="calendar-line" size="24rpx" color="#ff8a00"></Icon>
						<text class="date-text">{{selectedDate || '选择日期'}}</text>
						<Icon name="arrow-down-s-line" size="20rpx" color="#999"></Icon>
					</view>
				</picker>
			</view>
			<button class="clear-btn" @click="clearHistory" v-if="historyList.length > 0">
				<Icon name="delete-bin-line" size="24rpx" color="#666"></Icon>
				<text>清空历史</text>
			</button>
		</view>

		<!-- 历史记录列表 -->
		<view class="history-list">
			<!-- 按日期分组 -->
			<view class="date-group" v-for="(group, date) in groupedHistory" :key="date">
				<view class="date-header">
					<text class="date-title">{{formatDate(date)}}</text>
					<text class="date-count">{{group.length}}条记录</text>
				</view>
				
				<view class="history-item" v-for="(item, index) in group" :key="index" @click="viewDetail(item)">
					<view class="item-time">
						<text class="time-text">{{item.visitTime}}</text>
					</view>
					
					<view class="item-content">
						<view class="item-image">
							<image :src="item.image" class="image" mode="aspectFill" v-if="item.image"></image>
							<view class="image-placeholder" v-else>
								<Icon :name="item.icon" size="32rpx" color="#999"></Icon>
							</view>
						</view>
						
						<view class="item-info">
							<text class="item-title">{{item.title}}</text>
							<text class="item-desc">{{item.description}}</text>
							<view class="item-meta">
								<text class="item-type">{{getTypeText(item.type)}}</text>
								<text class="item-duration">浏览{{item.duration}}分钟</text>
							</view>
						</view>
					</view>
					
					<view class="item-actions">
						<button class="action-btn favorite" @click.stop="toggleFavorite(item)">
							<Icon :name="item.isFavorite ? 'heart-fill' : 'heart-line'" size="24rpx" :color="item.isFavorite ? '#ff4444' : '#666'"></Icon>
						</button>
						<button class="action-btn delete" @click.stop="deleteItem(item, date, index)">
							<Icon name="close-line" size="24rpx" color="#666"></Icon>
						</button>
					</view>
				</view>
			</view>
		</view>

		<!-- 空状态 -->
		<view class="empty-state" v-if="Object.keys(groupedHistory).length === 0">
			<Icon name="history-line" size="120rpx" color="#ccc"></Icon>
			<text class="empty-text">暂无浏览记录</text>
			<text class="empty-desc">您的浏览记录将显示在这里</text>
			<button class="empty-btn" @click="goToExplore">去浏览内容</button>
		</view>

		<!-- 加载更多 -->
		<view class="load-more" v-if="hasMore && Object.keys(groupedHistory).length > 0">
			<button class="load-more-btn" @click="loadMore" :disabled="loading">
				<text v-if="!loading">加载更多</text>
				<text v-else>加载中...</text>
			</button>
		</view>
	</view>
</template>

<script>
import Icon from '@/components/Icon/Icon.vue'
import { elderlyModeManager } from '@/utils/elderlyModeUtils.js'

export default {
	components: {
		Icon
	},
	data() {
		return {
			statusBarHeight: 0, // 状态栏高度
			isElderlyMode: false, // 适老化模式
			selectedDate: '',
			loading: false,
			hasMore: true,
			historyList: [
				{
					id: 1,
					type: 'service',
					title: '专业居家护理服务',
					description: '24小时专业护理师上门服务，提供生活照料和医疗护理',
					image: '/static/service/home-care.jpg',
					icon: 'heart-3-line',
					visitTime: '14:30',
					visitDate: '2024-01-15',
					duration: 5,
					isFavorite: true
				},
				{
					id: 2,
					type: 'news',
					title: '老年人健康饮食指南',
					description: '专家推荐的老年人营养搭配方案，科学饮食更健康',
					image: '/static/news/health-news.jpg',
					icon: 'article-line',
					visitTime: '10:15',
					visitDate: '2024-01-15',
					duration: 8,
					isFavorite: false
				},
				{
					id: 3,
					type: 'equipment',
					title: '多功能护理床',
					description: '电动升降护理床，适合长期卧床老人使用',
					image: '/static/equipment/care-bed.jpg',
					icon: 'hotel-bed-line',
					visitTime: '16:45',
					visitDate: '2024-01-14',
					duration: 3,
					isFavorite: false
				},
				{
					id: 4,
					type: 'institution',
					title: '阳光老年服务中心',
					description: '综合性老年服务机构，提供日间照料、康复训练等服务',
					image: '/static/community/center1.jpg',
					icon: 'building-line',
					visitTime: '09:20',
					visitDate: '2024-01-14',
					duration: 12,
					isFavorite: true
				},
				{
					id: 5,
					type: 'service',
					title: '康复训练服务',
					description: '专业物理治疗师指导，个性化康复训练方案',
					image: '/static/service/rehabilitation.jpg',
					icon: 'heart-pulse-line',
					visitTime: '15:30',
					visitDate: '2024-01-13',
					duration: 7,
					isFavorite: false
				},
				{
					id: 6,
					type: 'news',
					title: '智慧养老新政策解读',
					description: '最新养老政策详细解读，了解您的权益和福利',
					image: '/static/news/policy-news.jpg',
					icon: 'government-line',
					visitTime: '11:10',
					visitDate: '2024-01-13',
					duration: 15,
					isFavorite: true
				}
			]
		}
	},
	computed: {
		groupedHistory() {
			const filtered = this.selectedDate 
				? this.historyList.filter(item => item.visitDate === this.selectedDate)
				: this.historyList;
			
			const grouped = {};
			filtered.forEach(item => {
				if (!grouped[item.visitDate]) {
					grouped[item.visitDate] = [];
				}
				grouped[item.visitDate].push(item);
			});
			
			// 按时间倒序排列每组内的项目
			Object.keys(grouped).forEach(date => {
				grouped[date].sort((a, b) => b.visitTime.localeCompare(a.visitTime));
			});
			
			return grouped;
		}
	},
	onLoad() {
		// 获取状态栏高度
		const systemInfo = uni.getSystemInfoSync();
		this.statusBarHeight = systemInfo.statusBarHeight || 44;

		// 初始化适老化模式
		this.isElderlyMode = elderlyModeManager.getElderlyMode();
		elderlyModeManager.onElderlyModeChange((isEnabled) => {
			this.isElderlyMode = isEnabled;
		});
	},
	methods: {
		onDateChange(e) {
			this.selectedDate = e.detail.value;
		},
		formatDate(dateStr) {
			const date = new Date(dateStr);
			const today = new Date();
			const yesterday = new Date(today);
			yesterday.setDate(yesterday.getDate() - 1);
			
			if (dateStr === today.toISOString().split('T')[0]) {
				return '今天';
			} else if (dateStr === yesterday.toISOString().split('T')[0]) {
				return '昨天';
			} else {
				return `${date.getMonth() + 1}月${date.getDate()}日`;
			}
		},
		getTypeText(type) {
			const typeMap = {
				'service': '服务',
				'news': '资讯',
				'institution': '机构',
				'equipment': '辅具'
			};
			return typeMap[type] || '其他';
		},
		toggleFavorite(item) {
			item.isFavorite = !item.isFavorite;
			uni.showToast({
				title: item.isFavorite ? '已收藏' : '已取消收藏',
				icon: 'success'
			});
		},
		deleteItem(item, date, index) {
			uni.showModal({
				title: '删除记录',
				content: '确定要删除这条浏览记录吗？',
				success: (res) => {
					if (res.confirm) {
						// 从原数组中删除
						const originalIndex = this.historyList.findIndex(h => h.id === item.id);
						if (originalIndex > -1) {
							this.historyList.splice(originalIndex, 1);
						}
						
						uni.showToast({
							title: '删除成功',
							icon: 'success'
						});
					}
				}
			});
		},
		clearHistory() {
			uni.showModal({
				title: '清空历史',
				content: '确定要清空所有浏览记录吗？此操作不可恢复。',
				success: (res) => {
					if (res.confirm) {
						this.historyList = [];
						uni.showToast({
							title: '已清空历史记录',
							icon: 'success'
						});
					}
				}
			});
		},
		viewDetail(item) {
			// 记录新的访问时间
			const now = new Date();
			item.visitTime = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;
			item.visitDate = now.toISOString().split('T')[0];
			
			// 跳转到详情页
			let url = '';
			switch (item.type) {
				case 'service':
					url = `/pages/service/detail?id=${item.id}`;
					break;
				case 'news':
					url = `/pages/news/detail?id=${item.id}`;
					break;
				case 'institution':
					url = `/pages/institution/detail?id=${item.id}`;
					break;
				case 'equipment':
					url = `/pages/equipment/detail?id=${item.id}`;
					break;
				default:
					return;
			}
			uni.navigateTo({ url });
		},
		loadMore() {
			if (this.loading || !this.hasMore) return;
			
			this.loading = true;
			
			// 模拟加载更多数据
			setTimeout(() => {
				this.loading = false;
				this.hasMore = false; // 假设没有更多数据了
				uni.showToast({
					title: '没有更多数据了',
					icon: 'none'
				});
			}, 1500);
		},
		goToExplore() {
			uni.switchTab({
				url: '/pages/home/<USER>'
			});
		},

		// 返回上一页
		goBack() {
			uni.navigateBack({
				fail: () => {
					uni.switchTab({
						url: '/pages/profile/profile'
					});
				}
			});
		}
	}
}
</script>

<style scoped>
.container {
	background: #f8f9fa;
	min-height: 100vh;
}

/* 导航栏样式 */
.custom-navbar {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 1000;
	background: rgba(255, 255, 255, 0.95);
	backdrop-filter: blur(20rpx);
	-webkit-backdrop-filter: blur(20rpx);
	border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);
}

.navbar-content {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 20rpx 32rpx;
	min-height: 88rpx;
}

.navbar-left {
	display: flex;
	align-items: center;
	gap: 12rpx;
	padding: 8rpx 16rpx 8rpx 0;
	border-radius: 20rpx;
	transition: all 0.2s ease;
}

.navbar-left:active {
	background-color: rgba(0, 0, 0, 0.04);
	transform: scale(0.96);
}

.back-text {
	font-size: 32rpx;
	color: #333;
	font-weight: 500;
}

.navbar-center {
	position: absolute;
	left: 50%;
	transform: translateX(-50%);
}

.navbar-title {
	font-size: 34rpx;
	font-weight: 600;
	color: #333;
}

.navbar-right {
	display: flex;
	align-items: center;
}

.action-bar {
	background: white;
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20rpx 30rpx;
	box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
	margin-top: 220rpx; /* 为导航栏留出空间 */
}

.date-picker {
	display: flex;
	align-items: center;
	gap: 10rpx;
	padding: 15rpx 20rpx;
	background: #f8f9fa;
	border-radius: 20rpx;
}

.date-text {
	font-size: 26rpx;
	color: #333;
}

.clear-btn {
	display: flex;
	align-items: center;
	gap: 8rpx;
	background: white;
	border: 2rpx solid #e0e0e0;
	border-radius: 20rpx;
	padding: 15rpx 20rpx;
	font-size: 24rpx;
	color: #666;
}

.history-list {
	padding: 20rpx;
}

.date-group {
	margin-bottom: 30rpx;
}

.date-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20rpx 30rpx;
	background: white;
	border-radius: 20rpx 20rpx 0 0;
	border-bottom: 1rpx solid #f0f0f0;
}

.date-title {
	font-size: 28rpx;
	color: #333;
	font-weight: bold;
}

.date-count {
	font-size: 24rpx;
	color: #666;
}

.history-item {
	background: white;
	display: flex;
	align-items: center;
	gap: 20rpx;
	padding: 25rpx 30rpx;
	border-bottom: 1rpx solid #f8f8f8;
}

.history-item:last-child {
	border-bottom: none;
	border-radius: 0 0 20rpx 20rpx;
}

.item-time {
	width: 80rpx;
	text-align: center;
	flex-shrink: 0;
}

.time-text {
	font-size: 22rpx;
	color: #999;
}

.item-content {
	flex: 1;
	display: flex;
	gap: 20rpx;
}

.item-image {
	width: 80rpx;
	height: 80rpx;
	border-radius: 12rpx;
	overflow: hidden;
	flex-shrink: 0;
}

.image {
	width: 100%;
	height: 100%;
}

.image-placeholder {
	width: 100%;
	height: 100%;
	background: #f0f0f0;
	display: flex;
	align-items: center;
	justify-content: center;
}

.item-info {
	flex: 1;
}

.item-title {
	font-size: 26rpx;
	color: #333;
	font-weight: bold;
	display: block;
	margin-bottom: 8rpx;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.item-desc {
	font-size: 22rpx;
	color: #666;
	display: block;
	margin-bottom: 10rpx;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.item-meta {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.item-type {
	font-size: 20rpx;
	color: #ff8a00;
	background: rgba(255, 138, 0, 0.1);
	padding: 4rpx 10rpx;
	border-radius: 10rpx;
}

.item-duration {
	font-size: 20rpx;
	color: #999;
}

.item-actions {
	display: flex;
	gap: 10rpx;
	flex-shrink: 0;
}

.action-btn {
	width: 50rpx;
	height: 50rpx;
	border-radius: 50%;
	border: 1rpx solid #e0e0e0;
	background: white;
	display: flex;
	align-items: center;
	justify-content: center;
}

.empty-state {
	text-align: center;
	padding: 120rpx 40rpx;
}

.empty-text {
	font-size: 28rpx;
	color: #999;
	display: block;
	margin: 30rpx 0 15rpx;
}

.empty-desc {
	font-size: 24rpx;
	color: #ccc;
	display: block;
	margin-bottom: 40rpx;
}

.empty-btn {
	background: #ff8a00;
	color: white;
	border: none;
	border-radius: 25rpx;
	padding: 20rpx 40rpx;
	font-size: 28rpx;
}

.load-more {
	padding: 40rpx;
	text-align: center;
}

.load-more-btn {
	background: white;
	border: 2rpx solid #e0e0e0;
	border-radius: 25rpx;
	padding: 20rpx 40rpx;
	font-size: 26rpx;
	color: #666;
}

.load-more-btn:disabled {
	opacity: 0.6;
}

/* ===== 适老化模式样式 ===== */
.elderly-mode .navbar-content {
	padding: 24rpx 36rpx;
	min-height: 96rpx;
}

.elderly-mode .navbar-left {
	gap: 16rpx;
	padding: 12rpx 20rpx 12rpx 0;
}

.elderly-mode .back-text {
	font-size: 36rpx;
	font-weight: 600;
}

.elderly-mode .navbar-title {
	font-size: 38rpx;
	font-weight: 700;
}

.elderly-mode .action-bar {
	margin-top: 240rpx; /* 适老化模式下导航栏更高 */
	padding: 30rpx;
}

.elderly-mode .date-filter {
	font-size: 32rpx;
}

.elderly-mode .clear-btn {
	font-size: 30rpx;
	padding: 12rpx 24rpx;
}

.elderly-mode .history-item {
	padding: 40rpx 30rpx;
}

.elderly-mode .history-title {
	font-size: 36rpx;
}

.elderly-mode .history-desc {
	font-size: 30rpx;
}

.elderly-mode .history-time {
	font-size: 28rpx;
}

.elderly-mode .load-more-btn {
	font-size: 32rpx;
	padding: 30rpx;
}
</style>
