<template>
	<view class="container" :class="{ 'elderly-mode': isElderlyMode }">
		<!-- 自定义导航栏 -->
		<view class="custom-navbar" :class="{ 'elderly-mode': isElderlyMode }">
			<view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
			<view class="navbar-content">
				<view class="navbar-left" @click="goBack">
					<Icon
						name="arrow-left-line"
						:size="isElderlyMode ? '40rpx' : '36rpx'"
						color="#333"
					></Icon>
					<text class="back-text">返回</text>
				</view>
				<view class="navbar-center">
					<text class="navbar-title">功能增强测试</text>
				</view>
				<view class="navbar-right"></view>
			</view>
		</view>

		<!-- 页面头部 -->
		<view class="page-header">
			<text class="page-title">功能增强测试</text>
			<text class="page-subtitle">测试"我的"模块增强功能</text>
		</view>

		<!-- 测试功能列表 -->
		<view class="test-section">
			<text class="section-title">数据管理页面测试</text>
			
			<view class="test-item" @click="testDataManage">
				<view class="test-icon">
					<Icon name="database-2-line" size="32rpx" color="#ff8a00"></Icon>
				</view>
				<view class="test-info">
					<text class="test-title">数据管理页面</text>
					<text class="test-desc">测试增强的数据管理功能</text>
				</view>
				<Icon name="arrow-right-s-line" size="20rpx" color="#999"></Icon>
			</view>

			<view class="test-item" @click="testNotificationSettings">
				<view class="test-icon">
					<Icon name="notification-3-line" size="32rpx" color="#4caf50"></Icon>
				</view>
				<view class="test-info">
					<text class="test-title">通知设置页面</text>
					<text class="test-desc">测试增强的通知设置功能</text>
				</view>
				<Icon name="arrow-right-s-line" size="20rpx" color="#999"></Icon>
			</view>
		</view>

		<!-- 功能特性说明 -->
		<view class="features-section">
			<text class="section-title">增强功能特性</text>
			
			<view class="feature-card">
				<view class="feature-header">
					<Icon name="database-2-line" size="40rpx" color="#ff8a00"></Icon>
					<text class="feature-title">数据管理增强</text>
				</view>
				<view class="feature-list">
					<text class="feature-item">• 新增数据同步状态显示</text>
					<text class="feature-item">• 新增存储空间信息展示</text>
					<text class="feature-item">• 优化数据导入导出体验</text>
					<text class="feature-item">• 增强用户反馈和错误处理</text>
				</view>
			</view>

			<view class="feature-card">
				<view class="feature-header">
					<Icon name="notification-3-line" size="40rpx" color="#4caf50"></Icon>
					<text class="feature-title">通知设置增强</text>
				</view>
				<view class="feature-list">
					<text class="feature-item">• 增强开关操作反馈</text>
					<text class="feature-item">• 优化设置保存提示</text>
					<text class="feature-item">• 改进返回按钮体验</text>
					<text class="feature-item">• 完善错误处理机制</text>
				</view>
			</view>
		</view>

		<!-- 测试结果 -->
		<view class="result-section" v-if="testResults.length > 0">
			<text class="section-title">测试结果</text>
			
			<view class="result-item" v-for="(result, index) in testResults" :key="index">
				<Icon :name="result.success ? 'check-line' : 'close-line'" 
					  size="24rpx" 
					  :color="result.success ? '#4caf50' : '#f44336'"></Icon>
				<text class="result-text" :class="{ success: result.success, error: !result.success }">
					{{ result.message }}
				</text>
			</view>
		</view>
	</view>
</template>

<script>
import Icon from '@/components/Icon/Icon.vue'
import FeedbackUtils from '@/utils/feedback.js'
import { elderlyModeManager } from '@/utils/elderlyModeUtils.js'

export default {
	components: {
		Icon
	},
	data() {
		return {
			statusBarHeight: 0,
			isElderlyMode: false,
			testResults: []
		}
	},
	onLoad() {
		// 获取状态栏高度
		const systemInfo = uni.getSystemInfoSync();
		this.statusBarHeight = systemInfo.statusBarHeight || 44;

		// 初始化适老化模式
		this.isElderlyMode = elderlyModeManager.getElderlyMode();
		elderlyModeManager.onElderlyModeChange((isEnabled) => {
			this.isElderlyMode = isEnabled;
		});
	},
	methods: {
		// 测试数据管理页面
		testDataManage() {
			FeedbackUtils.lightFeedback();
			
			this.addTestResult('开始测试数据管理页面...', true);
			
			uni.navigateTo({
				url: '/pages/data/manage',
				success: () => {
					this.addTestResult('数据管理页面跳转成功', true);
				},
				fail: (err) => {
					console.error('跳转失败:', err);
					this.addTestResult('数据管理页面跳转失败', false);
				}
			});
		},

		// 测试通知设置页面
		testNotificationSettings() {
			FeedbackUtils.lightFeedback();
			
			this.addTestResult('开始测试通知设置页面...', true);
			
			uni.navigateTo({
				url: '/pages/settings/notification',
				success: () => {
					this.addTestResult('通知设置页面跳转成功', true);
				},
				fail: (err) => {
					console.error('跳转失败:', err);
					this.addTestResult('通知设置页面跳转失败', false);
				}
			});
		},

		// 添加测试结果
		addTestResult(message, success) {
			this.testResults.push({
				message,
				success,
				timestamp: new Date().toLocaleTimeString()
			});
		},

		// 返回上一页
		goBack() {
			FeedbackUtils.lightFeedback();
			uni.navigateBack({
				fail: () => {
					uni.switchTab({
						url: '/pages/profile/profile'
					});
				}
			});
		}
	}
}
</script>

<style scoped>
.container {
	background: #f8f9fa;
	min-height: 100vh;
	padding: 20rpx;
}

/* 导航栏样式 */
.custom-navbar {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 1000;
	background: rgba(255, 255, 255, 0.95);
	backdrop-filter: blur(20rpx);
	-webkit-backdrop-filter: blur(20rpx);
	border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);
}

.navbar-content {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 20rpx 32rpx;
	min-height: 88rpx;
}

.navbar-left {
	display: flex;
	align-items: center;
	gap: 12rpx;
	padding: 8rpx 16rpx 8rpx 0;
	border-radius: 20rpx;
	transition: all 0.2s ease;
}

.navbar-left:active {
	background-color: rgba(0, 0, 0, 0.04);
	transform: scale(0.96);
}

.back-text {
	font-size: 32rpx;
	color: #333;
	font-weight: 500;
}

.navbar-center {
	position: absolute;
	left: 50%;
	transform: translateX(-50%);
}

.navbar-title {
	font-size: 34rpx;
	font-weight: 600;
	color: #333;
}

.page-header {
	text-align: center;
	padding: 40rpx 20rpx;
	background: linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%);
	border-radius: 30rpx;
	margin-bottom: 30rpx;
	margin-top: 200rpx;
}

.page-title {
	font-size: 40rpx;
	font-weight: bold;
	color: white;
	display: block;
	margin-bottom: 10rpx;
}

.page-subtitle {
	font-size: 26rpx;
	color: rgba(255, 255, 255, 0.8);
	display: block;
}

.section-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 20rpx;
}

.test-section {
	margin-bottom: 40rpx;
}

.test-item {
	background: white;
	border-radius: 20rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	display: flex;
	align-items: center;
	gap: 20rpx;
	box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
	transition: all 0.2s ease;
}

.test-item:active {
	transform: scale(0.98);
	background-color: #f8f9fa;
}

.test-icon {
	width: 60rpx;
	height: 60rpx;
	border-radius: 50%;
	background: rgba(255, 138, 0, 0.1);
	display: flex;
	align-items: center;
	justify-content: center;
}

.test-info {
	flex: 1;
}

.test-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 8rpx;
}

.test-desc {
	font-size: 26rpx;
	color: #666;
	display: block;
}

.features-section {
	margin-bottom: 40rpx;
}

.feature-card {
	background: white;
	border-radius: 20rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.feature-header {
	display: flex;
	align-items: center;
	gap: 15rpx;
	margin-bottom: 20rpx;
}

.feature-title {
	font-size: 30rpx;
	font-weight: bold;
	color: #333;
}

.feature-list {
	display: flex;
	flex-direction: column;
	gap: 10rpx;
}

.feature-item {
	font-size: 26rpx;
	color: #666;
	line-height: 1.5;
}

.result-section {
	margin-bottom: 40rpx;
}

.result-item {
	background: white;
	border-radius: 15rpx;
	padding: 20rpx;
	margin-bottom: 15rpx;
	display: flex;
	align-items: center;
	gap: 15rpx;
	box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.08);
}

.result-text {
	font-size: 26rpx;
	flex: 1;
}

.result-text.success {
	color: #4caf50;
}

.result-text.error {
	color: #f44336;
}

/* ===== 适老化模式样式 ===== */
.elderly-mode .navbar-content {
	padding: 24rpx 36rpx;
	min-height: 96rpx;
}

.elderly-mode .navbar-left {
	gap: 16rpx;
	padding: 12rpx 20rpx 12rpx 0;
}

.elderly-mode .back-text {
	font-size: 36rpx;
	font-weight: 600;
}

.elderly-mode .navbar-title {
	font-size: 38rpx;
	font-weight: 700;
}
</style>
