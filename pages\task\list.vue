<template>
	<view class="container">
		<!-- 头部统计 -->
		<view class="header-stats">
			<view class="stat-card">
				<text class="stat-number">{{totalTasks}}</text>
				<text class="stat-label">总任务</text>
			</view>
			<view class="stat-card">
				<text class="stat-number">{{pendingTasks}}</text>
				<text class="stat-label">待完成</text>
			</view>
			<view class="stat-card">
				<text class="stat-number">{{completedTasks}}</text>
				<text class="stat-label">已完成</text>
			</view>
		</view>

		<!-- 筛选栏 -->
		<view class="filter-section">
			<scroll-view scroll-x="true" class="filter-scroll">
				<view class="filter-list">
					<view 
						class="filter-item" 
						:class="{ active: activeFilter === item.key }"
						v-for="(item, index) in filterList" 
						:key="index"
						@click="selectFilter(item.key)"
					>
						<text class="filter-text">{{item.name}}</text>
					</view>
				</view>
			</scroll-view>
		</view>

		<!-- 加载状态 -->
		<view class="loading-state" v-if="loading">
			<view class="loading-spinner"></view>
			<text class="loading-text">加载中...</text>
		</view>

		<!-- 任务列表 -->
		<view class="task-list" v-else>
			<view class="task-item" v-for="(task, index) in filteredTasks" :key="index" @click="viewTask(task)">
				<view class="task-header">
					<view class="task-priority" :class="task.priority">
						<text class="priority-text">{{getPriorityText(task.priority)}}</text>
					</view>
					<view class="task-status" :class="task.status">
						<text class="status-text">{{getStatusText(task.status)}}</text>
					</view>
				</view>
				<view class="task-content">
					<text class="task-title">{{task.title}}</text>
					<text class="task-desc">{{task.description}}</text>
					<view class="task-meta">
						<view class="meta-item">
							<Icon name="time-line" size="20rpx" color="#999"></Icon>
							<text class="meta-text">{{task.deadline}}</text>
						</view>
						<view class="meta-item" v-if="task.location">
							<Icon name="location-line" size="20rpx" color="#999"></Icon>
							<text class="meta-text">{{task.location}}</text>
						</view>
					</view>
					<view class="task-tags" v-if="task.tags && task.tags.length > 0">
						<text class="task-tag" v-for="tag in task.tags" :key="tag">{{tag}}</text>
					</view>
				</view>
				<view class="task-actions">
					<button class="action-btn complete" v-if="task.status === 'pending'" @click.stop="completeTask(task)">
						<Icon name="check-line" size="20rpx" color="white"></Icon>
						<text>完成</text>
					</button>
					<button class="action-btn edit" @click.stop="editTask(task)">
						<Icon name="edit-line" size="20rpx" color="#ff8a00"></Icon>
						<text>编辑</text>
					</button>
				</view>
			</view>
		</view>

		<!-- 空状态 -->
		<view class="empty-state" v-if="filteredTasks.length === 0">
			<Icon name="task-line" size="120rpx" color="#ccc"></Icon>
			<text class="empty-text">暂无任务</text>
			<text class="empty-desc">点击下方按钮创建您的第一个任务</text>
		</view>

		<!-- 浮动按钮 -->
		<view class="fab" @click="createTask">
			<Icon name="add-line" size="40rpx" color="white"></Icon>
		</view>
	</view>
</template>

<script>
import Icon from '@/components/Icon/Icon.vue'
import CrudAPI from '@/utils/crudAPI.js'
import FeedbackUtils from '@/utils/feedback.js'

export default {
	components: {
		Icon
	},
	data() {
		return {
			activeFilter: 'all',
			filterList: [
				{ key: 'all', name: '全部' },
				{ key: 'pending', name: '待完成' },
				{ key: 'completed', name: '已完成' },
				{ key: 'overdue', name: '已逾期' },
				{ key: 'today', name: '今日' }
			],
			taskList: [],
			loading: false
		}
	},
	onLoad() {
		this.loadTasks()
	},
	onShow() {
		// 页面显示时重新加载数据，确保数据同步
		this.loadTasks()
	},
	computed: {
		totalTasks() {
			return this.taskList.length;
		},
		pendingTasks() {
			return this.taskList.filter(task => task.status === 'pending').length;
		},
		completedTasks() {
			return this.taskList.filter(task => task.status === 'completed').length;
		},
		filteredTasks() {
			if (this.activeFilter === 'all') {
				return this.taskList;
			}
			return this.taskList.filter(task => {
				if (this.activeFilter === 'today') {
					const today = new Date().toISOString().split('T')[0];
					return task.deadline.startsWith(today);
				}
				return task.status === this.activeFilter;
			});
		}
	},
	methods: {
		// 加载任务列表
		async loadTasks() {
			if (this.loading) return

			this.loading = true
			try {
				const response = await CrudAPI.getTasks()
				if (response.success) {
					this.taskList = response.data || []
					console.log('任务列表加载成功:', this.taskList.length, '个任务')
				} else {
					console.error('加载任务失败:', response.message)
					FeedbackUtils.showError('加载任务失败')
				}
			} catch (error) {
				console.error('加载任务异常:', error)
				FeedbackUtils.showError('网络异常，请重试')
			} finally {
				this.loading = false
			}
		},

		selectFilter(filter) {
			this.activeFilter = filter;
		},

		viewTask(task) {
			uni.navigateTo({
				url: `/pages/task/detail?id=${task.id}`
			});
		},

		createTask() {
			uni.navigateTo({
				url: '/pages/task/manage'
			});
		},

		editTask(task) {
			uni.navigateTo({
				url: `/pages/task/manage?id=${task.id}&mode=edit`
			});
		},

		async completeTask(task) {
			try {
				const result = await new Promise((resolve) => {
					uni.showModal({
						title: '确认完成',
						content: `确定要完成任务"${task.title}"吗？`,
						success: (res) => resolve(res.confirm),
						fail: () => resolve(false)
					})
				})

				if (!result) return

				// 调用API更新任务状态
				const updateData = { ...task, status: 'completed' }
				const response = await CrudAPI.updateTask(task.id, updateData)

				if (response.success) {
					// 更新本地数据
					const index = this.taskList.findIndex(t => t.id === task.id)
					if (index !== -1) {
						this.taskList[index].status = 'completed'
					}
					FeedbackUtils.showSuccess('任务已完成')
				} else {
					FeedbackUtils.showError('更新失败，请重试')
				}
			} catch (error) {
				console.error('完成任务失败:', error)
				FeedbackUtils.showError('操作失败，请重试')
			}
		},
		getPriorityText(priority) {
			const priorityMap = {
				'high': '高',
				'medium': '中',
				'low': '低'
			};
			return priorityMap[priority] || '中';
		},
		getStatusText(status) {
			const statusMap = {
				'pending': '待完成',
				'completed': '已完成',
				'overdue': '已逾期'
			};
			return statusMap[status] || '未知';
		}
	}
}
</script>

<style scoped>
.container {
	background: #f8f9fa;
	min-height: 100vh;
	padding-bottom: 200rpx;
}

.header-stats {
	background: linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%);
	padding: 40rpx;
	display: flex;
	gap: 20rpx;
}

.stat-card {
	flex: 1;
	background: rgba(255, 255, 255, 0.2);
	border-radius: 20rpx;
	padding: 30rpx;
	text-align: center;
}

.stat-number {
	font-size: 48rpx;
	font-weight: bold;
	color: white;
	display: block;
	margin-bottom: 10rpx;
}

.stat-label {
	font-size: 24rpx;
	color: rgba(255, 255, 255, 0.9);
}

.filter-section {
	background: white;
	padding: 30rpx 0;
	border-bottom: 1rpx solid #f0f0f0;
}

.filter-scroll {
	white-space: nowrap;
}

.filter-list {
	display: inline-flex;
	gap: 20rpx;
	padding: 0 40rpx;
}

.filter-item {
	padding: 15rpx 30rpx;
	border-radius: 25rpx;
	background: #f8f9fa;
	white-space: nowrap;
}

.filter-item.active {
	background: linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%);
}

.filter-text {
	font-size: 26rpx;
	color: #666;
}

.filter-item.active .filter-text {
	color: white;
}

.loading-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 100rpx 40rpx;
}

.loading-spinner {
	width: 60rpx;
	height: 60rpx;
	border: 4rpx solid #f0f0f0;
	border-top: 4rpx solid #ff8a00;
	border-radius: 50%;
	animation: spin 1s linear infinite;
}

@keyframes spin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

.loading-text {
	font-size: 28rpx;
	color: #666;
	margin-top: 20rpx;
}

.task-list {
	padding: 40rpx;
}

.task-item {
	background: white;
	border-radius: 30rpx;
	padding: 30rpx;
	margin-bottom: 30rpx;
	box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.task-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20rpx;
}

.task-priority, .task-status {
	padding: 8rpx 20rpx;
	border-radius: 20rpx;
	font-size: 22rpx;
}

.task-priority.high {
	background: rgba(244, 67, 54, 0.1);
	color: #f44336;
}

.task-priority.medium {
	background: rgba(255, 152, 0, 0.1);
	color: #ff9800;
}

.task-priority.low {
	background: rgba(76, 175, 80, 0.1);
	color: #4caf50;
}

.task-status.pending {
	background: rgba(255, 152, 0, 0.1);
	color: #ff9800;
}

.task-status.completed {
	background: rgba(76, 175, 80, 0.1);
	color: #4caf50;
}

.task-status.overdue {
	background: rgba(244, 67, 54, 0.1);
	color: #f44336;
}

.task-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 10rpx;
}

.task-desc {
	font-size: 26rpx;
	color: #666;
	display: block;
	margin-bottom: 20rpx;
	line-height: 1.5;
}

.task-meta {
	display: flex;
	gap: 30rpx;
	margin-bottom: 20rpx;
}

.meta-item {
	display: flex;
	align-items: center;
	gap: 8rpx;
}

.meta-text {
	font-size: 24rpx;
	color: #666;
}

.task-tags {
	display: flex;
	gap: 10rpx;
	margin-bottom: 20rpx;
}

.task-tag {
	background: rgba(255, 138, 0, 0.1);
	color: #ff8a00;
	font-size: 22rpx;
	padding: 5rpx 15rpx;
	border-radius: 15rpx;
}

.task-actions {
	display: flex;
	gap: 15rpx;
}

.action-btn {
	padding: 15rpx 25rpx;
	border-radius: 20rpx;
	font-size: 24rpx;
	display: flex;
	align-items: center;
	gap: 8rpx;
	border: none;
}

.action-btn.complete {
	background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);
	color: white;
}

.action-btn.edit {
	background: white;
	color: #ff8a00;
	border: 2rpx solid #ff8a00;
}

.empty-state {
	text-align: center;
	padding: 100rpx 40rpx;
}

.empty-text {
	font-size: 32rpx;
	color: #999;
	display: block;
	margin: 30rpx 0 15rpx;
}

.empty-desc {
	font-size: 26rpx;
	color: #ccc;
	display: block;
}

.fab {
	position: fixed;
	bottom: 40rpx;
	right: 40rpx;
	width: 120rpx;
	height: 120rpx;
	background: linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%);
	border-radius: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 8rpx 30rpx rgba(255, 138, 0, 0.3);
}
</style>
