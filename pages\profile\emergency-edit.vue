<template>
	<view class="container">
		<!-- 自定义导航栏 -->
		<view class="custom-navbar">
			<view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
			<view class="navbar-content">
				<view class="navbar-left" @click="goBack">
					<Icon name="arrow-left-line" size="36rpx" color="#333"></Icon>
					<text class="back-text">返回</text>
				</view>
				<view class="navbar-center">
					<text class="navbar-title">{{isEdit ? '编辑联系人' : '添加联系人'}}</text>
				</view>
				<view class="navbar-right">
					<view class="nav-action" @click="saveContact">
						<text class="save-text">保存</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 联系人信息表单 -->
		<view class="form-section">
			<view class="form-item">
				<text class="form-label">姓名 *</text>
				<input class="form-input" v-model="contactInfo.name" placeholder="请输入联系人姓名" />
			</view>
			
			<view class="form-item">
				<text class="form-label">关系 *</text>
				<picker :value="relationIndex" :range="relationOptions" @change="onRelationChange">
					<view class="picker-input">
						<text>{{contactInfo.relation || '请选择关系'}}</text>
						<Icon name="arrow-down-s-line" size="24rpx" color="#999"></Icon>
					</view>
				</picker>
			</view>
			
			<view class="form-item">
				<text class="form-label">手机号码 *</text>
				<input class="form-input" v-model="contactInfo.phone" placeholder="请输入手机号码" type="number" />
			</view>
			
			<view class="form-item">
				<text class="form-label">座机号码</text>
				<input class="form-input" v-model="contactInfo.landline" placeholder="请输入座机号码（可选）" />
			</view>
			
			<view class="form-item">
				<text class="form-label">工作单位</text>
				<input class="form-input" v-model="contactInfo.workplace" placeholder="请输入工作单位（可选）" />
			</view>
			
			<view class="form-item">
				<text class="form-label">居住地址</text>
				<textarea class="form-textarea" v-model="contactInfo.address" placeholder="请输入居住地址（可选）"></textarea>
			</view>
		</view>

		<!-- 联系人设置 -->
		<view class="settings-section">
			<view class="section-header">
				<Icon name="settings-3-line" size="32rpx" color="#ff8a00"></Icon>
				<text class="section-title">联系人设置</text>
			</view>
			
			<view class="setting-item">
				<view class="setting-info">
					<text class="setting-label">设为主要联系人</text>
					<text class="setting-desc">主要联系人将优先接收通知</text>
				</view>
				<switch :checked="contactInfo.isPrimary" @change="onPrimaryChange" color="#ff8a00" />
			</view>
			
			<view class="setting-item">
				<view class="setting-info">
					<text class="setting-label">设为紧急联系人</text>
					<text class="setting-desc">紧急情况下会自动联系</text>
				</view>
				<switch :checked="contactInfo.isEmergency" @change="onEmergencyChange" color="#ff8a00" />
			</view>
			
			<view class="setting-item">
				<view class="setting-info">
					<text class="setting-label">接收健康报告</text>
					<text class="setting-desc">定期发送健康状况报告</text>
				</view>
				<switch :checked="contactInfo.receiveHealthReport" @change="onHealthReportChange" color="#ff8a00" />
			</view>
		</view>

		<!-- 通知偏好 -->
		<view class="notification-section">
			<view class="section-header">
				<Icon name="notification-3-line" size="32rpx" color="#ff8a00"></Icon>
				<text class="section-title">通知偏好</text>
			</view>
			
			<view class="notification-item">
				<text class="notification-label">短信通知</text>
				<switch :checked="contactInfo.notifications.sms" @change="onSmsChange" color="#ff8a00" />
			</view>
			
			<view class="notification-item">
				<text class="notification-label">电话通知</text>
				<switch :checked="contactInfo.notifications.call" @change="onCallChange" color="#ff8a00" />
			</view>
			
			<view class="notification-item">
				<text class="notification-label">微信通知</text>
				<switch :checked="contactInfo.notifications.wechat" @change="onWechatChange" color="#ff8a00" />
			</view>
		</view>

		<!-- 测试联系 -->
		<view class="test-section">
			<view class="section-header">
				<Icon name="phone-line" size="32rpx" color="#ff8a00"></Icon>
				<text class="section-title">测试联系</text>
			</view>
			
			<view class="test-buttons">
				<button class="test-btn call" @click="testCall">
					<Icon name="phone-line" size="32rpx" color="white"></Icon>
					<text>拨打电话</text>
				</button>
				<button class="test-btn message" @click="testMessage">
					<Icon name="message-line" size="32rpx" color="white"></Icon>
					<text>发送短信</text>
				</button>
			</view>
		</view>

		<!-- 保存按钮 -->
		<view class="save-section">
			<button class="save-btn" @click="saveContact">{{isEdit ? '更新联系人' : '添加联系人'}}</button>
		</view>
	</view>
</template>

<script>
import Icon from '@/components/Icon/Icon.vue'

export default {
	components: {
		Icon
	},
	data() {
		return {
			statusBarHeight: 0, // 状态栏高度
			isEdit: false,
			editIndex: -1,
			relationIndex: 0,
			relationOptions: ['儿子', '女儿', '配偶', '父母', '兄弟姐妹', '朋友', '邻居', '医生', '护工', '其他'],
			contactInfo: {
				name: '',
				relation: '',
				phone: '',
				landline: '',
				workplace: '',
				address: '',
				isPrimary: false,
				isEmergency: false,
				receiveHealthReport: false,
				notifications: {
					sms: true,
					call: true,
					wechat: false
				}
			}
		}
	},
	onLoad(options) {
		// 获取状态栏高度
		const systemInfo = uni.getSystemInfoSync();
		this.statusBarHeight = systemInfo.statusBarHeight || 44;

		if (options.index !== undefined) {
			this.isEdit = true;
			this.editIndex = parseInt(options.index);
			this.loadContactInfo();
		}
	},
	methods: {
		loadContactInfo() {
			const savedContacts = uni.getStorageSync('emergencyContacts') || [];
			if (this.isEdit && savedContacts[this.editIndex]) {
				this.contactInfo = { ...savedContacts[this.editIndex] };
				this.relationIndex = this.relationOptions.indexOf(this.contactInfo.relation);
			}
		},
		onRelationChange(e) {
			this.relationIndex = e.detail.value;
			this.contactInfo.relation = this.relationOptions[e.detail.value];
		},
		onPrimaryChange(e) {
			this.contactInfo.isPrimary = e.detail.value;
		},
		onEmergencyChange(e) {
			this.contactInfo.isEmergency = e.detail.value;
		},
		onHealthReportChange(e) {
			this.contactInfo.receiveHealthReport = e.detail.value;
		},
		onSmsChange(e) {
			this.contactInfo.notifications.sms = e.detail.value;
		},
		onCallChange(e) {
			this.contactInfo.notifications.call = e.detail.value;
		},
		onWechatChange(e) {
			this.contactInfo.notifications.wechat = e.detail.value;
		},
		testCall() {
			if (!this.contactInfo.phone) {
				uni.showToast({
					title: '请先输入手机号码',
					icon: 'none'
				});
				return;
			}
			
			uni.makePhoneCall({
				phoneNumber: this.contactInfo.phone
			});
		},
		testMessage() {
			if (!this.contactInfo.phone) {
				uni.showToast({
					title: '请先输入手机号码',
					icon: 'none'
				});
				return;
			}
			
			uni.showToast({
				title: '短信功能开发中',
				icon: 'none'
			});
		},
		validateForm() {
			if (!this.contactInfo.name.trim()) {
				uni.showToast({
					title: '请输入联系人姓名',
					icon: 'none'
				});
				return false;
			}
			
			if (!this.contactInfo.relation) {
				uni.showToast({
					title: '请选择关系',
					icon: 'none'
				});
				return false;
			}
			
			if (!this.contactInfo.phone.trim()) {
				uni.showToast({
					title: '请输入手机号码',
					icon: 'none'
				});
				return false;
			}
			
			// 验证手机号格式
			const phoneRegex = /^1[3-9]\d{9}$/;
			if (!phoneRegex.test(this.contactInfo.phone)) {
				uni.showToast({
					title: '请输入正确的手机号码',
					icon: 'none'
				});
				return false;
			}
			
			return true;
		},
		saveContact() {
			if (!this.validateForm()) {
				return;
			}
			
			let savedContacts = uni.getStorageSync('emergencyContacts') || [];
			
			if (this.isEdit) {
				// 更新现有联系人
				savedContacts[this.editIndex] = { ...this.contactInfo };
			} else {
				// 添加新联系人
				savedContacts.push({ ...this.contactInfo });
			}
			
			// 保存到本地存储
			uni.setStorageSync('emergencyContacts', savedContacts);
			
			uni.showToast({
				title: this.isEdit ? '更新成功' : '添加成功',
				icon: 'success'
			});
			
			setTimeout(() => {
				this.goBack();
			}, 1500);
		},

		// 返回上一页
		goBack() {
			uni.navigateBack({
				fail: () => {
					uni.navigateTo({
						url: '/pages/profile/emergency'
					});
				}
			});
		}
	}
}
</script>

<style scoped>
.container {
	background: #f8f9fa;
	min-height: 100vh;
	padding-bottom: 200rpx;
}

/* 导航栏样式 */
.custom-navbar {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 1000;
	background: rgba(255, 255, 255, 0.95);
	backdrop-filter: blur(20rpx);
	-webkit-backdrop-filter: blur(20rpx);
	border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);
}

.navbar-content {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 20rpx 32rpx;
	min-height: 88rpx;
}

.navbar-left {
	display: flex;
	align-items: center;
	gap: 12rpx;
	padding: 8rpx 16rpx 8rpx 0;
	border-radius: 20rpx;
	transition: all 0.2s ease;
}

.navbar-left:active {
	background-color: rgba(0, 0, 0, 0.04);
	transform: scale(0.96);
}

.back-text {
	font-size: 32rpx;
	color: #333;
	font-weight: 500;
}

.navbar-center {
	position: absolute;
	left: 50%;
	transform: translateX(-50%);
}

.navbar-title {
	font-size: 34rpx;
	font-weight: 600;
	color: #333;
}

.navbar-right {
	display: flex;
	align-items: center;
}

.nav-action {
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
	transition: all 0.2s ease;
}

.nav-action:active {
	background-color: rgba(255, 138, 0, 0.1);
	transform: scale(0.96);
}

.save-text {
	font-size: 32rpx;
	color: #ff8a00;
	font-weight: 600;
}

.form-section, .settings-section, .notification-section, .test-section {
	background: white;
	margin: 20rpx;
	border-radius: 30rpx;
	padding: 40rpx;
}

.form-section {
	margin-top: 220rpx; /* 为导航栏留出空间 */
}

.section-header {
	display: flex;
	align-items: center;
	gap: 15rpx;
	margin-bottom: 30rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.form-item {
	margin-bottom: 30rpx;
}

.form-item:last-child {
	margin-bottom: 0;
}

.form-label {
	font-size: 28rpx;
	color: #333;
	font-weight: bold;
	display: block;
	margin-bottom: 15rpx;
}

.form-input {
	background: #f8f9fa;
	border: 2rpx solid #e0e0e0;
	border-radius: 15rpx;
	padding: 25rpx 20rpx;
	font-size: 28rpx;
	color: #333;
}

.form-input:focus {
	border-color: #ff8a00;
}

.form-textarea {
	background: #f8f9fa;
	border: 2rpx solid #e0e0e0;
	border-radius: 15rpx;
	padding: 25rpx 20rpx;
	font-size: 28rpx;
	color: #333;
	min-height: 120rpx;
}

.form-textarea:focus {
	border-color: #ff8a00;
}

.picker-input {
	background: #f8f9fa;
	border: 2rpx solid #e0e0e0;
	border-radius: 15rpx;
	padding: 25rpx 20rpx;
	font-size: 28rpx;
	color: #333;
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.setting-item, .notification-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 25rpx 0;
	border-bottom: 1rpx solid #f0f0f0;
}

.setting-item:last-child, .notification-item:last-child {
	border-bottom: none;
}

.setting-info {
	flex: 1;
}

.setting-label, .notification-label {
	font-size: 28rpx;
	color: #333;
	font-weight: bold;
	display: block;
	margin-bottom: 5rpx;
}

.setting-desc {
	font-size: 24rpx;
	color: #666;
	display: block;
}

.test-buttons {
	display: flex;
	gap: 20rpx;
}

.test-btn {
	flex: 1;
	height: 80rpx;
	border-radius: 15rpx;
	border: none;
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 10rpx;
	font-size: 28rpx;
	color: white;
}

.test-btn.call {
	background: #4caf50;
}

.test-btn.message {
	background: #2196f3;
}

.save-section {
	padding: 40rpx;
}

.save-btn {
	width: 100%;
	height: 100rpx;
	background: linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%);
	color: white;
	border: none;
	border-radius: 20rpx;
	font-size: 32rpx;
	font-weight: bold;
}
</style>
