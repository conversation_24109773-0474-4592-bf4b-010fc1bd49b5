<template>
	<view class="form-builder" :class="{ 'elderly-mode': elderlyMode }">
		<view 
			v-for="(field, index) in fields" 
			:key="field.key || index"
			class="form-field"
			:class="[
				`field-${field.type}`,
				{ 'field-required': field.required },
				{ 'field-error': hasError(field.key) }
			]"
		>
			<!-- 字段标签 -->
			<view v-if="field.label" class="field-label">
				<text class="label-text">{{ field.label }}</text>
				<text v-if="field.required" class="required-mark">*</text>
			</view>
			
			<!-- 输入框 -->
			<input 
				v-if="field.type === 'input'"
				class="field-input"
				:type="field.inputType || 'text'"
				:placeholder="field.placeholder"
				:maxlength="field.maxlength"
				:disabled="field.disabled"
				:value="formData[field.key]"
				@input="handleInput(field.key, $event)"
				@blur="handleBlur(field.key)"
			/>
			
			<!-- 文本域 -->
			<textarea 
				v-else-if="field.type === 'textarea'"
				class="field-textarea"
				:placeholder="field.placeholder"
				:maxlength="field.maxlength"
				:disabled="field.disabled"
				:auto-height="field.autoHeight"
				:value="formData[field.key]"
				@input="handleInput(field.key, $event)"
				@blur="handleBlur(field.key)"
			></textarea>
			
			<!-- 选择器 -->
			<picker 
				v-else-if="field.type === 'picker'"
				:mode="field.mode || 'selector'"
				:range="field.options"
				:range-key="field.optionLabel"
				:value="getPickerValue(field)"
				:disabled="field.disabled"
				@change="handlePickerChange(field.key, field, $event)"
			>
				<view class="field-picker">
					<text class="picker-text" :class="{ 'placeholder': !formData[field.key] }">
						{{ getPickerDisplayText(field) }}
					</text>
					<Icon name="arrow-down-line" size="24rpx" color="#999"></Icon>
				</view>
			</picker>
			
			<!-- 开关 -->
			<switch 
				v-else-if="field.type === 'switch'"
				class="field-switch"
				:checked="formData[field.key]"
				:disabled="field.disabled"
				@change="handleSwitchChange(field.key, $event)"
			></switch>
			
			<!-- 单选框组 -->
			<radio-group 
				v-else-if="field.type === 'radio'"
				class="field-radio-group"
				@change="handleRadioChange(field.key, $event)"
			>
				<label 
					v-for="option in field.options" 
					:key="option.value"
					class="radio-item"
				>
					<radio 
						:value="option.value" 
						:checked="formData[field.key] === option.value"
						:disabled="field.disabled"
					></radio>
					<text class="radio-text">{{ option.label }}</text>
				</label>
			</radio-group>
			
			<!-- 复选框组 -->
			<checkbox-group 
				v-else-if="field.type === 'checkbox'"
				class="field-checkbox-group"
				@change="handleCheckboxChange(field.key, $event)"
			>
				<label 
					v-for="option in field.options" 
					:key="option.value"
					class="checkbox-item"
				>
					<checkbox 
						:value="option.value" 
						:checked="isCheckboxChecked(field.key, option.value)"
						:disabled="field.disabled"
					></checkbox>
					<text class="checkbox-text">{{ option.label }}</text>
				</label>
			</checkbox-group>
			
			<!-- 日期选择器 -->
			<picker 
				v-else-if="field.type === 'date'"
				mode="date"
				:start="field.startDate"
				:end="field.endDate"
				:value="formData[field.key]"
				:disabled="field.disabled"
				@change="handleDateChange(field.key, $event)"
			>
				<view class="field-picker">
					<text class="picker-text" :class="{ 'placeholder': !formData[field.key] }">
						{{ formData[field.key] || field.placeholder || '请选择日期' }}
					</text>
					<Icon name="calendar-line" size="24rpx" color="#999"></Icon>
				</view>
			</picker>
			
			<!-- 时间选择器 -->
			<picker 
				v-else-if="field.type === 'time'"
				mode="time"
				:value="formData[field.key]"
				:disabled="field.disabled"
				@change="handleTimeChange(field.key, $event)"
			>
				<view class="field-picker">
					<text class="picker-text" :class="{ 'placeholder': !formData[field.key] }">
						{{ formData[field.key] || field.placeholder || '请选择时间' }}
					</text>
					<Icon name="time-line" size="24rpx" color="#999"></Icon>
				</view>
			</picker>
			
			<!-- 字段描述 -->
			<text v-if="field.description" class="field-description">
				{{ field.description }}
			</text>
			
			<!-- 错误信息 -->
			<text v-if="hasError(field.key)" class="field-error-text">
				{{ getError(field.key) }}
			</text>
		</view>
	</view>
</template>

<script>
import Icon from '@/components/Icon/Icon.vue'

export default {
	name: 'FormBuilder',
	components: {
		Icon
	},
	props: {
		fields: {
			type: Array,
			required: true
		},
		value: {
			type: Object,
			default: () => ({})
		},
		elderlyMode: {
			type: Boolean,
			default: false
		}
	},
	data() {
		return {
			formData: {},
			errors: {}
		}
	},
	watch: {
		value: {
			handler(newVal) {
				this.formData = { ...newVal }
			},
			immediate: true,
			deep: true
		},
		formData: {
			handler(newVal) {
				this.$emit('input', newVal)
				this.$emit('change', newVal)
			},
			deep: true
		}
	},
	methods: {
		handleInput(key, event) {
			this.formData[key] = event.detail.value
			this.clearError(key)
		},
		handleBlur(key) {
			this.validateField(key)
		},
		handlePickerChange(key, field, event) {
			const index = event.detail.value
			if (field.mode === 'multiSelector') {
				// 多列选择器处理
				this.formData[key] = event.detail.value
			} else {
				// 单列选择器处理
				const option = field.options[index]
				this.formData[key] = option ? option.value : ''
			}
			this.clearError(key)
		},
		handleSwitchChange(key, event) {
			this.formData[key] = event.detail.value
			this.clearError(key)
		},
		handleRadioChange(key, event) {
			this.formData[key] = event.detail.value
			this.clearError(key)
		},
		handleCheckboxChange(key, event) {
			this.formData[key] = event.detail.value
			this.clearError(key)
		},
		handleDateChange(key, event) {
			this.formData[key] = event.detail.value
			this.clearError(key)
		},
		handleTimeChange(key, event) {
			this.formData[key] = event.detail.value
			this.clearError(key)
		},
		getPickerValue(field) {
			if (field.mode === 'multiSelector') {
				return this.formData[field.key] || []
			}
			const value = this.formData[field.key]
			const index = field.options.findIndex(option => option.value === value)
			return Math.max(0, index)
		},
		getPickerDisplayText(field) {
			const value = this.formData[field.key]
			if (!value) {
				return field.placeholder || '请选择'
			}
			
			if (field.mode === 'multiSelector') {
				// 多列选择器显示逻辑
				return value.join(' ')
			}
			
			const option = field.options.find(opt => opt.value === value)
			return option ? option.label : field.placeholder || '请选择'
		},
		isCheckboxChecked(key, value) {
			const values = this.formData[key] || []
			return values.includes(value)
		},
		validateField(key) {
			const field = this.fields.find(f => f.key === key)
			if (!field) return true
			
			const value = this.formData[key]
			
			// 必填验证
			if (field.required && (!value || value === '')) {
				this.setError(key, `${field.label}不能为空`)
				return false
			}
			
			// 自定义验证
			if (field.validator && typeof field.validator === 'function') {
				const result = field.validator(value, this.formData)
				if (result !== true) {
					this.setError(key, result)
					return false
				}
			}
			
			this.clearError(key)
			return true
		},
		validate() {
			let isValid = true
			this.fields.forEach(field => {
				if (!this.validateField(field.key)) {
					isValid = false
				}
			})
			return isValid
		},
		setError(key, message) {
			this.$set(this.errors, key, message)
		},
		clearError(key) {
			this.$delete(this.errors, key)
		},
		hasError(key) {
			return !!this.errors[key]
		},
		getError(key) {
			return this.errors[key]
		},
		reset() {
			this.formData = {}
			this.errors = {}
		}
	}
}
</script>

<style scoped>
.form-builder {
	padding: 20rpx;
}

.form-field {
	margin-bottom: 40rpx;
}

.field-label {
	display: flex;
	align-items: center;
	margin-bottom: 15rpx;
}

.label-text {
	font-size: 28rpx;
	color: #333;
	font-weight: 500;
}

.required-mark {
	color: #ff4444;
	margin-left: 5rpx;
	font-size: 28rpx;
}

.field-input,
.field-textarea {
	width: 100%;
	padding: 20rpx;
	border: 2rpx solid #e0e0e0;
	border-radius: 8rpx;
	font-size: 28rpx;
	color: #333;
	background: #fff;
	box-sizing: border-box;
}

.field-textarea {
	min-height: 120rpx;
	resize: none;
}

.field-picker {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 20rpx;
	border: 2rpx solid #e0e0e0;
	border-radius: 8rpx;
	background: #fff;
}

.picker-text {
	font-size: 28rpx;
	color: #333;
}

.picker-text.placeholder {
	color: #999;
}

.field-switch {
	transform: scale(1.2);
}

.field-radio-group,
.field-checkbox-group {
	display: flex;
	flex-wrap: wrap;
	gap: 20rpx;
}

.radio-item,
.checkbox-item {
	display: flex;
	align-items: center;
	gap: 10rpx;
}

.radio-text,
.checkbox-text {
	font-size: 28rpx;
	color: #333;
}

.field-description {
	font-size: 24rpx;
	color: #999;
	margin-top: 10rpx;
	line-height: 1.4;
}

.field-error-text {
	font-size: 24rpx;
	color: #ff4444;
	margin-top: 10rpx;
}

.field-error .field-input,
.field-error .field-textarea,
.field-error .field-picker {
	border-color: #ff4444;
}

/* 适老版样式 */
.elderly-mode .label-text {
	font-size: 32rpx;
}

.elderly-mode .field-input,
.elderly-mode .field-textarea,
.elderly-mode .picker-text {
	font-size: 32rpx;
}

.elderly-mode .field-input,
.elderly-mode .field-textarea,
.elderly-mode .field-picker {
	padding: 25rpx;
}

.elderly-mode .radio-text,
.elderly-mode .checkbox-text {
	font-size: 32rpx;
}
</style>
