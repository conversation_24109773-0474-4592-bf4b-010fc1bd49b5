/**
 * iOS风格适老化工具类
 * 在iOS设计规范基础上优化适老化体验
 * 确保老年用户的易用性和现代感并存
 */

// iOS风格适老化配置
export const IOS_ELDERLY_CONFIG = {
  // 字体系统 - 基于iOS Typography 增强版
  typography: {
    // 字体放大倍数（保持iOS比例关系）
    scaleRatio: 1.4, // 增加到1.4倍
    // 最小字体大小（确保可读性）
    minFontSize: 36, // 增加到36rpx
    // 最大字体大小（防止过大）
    maxFontSize: 80, // 新增最大字体限制
    // 字体粗细增强
    fontWeight: {
      regular: 600,   // 增强到iOS Semibold
      medium: 700,    // 增强到iOS Bold
      bold: 800       // 增强到iOS Heavy
    },
    // 行高增强（提升可读性）
    lineHeight: 1.7, // 增加行高
    // 字母间距增强
    letterSpacing: 0.02 // 新增字母间距
  },

  // 颜色系统 - iOS风格超高对比度增强版
  colors: {
    // 文字颜色（超高对比度）
    text: {
      primary: '#000000',      // 纯黑文字
      secondary: '#1a1a1a',    // 深黑次要文字
      tertiary: '#333333',     // 深灰三级文字
      quaternary: '#4a4a4a',   // 中灰四级文字
      disabled: '#999999',     // 禁用文字
      inverse: '#ffffff'       // 反色文字
    },
    // 背景颜色（保持iOS风格）
    background: {
      primary: '#ffffff',      // 纯白背景
      secondary: '#f8f8f8',    // 浅灰背景
      tertiary: '#f0f0f0',     // 中灰背景
      card: '#ffffff',         // 卡片背景
      overlay: 'rgba(0, 0, 0, 0.8)' // 遮罩背景
    },
    // 边框颜色（增强可见性）
    border: {
      primary: '#666666',      // 深灰边框
      secondary: '#999999',    // 中灰边框
      light: '#cccccc',        // 浅灰边框
      focus: '#ff8a00',        // 焦点边框（品牌色）
      error: '#cc0000',        // 错误边框
      success: '#006600'       // 成功边框
    },
    // 状态颜色（增强对比度版本）
    status: {
      success: '#006600',      // 深绿色
      warning: '#cc6600',      // 深橙色
      error: '#cc0000',        // 深红色
      info: '#0066cc',         // 深蓝色
      primary: '#ff8a00'       // 品牌色保持
    }
  },

  // 间距系统 - iOS风格大幅放大
  spacing: {
    // 基础间距放大倍数
    scaleRatio: 1.6, // 增加到1.6倍
    // 最小触摸目标（适老化推荐56pt = 112rpx）
    minTouchTarget: 112, // 增加到112rpx
    // 舒适触摸目标
    comfortableTouchTarget: 128, // 新增舒适触摸目标
    // 大触摸目标
    largeTouchTarget: 144, // 新增大触摸目标
    // 内边距增强
    padding: {
      xs: 16,   // 8 * 1.6 = 12.8 ≈ 16
      sm: 20,   // 12 * 1.6 = 19.2 ≈ 20
      md: 26,   // 16 * 1.6 = 25.6 ≈ 26
      lg: 38,   // 24 * 1.6 = 38.4 ≈ 38
      xl: 51,   // 32 * 1.6 = 51.2 ≈ 51
      xxl: 64   // 新增超大内边距
    },
    // 外边距增强
    margin: {
      xs: 10,   // 6 * 1.6 = 9.6 ≈ 10
      sm: 13,   // 8 * 1.6 = 12.8 ≈ 13
      md: 19,   // 12 * 1.6 = 19.2 ≈ 19
      lg: 26,   // 16 * 1.6 = 25.6 ≈ 26
      xl: 38,   // 24 * 1.6 = 38.4 ≈ 38
      xxl: 51   // 新增超大外边距
    }
  },

  // 圆角系统 - iOS风格适度增大
  borderRadius: {
    xs: 10,   // 8 * 1.25 = 10
    sm: 15,   // 12 * 1.25 = 15
    md: 20,   // 16 * 1.25 = 20
    lg: 25,   // 20 * 1.25 = 25
    xl: 30    // 24 * 1.25 = 30
  },

  // 阴影系统 - iOS风格增强
  shadows: {
    light: '0 2rpx 8rpx rgba(0, 0, 0, 0.06)',
    medium: '0 4rpx 16rpx rgba(0, 0, 0, 0.1)',
    heavy: '0 8rpx 24rpx rgba(0, 0, 0, 0.15)',
    focus: '0 0 0 4rpx rgba(255, 138, 0, 0.3)' // 焦点阴影
  },

  // 动画系统 - 适老化调整
  animations: {
    // 动画时长（稍微延长）
    duration: {
      fast: 200,     // 150 * 1.33 ≈ 200
      standard: 350, // 250 * 1.4 = 350
      slow: 500      // 350 * 1.43 ≈ 500
    },
    // 缓动函数（保持iOS风格）
    easing: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)',
    // 减少复杂动画
    reduceMotion: true
  },

  // 交互反馈 - 适老化全面增强
  feedback: {
    // 震动强度
    vibration: {
      light: 'heavy',   // 轻触使用重度强度
      medium: 'heavy',  // 中等使用重度
      heavy: 'heavy'    // 重度保持重度
    },
    // 语音播报
    voice: {
      enabled: true,
      volume: 0.8,      // 增加音量
      speed: 0.6,       // 更慢语速
      pitch: 1.0,       // 音调
      language: 'zh-CN' // 中文
    },
    // 提示时长
    toastDuration: {
      short: 4000,      // 延长到4秒
      long: 6000,       // 延长到6秒
      extra: 8000       // 新增超长提示
    },
    // 音效反馈
    sound: {
      enabled: true,
      success: 'success.wav',
      error: 'error.wav',
      click: 'click.wav'
    },
    // 视觉反馈增强
    visual: {
      highlightDuration: 800, // 高亮持续时间
      focusRingWidth: 4,      // 焦点环宽度
      animationScale: 1.1     // 动画缩放比例
    }
  }
}

/**
 * 适老化模式管理类
 */
export class ElderlyModeManager {
  constructor() {
    this.isEnabled = false
    this.settings = {}
    this.init()
  }

  /**
   * 初始化适老化模式
   */
  init() {
    try {
      this.isEnabled = uni.getStorageSync('elderlyMode') || false
      this.settings = uni.getStorageSync('elderlySettings') || {}
      
      if (this.isEnabled) {
        this.applyElderlyStyles()
      }
    } catch (error) {
      console.error('适老化模式初始化失败:', error)
    }
  }

  /**
   * 启用适老化模式
   */
  enable() {
    this.isEnabled = true
    uni.setStorageSync('elderlyMode', true)
    this.applyElderlyStyles()
    this.showModeChangeNotification('已开启适老版')
  }

  /**
   * 禁用适老化模式
   */
  disable() {
    this.isEnabled = false
    uni.setStorageSync('elderlyMode', false)
    this.removeElderlyStyles()
    this.showModeChangeNotification('已关闭适老版')
  }

  /**
   * 切换适老化模式
   */
  toggle() {
    if (this.isEnabled) {
      this.disable()
    } else {
      this.enable()
    }
    return this.isEnabled
  }

  /**
   * 应用适老化样式 - 增强版
   */
  applyElderlyStyles() {
    const config = IOS_ELDERLY_CONFIG

    // #ifdef H5
    // 设置CSS变量
    const root = document.documentElement || document.body
    if (root && root.style) {
      // 字体系统
      root.style.setProperty('--elderly-font-scale', config.typography.scaleRatio)
      root.style.setProperty('--elderly-line-height', config.typography.lineHeight)
      root.style.setProperty('--elderly-letter-spacing', config.typography.letterSpacing)
      root.style.setProperty('--elderly-min-font-size', `${config.typography.minFontSize}rpx`)
      root.style.setProperty('--elderly-max-font-size', `${config.typography.maxFontSize}rpx`)

      // 颜色系统 - 完整设置
      root.style.setProperty('--elderly-text-primary', config.colors.text.primary)
      root.style.setProperty('--elderly-text-secondary', config.colors.text.secondary)
      root.style.setProperty('--elderly-text-tertiary', config.colors.text.tertiary)
      root.style.setProperty('--elderly-bg-primary', config.colors.background.primary)
      root.style.setProperty('--elderly-bg-secondary', config.colors.background.secondary)
      root.style.setProperty('--elderly-border-primary', config.colors.border.primary)
      root.style.setProperty('--elderly-border-focus', config.colors.border.focus)

      // 间距系统 - 完整设置
      root.style.setProperty('--elderly-spacing-scale', config.spacing.scaleRatio)
      root.style.setProperty('--elderly-touch-target-min', `${config.spacing.minTouchTarget}rpx`)
      root.style.setProperty('--elderly-touch-target-comfortable', `${config.spacing.comfortableTouchTarget}rpx`)
      root.style.setProperty('--elderly-touch-target-large', `${config.spacing.largeTouchTarget}rpx`)

      // 视觉反馈系统
      root.style.setProperty('--elderly-focus-ring-width', `${config.feedback.visual.focusRingWidth}rpx`)
      root.style.setProperty('--elderly-animation-scale', config.feedback.visual.animationScale)
      root.style.setProperty('--elderly-highlight-duration', `${config.feedback.visual.highlightDuration}ms`)
    }

    // 添加全局适老化类
    const body = document.body
    if (body) {
      body.classList.add('ios-elderly-mode')
    }
    // #endif

    // 触发适老化样式更新事件
    this.triggerElderlyStyleUpdate()
  }

  /**
   * 移除适老化样式
   */
  removeElderlyStyles() {
    // #ifdef H5
    const body = document.body
    if (body) {
      body.classList.remove('ios-elderly-mode')
    }
    // #endif
  }

  /**
   * 显示模式切换通知
   */
  showModeChangeNotification(message) {
    const config = IOS_ELDERLY_CONFIG.feedback
    
    uni.showToast({
      title: message,
      icon: 'success',
      duration: config.toastDuration.short
    })

    // 语音播报（如果启用）
    if (config.voice.enabled) {
      this.speakText(message)
    }

    // 震动反馈
    try {
      uni.vibrateShort({
        type: config.vibration.medium
      })
    } catch (error) {
      console.log('震动反馈不可用')
    }
  }

  /**
   * 语音播报
   */
  speakText(text) {
    // #ifdef APP-PLUS
    try {
      if (plus.speech) {
        const config = IOS_ELDERLY_CONFIG.feedback.voice
        plus.speech.startSpeech({
          content: text,
          volume: config.volume,
          speed: config.speed
        })
      }
    } catch (error) {
      console.log('语音播报失败:', error)
    }
    // #endif
  }

  /**
   * 获取适老化配置
   */
  getConfig() {
    return IOS_ELDERLY_CONFIG
  }

  /**
   * 检查是否启用
   */
  isElderlyMode() {
    return this.isEnabled
  }

  /**
   * 获取适老化字体大小
   */
  getElderlyFontSize(baseFontSize) {
    if (!this.isEnabled) return baseFontSize
    
    const config = IOS_ELDERLY_CONFIG.typography
    const scaledSize = baseFontSize * config.scaleRatio
    
    // 确保不小于最小字体大小
    return Math.max(scaledSize, config.minFontSize)
  }

  /**
   * 获取适老化间距
   */
  getElderlySpacing(baseSpacing) {
    if (!this.isEnabled) return baseSpacing
    
    return baseSpacing * IOS_ELDERLY_CONFIG.spacing.scaleRatio
  }

  /**
   * 获取适老化触摸目标大小
   */
  getElderlyTouchTarget(baseSize, level = 'min') {
    if (!this.isEnabled) return baseSize

    const config = IOS_ELDERLY_CONFIG.spacing
    const targetSizes = {
      min: config.minTouchTarget,
      comfortable: config.comfortableTouchTarget,
      large: config.largeTouchTarget
    }

    const targetSize = targetSizes[level] || config.minTouchTarget
    return Math.max(baseSize, targetSize)
  }

  /**
   * 触发适老化样式更新事件
   */
  triggerElderlyStyleUpdate() {
    try {
      // #ifdef H5
      // 触发自定义事件
      const event = new CustomEvent('elderlyModeUpdate', {
        detail: {
          isEnabled: this.isEnabled,
          config: IOS_ELDERLY_CONFIG
        }
      })
      document.dispatchEvent(event)
      // #endif
    } catch (error) {
      console.log('适老化事件触发失败:', error)
    }
  }

  /**
   * 获取适老化圆角大小
   */
  getElderlyBorderRadius(baseRadius) {
    if (!this.isEnabled) return baseRadius

    const config = IOS_ELDERLY_CONFIG.borderRadius
    const radiusMap = {
      8: config.xs,
      12: config.sm,
      16: config.md,
      20: config.lg,
      24: config.xl
    }

    return radiusMap[baseRadius] || baseRadius * 1.25
  }

  /**
   * 获取适老化阴影
   */
  getElderlyShadow(level = 'medium') {
    if (!this.isEnabled) return ''

    const config = IOS_ELDERLY_CONFIG.shadows
    return config[level] || config.medium
  }

  /**
   * 播放适老化音效
   */
  playElderlySound(type = 'click') {
    if (!this.isEnabled) return

    const config = IOS_ELDERLY_CONFIG.feedback.sound
    if (!config.enabled) return

    try {
      // #ifdef APP-PLUS
      if (plus.audio) {
        const soundFile = config[type]
        if (soundFile) {
          plus.audio.createPlayer(soundFile).play()
        }
      }
      // #endif
    } catch (error) {
      console.log('音效播放失败:', error)
    }
  }
}

// 创建全局适老化管理器实例
export const elderlyModeManager = new ElderlyModeManager()

// 导出工具函数
export const ElderlyUtils = {
  /**
   * 检查是否为适老化模式
   */
  isElderlyMode() {
    return elderlyModeManager.isElderlyMode()
  },

  /**
   * 获取适老化配置
   */
  getConfig() {
    return IOS_ELDERLY_CONFIG
  },

  /**
   * 应用适老化样式到元素
   */
  applyElderlyStyle(element, styles) {
    if (!this.isElderlyMode() || !element) return

    Object.keys(styles).forEach(key => {
      element.style[key] = styles[key]
    })
  },

  /**
   * 获取适老化类名
   */
  getElderlyClass(baseClass = '') {
    return this.isElderlyMode() ? `${baseClass} ios-elderly-mode` : baseClass
  }
}

// 导出默认对象
export default {
  IOS_ELDERLY_CONFIG,
  ElderlyModeManager,
  elderlyModeManager,
  ElderlyUtils
}
