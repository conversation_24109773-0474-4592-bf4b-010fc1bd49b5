<template>
	<view class="container">
		<PageHeader title="系统升级详情" showBack></PageHeader>
		
		<view class="content">
			<!-- 升级概览 -->
			<view class="upgrade-overview">
				<view class="upgrade-icon">
					<Icon name="rocket-line" size="80rpx" color="white"></Icon>
				</view>
				<text class="upgrade-title">智慧养老系统 v2.1.0</text>
				<text class="upgrade-subtitle">全新升级，更智能的养老服务</text>
				<view class="upgrade-status">
					<text class="status-text">升级已完成</text>
				</view>
			</view>

			<!-- 升级内容 -->
			<view class="section">
				<text class="section-title">升级内容</text>
				<view class="upgrade-features">
					<view class="feature-item" v-for="(feature, index) in upgradeFeatures" :key="index">
						<view class="feature-icon" :class="feature.type">
							<Icon :name="feature.icon" size="32rpx" color="white"></Icon>
						</view>
						<view class="feature-content">
							<text class="feature-title">{{ feature.title }}</text>
							<text class="feature-desc">{{ feature.description }}</text>
						</view>
						<view class="feature-badge" v-if="feature.isNew">
							<text class="badge-text">NEW</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 性能提升 -->
			<view class="section">
				<text class="section-title">性能提升</text>
				<view class="performance-card">
					<view class="performance-item" v-for="(item, index) in performanceData" :key="index">
						<text class="performance-label">{{ item.label }}</text>
						<view class="performance-bar">
							<view class="bar-bg">
								<view class="bar-fill" :style="{ width: item.percentage + '%' }"></view>
							</view>
							<text class="performance-value">{{ item.improvement }}</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 修复问题 -->
			<view class="section">
				<text class="section-title">修复问题</text>
				<view class="fixes-card">
					<view class="fix-item" v-for="(fix, index) in fixedIssues" :key="index">
						<Icon name="check-line" size="24rpx" color="#4caf50"></Icon>
						<text class="fix-text">{{ fix }}</text>
					</view>
				</view>
			</view>

			<!-- 使用指南 -->
			<view class="section">
				<text class="section-title">新功能使用指南</text>
				<view class="guide-list">
					<view class="guide-item" v-for="(guide, index) in userGuides" :key="index" @click="viewGuide(guide)">
						<view class="guide-icon">
							<Icon :name="guide.icon" size="32rpx" color="#ff8a00"></Icon>
						</view>
						<view class="guide-content">
							<text class="guide-title">{{ guide.title }}</text>
							<text class="guide-desc">{{ guide.description }}</text>
						</view>
						<Icon name="arrow-right-s-line" size="24rpx" color="#999"></Icon>
					</view>
				</view>
			</view>

			<!-- 反馈建议 -->
			<view class="section">
				<text class="section-title">意见反馈</text>
				<view class="feedback-card">
					<text class="feedback-desc">如果您在使用过程中遇到问题或有改进建议，欢迎反馈给我们。</text>
					<view class="feedback-actions">
						<InteractiveButton 
							type="secondary" 
							text="问题反馈" 
							leftIcon="bug-line"
							@click="reportBug"
						/>
						<InteractiveButton 
							type="primary" 
							text="建议反馈" 
							leftIcon="lightbulb-line"
							@click="submitSuggestion"
						/>
					</view>
				</view>
			</view>

			<!-- 版本历史 -->
			<view class="section">
				<view class="section-header">
					<text class="section-title">版本历史</text>
					<text class="view-all" @click="viewAllVersions">查看全部</text>
				</view>
				<view class="version-list">
					<view class="version-item" v-for="(version, index) in versionHistory" :key="index">
						<view class="version-info">
							<text class="version-number">{{ version.version }}</text>
							<text class="version-date">{{ version.date }}</text>
						</view>
						<text class="version-desc">{{ version.description }}</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import Icon from '@/components/Icon/Icon.vue'
import PageHeader from '@/components/PageHeader/PageHeader.vue'
import InteractiveButton from '@/components/InteractiveButton/InteractiveButton.vue'

export default {
	name: 'SystemUpgradeDetail',
	components: {
		Icon,
		PageHeader,
		InteractiveButton
	},
	data() {
		return {
			upgradeFeatures: [
				{
					title: '智能健康监护',
					description: '新增AI健康分析，实时监测健康状态',
					icon: 'heart-pulse-line',
					type: 'health',
					isNew: true
				},
				{
					title: '语音交互优化',
					description: '提升语音识别准确率，支持方言识别',
					icon: 'mic-line',
					type: 'voice',
					isNew: true
				},
				{
					title: '紧急呼叫升级',
					description: '优化紧急呼叫流程，响应速度提升50%',
					icon: 'phone-line',
					type: 'emergency',
					isNew: false
				},
				{
					title: '界面适老化改进',
					description: '字体更大，操作更简单，适合老年人使用',
					icon: 'eye-line',
					type: 'ui',
					isNew: false
				},
				{
					title: '数据同步优化',
					description: '健康数据云端同步，多设备数据一致',
					icon: 'cloud-line',
					type: 'sync',
					isNew: true
				}
			],
			performanceData: [
				{
					label: '启动速度',
					improvement: '提升 40%',
					percentage: 40
				},
				{
					label: '响应速度',
					improvement: '提升 35%',
					percentage: 35
				},
				{
					label: '内存占用',
					improvement: '减少 25%',
					percentage: 25
				},
				{
					label: '电池续航',
					improvement: '延长 20%',
					percentage: 20
				}
			],
			fixedIssues: [
				'修复了健康数据偶尔丢失的问题',
				'解决了语音识别在嘈杂环境下的准确性问题',
				'修复了紧急呼叫按钮偶尔无响应的问题',
				'解决了部分机型上字体显示模糊的问题',
				'修复了消息推送延迟的问题',
				'解决了定位服务偶尔失效的问题'
			],
			userGuides: [
				{
					title: '如何使用智能健康监护',
					description: '了解新的AI健康分析功能',
					icon: 'book-line'
				},
				{
					title: '语音交互使用技巧',
					description: '掌握语音操作的最佳实践',
					icon: 'volume-up-line'
				},
				{
					title: '紧急呼叫设置指南',
					description: '配置紧急联系人和呼叫设置',
					icon: 'settings-line'
				},
				{
					title: '数据同步设置',
					description: '设置云端数据同步功能',
					icon: 'refresh-line'
				}
			],
			versionHistory: [
				{
					version: 'v2.0.5',
					date: '2024-01-10',
					description: '修复了若干已知问题，提升了系统稳定性'
				},
				{
					version: 'v2.0.0',
					date: '2023-12-15',
					description: '重大版本更新，全新的用户界面和功能'
				},
				{
					version: 'v1.9.8',
					date: '2023-11-20',
					description: '优化了健康数据管理功能'
				}
			]
		}
	},
	methods: {
		viewGuide(guide) {
			uni.showModal({
				title: guide.title,
				content: guide.description,
				showCancel: false
			})
		},
		reportBug() {
			uni.showToast({
				title: '跳转到问题反馈页面',
				icon: 'none'
			})
		},
		submitSuggestion() {
			uni.showToast({
				title: '跳转到建议反馈页面',
				icon: 'none'
			})
		},
		viewAllVersions() {
			uni.showToast({
				title: '查看完整版本历史',
				icon: 'none'
			})
		}
	}
}
</script>

<style scoped>
.container {
	min-height: 100vh;
	background: #f5f5f5;
}

.content {
	padding: 140rpx 32rpx 40rpx;
}

.upgrade-overview {
	background: linear-gradient(135deg, #ff8a00, #ff6b35);
	border-radius: 24rpx;
	padding: 48rpx 32rpx;
	text-align: center;
	margin-bottom: 32rpx;
	color: white;
}

.upgrade-icon {
	width: 120rpx;
	height: 120rpx;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.2);
	display: flex;
	align-items: center;
	justify-content: center;
	margin: 0 auto 24rpx;
}

.upgrade-title {
	font-size: 36rpx;
	font-weight: 600;
	display: block;
	margin-bottom: 8rpx;
}

.upgrade-subtitle {
	font-size: 26rpx;
	opacity: 0.9;
	display: block;
	margin-bottom: 24rpx;
}

.upgrade-status {
	background: rgba(255, 255, 255, 0.2);
	border-radius: 20rpx;
	padding: 8rpx 24rpx;
	display: inline-block;
}

.status-text {
	font-size: 24rpx;
	font-weight: 500;
}

.section {
	margin-bottom: 32rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 24rpx;
	display: block;
}

.section-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 24rpx;
}

.view-all {
	font-size: 26rpx;
	color: #ff8a00;
}

.upgrade-features {
	background: white;
	border-radius: 16rpx;
	padding: 24rpx;
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

.feature-item {
	display: flex;
	align-items: center;
	gap: 16rpx;
	margin-bottom: 24rpx;
	position: relative;
}

.feature-item:last-child {
	margin-bottom: 0;
}

.feature-icon {
	width: 56rpx;
	height: 56rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
}

.feature-icon.health {
	background: #e91e63;
}

.feature-icon.voice {
	background: #9c27b0;
}

.feature-icon.emergency {
	background: #f44336;
}

.feature-icon.ui {
	background: #2196f3;
}

.feature-icon.sync {
	background: #4caf50;
}

.feature-content {
	flex: 1;
}

.feature-title {
	font-size: 28rpx;
	font-weight: 600;
	color: #333;
	display: block;
	margin-bottom: 4rpx;
}

.feature-desc {
	font-size: 24rpx;
	color: #666;
	line-height: 1.4;
}

.feature-badge {
	background: #ff8a00;
	color: white;
	font-size: 20rpx;
	padding: 4rpx 12rpx;
	border-radius: 12rpx;
	position: absolute;
	top: -8rpx;
	right: 0;
}

.badge-text {
	font-weight: 600;
}

.performance-card {
	background: white;
	border-radius: 16rpx;
	padding: 24rpx;
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

.performance-item {
	margin-bottom: 24rpx;
}

.performance-item:last-child {
	margin-bottom: 0;
}

.performance-label {
	font-size: 28rpx;
	color: #333;
	display: block;
	margin-bottom: 12rpx;
}

.performance-bar {
	display: flex;
	align-items: center;
	gap: 16rpx;
}

.bar-bg {
	flex: 1;
	height: 12rpx;
	background: #f0f0f0;
	border-radius: 6rpx;
	overflow: hidden;
}

.bar-fill {
	height: 100%;
	background: linear-gradient(90deg, #ff8a00, #ff6b35);
	border-radius: 6rpx;
	transition: width 0.3s ease;
}

.performance-value {
	font-size: 24rpx;
	color: #ff8a00;
	font-weight: 600;
	min-width: 120rpx;
	text-align: right;
}

.fixes-card {
	background: white;
	border-radius: 16rpx;
	padding: 24rpx;
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

.fix-item {
	display: flex;
	align-items: flex-start;
	gap: 12rpx;
	margin-bottom: 16rpx;
}

.fix-item:last-child {
	margin-bottom: 0;
}

.fix-text {
	font-size: 26rpx;
	color: #666;
	line-height: 1.5;
}

.guide-list {
	background: white;
	border-radius: 16rpx;
	padding: 24rpx;
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

.guide-item {
	display: flex;
	align-items: center;
	gap: 16rpx;
	margin-bottom: 24rpx;
}

.guide-item:last-child {
	margin-bottom: 0;
}

.guide-icon {
	width: 48rpx;
	height: 48rpx;
	border-radius: 50%;
	background: rgba(255, 138, 0, 0.1);
	display: flex;
	align-items: center;
	justify-content: center;
}

.guide-content {
	flex: 1;
}

.guide-title {
	font-size: 28rpx;
	font-weight: 600;
	color: #333;
	display: block;
	margin-bottom: 4rpx;
}

.guide-desc {
	font-size: 24rpx;
	color: #666;
}

.feedback-card {
	background: white;
	border-radius: 16rpx;
	padding: 24rpx;
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

.feedback-desc {
	font-size: 26rpx;
	color: #666;
	line-height: 1.5;
	margin-bottom: 24rpx;
	display: block;
}

.feedback-actions {
	display: flex;
	gap: 16rpx;
}

.version-list {
	background: white;
	border-radius: 16rpx;
	padding: 24rpx;
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

.version-item {
	margin-bottom: 24rpx;
	padding-bottom: 24rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.version-item:last-child {
	margin-bottom: 0;
	padding-bottom: 0;
	border-bottom: none;
}

.version-info {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 8rpx;
}

.version-number {
	font-size: 28rpx;
	font-weight: 600;
	color: #333;
}

.version-date {
	font-size: 24rpx;
	color: #999;
}

.version-desc {
	font-size: 26rpx;
	color: #666;
	line-height: 1.5;
}
</style>
