<template>
	<view class="container">
		<!-- 页面头部 -->
		<view class="page-header">
			<text class="page-title">服务管理</text>
			<InteractiveButton 
				type="primary" 
				size="medium" 
				text="添加服务" 
				icon="add-line"
				@click="showAddForm"
			></InteractiveButton>
		</view>

		<!-- 搜索和筛选 -->
		<SearchFilter
			placeholder="搜索服务名称或描述"
			:categories="categories"
			@search="onSearch"
			@filter="onFilter"
		></SearchFilter>

		<!-- 服务列表 -->
		<scroll-view 
			scroll-y="true" 
			class="service-list"
			@scrolltolower="loadMore"
			refresher-enabled="true"
			@refresherrefresh="refreshData"
			:refresher-triggered="refreshing"
		>
			<InteractiveCard 
				v-for="(item, index) in serviceList" 
				:key="item.id"
				class="service-item"
				:loading="false"
				@click="viewDetail(item)"
			>
				<view class="service-content">
					<view class="service-header">
						<text class="service-name">{{ item.name }}</text>
						<view class="service-price">
							<text class="price-text">{{ item.price }}</text>
						</view>
					</view>
					<view class="service-meta">
						<text class="service-category">{{ item.category }}</text>
						<text class="service-provider">{{ item.provider }}</text>
						<view class="service-rating">
							<Icon name="star-fill" size="24rpx" color="#ff9500"></Icon>
							<text class="rating-text">{{ item.rating }}</text>
						</view>
					</view>
					<text class="service-description">{{ item.description }}</text>
					<view class="service-tags">
						<text 
							class="tag" 
							v-for="(tag, tagIndex) in item.tags" 
							:key="tagIndex"
						>{{ tag }}</text>
					</view>
					<view class="service-actions">
						<InteractiveButton 
							type="secondary" 
							size="small" 
							text="编辑" 
							icon="edit-line"
							@click.stop="editService(item)"
						></InteractiveButton>
						<InteractiveButton 
							type="danger" 
							size="small" 
							text="删除" 
							icon="delete-bin-line"
							@click.stop="deleteService(item)"
						></InteractiveButton>
					</view>
				</view>
			</InteractiveCard>

			<!-- 加载状态 -->
			<view class="load-more" v-if="hasMore && loading">
				<view class="loading-spinner"></view>
				<text class="loading-text">加载中...</text>
			</view>
			<view class="no-more" v-else-if="!hasMore && serviceList.length > 0">
				<text>没有更多数据了</text>
			</view>
			<view class="empty" v-else-if="!loading && serviceList.length === 0">
				<Icon name="service-line" size="120rpx" color="#ccc"></Icon>
				<text class="empty-text">暂无服务信息</text>
				<InteractiveButton 
					type="primary" 
					size="medium" 
					text="添加第一个服务" 
					@click="showAddForm"
				></InteractiveButton>
			</view>
		</scroll-view>

		<!-- 添加/编辑表单弹窗 -->
		<uni-popup ref="formPopup" type="bottom" :mask-click="false">
			<view class="form-popup">
				<FormBuilder
					:title="isEditing ? '编辑服务' : '添加服务'"
					:fields="formFields"
					:initial-data="currentService"
					:submitting="submitting"
					@submit="handleSubmit"
					@cancel="hideForm"
				></FormBuilder>
			</view>
		</uni-popup>
	</view>
</template>

<script>
import SearchFilter from '@/components/SearchFilter/SearchFilter.vue'
import InteractiveCard from '@/components/InteractiveCard/InteractiveCard.vue'
import InteractiveButton from '@/components/InteractiveButton/InteractiveButton.vue'
import FormBuilder from '@/components/FormBuilder/FormBuilder.vue'
import Icon from '@/components/Icon/Icon.vue'
import FeedbackUtils from '@/utils/feedback.js'
import MockAPI from '@/utils/mockData.js'
import OfflineDataManager from '@/utils/offlineData.js'

export default {
	components: {
		SearchFilter,
		InteractiveCard,
		InteractiveButton,
		FormBuilder,
		Icon
	},
	data() {
		return {
			serviceList: [],
			loading: false,
			refreshing: false,
			hasMore: true,
			page: 1,
			pageSize: 10,
			searchKeyword: '',
			filterData: {
				category: '',
				sort: 'default'
			},
			
			// 表单相关
			isEditing: false,
			submitting: false,
			currentService: {},
			
			// 分类选项
			categories: [
				{ label: '全部', value: '' },
				{ label: '医疗护理', value: '医疗护理' },
				{ label: '生活照料', value: '生活照料' },
				{ label: '康复训练', value: '康复训练' },
				{ label: '心理关怀', value: '心理关怀' },
				{ label: '文娱活动', value: '文娱活动' }
			],
			
			// 表单字段配置
			formFields: [
				{
					key: 'name',
					label: '服务名称',
					type: 'text',
					placeholder: '请输入服务名称',
					required: true,
					validator: (value) => {
						if (value.length < 2) return '服务名称至少2个字符';
						if (value.length > 50) return '服务名称不能超过50个字符';
						return true;
					}
				},
				{
					key: 'category',
					label: '服务分类',
					type: 'select',
					placeholder: '请选择服务分类',
					required: true,
					options: [
						{ label: '医疗护理', value: '医疗护理' },
						{ label: '生活照料', value: '生活照料' },
						{ label: '康复训练', value: '康复训练' },
						{ label: '心理关怀', value: '心理关怀' },
						{ label: '文娱活动', value: '文娱活动' }
					]
				},
				{
					key: 'provider',
					label: '服务提供商',
					type: 'text',
					placeholder: '请输入服务提供商名称',
					required: true
				},
				{
					key: 'price',
					label: '服务价格',
					type: 'text',
					placeholder: '请输入价格，如：150元/小时',
					required: true,
					help: '请包含单位，如：元/小时、元/次、元/月等'
				},
				{
					key: 'description',
					label: '服务描述',
					type: 'textarea',
					placeholder: '请详细描述服务内容和特点',
					required: true,
					validator: (value) => {
						if (value.length < 10) return '服务描述至少10个字符';
						if (value.length > 200) return '服务描述不能超过200个字符';
						return true;
					}
				},
				{
					key: 'rating',
					label: '服务评分',
					type: 'number',
					placeholder: '请输入评分（1-5分）',
					validator: (value) => {
						if (!value) return true; // 可选字段
						const num = parseFloat(value);
						if (isNaN(num) || num < 1 || num > 5) return '评分必须在1-5之间';
						return true;
					}
				},
				{
					key: 'tags',
					label: '服务标签',
					type: 'text',
					placeholder: '请输入服务标签，用逗号分隔',
					help: '例如：专业认证,24小时,医保支持'
				},
				{
					key: 'contactPhone',
					label: '联系电话',
					type: 'text',
					placeholder: '请输入联系电话',
					validator: (value) => {
						if (!value) return true; // 可选字段
						const phoneRegex = /^1[3-9]\d{9}$|^0\d{2,3}-?\d{7,8}$/;
						if (!phoneRegex.test(value)) return '请输入正确的电话号码';
						return true;
					}
				},
				{
					key: 'serviceArea',
					label: '服务区域',
					type: 'text',
					placeholder: '请输入服务覆盖区域',
					help: '例如：朝阳区、海淀区或全市'
				}
			]
		}
	},
	onLoad() {
		// 初始化离线数据
		OfflineDataManager.initOfflineData();
		this.loadServices();
	},
	methods: {
		// 加载服务列表
		async loadServices() {
			if (this.loading) return;

			// 直接使用离线数据，确保100%可用性
			try {
				const params = {
					page: this.page,
					pageSize: this.pageSize,
					keyword: this.searchKeyword,
					...this.filterData
				};

				const result = OfflineDataManager.getOfflineServices(params);

				if (this.page === 1) {
					this.serviceList = result.data;
				} else {
					this.serviceList.push(...result.data);
				}

				this.hasMore = result.data.length === this.pageSize;
				this.loading = false;
				this.refreshing = false;

				// 显示成功提示
				if (this.page === 1 && result.data.length > 0) {
					FeedbackUtils.showSuccess(`已加载 ${result.data.length} 个服务`);
				}
			} catch (error) {
				this.loading = false;
				this.refreshing = false;
				FeedbackUtils.showError('数据加载失败，请重试');
				console.error('加载服务数据失败:', error);
			}
		},

		// 搜索功能
		onSearch(params) {
			this.searchKeyword = params.keyword;
			this.resetAndLoad();
		},
		
		// 筛选功能
		onFilter(params) {
			this.filterData = {
				...this.filterData,
				category: params.category,
				sort: params.sort
			};
			this.resetAndLoad();
		},
		
		// 重置并加载
		resetAndLoad() {
			this.page = 1;
			this.hasMore = true;
			this.serviceList = [];
			this.loadServices();
		},
		
		// 下拉刷新
		refreshData() {
			FeedbackUtils.lightFeedback();
			this.refreshing = true;
			this.resetAndLoad();
		},
		
		// 上拉加载更多
		loadMore() {
			if (this.hasMore && !this.loading) {
				this.page++;
				this.loadServices();
			}
		},

		// 查看详情
		viewDetail(item) {
			FeedbackUtils.lightFeedback();
			uni.navigateTo({
				url: `/pages/service/detail?id=${item.id}`
			});
		},

		// 显示添加表单
		showAddForm() {
			this.isEditing = false;
			this.currentService = {};
			this.$refs.formPopup.open();
		},

		// 编辑服务
		editService(item) {
			FeedbackUtils.lightFeedback();
			this.isEditing = true;
			this.currentService = { 
				...item,
				tags: item.tags ? item.tags.join(',') : ''
			};
			this.$refs.formPopup.open();
		},

		// 删除服务
		async deleteService(item) {
			try {
				await FeedbackUtils.showConfirm({
					title: '删除确认',
					content: `确定要删除服务"${item.name}"吗？此操作不可恢复。`,
					confirmText: '删除',
					cancelText: '取消'
				});
				
				FeedbackUtils.showLoading('删除中...');
				
				const result = await MockAPI.deleteService(item.id);
				
				FeedbackUtils.hideLoading();
				
				if (result.success) {
					FeedbackUtils.showSuccess('删除成功');
					this.resetAndLoad();
				} else {
					FeedbackUtils.showError(result.message || '删除失败');
				}
			} catch (error) {
				FeedbackUtils.hideLoading();
				console.log('用户取消删除');
			}
		},

		// 隐藏表单
		hideForm() {
			this.$refs.formPopup.close();
		},

		// 处理表单提交
		async handleSubmit(formData) {
			try {
				this.submitting = true;
				
				// 处理标签数据
				const submitData = {
					...formData,
					tags: formData.tags ? formData.tags.split(',').map(tag => tag.trim()).filter(tag => tag) : [],
					rating: formData.rating ? parseFloat(formData.rating) : 4.5,
					image: '/static/services/default.jpg' // 默认图片
				};
				
				let result;
				if (this.isEditing) {
					result = await MockAPI.updateService(this.currentService.id, submitData);
				} else {
					result = await MockAPI.createService(submitData);
				}
				
				if (result.success) {
					FeedbackUtils.showSuccess(this.isEditing ? '更新成功' : '添加成功');
					this.hideForm();
					this.resetAndLoad();
				} else {
					FeedbackUtils.showError(result.message || '操作失败');
				}
			} catch (error) {
				console.error('提交失败:', error);
				FeedbackUtils.showError('操作失败，请重试');
			} finally {
				this.submitting = false;
			}
		}
	}
}
</script>

<style scoped>
.container {
	background-color: #f5f5f5;
	min-height: 100vh;
	display: flex;
	flex-direction: column;
}

.page-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 30rpx 40rpx;
	background: white;
	border-bottom: 1rpx solid #f0f0f0;
}

.page-title {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
}

.service-list {
	flex: 1;
	padding: 20rpx;
}

.service-item {
	margin-bottom: 20rpx;
}

.service-content {
	padding: 30rpx;
}

.service-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 15rpx;
}

.service-name {
	flex: 1;
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.service-price {
	background: linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%);
	color: white;
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
}

.price-text {
	font-size: 24rpx;
	font-weight: 500;
}

.service-meta {
	display: flex;
	align-items: center;
	gap: 20rpx;
	margin-bottom: 15rpx;
	flex-wrap: wrap;
}

.service-category {
	background: rgba(255, 138, 0, 0.1);
	color: #ff8a00;
	padding: 6rpx 12rpx;
	border-radius: 12rpx;
	font-size: 22rpx;
}

.service-provider {
	font-size: 24rpx;
	color: #666;
}

.service-rating {
	display: flex;
	align-items: center;
	gap: 5rpx;
}

.rating-text {
	font-size: 24rpx;
	color: #ff9500;
	font-weight: 500;
}

.service-description {
	font-size: 26rpx;
	color: #666;
	line-height: 1.5;
	margin-bottom: 15rpx;
}

.service-tags {
	display: flex;
	gap: 10rpx;
	margin-bottom: 20rpx;
	flex-wrap: wrap;
}

.tag {
	padding: 6rpx 12rpx;
	background-color: #f0f0f0;
	color: #666;
	font-size: 20rpx;
	border-radius: 12rpx;
}

.service-actions {
	display: flex;
	gap: 20rpx;
}

.load-more {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 40rpx;
	gap: 20rpx;
}

.loading-spinner {
	width: 40rpx;
	height: 40rpx;
	border: 3rpx solid rgba(255, 138, 0, 0.2);
	border-top: 3rpx solid #ff8a00;
	border-radius: 50%;
	animation: loading 1s linear infinite;
}

.loading-text {
	font-size: 26rpx;
	color: #999;
}

.no-more {
	text-align: center;
	padding: 40rpx;
	color: #999;
	font-size: 26rpx;
}

.empty {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 100rpx 40rpx;
	gap: 30rpx;
}

.empty-text {
	font-size: 28rpx;
	color: #999;
}

.form-popup {
	background: white;
	border-radius: 30rpx 30rpx 0 0;
	max-height: 90vh;
	overflow-y: auto;
}

@keyframes loading {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}
</style>
