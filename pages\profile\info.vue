<template>
	<view class="container">
		<!-- 自定义导航栏 -->
		<view class="custom-navbar">
			<view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
			<view class="navbar-content">
				<view class="navbar-left" @click="goBack">
					<Icon name="arrow-left-line" size="36rpx" color="#333"></Icon>
					<text class="back-text">返回</text>
				</view>
				<view class="navbar-center">
					<text class="navbar-title">基本信息</text>
				</view>
				<view class="navbar-right">
					<view class="nav-action" @click="saveInfo">
						<text class="save-text">保存</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 头像设置 -->
		<view class="avatar-section">
			<view class="avatar-wrapper" @click="changeAvatar">
				<image :src="userInfo.avatar" class="avatar-image" mode="aspectFill" v-if="userInfo.avatar"></image>
				<view class="avatar-placeholder" v-else>
					<Icon name="user-line" size="80rpx" color="#999"></Icon>
				</view>
				<view class="avatar-edit">
					<Icon name="camera-line" size="32rpx" color="white"></Icon>
				</view>
			</view>
			<text class="avatar-tip">点击更换头像</text>
		</view>

		<!-- 基本信息表单 -->
		<view class="form-section">
			<view class="form-item">
				<text class="form-label">姓名</text>
				<input class="form-input" v-model="userInfo.name" placeholder="请输入姓名" />
			</view>
			
			<view class="form-item">
				<text class="form-label">性别</text>
				<picker :value="genderIndex" :range="genderOptions" @change="onGenderChange">
					<view class="picker-input">
						<text>{{userInfo.gender || '请选择性别'}}</text>
						<Icon name="arrow-down-s-line" size="24rpx" color="#999"></Icon>
					</view>
				</picker>
			</view>
			
			<view class="form-item">
				<text class="form-label">出生日期</text>
				<picker mode="date" :value="userInfo.birthday" @change="onBirthdayChange">
					<view class="picker-input">
						<text>{{userInfo.birthday || '请选择出生日期'}}</text>
						<Icon name="arrow-down-s-line" size="24rpx" color="#999"></Icon>
					</view>
				</picker>
			</view>
			
			<view class="form-item">
				<text class="form-label">身份证号</text>
				<input class="form-input" v-model="userInfo.idCard" placeholder="请输入身份证号" />
			</view>
			
			<view class="form-item">
				<text class="form-label">手机号码</text>
				<input class="form-input" v-model="userInfo.phone" placeholder="请输入手机号码" />
			</view>
			
			<view class="form-item">
				<text class="form-label">居住地址</text>
				<textarea class="form-textarea" v-model="userInfo.address" placeholder="请输入详细地址"></textarea>
			</view>
			
			<view class="form-item">
				<text class="form-label">健康状况</text>
				<picker :value="healthIndex" :range="healthOptions" @change="onHealthChange">
					<view class="picker-input">
						<text>{{userInfo.healthStatus || '请选择健康状况'}}</text>
						<Icon name="arrow-down-s-line" size="24rpx" color="#999"></Icon>
					</view>
				</picker>
			</view>
			
			<view class="form-item">
				<text class="form-label">特殊需求</text>
				<textarea class="form-textarea" v-model="userInfo.specialNeeds" placeholder="请描述特殊需求或注意事项"></textarea>
			</view>
		</view>

		<!-- 保存按钮 -->
		<view class="save-section">
			<button class="save-btn" @click="saveInfo">保存信息</button>
		</view>
	</view>
</template>

<script>
import Icon from '@/components/Icon/Icon.vue'

export default {
	components: {
		Icon
	},
	data() {
		return {
			statusBarHeight: 0,
			userInfo: {
				name: '张奶奶',
				gender: '女',
				birthday: '1945-03-15',
				idCard: '110101194503150001',
				phone: '138****5678',
				address: '北京市朝阳区建国路88号',
				healthStatus: '良好',
				specialNeeds: '',
				avatar: '/static/avatar/default-avatar.jpg'
			},
			genderIndex: 1,
			genderOptions: ['男', '女'],
			healthIndex: 0,
			healthOptions: ['良好', '一般', '较差', '需要特殊照护']
		}
	},
	onLoad() {
		// 获取状态栏高度
		const systemInfo = uni.getSystemInfoSync();
		this.statusBarHeight = systemInfo.statusBarHeight || 44;

		this.loadUserInfo();
	},
	methods: {
		loadUserInfo() {
			// 从本地存储或服务器加载用户信息
			const savedInfo = uni.getStorageSync('userInfo');
			if (savedInfo) {
				this.userInfo = { ...this.userInfo, ...savedInfo };
			}
			
			// 设置选择器索引
			this.genderIndex = this.genderOptions.indexOf(this.userInfo.gender);
			this.healthIndex = this.healthOptions.indexOf(this.userInfo.healthStatus);
		},
		onGenderChange(e) {
			this.genderIndex = e.detail.value;
			this.userInfo.gender = this.genderOptions[e.detail.value];
		},
		onBirthdayChange(e) {
			this.userInfo.birthday = e.detail.value;
		},
		onHealthChange(e) {
			this.healthIndex = e.detail.value;
			this.userInfo.healthStatus = this.healthOptions[e.detail.value];
		},
		changeAvatar() {
			uni.chooseImage({
				count: 1,
				sizeType: ['compressed'],
				sourceType: ['album', 'camera'],
				success: (res) => {
					this.userInfo.avatar = res.tempFilePaths[0];
					uni.showToast({
						title: '头像已更新',
						icon: 'success'
					});
				}
			});
		},
		saveInfo() {
			// 验证必填字段
			if (!this.userInfo.name) {
				uni.showToast({
					title: '请输入姓名',
					icon: 'none'
				});
				return;
			}
			
			if (!this.userInfo.phone) {
				uni.showToast({
					title: '请输入手机号码',
					icon: 'none'
				});
				return;
			}
			
			// 保存到本地存储
			uni.setStorageSync('userInfo', this.userInfo);
			
			// 显示成功提示
			uni.showToast({
				title: '保存成功',
				icon: 'success'
			});
			
			// 返回上一页
			setTimeout(() => {
				this.goBack();
			}, 1500);
		},

		// 返回上一页
		goBack() {
			uni.navigateBack({
				fail: () => {
					uni.switchTab({
						url: '/pages/profile/profile'
					});
				}
			});
		}
	}
}
</script>

<style scoped>
.container {
	background: #f8f9fa;
	min-height: 100vh;
	padding-bottom: 200rpx;
}

/* 导航栏样式 */
.custom-navbar {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 1000;
	background: rgba(255, 255, 255, 0.95);
	backdrop-filter: blur(20rpx);
	-webkit-backdrop-filter: blur(20rpx);
	border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);
}

.navbar-content {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 20rpx 32rpx;
	min-height: 88rpx;
}

.navbar-left {
	display: flex;
	align-items: center;
	gap: 12rpx;
	padding: 8rpx 16rpx 8rpx 0;
	border-radius: 20rpx;
	transition: all 0.2s ease;
}

.navbar-left:active {
	background-color: rgba(0, 0, 0, 0.04);
	transform: scale(0.96);
}

.back-text {
	font-size: 32rpx;
	color: #333;
	font-weight: 500;
}

.navbar-center {
	position: absolute;
	left: 50%;
	transform: translateX(-50%);
}

.navbar-title {
	font-size: 34rpx;
	font-weight: 600;
	color: #333;
}

.navbar-right {
	display: flex;
	align-items: center;
}

.nav-action {
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
	transition: all 0.2s ease;
}

.nav-action:active {
	background-color: rgba(255, 138, 0, 0.1);
	transform: scale(0.96);
}

.save-text {
	font-size: 32rpx;
	color: #ff8a00;
	font-weight: 600;
}

.avatar-section {
	background: white;
	padding: 60rpx 40rpx;
	text-align: center;
	margin-bottom: 20rpx;
	margin-top: 200rpx; /* 为导航栏留出空间 */
}

.avatar-wrapper {
	position: relative;
	width: 200rpx;
	height: 200rpx;
	margin: 0 auto 20rpx;
}

.avatar-image {
	width: 100%;
	height: 100%;
	border-radius: 50%;
}

.avatar-placeholder {
	width: 100%;
	height: 100%;
	border-radius: 50%;
	background: #f0f0f0;
	display: flex;
	align-items: center;
	justify-content: center;
}

.avatar-edit {
	position: absolute;
	bottom: 10rpx;
	right: 10rpx;
	width: 60rpx;
	height: 60rpx;
	background: linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 4rpx 12rpx rgba(255, 138, 0, 0.3);
}

.avatar-tip {
	font-size: 26rpx;
	color: #666;
}

.form-section {
	background: white;
	margin: 20rpx;
	border-radius: 30rpx;
	padding: 40rpx;
}

.form-item {
	display: flex;
	flex-direction: column;
	margin-bottom: 40rpx;
}

.form-item:last-child {
	margin-bottom: 0;
}

.form-label {
	font-size: 28rpx;
	color: #333;
	font-weight: bold;
	margin-bottom: 15rpx;
}

.form-input {
	background: #f8f9fa;
	border: 2rpx solid #e0e0e0;
	border-radius: 15rpx;
	padding: 25rpx 20rpx;
	font-size: 28rpx;
	color: #333;
}

.form-input:focus {
	border-color: #ff8a00;
}

.form-textarea {
	background: #f8f9fa;
	border: 2rpx solid #e0e0e0;
	border-radius: 15rpx;
	padding: 25rpx 20rpx;
	font-size: 28rpx;
	color: #333;
	min-height: 120rpx;
}

.form-textarea:focus {
	border-color: #ff8a00;
}

.picker-input {
	background: #f8f9fa;
	border: 2rpx solid #e0e0e0;
	border-radius: 15rpx;
	padding: 25rpx 20rpx;
	font-size: 28rpx;
	color: #333;
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.save-section {
	padding: 40rpx;
}

.save-btn {
	width: 100%;
	height: 100rpx;
	background: linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%);
	color: white;
	border: none;
	border-radius: 20rpx;
	font-size: 32rpx;
	font-weight: bold;
}
</style>
