/* 智慧养老项目 - 统一导航栏样式规范 */

/* ===== 自定义导航栏容器 ===== */
.custom-navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20rpx);
    -webkit-backdrop-filter: blur(20rpx);
    border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);
    box-shadow: 0 2rpx 16rpx rgba(0, 0, 0, 0.08);
}

/* 状态栏占位 */
.status-bar {
    background: transparent;
}

/* 导航栏内容区域 */
.navbar-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20rpx 32rpx;
    min-height: 88rpx;
    position: relative;
}

/* ===== 导航栏左侧区域（返回按钮） ===== */
.navbar-left {
    display: flex;
    align-items: center;
    gap: 12rpx;
    padding: 8rpx 16rpx 8rpx 0;
    border-radius: 20rpx;
    transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    min-width: 100rpx;
    z-index: 10;
}

.navbar-left:active {
    background-color: rgba(0, 0, 0, 0.04);
    transform: scale(0.96);
}

/* 返回按钮文字 */
.back-text {
    font-size: 32rpx;
    color: #333;
    font-weight: 500;
    line-height: 1.2;
}

/* ===== 导航栏中间区域（标题） ===== */
.navbar-center {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    max-width: 60%;
}

.navbar-title {
    font-size: 34rpx;
    font-weight: 600;
    color: #333;
    text-align: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: 1.2;
}

.navbar-subtitle {
    font-size: 26rpx;
    color: rgba(51, 51, 51, 0.7);
    text-align: center;
    margin-top: 4rpx;
    line-height: 1.2;
}

/* ===== 导航栏右侧区域 ===== */
.navbar-right {
    display: flex;
    align-items: center;
    gap: 12rpx;
    min-width: 100rpx;
    justify-content: flex-end;
    z-index: 10;
}

.nav-action {
    padding: 8rpx;
    border-radius: 16rpx;
    transition: all 0.2s ease;
}

.nav-action:active {
    background-color: rgba(0, 0, 0, 0.04);
    transform: scale(0.96);
}

/* ===== 适老化模式样式 ===== */
.elderly-mode .navbar-content {
    padding: 24rpx 36rpx;
    min-height: 96rpx;
}

.elderly-mode .navbar-left {
    gap: 16rpx;
    padding: 12rpx 20rpx 12rpx 0;
}

.elderly-mode .back-text {
    font-size: 36rpx;
    font-weight: 600;
}

.elderly-mode .navbar-title {
    font-size: 38rpx;
    font-weight: 700;
}

.elderly-mode .navbar-subtitle {
    font-size: 30rpx;
    margin-top: 6rpx;
}

.elderly-mode .navbar-right {
    gap: 16rpx;
}

.elderly-mode .nav-action {
    padding: 12rpx;
    border-radius: 20rpx;
}

/* ===== 图标标准化 ===== */
/* 返回按钮图标 */
.back-icon {
    /* 通过Icon组件属性控制：size="36rpx" color="#333" */
}

.elderly-mode .back-icon {
    /* 通过Icon组件属性控制：size="40rpx" color="#333" */
}

/* 右侧操作图标 */
.nav-icon {
    /* 通过Icon组件属性控制：size="32rpx" color="#666" */
}

.elderly-mode .nav-icon {
    /* 通过Icon组件属性控制：size="36rpx" color="#666" */
}

/* ===== 特殊主题变体 ===== */
/* 橙色主题导航栏（与PageHeader保持一致） */
.custom-navbar.theme-orange {
    background: #ff8a00;
    border-bottom: none;
    box-shadow: 0 2rpx 16rpx rgba(255, 138, 0, 0.2);
}

.theme-orange .back-text {
    color: #ffffff;
}

.theme-orange .navbar-title {
    color: #ffffff;
}

.theme-orange .navbar-subtitle {
    color: rgba(255, 255, 255, 0.8);
}

.theme-orange .navbar-left:active {
    background-color: rgba(255, 255, 255, 0.1);
}

.theme-orange .nav-action:active {
    background-color: rgba(255, 255, 255, 0.1);
}

/* ===== 动画和过渡效果 ===== */
.custom-navbar {
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.navbar-title {
    transition: all 0.2s ease;
}

.back-text {
    transition: all 0.2s ease;
}

/* ===== 响应式适配 ===== */
/* 小屏幕设备 */
@media (max-width: 375px) {
    .navbar-content {
        padding: 20rpx 24rpx;
    }
    
    .navbar-center {
        max-width: 50%;
    }
    
    .elderly-mode .navbar-content {
        padding: 24rpx 28rpx;
    }
}

/* 大屏幕设备 */
@media (min-width: 768px) {
    .navbar-content {
        padding: 20rpx 40rpx;
    }
    
    .elderly-mode .navbar-content {
        padding: 24rpx 44rpx;
    }
}

/* ===== 平台特定样式 ===== */
/* iOS平台优化 */
.ios .custom-navbar {
    backdrop-filter: blur(20rpx);
    -webkit-backdrop-filter: blur(20rpx);
}

/* Android平台优化 */
.android .custom-navbar {
    /* Android可能不支持毛玻璃效果，使用纯色背景 */
    background: rgba(255, 255, 255, 0.98);
}

/* 小程序平台优化 */
.mp-weixin .custom-navbar {
    /* 小程序中毛玻璃效果可能有限制 */
    background: rgba(255, 255, 255, 0.96);
}

/* ===== 无障碍支持 ===== */
.navbar-left[aria-label] {
    /* 为屏幕阅读器提供更好的支持 */
}

.navbar-title[role="heading"] {
    /* 标题语义化 */
}

/* ===== 调试辅助样式（开发时使用） ===== */
.debug .custom-navbar {
    border: 2rpx solid red;
}

.debug .navbar-content {
    border: 1rpx solid blue;
}

.debug .navbar-left,
.debug .navbar-center,
.debug .navbar-right {
    border: 1rpx solid green;
}
