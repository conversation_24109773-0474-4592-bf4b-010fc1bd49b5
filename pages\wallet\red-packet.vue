<template>
	<view class="container">
		<!-- 自定义导航栏 -->
		<view class="custom-navbar">
			<view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
			<view class="navbar-content">
				<view class="navbar-left" @click="goBack">
					<Icon name="arrow-left-line" size="36rpx" color="#333"></Icon>
					<text class="back-text">返回</text>
				</view>
				<view class="navbar-center">
					<text class="navbar-title">红包</text>
				</view>
				<view class="navbar-right"></view>
			</view>
		</view>

		<!-- 红包功能选择 -->
		<view class="function-section">
			<view class="function-item" @click="sendRedPacket">
				<view class="function-icon send">
					<Icon name="gift-line" size="48rpx" color="white"></Icon>
				</view>
				<view class="function-info">
					<text class="function-title">发红包</text>
					<text class="function-desc">给亲友发送红包</text>
				</view>
				<Icon name="arrow-right-s-line" size="24rpx" color="#999"></Icon>
			</view>
			
			<view class="function-item" @click="viewRedPacketHistory">
				<view class="function-icon history">
					<Icon name="history-line" size="48rpx" color="white"></Icon>
				</view>
				<view class="function-info">
					<text class="function-title">红包记录</text>
					<text class="function-desc">查看收发红包记录</text>
				</view>
				<Icon name="arrow-right-s-line" size="24rpx" color="#999"></Icon>
			</view>
		</view>

		<!-- 最近红包记录 -->
		<view class="recent-section">
			<view class="section-title">最近红包</view>
			<view class="red-packet-list">
				<view class="red-packet-item" 
					v-for="packet in recentRedPackets" 
					:key="packet.id"
					@click="viewRedPacketDetail(packet)">
					<view class="packet-icon" :class="packet.type">
						<Icon name="gift-line" size="32rpx" color="white"></Icon>
					</view>
					<view class="packet-info">
						<text class="packet-title">{{packet.title}}</text>
						<text class="packet-desc">{{packet.description}}</text>
						<text class="packet-time">{{packet.time}}</text>
					</view>
					<view class="packet-amount">
						<text class="amount-text" :class="packet.type">
							{{packet.type === 'received' ? '+' : '-'}}¥{{packet.amount}}
						</text>
						<text class="status-text" :class="packet.status">{{getStatusText(packet.status)}}</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 红包说明 -->
		<view class="notice-section">
			<view class="notice-title">
				<Icon name="information-line" size="24rpx" color="#ff8a00"></Icon>
				<text>红包说明</text>
			</view>
			<view class="notice-content">
				<text class="notice-item">• 红包金额范围：0.01元 - 200元</text>
				<text class="notice-item">• 红包有效期为24小时，过期自动退回</text>
				<text class="notice-item">• 发出的红包可在红包记录中查看状态</text>
				<text class="notice-item">• 收到的红包将自动存入钱包余额</text>
			</view>
		</view>

		<!-- 功能开发中提示 -->
		<view class="dev-notice">
			<Icon name="tools-line" size="32rpx" color="#ff8a00"></Icon>
			<text class="dev-text">此功能正在开发中，敬请期待</text>
		</view>
	</view>
</template>

<script>
import Icon from '@/components/Icon/Icon.vue'

export default {
	components: {
		Icon
	},
	data() {
		return {
			statusBarHeight: 0,
			recentRedPackets: [
				{
					id: 1,
					type: 'sent',
					title: '发给李阿姨',
					description: '祝您身体健康',
					amount: '50.00',
					time: '今天 14:30',
					status: 'received'
				},
				{
					id: 2,
					type: 'received',
					title: '来自王大爷',
					description: '新年快乐',
					amount: '88.00',
					time: '昨天 09:15',
					status: 'received'
				},
				{
					id: 3,
					type: 'sent',
					title: '发给张护士',
					description: '感谢照顾',
					amount: '100.00',
					time: '01-13 16:20',
					status: 'pending'
				},
				{
					id: 4,
					type: 'received',
					title: '来自小明',
					description: '生日快乐',
					amount: '66.00',
					time: '01-12 11:45',
					status: 'received'
				}
			]
		}
	},
	onLoad() {
		const systemInfo = uni.getSystemInfoSync();
		this.statusBarHeight = systemInfo.statusBarHeight || 44;
	},
	methods: {
		sendRedPacket() {
			uni.showToast({
				title: '发红包功能开发中',
				icon: 'none',
				duration: 2000
			});
		},
		viewRedPacketHistory() {
			uni.showToast({
				title: '红包记录功能开发中',
				icon: 'none',
				duration: 2000
			});
		},
		viewRedPacketDetail(packet) {
			uni.showToast({
				title: '红包详情功能开发中',
				icon: 'none',
				duration: 2000
			});
		},
		getStatusText(status) {
			const statusMap = {
				'received': '已领取',
				'pending': '待领取',
				'expired': '已过期',
				'refunded': '已退回'
			};
			return statusMap[status] || '未知';
		},
		goBack() {
			uni.navigateBack({
				fail: () => {
					uni.navigateTo({
						url: '/pages/wallet/wallet'
					});
				}
			});
		}
	}
}
</script>

<style scoped>
.container {
	background: #f8f9fa;
	min-height: 100vh;
}

/* 导航栏样式 */
.custom-navbar {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 1000;
	background: rgba(255, 255, 255, 0.95);
	backdrop-filter: blur(20rpx);
	border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);
}

.navbar-content {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 20rpx 32rpx;
	min-height: 88rpx;
}

.navbar-left {
	display: flex;
	align-items: center;
	gap: 12rpx;
	padding: 8rpx 16rpx 8rpx 0;
	border-radius: 20rpx;
	transition: all 0.2s ease;
}

.navbar-left:active {
	background-color: rgba(0, 0, 0, 0.04);
	transform: scale(0.96);
}

.back-text {
	font-size: 32rpx;
	color: #333;
	font-weight: 500;
}

.navbar-center {
	position: absolute;
	left: 50%;
	transform: translateX(-50%);
}

.navbar-title {
	font-size: 34rpx;
	font-weight: 600;
	color: #333;
}

.navbar-right {
	display: flex;
	align-items: center;
}

/* 功能选择区域 */
.function-section {
	background: white;
	margin: 20rpx;
	margin-top: 220rpx;
	border-radius: 30rpx;
	padding: 40rpx;
}

.function-item {
	display: flex;
	align-items: center;
	padding: 30rpx 0;
	border-bottom: 1rpx solid #f0f0f0;
	transition: all 0.2s ease;
}

.function-item:last-child {
	border-bottom: none;
}

.function-item:active {
	background: rgba(255, 138, 0, 0.05);
	border-radius: 15rpx;
}

.function-icon {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 25rpx;
}

.function-icon.send {
	background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
}

.function-icon.history {
	background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
}

.function-info {
	flex: 1;
}

.function-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 8rpx;
}

.function-desc {
	font-size: 26rpx;
	color: #666;
	display: block;
}

/* 最近红包记录 */
.recent-section, .notice-section {
	background: white;
	margin: 20rpx;
	border-radius: 30rpx;
	padding: 40rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 30rpx;
}

.red-packet-list {
	display: flex;
	flex-direction: column;
	gap: 25rpx;
}

.red-packet-item {
	display: flex;
	align-items: center;
	gap: 20rpx;
	padding: 20rpx;
	background: #f8f9fa;
	border-radius: 20rpx;
	transition: all 0.2s ease;
}

.red-packet-item:active {
	background: rgba(255, 138, 0, 0.1);
}

.packet-icon {
	width: 60rpx;
	height: 60rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
}

.packet-icon.sent {
	background: #ff6b6b;
}

.packet-icon.received {
	background: #4ecdc4;
}

.packet-info {
	flex: 1;
}

.packet-title {
	font-size: 28rpx;
	color: #333;
	font-weight: bold;
	display: block;
	margin-bottom: 5rpx;
}

.packet-desc {
	font-size: 24rpx;
	color: #666;
	display: block;
	margin-bottom: 5rpx;
}

.packet-time {
	font-size: 22rpx;
	color: #999;
	display: block;
}

.packet-amount {
	text-align: right;
}

.amount-text {
	font-size: 28rpx;
	font-weight: bold;
	display: block;
	margin-bottom: 5rpx;
}

.amount-text.sent {
	color: #ff6b6b;
}

.amount-text.received {
	color: #4ecdc4;
}

.status-text {
	font-size: 22rpx;
	display: block;
}

.status-text.received {
	color: #4caf50;
}

.status-text.pending {
	color: #ff9800;
}

.status-text.expired {
	color: #999;
}

.status-text.refunded {
	color: #f44336;
}

.notice-title {
	display: flex;
	align-items: center;
	gap: 10rpx;
	font-size: 28rpx;
	font-weight: bold;
	color: #ff8a00;
	margin-bottom: 20rpx;
}

.notice-content {
	display: flex;
	flex-direction: column;
	gap: 15rpx;
}

.notice-item {
	font-size: 26rpx;
	color: #666;
	line-height: 1.5;
}

.dev-notice {
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 15rpx;
	padding: 30rpx;
	margin: 20rpx;
	background: rgba(255, 138, 0, 0.1);
	border-radius: 20rpx;
}

.dev-text {
	font-size: 26rpx;
	color: #ff8a00;
}
</style>
