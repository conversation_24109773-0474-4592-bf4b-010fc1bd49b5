<template>
	<view 
		class="lazy-image-container" 
		:style="containerStyle"
		@click="handleClick"
	>
		<!-- 图片加载成功 -->
		<image 
			v-if="!imageError && !loading"
			:src="imageSrc" 
			:mode="mode"
			class="lazy-image"
			:style="imageStyle"
			@load="handleLoad"
			@error="handleError"
		></image>
		
		<!-- 加载中状态 -->
		<view v-else-if="loading" class="placeholder loading" :style="placeholderStyle">
			<view class="loading-spinner"></view>
			<text v-if="showPlaceholder" class="loading-text">加载中...</text>
		</view>
		
		<!-- 加载失败状态 -->
		<view v-else-if="imageError" class="placeholder error" :style="placeholderStyle">
			<Icon :name="errorIcon" :size="iconSize" color="#ccc"></Icon>
			<text v-if="showPlaceholder" class="error-text">加载失败</text>
		</view>
		
		<!-- 默认占位符 -->
		<view v-else class="placeholder default" :style="placeholderStyle">
			<Icon :name="placeholderIcon" :size="iconSize" color="#ddd"></Icon>
			<text v-if="showPlaceholder" class="placeholder-text">暂无图片</text>
		</view>
	</view>
</template>

<script>
import Icon from '@/components/Icon/Icon.vue'

export default {
	name: 'LazyImage',
	components: {
		Icon
	},
	props: {
		src: {
			type: String,
			default: ''
		},
		width: {
			type: [String, Number],
			default: '100%'
		},
		height: {
			type: [String, Number],
			default: 'auto'
		},
		mode: {
			type: String,
			default: 'aspectFill'
		},
		borderRadius: {
			type: [String, Number],
			default: 0
		},
		placeholderIcon: {
			type: String,
			default: 'image-line'
		},
		errorIcon: {
			type: String,
			default: 'error-warning-line'
		},
		showPlaceholder: {
			type: Boolean,
			default: true
		},
		lazy: {
			type: Boolean,
			default: true
		}
	},
	data() {
		return {
			loading: false, // 初始状态改为false，只有开始加载时才显示loading
			imageError: false,
			imageSrc: '',
			isIntersecting: false,
			timeoutId: null // 添加超时ID用于清理
		}
	},
	computed: {
		containerStyle() {
			const style = {}
			
			if (typeof this.width === 'number') {
				style.width = this.width + 'rpx'
			} else {
				style.width = this.width
			}
			
			if (typeof this.height === 'number') {
				style.height = this.height + 'rpx'
			} else if (this.height !== 'auto') {
				style.height = this.height
			}
			
			if (this.borderRadius) {
				const radius = typeof this.borderRadius === 'number' 
					? this.borderRadius + 'rpx' 
					: this.borderRadius
				style.borderRadius = radius
			}
			
			return style
		},
		imageStyle() {
			const style = {
				width: '100%',
				height: '100%'
			}
			
			if (this.borderRadius) {
				const radius = typeof this.borderRadius === 'number' 
					? this.borderRadius + 'rpx' 
					: this.borderRadius
				style.borderRadius = radius
			}
			
			return style
		},
		placeholderStyle() {
			const style = {
				width: '100%',
				height: '100%'
			}
			
			if (this.borderRadius) {
				const radius = typeof this.borderRadius === 'number' 
					? this.borderRadius + 'rpx' 
					: this.borderRadius
				style.borderRadius = radius
			}
			
			return style
		},
		iconSize() {
			// 根据容器大小动态计算图标大小
			const containerWidth = typeof this.width === 'number' ? this.width : 100
			const containerHeight = typeof this.height === 'number' ? this.height : 100
			const minSize = Math.min(containerWidth, containerHeight)
			
			if (minSize < 60) return '24rpx'
			if (minSize < 120) return '32rpx'
			if (minSize < 200) return '48rpx'
			return '64rpx'
		}
	},
	watch: {
		src: {
			handler(newSrc) {
				if (newSrc) {
					this.loadImage()
				} else {
					this.imageSrc = ''
					this.loading = false
					this.imageError = false
				}
			},
			immediate: true
		}
	},
	mounted() {
		if (this.lazy) {
			this.setupIntersectionObserver()
		} else if (this.src) {
			this.loadImage()
		}
	},
	beforeDestroy() {
		if (this.observer) {
			this.observer.disconnect()
		}
		// 清理超时
		if (this.timeoutId) {
			clearTimeout(this.timeoutId)
			this.timeoutId = null
		}
	},
	methods: {
		setupIntersectionObserver() {
			// 在小程序环境中，使用简化的懒加载逻辑
			// 实际项目中可以使用 uni.createIntersectionObserver
			this.loadImage()
		},
		loadImage() {
			if (!this.src) {
				this.loading = false
				this.imageError = false
				this.imageSrc = ''
				return
			}

			// 清理之前的超时
			if (this.timeoutId) {
				clearTimeout(this.timeoutId)
				this.timeoutId = null
			}

			console.log('LazyImage开始加载图片:', this.src)
			this.loading = true
			this.imageError = false
			this.imageSrc = this.src

			// 添加超时处理，防止一直loading
			this.timeoutId = setTimeout(() => {
				if (this.loading) {
					console.warn('LazyImage加载超时，切换到错误状态:', this.src)
					this.loading = false
					this.imageError = true
					this.$emit('error')
				}
			}, 3000) // 减少到3秒超时，更快响应
		},
		handleLoad() {
			console.log('LazyImage加载成功:', this.src)
			// 清理超时
			if (this.timeoutId) {
				clearTimeout(this.timeoutId)
				this.timeoutId = null
			}
			this.loading = false
			this.imageError = false
			this.$emit('load')
		},
		handleError() {
			console.log('LazyImage加载失败:', this.src)
			// 清理超时
			if (this.timeoutId) {
				clearTimeout(this.timeoutId)
				this.timeoutId = null
			}
			this.loading = false
			this.imageError = true

			// 尝试使用备用图片路径
			if (this.src && !this.src.startsWith('/static/')) {
				console.log('原图片路径可能错误，建议使用正确路径:', '/static' + this.src)
			}

			this.$emit('error', {
				originalSrc: this.src,
				suggestedSrc: this.src && !this.src.startsWith('/static/') ? '/static' + this.src : null
			})
		},
		handleClick(e) {
			this.$emit('click', e)
		}
	}
}
</script>

<style scoped>
.lazy-image-container {
	position: relative;
	overflow: hidden;
	background: #f5f5f5;
	display: flex;
	align-items: center;
	justify-content: center;
}

.lazy-image {
	display: block;
}

.placeholder {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	background: #f8f9fa;
	color: #999;
	gap: 10rpx;
}

.placeholder.loading {
	background: #f0f0f0;
}

.placeholder.error {
	background: #fafafa;
}

.loading-spinner {
	width: 40rpx;
	height: 40rpx;
	border: 4rpx solid #e0e0e0;
	border-top: 4rpx solid #ff8a00;
	border-radius: 50%;
	animation: spin 1s linear infinite;
}

.loading-text,
.error-text,
.placeholder-text {
	font-size: 20rpx;
	color: #999;
	text-align: center;
}

.error-text {
	color: #ff6b6b;
}

@keyframes spin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

/* 响应式调整 */
@media (max-width: 750rpx) {
	.loading-text,
	.error-text,
	.placeholder-text {
		font-size: 18rpx;
	}
}
</style>
