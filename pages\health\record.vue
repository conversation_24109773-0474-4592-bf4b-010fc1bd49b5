<template>
	<view class="container">
		<PageHeader :title="isEditing ? '编辑记录' : '添加记录'" showBack></PageHeader>
		
		<view class="content">
			<!-- 记录类型选择 -->
			<view class="section">
				<text class="section-title">记录类型</text>
				<view class="type-grid">
					<view 
						class="type-item" 
						:class="{ active: formData.type === type.key }"
						v-for="type in healthTypes" 
						:key="type.key"
						@click="selectType(type)"
					>
						<Icon :name="type.icon" size="40rpx" :color="formData.type === type.key ? 'white' : type.color"></Icon>
						<text class="type-name">{{ type.name }}</text>
					</view>
				</view>
			</view>

			<!-- 测量数据 -->
			<view class="section" v-if="formData.type">
				<text class="section-title">测量数据</text>
				<view class="form-card">
					<!-- 血压 -->
					<template v-if="formData.type === '血压'">
						<view class="form-row">
							<view class="form-item">
								<text class="form-label">收缩压</text>
								<input 
									class="form-input" 
									v-model="formData.systolic" 
									placeholder="如：120"
									type="number"
								/>
								<text class="form-unit">mmHg</text>
							</view>
							<view class="form-item">
								<text class="form-label">舒张压</text>
								<input 
									class="form-input" 
									v-model="formData.diastolic" 
									placeholder="如：80"
									type="number"
								/>
								<text class="form-unit">mmHg</text>
							</view>
						</view>
					</template>

					<!-- 血糖 -->
					<template v-if="formData.type === '血糖'">
						<view class="form-item">
							<text class="form-label">血糖值</text>
							<input 
								class="form-input" 
								v-model="formData.bloodSugar" 
								placeholder="如：5.6"
								type="digit"
							/>
							<text class="form-unit">mmol/L</text>
						</view>
						<view class="form-item">
							<text class="form-label">测量时机</text>
							<picker 
								:value="timingIndex" 
								:range="timingOptions" 
								@change="onTimingChange"
							>
								<view class="picker-display">
									<text class="picker-text">{{ formData.timing || '请选择测量时机' }}</text>
									<Icon name="arrow-down-s-line" size="20rpx" color="#999"></Icon>
								</view>
							</picker>
						</view>
					</template>

					<!-- 心率 -->
					<template v-if="formData.type === '心率'">
						<view class="form-item">
							<text class="form-label">心率</text>
							<input 
								class="form-input" 
								v-model="formData.heartRate" 
								placeholder="如：72"
								type="number"
							/>
							<text class="form-unit">bpm</text>
						</view>
					</template>

					<!-- 体重 -->
					<template v-if="formData.type === '体重'">
						<view class="form-item">
							<text class="form-label">体重</text>
							<input 
								class="form-input" 
								v-model="formData.weight" 
								placeholder="如：65.5"
								type="digit"
							/>
							<text class="form-unit">kg</text>
						</view>
					</template>

					<!-- 体温 -->
					<template v-if="formData.type === '体温'">
						<view class="form-item">
							<text class="form-label">体温</text>
							<input 
								class="form-input" 
								v-model="formData.temperature" 
								placeholder="如：36.5"
								type="digit"
							/>
							<text class="form-unit">°C</text>
						</view>
					</template>
				</view>
			</view>

			<!-- 测量时间 -->
			<view class="section">
				<text class="section-title">测量时间</text>
				<view class="form-card">
					<view class="form-item">
						<text class="form-label">日期</text>
						<picker 
							mode="date" 
							:value="formData.date" 
							@change="onDateChange"
						>
							<view class="picker-display">
								<text class="picker-text">{{ formData.date || '请选择日期' }}</text>
								<Icon name="calendar-line" size="20rpx" color="#999"></Icon>
							</view>
						</picker>
					</view>
					<view class="form-item">
						<text class="form-label">时间</text>
						<picker 
							mode="time" 
							:value="formData.time" 
							@change="onTimeChange"
						>
							<view class="picker-display">
								<text class="picker-text">{{ formData.time || '请选择时间' }}</text>
								<Icon name="time-line" size="20rpx" color="#999"></Icon>
							</view>
						</picker>
					</view>
				</view>
			</view>

			<!-- 备注信息 -->
			<view class="section">
				<text class="section-title">备注信息</text>
				<view class="form-card">
					<textarea 
						class="form-textarea" 
						v-model="formData.note" 
						placeholder="请输入备注信息（可选）"
						maxlength="200"
					/>
					<text class="char-count">{{ formData.note.length }}/200</text>
				</view>
			</view>

			<!-- 数据分析 -->
			<view class="section" v-if="analysisResult">
				<text class="section-title">数据分析</text>
				<view class="analysis-card">
					<view class="analysis-status" :class="analysisResult.status">
						<Icon :name="analysisResult.icon" size="32rpx" color="white"></Icon>
						<text class="status-text">{{ analysisResult.statusText }}</text>
					</view>
					<text class="analysis-desc">{{ analysisResult.description }}</text>
					<view class="suggestions" v-if="analysisResult.suggestions">
						<text class="suggestions-title">建议：</text>
						<text class="suggestions-text">{{ analysisResult.suggestions }}</text>
					</view>
				</view>
			</view>

			<!-- 底部操作 -->
			<view class="bottom-actions">
				<InteractiveButton 
					type="secondary" 
					text="取消" 
					@click="cancel"
				/>
				<InteractiveButton 
					type="primary" 
					text="保存" 
					:loading="saving"
					@click="save"
				/>
			</view>
		</view>
	</view>
</template>

<script>
import Icon from '@/components/Icon/Icon.vue'
import PageHeader from '@/components/PageHeader/PageHeader.vue'
import InteractiveButton from '@/components/InteractiveButton/InteractiveButton.vue'

export default {
	name: 'HealthRecord',
	components: {
		Icon,
		PageHeader,
		InteractiveButton
	},
	data() {
		return {
			isEditing: false,
			saving: false,
			timingIndex: 0,
			formData: {
				type: '',
				systolic: '',
				diastolic: '',
				bloodSugar: '',
				heartRate: '',
				weight: '',
				temperature: '',
				timing: '',
				date: '',
				time: '',
				note: ''
			},
			healthTypes: [
				{
					key: '血压',
					name: '血压',
					icon: 'heart-pulse-line',
					color: '#e91e63'
				},
				{
					key: '血糖',
					name: '血糖',
					icon: 'drop-line',
					color: '#2196f3'
				},
				{
					key: '心率',
					name: '心率',
					icon: 'heart-line',
					color: '#f44336'
				},
				{
					key: '体重',
					name: '体重',
					icon: 'scales-3-line',
					color: '#4caf50'
				},
				{
					key: '体温',
					name: '体温',
					icon: 'temp-line',
					color: '#ff9800'
				}
			],
			timingOptions: ['空腹', '餐前', '餐后2小时', '睡前', '其他'],
			analysisResult: null
		}
	},
	onLoad(options) {
		if (options.type) {
			const type = this.healthTypes.find(t => t.key === options.type)
			if (type) {
				this.selectType(type)
			}
		}
		if (options.id && options.mode === 'edit') {
			this.isEditing = true
			this.loadRecordData(options.id)
		}
		this.initDateTime()
	},
	methods: {
		initDateTime() {
			const now = new Date()
			this.formData.date = now.toISOString().split('T')[0]
			this.formData.time = now.toTimeString().slice(0, 5)
		},
		selectType(type) {
			this.formData.type = type.key
			this.analyzeData()
		},
		onTimingChange(e) {
			this.timingIndex = e.detail.value
			this.formData.timing = this.timingOptions[this.timingIndex]
		},
		onDateChange(e) {
			this.formData.date = e.detail.value
		},
		onTimeChange(e) {
			this.formData.time = e.detail.value
		},
		loadRecordData(id) {
			// 模拟加载记录数据
			console.log('加载记录数据:', id)
		},
		analyzeData() {
			// 模拟数据分析
			if (this.formData.type === '血压' && this.formData.systolic && this.formData.diastolic) {
				const systolic = parseInt(this.formData.systolic)
				const diastolic = parseInt(this.formData.diastolic)
				
				if (systolic >= 140 || diastolic >= 90) {
					this.analysisResult = {
						status: 'danger',
						statusText: '血压偏高',
						icon: 'alert-line',
						description: '您的血压值超出正常范围，建议及时就医。',
						suggestions: '减少盐分摄入，适量运动，保持良好作息。'
					}
				} else if (systolic >= 120 || diastolic >= 80) {
					this.analysisResult = {
						status: 'warning',
						statusText: '血压正常偏高',
						icon: 'information-line',
						description: '您的血压值处于正常偏高范围。',
						suggestions: '注意饮食清淡，适量运动，定期监测。'
					}
				} else {
					this.analysisResult = {
						status: 'normal',
						statusText: '血压正常',
						icon: 'check-line',
						description: '您的血压值在正常范围内。',
						suggestions: '继续保持良好的生活习惯。'
					}
				}
			}
		},
		validate() {
			if (!this.formData.type) {
				uni.showToast({ title: '请选择记录类型', icon: 'none' })
				return false
			}
			
			// 根据类型验证必填字段
			switch (this.formData.type) {
				case '血压':
					if (!this.formData.systolic || !this.formData.diastolic) {
						uni.showToast({ title: '请输入血压值', icon: 'none' })
						return false
					}
					break
				case '血糖':
					if (!this.formData.bloodSugar) {
						uni.showToast({ title: '请输入血糖值', icon: 'none' })
						return false
					}
					break
				case '心率':
					if (!this.formData.heartRate) {
						uni.showToast({ title: '请输入心率值', icon: 'none' })
						return false
					}
					break
				case '体重':
					if (!this.formData.weight) {
						uni.showToast({ title: '请输入体重值', icon: 'none' })
						return false
					}
					break
				case '体温':
					if (!this.formData.temperature) {
						uni.showToast({ title: '请输入体温值', icon: 'none' })
						return false
					}
					break
			}
			
			if (!this.formData.date) {
				uni.showToast({ title: '请选择日期', icon: 'none' })
				return false
			}
			
			if (!this.formData.time) {
				uni.showToast({ title: '请选择时间', icon: 'none' })
				return false
			}
			
			return true
		},
		async save() {
			if (!this.validate()) return
			
			this.saving = true
			try {
				// 模拟保存数据
				await new Promise(resolve => setTimeout(resolve, 1500))
				
				uni.showToast({
					title: this.isEditing ? '修改成功' : '保存成功',
					icon: 'success'
				})
				
				setTimeout(() => {
					uni.navigateBack()
				}, 1500)
			} catch (error) {
				uni.showToast({
					title: '保存失败',
					icon: 'none'
				})
			} finally {
				this.saving = false
			}
		},
		cancel() {
			uni.showModal({
				title: '确认取消',
				content: '确定要取消吗？未保存的内容将丢失。',
				success: (res) => {
					if (res.confirm) {
						uni.navigateBack()
					}
				}
			})
		}
	},
	watch: {
		'formData.systolic'() {
			this.analyzeData()
		},
		'formData.diastolic'() {
			this.analyzeData()
		}
	}
}
</script>

<style scoped>
.container {
	min-height: 100vh;
	background: #f5f5f5;
}

.content {
	padding: 140rpx 32rpx 200rpx;
}

.section {
	margin-bottom: 32rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 24rpx;
	display: block;
}

.type-grid {
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	gap: 16rpx;
}

.type-item {
	background: white;
	border-radius: 16rpx;
	padding: 24rpx;
	text-align: center;
	border: 2rpx solid #f0f0f0;
	transition: all 0.3s ease;
}

.type-item.active {
	border-color: #ff8a00;
	background: #ff8a00;
}

.type-name {
	font-size: 26rpx;
	color: #333;
	display: block;
	margin-top: 12rpx;
}

.type-item.active .type-name {
	color: white;
}

.form-card {
	background: white;
	border-radius: 16rpx;
	padding: 24rpx;
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

.form-row {
	display: flex;
	gap: 16rpx;
}

.form-item {
	margin-bottom: 24rpx;
	flex: 1;
}

.form-item:last-child {
	margin-bottom: 0;
}

.form-label {
	font-size: 28rpx;
	color: #333;
	margin-bottom: 12rpx;
	display: block;
}

.form-input {
	width: 100%;
	padding: 20rpx;
	border: 1rpx solid #e0e0e0;
	border-radius: 12rpx;
	font-size: 28rpx;
	background: #fafafa;
	text-align: center;
}

.form-unit {
	font-size: 24rpx;
	color: #666;
	text-align: center;
	display: block;
	margin-top: 8rpx;
}

.picker-display {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20rpx;
	border: 1rpx solid #e0e0e0;
	border-radius: 12rpx;
	background: #fafafa;
}

.picker-text {
	font-size: 28rpx;
	color: #333;
}

.form-textarea {
	width: 100%;
	min-height: 120rpx;
	padding: 20rpx;
	border: 1rpx solid #e0e0e0;
	border-radius: 12rpx;
	font-size: 28rpx;
	background: #fafafa;
	resize: none;
}

.char-count {
	font-size: 24rpx;
	color: #999;
	text-align: right;
	display: block;
	margin-top: 8rpx;
}

.analysis-card {
	background: white;
	border-radius: 16rpx;
	padding: 24rpx;
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

.analysis-status {
	display: flex;
	align-items: center;
	gap: 12rpx;
	padding: 16rpx 20rpx;
	border-radius: 12rpx;
	margin-bottom: 16rpx;
}

.analysis-status.normal {
	background: #4caf50;
}

.analysis-status.warning {
	background: #ff9800;
}

.analysis-status.danger {
	background: #f44336;
}

.status-text {
	font-size: 28rpx;
	font-weight: 600;
	color: white;
}

.analysis-desc {
	font-size: 26rpx;
	color: #666;
	line-height: 1.5;
	margin-bottom: 16rpx;
	display: block;
}

.suggestions {
	padding-top: 16rpx;
	border-top: 1rpx solid #f0f0f0;
}

.suggestions-title {
	font-size: 26rpx;
	font-weight: 600;
	color: #333;
	display: block;
	margin-bottom: 8rpx;
}

.suggestions-text {
	font-size: 26rpx;
	color: #666;
	line-height: 1.5;
}

.bottom-actions {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background: white;
	padding: 24rpx 32rpx;
	border-top: 1rpx solid #f0f0f0;
	display: flex;
	gap: 16rpx;
}
</style>
