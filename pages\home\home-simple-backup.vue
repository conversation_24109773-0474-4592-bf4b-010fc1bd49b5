<template>
	<view class="container">
		<view class="header">
			<text class="title">慧养老</text>
			<text class="subtitle">智慧养老服务平台</text>
		</view>
		
		<view class="content">
			<view class="status">
				<text>✅ 页面加载成功！</text>
			</view>
			
			<view class="functions">
				<view class="function-item">
					<text>🏢 选机构</text>
				</view>
				<view class="function-item">
					<text>🔍 找服务</text>
				</view>
				<view class="function-item">
					<text>💰 领补贴</text>
				</view>
				<view class="function-item">
					<text>👴 适老版</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			message: '首页加载成功'
		}
	},
	onLoad() {
		console.log('简化版首页加载成功')
	}
}
</script>

<style scoped>
.container {
	background: linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%);
	min-height: 100vh;
	padding: 40rpx;
	color: white;
}

.header {
	text-align: center;
	margin-bottom: 60rpx;
}

.title {
	font-size: 64rpx;
	font-weight: bold;
	display: block;
	margin-bottom: 20rpx;
}

.subtitle {
	font-size: 32rpx;
	opacity: 0.9;
}

.content {
	max-width: 600rpx;
	margin: 0 auto;
}

.status {
	background: rgba(255, 255, 255, 0.2);
	border-radius: 20rpx;
	padding: 30rpx;
	text-align: center;
	margin-bottom: 40rpx;
}

.functions {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	gap: 20rpx;
}

.function-item {
	background: rgba(255, 255, 255, 0.2);
	border-radius: 20rpx;
	padding: 40rpx 20rpx;
	text-align: center;
	font-size: 32rpx;
}
</style>
