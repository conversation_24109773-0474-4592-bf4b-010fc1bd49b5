<template>
	<view class="container">
		<!-- 页面头部 -->
		<PageHeader title="资讯中心">
			<template #actions>
				<InteractiveButton
					type="secondary"
					size="small"
					text="筛选"
					icon="filter-line"
					@click="showFilter"
				></InteractiveButton>
			</template>
		</PageHeader>

		<!-- 分类筛选 -->
		<view class="category-filter">
			<scroll-view scroll-x="true" class="category-scroll">
				<view class="category-list">
					<view 
						v-for="(category, index) in categories" 
						:key="index"
						class="category-item"
						:class="{ active: selectedCategory === category.value }"
						@click="selectCategory(category.value)"
					>
						<Icon :name="category.icon" size="32rpx" :color="selectedCategory === category.value ? '#fff' : '#ff8a00'"></Icon>
						<text class="category-text">{{ category.label }}</text>
					</view>
				</view>
			</scroll-view>
		</view>

		<!-- 资讯列表 -->
		<scroll-view 
			scroll-y="true" 
			class="news-list"
			@scrolltolower="loadMore"
			refresher-enabled="true"
			@refresherrefresh="refreshData"
			:refresher-triggered="refreshing"
		>
			<InteractiveCard 
				v-for="(item, index) in filteredNews" 
				:key="item.id"
				class="news-item"
				:loading="false"
				@click="viewDetail(item)"
			>
				<view class="news-content">
					<!-- 资讯图片或图标占位符 -->
					<view class="news-image-container">
						<image
							v-if="item.image && !item.imageError"
							:src="getImagePath(item.image)"
							class="news-image"
							mode="aspectFill"
							:lazy-load="true"
							:fade-show="true"
							@error="handleImageError(item)"
							@load="handleImageLoad(item)"
						></image>
						<view v-else class="news-icon-placeholder" :style="{ background: getIconBgColor(item.category) }">
							<Icon :name="getCategoryIcon(item.category)" size="48rpx" color="#fff"></Icon>
						</view>
					</view>

					<!-- 资讯信息 -->
					<view class="news-info">
						<view class="news-header">
							<text class="news-title">{{ item.title }}</text>
							<view class="news-category" :style="{ backgroundColor: getCategoryColor(item.category) }">
								<text class="category-label">{{ item.category }}</text>
							</view>
						</view>
						<text class="news-summary">{{ item.summary }}</text>
						<view class="news-meta">
							<view class="meta-item">
								<Icon name="time-line" size="24rpx" color="#999"></Icon>
								<text class="meta-text">{{ formatTime(item.publishTime) }}</text>
							</view>
							<view class="meta-item">
								<Icon name="eye-line" size="24rpx" color="#999"></Icon>
								<text class="meta-text">{{ item.views }}次浏览</text>
							</view>
							<view class="meta-item">
								<Icon name="thumb-up-line" size="24rpx" color="#999"></Icon>
								<text class="meta-text">{{ item.likes }}</text>
							</view>
						</view>
					</view>
				</view>
			</InteractiveCard>

			<!-- 加载状态 -->
			<view class="load-more" v-if="hasMore && loading">
				<view class="loading-spinner"></view>
				<text class="loading-text">加载中...</text>
			</view>
			<view class="no-more" v-else-if="!hasMore && filteredNews.length > 0">
				<text>没有更多资讯了</text>
			</view>
			<view class="empty" v-else-if="!loading && filteredNews.length === 0">
				<Icon name="newspaper-line" size="120rpx" color="#ccc"></Icon>
				<text class="empty-text">暂无资讯内容</text>
				<text class="empty-tip">请稍后再来查看</text>
			</view>
		</scroll-view>
	</view>
</template>

<script>
import InteractiveCard from '@/components/InteractiveCard/InteractiveCard.vue'
import InteractiveButton from '@/components/InteractiveButton/InteractiveButton.vue'
import Icon from '@/components/Icon/Icon.vue'
import PageHeader from '@/components/PageHeader/PageHeader.vue'
import FeedbackUtils from '@/utils/feedback.js'
import OfflineDataManager from '@/utils/offlineData.js'

export default {
	components: {
		InteractiveCard,
		InteractiveButton,
		Icon,
		PageHeader
	},
	data() {
		return {
			newsList: [],
			loading: false,
			refreshing: false,
			hasMore: true,
			page: 1,
			pageSize: 10,
			selectedCategory: '',
			
			// 分类选项
			categories: [
				{ label: '全部', value: '', icon: 'apps-line' },
				{ label: '政策解读', value: '政策解读', icon: 'government-line' },
				{ label: '健康养生', value: '健康养生', icon: 'heart-pulse-line' },
				{ label: '科技创新', value: '科技创新', icon: 'rocket-line' },
				{ label: '服务动态', value: '服务动态', icon: 'service-line' },
				{ label: '社区活动', value: '社区活动', icon: 'community-line' },
				{ label: '行业资讯', value: '行业资讯', icon: 'building-line' }
			],

			// 模拟资讯数据 - 使用本地图片资源
			mockNews: [
				{
					id: 1,
					title: '养老服务新政策发布，惠及更多老年人',
					summary: '政府出台新的养老服务补贴政策，进一步完善养老服务体系，提高老年人生活质量。',
					category: '政策解读',
					publishTime: '2024-01-15 10:30',
					views: 1256,
					likes: 89,
					author: '政策解读员',
					image: '/static/picture/zixun/W020211011780554733191.jpg',
					imageError: false
				},
				{
					id: 2,
					title: '智慧养老技术创新助力老年人生活',
					summary: '人工智能、物联网等新技术在养老领域的应用，为老年人提供更便捷的生活服务。',
					category: '科技创新',
					publishTime: '2024-01-14 15:20',
					views: 892,
					likes: 67,
					author: '科技观察员',
					image: '/static/picture/zixun/71E00B4C613705188E11E072AC3B97F9A9493F32_size101_w1280_h853.jpg',
					imageError: false
				},
				{
					id: 3,
					title: '社区养老服务中心建设加速推进',
					summary: '各地加快推进社区养老服务中心建设，为老年人提供就近便民的养老服务。',
					category: '服务动态',
					publishTime: '2024-01-13 09:15',
					views: 743,
					likes: 52,
					author: '服务观察员',
					image: '/static/picture/zixun/8663976bbb664a0e9f6fd0ee564e5a8c.jpeg',
					imageError: false
				},
				{
					id: 4,
					title: '老年人健康管理新模式探索',
					summary: '结合互联网医疗和智能设备，探索老年人健康管理的新模式和新方法。',
					category: '健康养生',
					publishTime: '2024-01-12 14:45',
					views: 654,
					likes: 43,
					author: '健康专家',
					image: '/static/picture/zixun/OIP-C.jpg',
					imageError: false
				},
				{
					id: 5,
					title: '社区老年大学开展丰富多彩的文化活动',
					summary: '社区老年大学举办书法、绘画、音乐等多种文化活动，丰富老年人精神文化生活。',
					category: '社区活动',
					publishTime: '2024-01-11 16:30',
					views: 521,
					likes: 38,
					author: '社区记者',
					image: '/static/picture/zixun/R-C.jpg',
					imageError: false
				},
				{
					id: 6,
					title: '养老行业数字化转型趋势分析',
					summary: '分析当前养老行业数字化转型的现状、挑战和发展趋势，为行业发展提供参考。',
					category: '行业资讯',
					publishTime: '2024-01-10 11:20',
					views: 432,
					likes: 29,
					author: '行业分析师',
					image: '/static/picture/zixun/0b0b-778029837c1616fbb2e33f0028be1b5d.jpg',
					imageError: false
				},
				{
					id: 7,
					title: '适老化改造惠民工程全面启动',
					summary: '政府启动大规模适老化改造工程，为老年人居住环境提供无障碍改造和智能化升级。',
					category: '政策解读',
					publishTime: '2024-01-09 08:45',
					views: 876,
					likes: 65,
					author: '民生记者',
					image: '/static/picture/zixun/R-C.jpg',
					imageError: false
				},
				{
					id: 8,
					title: '老年人数字生活技能培训班火热开班',
					summary: '社区开设老年人智能手机使用、网上购物等数字技能培训，帮助老年人融入数字时代。',
					category: '社区活动',
					publishTime: '2024-01-08 14:20',
					views: 623,
					likes: 47,
					author: '教育专员',
					image: '/static/picture/zixun/OIP-C.jpg',
					imageError: false
				}
			]
		}
	},
	computed: {
		// 筛选后的资讯列表
		filteredNews() {
			if (!this.selectedCategory) {
				return this.newsList;
			}
			return this.newsList.filter(item => item.category === this.selectedCategory);
		}
	},
	onLoad() {
		// 确保离线数据已初始化
		OfflineDataManager.initOfflineData();
		this.loadNews();
	},
	methods: {
		// 加载资讯列表
		async loadNews() {
			if (this.loading) return;

			try {
				this.loading = true;

				// 确保离线数据已初始化
				OfflineDataManager.initOfflineData();

				// 直接使用离线数据，确保100%可用性
				const params = {
					page: this.page,
					pageSize: this.pageSize,
					category: this.selectedCategory === 'all' ? undefined : this.selectedCategory
				};

				const result = OfflineDataManager.getOfflineNews(params);

				// 如果离线数据为空，使用备用数据
				if (!result.data || result.data.length === 0) {
					console.log('离线数据为空，使用备用数据');
					const backupData = this.getBackupNewsData();

					if (this.page === 1) {
						this.newsList = backupData;
					} else {
						this.newsList.push(...backupData);
					}

					this.hasMore = false;
				} else {
					if (this.page === 1) {
						this.newsList = result.data;
					} else {
						this.newsList.push(...result.data);
					}

					this.hasMore = result.hasMore;
				}

				// 显示成功提示
				if (this.page === 1 && this.newsList.length > 0) {
					FeedbackUtils.showSuccess(`已加载 ${this.newsList.length} 条资讯`);
				}
			} catch (error) {
				console.error('加载资讯失败:', error);

				// 如果出错，使用备用数据
				if (this.page === 1) {
					this.newsList = this.getBackupNewsData();
					FeedbackUtils.showInfo('已加载本地资讯数据');
				} else {
					FeedbackUtils.showError('数据加载失败，请重试');
				}
			} finally {
				this.loading = false;
				this.refreshing = false;
			}
		},

		// 获取备用资讯数据
		getBackupNewsData() {
			return [
				{
					id: 1,
					title: '养老服务新政策发布，惠及更多老年人',
					summary: '政府出台新的养老服务补贴政策，进一步完善养老服务体系，提高老年人生活质量。',
					category: '政策解读',
					publishTime: '2024-01-15 10:30',
					views: 1256,
					likes: 89,
					author: '政策解读员',
					image: '/static/picture/zixun/W020211011780554733191.jpg',
					imageError: false
				},
				{
					id: 2,
					title: '智慧养老技术创新助力老年人生活',
					summary: '人工智能、物联网等新技术在养老领域的应用，为老年人提供更便捷的生活服务。',
					category: '科技创新',
					publishTime: '2024-01-14 15:20',
					views: 892,
					likes: 67,
					author: '科技观察员',
					image: '/static/picture/zixun/71E00B4C613705188E11E072AC3B97F9A9493F32_size101_w1280_h853.jpg',
					imageError: false
				},
				{
					id: 3,
					title: '社区养老服务中心建设加速推进',
					summary: '各地加快推进社区养老服务中心建设，为老年人提供就近便民的养老服务。',
					category: '服务动态',
					publishTime: '2024-01-13 09:15',
					views: 743,
					likes: 52,
					author: '服务观察员',
					image: '/static/picture/zixun/8663976bbb664a0e9f6fd0ee564e5a8c.jpeg',
					imageError: false
				},
				{
					id: 4,
					title: '老年人健康管理新模式探索',
					summary: '结合互联网医疗和智能设备，探索老年人健康管理的新模式和新方法。',
					category: '健康养生',
					publishTime: '2024-01-12 14:45',
					views: 654,
					likes: 43,
					author: '健康专家',
					image: '/static/picture/zixun/OIP-C.jpg',
					imageError: false
				},
				{
					id: 5,
					title: '社区老年大学开展丰富多彩的文化活动',
					summary: '社区老年大学举办书法、绘画、音乐等多种文化活动，丰富老年人精神文化生活。',
					category: '社区活动',
					publishTime: '2024-01-11 16:30',
					views: 521,
					likes: 38,
					author: '社区记者',
					image: '/static/picture/zixun/R-C.jpg',
					imageError: false
				}
			];
		},

		// 选择分类
		selectCategory(category) {
			FeedbackUtils.lightFeedback();
			this.selectedCategory = category;
		},
		
		// 下拉刷新
		refreshData() {
			FeedbackUtils.lightFeedback();
			this.refreshing = true;
			this.page = 1;
			this.hasMore = true;
			this.loadNews();
		},
		
		// 上拉加载更多
		loadMore() {
			if (this.hasMore && !this.loading) {
				this.page++;
				this.loadNews();
			}
		},

		// 查看详情
		viewDetail(item) {
			FeedbackUtils.lightFeedback();
			uni.navigateTo({
				url: `/pages/news/detail?id=${item.id}`
			});
		},

		// 显示筛选
		showFilter() {
			FeedbackUtils.lightFeedback();
			FeedbackUtils.showInfo('筛选功能开发中');
		},

		// 获取图片路径 - 优化图片加载
		getImagePath(imagePath) {
			if (!imagePath) return '';

			// 确保使用static目录下的图片路径
			if (imagePath.startsWith('/static/')) {
				return imagePath;
			}

			// 如果是旧的picture路径，转换为static路径
			if (imagePath.startsWith('/picture/')) {
				return imagePath.replace('/picture/', '/static/picture/');
			}

			// 如果是文件名，添加完整的static路径
			if (!imagePath.includes('/')) {
				return `/static/picture/zixun/${imagePath}`;
			}

			return imagePath;
		},

		// 处理图片加载成功
		handleImageLoad(item) {
			console.log('图片加载成功:', item.image);
			item.imageError = false;
		},

		// 处理图片加载错误
		handleImageError(item) {
			console.log('图片加载失败:', item.image);
			item.imageError = true;

			// 尝试使用备用图片路径
			const backupImages = [
				'/static/picture/zixun/W020211011780554733191.jpg',
				'/static/picture/zixun/OIP-C.jpg',
				'/static/picture/zixun/R-C.jpg'
			];

			// 如果当前图片不在备用列表中，尝试第一个备用图片
			if (!backupImages.includes(item.image)) {
				item.image = backupImages[0];
				item.imageError = false;
				console.log('尝试使用备用图片:', item.image);
				// 给一点时间让图片重新加载
				setTimeout(() => {
					this.$forceUpdate();
				}, 100);
			} else {
				console.log('所有图片都加载失败，显示默认图标');
			}
		},

		// 获取分类图标
		getCategoryIcon(category) {
			const iconMap = {
				'政策解读': 'government-line',
				'健康养生': 'heart-pulse-line',
				'科技创新': 'rocket-line',
				'服务动态': 'service-line',
				'社区活动': 'community-line',
				'行业资讯': 'building-line'
			};
			return iconMap[category] || 'article-line';
		},

		// 获取分类颜色
		getCategoryColor(category) {
			const colorMap = {
				'政策解读': '#ff8a00',
				'健康养生': '#4caf50',
				'科技创新': '#2196f3',
				'服务动态': '#9c27b0',
				'社区活动': '#ff9800',
				'行业资讯': '#607d8b'
			};
			return colorMap[category] || '#666';
		},

		// 获取图标背景颜色
		getIconBgColor(category) {
			const colorMap = {
				'政策解读': 'linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%)',
				'健康养生': 'linear-gradient(135deg, #4caf50 0%, #45a049 100%)',
				'科技创新': 'linear-gradient(135deg, #2196f3 0%, #1976d2 100%)',
				'服务动态': 'linear-gradient(135deg, #9c27b0 0%, #7b1fa2 100%)',
				'社区活动': 'linear-gradient(135deg, #ff9800 0%, #f57c00 100%)',
				'行业资讯': 'linear-gradient(135deg, #607d8b 0%, #455a64 100%)'
			};
			return colorMap[category] || 'linear-gradient(135deg, #666 0%, #555 100%)';
		},

		// 格式化时间
		formatTime(timeString) {
			const date = new Date(timeString);
			const now = new Date();
			const diff = now - date;
			const hours = Math.floor(diff / (1000 * 60 * 60));
			const days = Math.floor(hours / 24);
			
			if (days > 0) {
				return `${days}天前`;
			} else if (hours > 0) {
				return `${hours}小时前`;
			} else {
				return '刚刚';
			}
		}
	}
}
</script>

<style scoped>
.container {
	background-color: #f5f5f5;
	min-height: 100vh;
	display: flex;
	flex-direction: column;
}

.page-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 30rpx 40rpx;
	background: white;
	border-bottom: 1rpx solid #f0f0f0;
}

.page-title {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
}

.category-filter {
	background: white;
	border-bottom: 1rpx solid #f0f0f0;
}

.category-scroll {
	white-space: nowrap;
}

.category-list {
	display: flex;
	padding: 20rpx 40rpx;
	gap: 20rpx;
}

.category-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 8rpx;
	padding: 20rpx 15rpx;
	border-radius: 20rpx;
	background: rgba(255, 138, 0, 0.1);
	white-space: nowrap;
	transition: all 0.3s ease;
	min-width: 100rpx;
}

.category-item.active {
	background: linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%);
}

.category-text {
	font-size: 24rpx;
	color: #ff8a00;
}

.category-item.active .category-text {
	color: white;
	font-weight: 500;
}

.news-list {
	flex: 1;
	padding: 20rpx;
}

.news-item {
	margin-bottom: 20rpx;
}

.news-content {
	display: flex;
	padding: 30rpx;
	gap: 20rpx;
}

.news-image-container {
	flex-shrink: 0;
	width: 180rpx;
	height: 140rpx;
	position: relative;
	overflow: hidden;
	border-radius: 20rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
	background: #f8f9fa;
}

.news-image {
	width: 100%;
	height: 100%;
	border-radius: 20rpx;
	object-fit: cover;
	transition: all 0.3s ease;
	background: #f0f0f0;
}

.news-image:hover {
	transform: scale(1.02);
}

/* 图片加载状态 */
.news-image-container::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
	transform: translateX(-100%);
	animation: loading-shimmer 1.5s infinite;
	z-index: 1;
	pointer-events: none;
}

.news-image-container:has(.news-image)::before {
	display: none;
}

@keyframes loading-shimmer {
	0% { transform: translateX(-100%); }
	100% { transform: translateX(100%); }
}

.news-icon-placeholder {
	width: 100%;
	height: 100%;
	border-radius: 20rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	position: relative;
	overflow: hidden;
}

.news-icon-placeholder::after {
	content: '';
	position: absolute;
	top: -50%;
	left: -50%;
	width: 200%;
	height: 200%;
	background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.2), transparent);
	transform: rotate(45deg);
	animation: icon-shimmer 3s infinite;
}

@keyframes icon-shimmer {
	0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
	100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

.news-info {
	flex: 1;
	display: flex;
	flex-direction: column;
	gap: 10rpx;
}

.news-header {
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
	gap: 15rpx;
}

.news-title {
	flex: 1;
	font-size: 30rpx;
	font-weight: bold;
	color: #333;
	line-height: 1.4;
	display: -webkit-box;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
	overflow: hidden;
}

.news-category {
	flex-shrink: 0;
	padding: 6rpx 12rpx;
	border-radius: 12rpx;
}

.category-label {
	font-size: 20rpx;
	color: white;
	font-weight: 500;
}

.news-summary {
	font-size: 26rpx;
	color: #666;
	line-height: 1.5;
	display: -webkit-box;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
	overflow: hidden;
}

.news-meta {
	display: flex;
	gap: 20rpx;
	margin-top: auto;
}

.meta-item {
	display: flex;
	align-items: center;
	gap: 6rpx;
}

.meta-text {
	font-size: 22rpx;
	color: #999;
}

.load-more {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 40rpx;
	gap: 20rpx;
}

.loading-spinner {
	width: 40rpx;
	height: 40rpx;
	border: 3rpx solid rgba(255, 138, 0, 0.2);
	border-top: 3rpx solid #ff8a00;
	border-radius: 50%;
	animation: loading 1s linear infinite;
}

.loading-text {
	font-size: 26rpx;
	color: #999;
}

.no-more {
	text-align: center;
	padding: 40rpx;
	color: #999;
	font-size: 26rpx;
}

.empty {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 100rpx 40rpx;
	gap: 20rpx;
}

.empty-text {
	font-size: 28rpx;
	color: #999;
}

.empty-tip {
	font-size: 24rpx;
	color: #ccc;
}

@keyframes loading {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}
</style>
