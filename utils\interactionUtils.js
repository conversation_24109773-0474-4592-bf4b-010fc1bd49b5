/**
 * 交互工具类
 * 统一管理用户交互反馈，提供一致的用户体验
 */

import FeedbackUtils from './feedback.js'

// 交互状态管理
const interactionState = {
  isProcessing: false,
  lastClickTime: 0,
  processingOperations: new Set()
}

/**
 * 防抖点击处理
 */
function debounceClick(callback, delay = 300) {
  const now = Date.now()
  if (now - interactionState.lastClickTime < delay) {
    return false
  }
  interactionState.lastClickTime = now
  return callback()
}

/**
 * 检查是否为适老化模式
 */
function isElderlyMode() {
  try {
    return uni.getStorageSync('elderlyMode') || false
  } catch (e) {
    return false
  }
}

/**
 * 适老化反馈增强
 */
function enhancedFeedback(type = 'light') {
  const elderly = isElderlyMode()
  
  // 震动反馈
  if (elderly) {
    FeedbackUtils.vibrate('medium') // 更强的震动
  } else {
    FeedbackUtils.vibrate(type)
  }
  
  // 语音反馈（App平台）
  // #ifdef APP-PLUS
  if (elderly) {
    try {
      plus.speech && plus.speech.startSpeech({
        content: '操作已确认',
        volume: 0.3
      })
    } catch (e) {
      console.log('语音反馈不可用')
    }
  }
  // #endif
}

/**
 * 统一的按钮点击处理
 */
export function handleButtonClick(options = {}) {
  const {
    callback,
    loadingText = '处理中...',
    successText = '操作成功',
    errorText = '操作失败',
    showLoading = true,
    showSuccess = true,
    debounce = true,
    vibrate = true,
    operationId = null
  } = options
  
  // 防抖处理
  if (debounce) {
    return debounceClick(async () => {
      return await executeButtonClick(options)
    })
  } else {
    return executeButtonClick(options)
  }
}

/**
 * 执行按钮点击逻辑
 */
async function executeButtonClick(options) {
  const {
    callback,
    loadingText,
    successText,
    errorText,
    showLoading,
    showSuccess,
    vibrate,
    operationId
  } = options
  
  try {
    // 检查是否正在处理相同操作
    if (operationId && interactionState.processingOperations.has(operationId)) {
      return false
    }
    
    // 添加操作到处理集合
    if (operationId) {
      interactionState.processingOperations.add(operationId)
    }
    
    // 震动反馈
    if (vibrate) {
      enhancedFeedback()
    }
    
    // 显示加载状态
    if (showLoading) {
      FeedbackUtils.showLoading(loadingText)
    }
    
    // 执行回调
    const result = await callback()
    
    // 隐藏加载状态
    if (showLoading) {
      FeedbackUtils.hideLoading()
    }
    
    // 显示成功提示
    if (showSuccess && result !== false) {
      FeedbackUtils.showSuccess(successText)
    }
    
    return result
    
  } catch (error) {
    console.error('按钮操作失败:', error)
    
    // 隐藏加载状态
    if (showLoading) {
      FeedbackUtils.hideLoading()
    }
    
    // 显示错误提示
    FeedbackUtils.showError(errorText)
    
    return false
    
  } finally {
    // 从处理集合中移除操作
    if (operationId) {
      interactionState.processingOperations.delete(operationId)
    }
  }
}

/**
 * 统一的页面跳转处理
 */
export function handleNavigation(options = {}) {
  const {
    url,
    type = 'navigateTo',
    params = {},
    loadingText = '跳转中...',
    showLoading = true,
    vibrate = true,
    beforeNavigate = null,
    afterNavigate = null
  } = options
  
  return handleButtonClick({
    callback: async () => {
      // 执行跳转前回调
      if (beforeNavigate) {
        const shouldContinue = await beforeNavigate()
        if (shouldContinue === false) {
          return false
        }
      }
      
      // 构建完整URL
      let fullUrl = url
      if (Object.keys(params).length > 0) {
        const queryString = Object.keys(params)
          .map(key => `${key}=${encodeURIComponent(params[key])}`)
          .join('&')
        fullUrl += (url.includes('?') ? '&' : '?') + queryString
      }
      
      // 执行跳转
      return new Promise((resolve, reject) => {
        const navigationMethod = uni[type] || uni.navigateTo
        
        navigationMethod({
          url: fullUrl,
          success: (res) => {
            console.log('页面跳转成功:', fullUrl)
            if (afterNavigate) {
              afterNavigate(res)
            }
            resolve(res)
          },
          fail: (error) => {
            console.error('页面跳转失败:', error)
            reject(error)
          }
        })
      })
    },
    loadingText,
    successText: '',
    errorText: '页面跳转失败',
    showLoading,
    showSuccess: false,
    vibrate,
    operationId: `navigation_${url}`
  })
}

/**
 * 统一的表单提交处理
 */
export function handleFormSubmit(options = {}) {
  const {
    formData,
    validator = null,
    submitCallback,
    loadingText = '提交中...',
    successText = '提交成功',
    errorText = '提交失败'
  } = options
  
  return handleButtonClick({
    callback: async () => {
      // 表单验证
      if (validator) {
        const validationResult = await validator(formData)
        if (validationResult !== true) {
          FeedbackUtils.showError(validationResult || '表单验证失败')
          return false
        }
      }
      
      // 执行提交
      return await submitCallback(formData)
    },
    loadingText,
    successText,
    errorText,
    operationId: 'form_submit'
  })
}

/**
 * 统一的异步操作处理
 */
export function handleAsyncOperation(options = {}) {
  const {
    operation,
    loadingText = '处理中...',
    successText = '操作成功',
    errorText = '操作失败',
    showLoading = true,
    showSuccess = true,
    operationId = null
  } = options
  
  return handleButtonClick({
    callback: operation,
    loadingText,
    successText,
    errorText,
    showLoading,
    showSuccess,
    operationId
  })
}

/**
 * 紧急服务专用处理
 */
export function handleEmergencyAction(options = {}) {
  const {
    actionType,
    callback,
    confirmText = null
  } = options
  
  return handleButtonClick({
    callback: async () => {
      // 紧急操作需要确认
      if (confirmText) {
        const confirmed = await FeedbackUtils.showConfirm(confirmText)
        if (!confirmed) {
          return false
        }
      }
      
      return await callback()
    },
    loadingText: '正在连接...',
    successText: '连接成功',
    errorText: '连接失败，请重试',
    vibrate: true,
    operationId: `emergency_${actionType}`
  })
}

/**
 * 收藏/取消收藏处理
 */
export function handleFavoriteToggle(options = {}) {
  const {
    itemId,
    currentStatus,
    toggleCallback
  } = options
  
  const actionText = currentStatus ? '取消收藏' : '收藏'
  const successText = currentStatus ? '已取消收藏' : '已添加到收藏'
  
  return handleButtonClick({
    callback: () => toggleCallback(itemId, !currentStatus),
    loadingText: `${actionText}中...`,
    successText,
    errorText: `${actionText}失败`,
    operationId: `favorite_${itemId}`
  })
}

/**
 * 分享操作处理
 */
export function handleShare(options = {}) {
  const {
    shareData,
    shareCallback
  } = options
  
  return handleButtonClick({
    callback: () => shareCallback(shareData),
    loadingText: '准备分享...',
    successText: '',
    errorText: '分享失败',
    showSuccess: false,
    operationId: 'share_action'
  })
}

/**
 * 电话拨打处理
 */
export function handlePhoneCall(options = {}) {
  const {
    phoneNumber,
    confirmText = `确定要拨打电话 ${phoneNumber} 吗？`
  } = options
  
  return handleEmergencyAction({
    actionType: 'phone_call',
    confirmText,
    callback: () => {
      return new Promise((resolve, reject) => {
        uni.makePhoneCall({
          phoneNumber,
          success: resolve,
          fail: reject
        })
      })
    }
  })
}

/**
 * 重置交互状态（用于页面卸载时清理）
 */
export function resetInteractionState() {
  interactionState.isProcessing = false
  interactionState.lastClickTime = 0
  interactionState.processingOperations.clear()
}

// 导出工具对象
export default {
  handleButtonClick,
  handleNavigation,
  handleFormSubmit,
  handleAsyncOperation,
  handleEmergencyAction,
  handleFavoriteToggle,
  handleShare,
  handlePhoneCall,
  resetInteractionState,
  enhancedFeedback
}
