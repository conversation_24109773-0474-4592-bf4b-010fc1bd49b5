<template>
	<view class="container">
		<!-- 头部横幅 -->
		<view class="header-banner">
			<view class="banner-content">
				<view class="banner-text">
					<text class="banner-title">社区养老</text>
					<text class="banner-subtitle">就近享受专业养老服务</text>
				</view>
				<view class="banner-icon">
					<Icon name="community-line" size="80rpx" color="white"></Icon>
				</view>
			</view>
		</view>

		<!-- 服务特色 -->
		<view class="section">
			<view class="section-header">
				<text class="section-title">服务特色</text>
				<text class="section-subtitle">社区养老的独特优势</text>
			</view>
			<view class="feature-grid">
				<view class="feature-item">
					<view class="feature-icon location">
						<Icon name="map-pin-line" size="48rpx" color="white"></Icon>
					</view>
					<text class="feature-title">就近便民</text>
					<text class="feature-desc">社区内就能享受专业服务</text>
				</view>
				<view class="feature-item">
					<view class="feature-icon professional">
						<Icon name="user-heart-line" size="48rpx" color="white"></Icon>
					</view>
					<text class="feature-title">专业团队</text>
					<text class="feature-desc">经验丰富的护理团队</text>
				</view>
				<view class="feature-item">
					<view class="feature-icon affordable">
						<Icon name="money-cny-circle-line" size="48rpx" color="white"></Icon>
					</view>
					<text class="feature-title">价格实惠</text>
					<text class="feature-desc">比机构养老更经济实惠</text>
				</view>
				<view class="feature-item">
					<view class="feature-icon flexible">
						<Icon name="time-line" size="48rpx" color="white"></Icon>
					</view>
					<text class="feature-title">时间灵活</text>
					<text class="feature-desc">根据需求灵活安排时间</text>
				</view>
			</view>
		</view>

		<!-- 服务项目 -->
		<view class="section">
			<view class="section-header">
				<text class="section-title">服务项目</text>
				<text class="section-subtitle">丰富多样的养老服务</text>
			</view>
			<view class="service-list">
				<view class="service-item" v-for="(item, index) in serviceList" :key="index" @click="viewService(item)">
					<view class="service-icon" :style="{backgroundColor: item.color}">
						<Icon :name="item.icon" size="40rpx" color="white"></Icon>
					</view>
					<view class="service-content">
						<text class="service-name">{{item.name}}</text>
						<text class="service-desc">{{item.description}}</text>
						<view class="service-meta">
							<text class="service-price">¥{{item.price}}/次</text>
							<text class="service-duration">{{item.duration}}</text>
						</view>
					</view>
					<view class="service-action">
						<button class="book-btn" @click.stop="bookService(item)">预约</button>
					</view>
				</view>
			</view>
		</view>

		<!-- 附近社区 -->
		<view class="section">
			<view class="section-header">
				<text class="section-title">附近社区</text>
				<text class="section-subtitle">选择就近的社区服务中心</text>
			</view>
			<view class="community-list">
				<view class="community-item" v-for="(item, index) in communityList" :key="index" @click="viewCommunity(item)">
					<view class="community-image">
						<Icon name="building-line" size="60rpx" color="#009688"></Icon>
					</view>
					<view class="community-content">
						<text class="community-name">{{item.name}}</text>
						<text class="community-address">{{item.address}}</text>
						<view class="community-services">
							<text class="service-tag" v-for="service in item.services" :key="service">{{service}}</text>
						</view>
						<view class="community-meta">
							<view class="distance">
								<Icon name="map-pin-line" size="24rpx" color="#999"></Icon>
								<text class="distance-text">{{item.distance}}</text>
							</view>
							<view class="rating">
								<Icon name="star-fill" size="24rpx" color="#ffc107"></Icon>
								<text class="rating-text">{{item.rating}}</text>
							</view>
						</view>
					</view>
					<view class="community-action">
						<Icon name="arrow-right-s-line" size="24rpx" color="#999"></Icon>
					</view>
				</view>
			</view>
		</view>

		<!-- 预约记录 -->
		<view class="section" v-if="bookingList.length > 0">
			<view class="section-header">
				<text class="section-title">我的预约</text>
				<text class="section-subtitle">查看预约记录</text>
			</view>
			<view class="booking-list">
				<view class="booking-item" v-for="(item, index) in bookingList" :key="index">
					<view class="booking-header">
						<text class="booking-service">{{item.serviceName}}</text>
						<view class="booking-status" :class="item.status">{{item.statusText}}</view>
					</view>
					<view class="booking-info">
						<text class="booking-time">服务时间：{{item.serviceTime}}</text>
						<text class="booking-location">服务地点：{{item.location}}</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 底部操作 -->
		<view class="bottom-actions">
			<button class="action-btn secondary" @click="findCommunity">
				<Icon name="map-line" size="32rpx" color="#ff8a00"></Icon>
				<text>查找社区</text>
			</button>
			<button class="action-btn primary" @click="consultService">
				<Icon name="customer-service-2-line" size="32rpx" color="white"></Icon>
				<text>咨询服务</text>
			</button>
		</view>
	</view>
</template>

<script>
import Icon from '@/components/Icon/Icon.vue'

export default {
	components: {
		Icon
	},
	data() {
		return {
			serviceList: [
				{
					id: 1,
					name: '生活照料',
					description: '协助日常生活起居，洗衣做饭等',
					price: 80,
					duration: '2小时',
					icon: 'heart-3-line',
					color: '#e91e63'
				},
				{
					id: 2,
					name: '医疗护理',
					description: '基础医疗护理，健康监测',
					price: 120,
					duration: '1小时',
					icon: 'health-book-line',
					color: '#4caf50'
				},
				{
					id: 3,
					name: '康复训练',
					description: '专业康复指导，功能训练',
					price: 150,
					duration: '1.5小时',
					icon: 'run-line',
					color: '#2196f3'
				},
				{
					id: 4,
					name: '心理关怀',
					description: '心理疏导，情感陪伴',
					price: 100,
					duration: '1小时',
					icon: 'emotion-happy-line',
					color: '#ff9800'
				}
			],
			communityList: [
				{
					id: 1,
					name: '阳光社区服务中心',
					address: '阳光路123号',
					distance: '500m',
					rating: 4.8,
					services: ['生活照料', '医疗护理', '康复训练']
				},
				{
					id: 2,
					name: '和谐社区养老站',
					address: '和谐街456号',
					distance: '800m',
					rating: 4.6,
					services: ['心理关怀', '文娱活动', '健康咨询']
				},
				{
					id: 3,
					name: '幸福社区日间照料中心',
					address: '幸福大道789号',
					distance: '1.2km',
					rating: 4.9,
					services: ['日间照料', '营养配餐', '康复训练']
				}
			],
			bookingList: [
				// 示例数据，实际应从后端获取
			]
		}
	},
	methods: {
		viewService(service) {
			uni.navigateTo({
				url: `/pages/community-care/service-detail?id=${service.id}`
			});
		},
		bookService(service) {
			uni.navigateTo({
				url: `/pages/community-care/booking?serviceId=${service.id}`
			});
		},
		viewCommunity(community) {
			uni.navigateTo({
				url: `/pages/community-care/community-detail?id=${community.id}`
			});
		},
		findCommunity() {
			uni.navigateTo({
				url: '/pages/map/map?category=community'
			});
		},
		consultService() {
			uni.makePhoneCall({
				phoneNumber: '************'
			});
		}
	}
}
</script>

<style scoped>
.container {
	background: linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%);
	min-height: 100vh;
	padding-bottom: 200rpx;
}

.header-banner {
	padding: 40rpx;
	margin-bottom: 40rpx;
}

.banner-content {
	background: rgba(255, 255, 255, 0.15);
	border-radius: 30rpx;
	padding: 40rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.banner-text {
	flex: 1;
}

.banner-title {
	font-size: 48rpx;
	font-weight: bold;
	color: white;
	display: block;
	margin-bottom: 15rpx;
}

.banner-subtitle {
	font-size: 28rpx;
	color: rgba(255, 255, 255, 0.9);
	display: block;
}

.banner-icon {
	margin-left: 30rpx;
}

.section {
	margin-bottom: 40rpx;
}

.section-header {
	padding: 0 40rpx 30rpx;
}

.section-title {
	font-size: 36rpx;
	font-weight: bold;
	color: white;
	display: block;
	margin-bottom: 10rpx;
}

.section-subtitle {
	font-size: 26rpx;
	color: rgba(255, 255, 255, 0.8);
	display: block;
}

.feature-grid {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	gap: 30rpx;
	padding: 0 40rpx;
}

.feature-item {
	background: white;
	border-radius: 30rpx;
	padding: 40rpx;
	text-align: center;
	box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.feature-icon {
	width: 100rpx;
	height: 100rpx;
	border-radius: 20rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin: 0 auto 20rpx;
}

.feature-icon.location { background: linear-gradient(135deg, #009688 0%, #00695c 100%); }
.feature-icon.professional { background: linear-gradient(135deg, #e91e63 0%, #c2185b 100%); }
.feature-icon.affordable { background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%); }
.feature-icon.flexible { background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%); }

.feature-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 10rpx;
}

.feature-desc {
	font-size: 24rpx;
	color: #666;
	display: block;
}

.service-list, .community-list, .booking-list {
	padding: 0 40rpx;
}

.service-item, .community-item, .booking-item {
	background: white;
	border-radius: 30rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	display: flex;
	align-items: center;
	gap: 20rpx;
	box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.service-icon {
	width: 80rpx;
	height: 80rpx;
	border-radius: 20rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.service-content, .community-content {
	flex: 1;
}

.service-name, .community-name {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 8rpx;
}

.service-desc, .community-address {
	font-size: 26rpx;
	color: #666;
	display: block;
	margin-bottom: 15rpx;
}

.service-meta, .community-meta {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.service-price {
	font-size: 28rpx;
	font-weight: bold;
	color: #ff8a00;
}

.service-duration {
	font-size: 24rpx;
	color: #999;
}

.book-btn {
	background: linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%);
	color: white;
	border: none;
	padding: 15rpx 30rpx;
	border-radius: 20rpx;
	font-size: 26rpx;
	font-weight: 500;
}

.community-image {
	width: 100rpx;
	height: 100rpx;
	background: #f8f9fa;
	border-radius: 20rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.community-services {
	display: flex;
	gap: 10rpx;
	margin-bottom: 15rpx;
}

.service-tag {
	background: rgba(0, 150, 136, 0.1);
	color: #009688;
	font-size: 22rpx;
	padding: 5rpx 15rpx;
	border-radius: 15rpx;
}

.distance, .rating {
	display: flex;
	align-items: center;
	gap: 8rpx;
}

.distance-text, .rating-text {
	font-size: 24rpx;
	color: #666;
}

.booking-item {
	flex-direction: column;
	align-items: stretch;
}

.booking-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 15rpx;
}

.booking-service {
	font-size: 30rpx;
	font-weight: bold;
	color: #333;
}

.booking-status {
	padding: 8rpx 20rpx;
	border-radius: 20rpx;
	font-size: 24rpx;
}

.booking-status.confirmed {
	background: rgba(76, 175, 80, 0.1);
	color: #4caf50;
}

.booking-status.pending {
	background: rgba(255, 152, 0, 0.1);
	color: #ff9800;
}

.booking-info {
	display: flex;
	flex-direction: column;
	gap: 8rpx;
}

.booking-time, .booking-location {
	font-size: 26rpx;
	color: #666;
}

.bottom-actions {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background: white;
	padding: 30rpx 40rpx;
	display: flex;
	gap: 20rpx;
	box-shadow: 0 -4rpx 20rpx rgba(0,0,0,0.1);
}

.action-btn {
	flex: 1;
	height: 100rpx;
	border-radius: 20rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 15rpx;
	font-size: 32rpx;
	font-weight: 500;
}

.action-btn.primary {
	background: linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%);
	color: white;
	border: none;
}

.action-btn.secondary {
	background: white;
	color: #ff8a00;
	border: 2rpx solid #ff8a00;
}
</style>
