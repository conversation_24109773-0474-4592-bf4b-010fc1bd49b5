<template>
	<view class="container">
		<PageHeader title="日程详情" showBack></PageHeader>
		
		<view class="content">
			<!-- 日程头部信息 -->
			<view class="schedule-header">
				<view class="schedule-type" :style="{ background: schedule.color }">
					<Icon :name="getTypeIcon(schedule.type)" size="48rpx" color="#fff"></Icon>
				</view>
				<view class="schedule-info">
					<text class="schedule-title">{{ schedule.title }}</text>
					<text class="schedule-type-text">{{ schedule.type }}</text>
				</view>
			</view>

			<!-- 日程详情 -->
			<view class="detail-card">
				<text class="card-title">日程信息</text>
				<view class="detail-list">
					<view class="detail-item">
						<Icon name="calendar-line" size="32rpx" color="#666"></Icon>
						<text class="detail-label">日期</text>
						<text class="detail-value">{{ schedule.date }}</text>
					</view>
					<view class="detail-item">
						<Icon name="time-line" size="32rpx" color="#666"></Icon>
						<text class="detail-label">时间</text>
						<text class="detail-value">{{ schedule.time }}</text>
					</view>
					<view class="detail-item" v-if="schedule.location">
						<Icon name="location-line" size="32rpx" color="#666"></Icon>
						<text class="detail-label">地点</text>
						<text class="detail-value">{{ schedule.location }}</text>
					</view>
					<view class="detail-item" v-if="schedule.description">
						<Icon name="file-line" size="32rpx" color="#666"></Icon>
						<text class="detail-label">备注</text>
						<text class="detail-value">{{ schedule.description }}</text>
					</view>
				</view>
			</view>

			<!-- 提醒设置 -->
			<view class="detail-card" v-if="schedule.reminder">
				<text class="card-title">提醒设置</text>
				<view class="reminder-info">
					<Icon name="notification-line" size="32rpx" color="#4caf50"></Icon>
					<text class="reminder-text">{{ schedule.reminderText }}</text>
				</view>
			</view>

			<!-- 操作按钮 -->
			<view class="action-buttons">
				<InteractiveButton 
					type="secondary" 
					text="编辑" 
					leftIcon="edit-line"
					@click="editSchedule"
				/>
				<InteractiveButton 
					type="danger" 
					text="删除" 
					leftIcon="delete-line"
					@click="deleteSchedule"
				/>
			</view>
		</view>
	</view>
</template>

<script>
import Icon from '@/components/Icon/Icon.vue'
import PageHeader from '@/components/PageHeader/PageHeader.vue'
import InteractiveButton from '@/components/InteractiveButton/InteractiveButton.vue'

export default {
	name: 'ScheduleDetail',
	components: {
		Icon,
		PageHeader,
		InteractiveButton
	},
	data() {
		return {
			scheduleId: '',
			schedule: {
				id: 1,
				title: '晨练',
				type: '运动',
				date: '2024年1月18日',
				time: '09:00',
				location: '社区公园',
				description: '每日晨练，保持身体健康',
				color: '#4caf50',
				reminder: true,
				reminderText: '提前15分钟提醒'
			}
		}
	},
	onLoad(options) {
		if (options.id) {
			this.scheduleId = options.id
			this.loadScheduleDetail()
		}
	},
	methods: {
		loadScheduleDetail() {
			// 模拟加载日程详情数据
			console.log('加载日程详情:', this.scheduleId)
		},
		getTypeIcon(type) {
			const iconMap = {
				'医疗': 'stethoscope-line',
				'运动': 'run-line',
				'学习': 'book-line',
				'社交': 'user-smile-line',
				'娱乐': 'game-line',
				'工作': 'briefcase-line',
				'其他': 'more-line'
			}
			return iconMap[type] || 'calendar-line'
		},
		editSchedule() {
			uni.navigateTo({
				url: `/pages/schedule/create?id=${this.scheduleId}&mode=edit`
			})
		},
		deleteSchedule() {
			uni.showModal({
				title: '确认删除',
				content: '确定要删除这个日程吗？删除后不可恢复。',
				success: (res) => {
					if (res.confirm) {
						this.performDelete()
					}
				}
			})
		},
		async performDelete() {
			try {
				// 模拟删除操作
				await new Promise(resolve => setTimeout(resolve, 1000))
				
				uni.showToast({
					title: '删除成功',
					icon: 'success'
				})
				
				setTimeout(() => {
					uni.navigateBack()
				}, 1500)
			} catch (error) {
				uni.showToast({
					title: '删除失败',
					icon: 'none'
				})
			}
		}
	}
}
</script>

<style scoped>
.container {
	min-height: 100vh;
	background: #f5f5f5;
}

.content {
	padding: 140rpx 32rpx 40rpx;
}

.schedule-header {
	background: white;
	border-radius: 20rpx;
	padding: 32rpx;
	margin-bottom: 24rpx;
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
	display: flex;
	align-items: center;
	gap: 24rpx;
}

.schedule-type {
	width: 96rpx;
	height: 96rpx;
	border-radius: 24rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.schedule-info {
	flex: 1;
}

.schedule-title {
	font-size: 36rpx;
	font-weight: 600;
	color: #333;
	display: block;
	margin-bottom: 8rpx;
}

.schedule-type-text {
	font-size: 26rpx;
	color: #666;
}

.detail-card {
	background: white;
	border-radius: 20rpx;
	padding: 32rpx;
	margin-bottom: 24rpx;
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

.card-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
	display: block;
	margin-bottom: 24rpx;
}

.detail-list {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

.detail-item {
	display: flex;
	align-items: center;
	gap: 16rpx;
}

.detail-label {
	font-size: 28rpx;
	color: #666;
	min-width: 80rpx;
}

.detail-value {
	font-size: 28rpx;
	color: #333;
	flex: 1;
}

.reminder-info {
	display: flex;
	align-items: center;
	gap: 16rpx;
}

.reminder-text {
	font-size: 28rpx;
	color: #4caf50;
}

.action-buttons {
	display: flex;
	gap: 16rpx;
	margin-top: 40rpx;
}
</style>
