<template>
	<view class="container" :class="{ 'elderly-mode': isElderlyMode }">
		<!-- 自定义导航栏 -->
		<view class="custom-navbar" :class="{ 'elderly-mode': isElderlyMode }">
			<view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
			<view class="navbar-content">
				<view class="navbar-left" @click="goBack">
					<Icon
						name="arrow-left-line"
						:size="isElderlyMode ? '40rpx' : '36rpx'"
						color="#333"
					></Icon>
					<text class="back-text">返回</text>
				</view>
				<view class="navbar-center">
					<text class="navbar-title">意见反馈</text>
				</view>
				<view class="navbar-right"></view>
			</view>
		</view>

		<!-- 反馈表单 -->
		<view class="feedback-form">
			<view class="form-header">
				<Icon name="feedback-line" size="60rpx" color="#ff8a00"></Icon>
				<text class="form-title">意见反馈</text>
				<text class="form-desc">您的意见对我们很重要，我们会认真对待每一条反馈</text>
			</view>

			<view class="form-content">
				<!-- 反馈类型 -->
				<view class="form-item">
					<text class="form-label">反馈类型 <text class="required">*</text></text>
					<view class="type-grid">
						<view class="type-item" v-for="(type, index) in feedbackTypes" :key="index" 
							  :class="{ active: selectedTypeIndex === index }" @click="selectType(index)">
							<Icon :name="type.icon" size="32rpx" :color="selectedTypeIndex === index ? '#ff8a00' : '#666'"></Icon>
							<text class="type-text">{{type.name}}</text>
						</view>
					</view>
				</view>

				<!-- 问题描述 -->
				<view class="form-item">
					<text class="form-label">问题描述 <text class="required">*</text></text>
					<textarea 
						class="form-textarea" 
						v-model="formData.description" 
						placeholder="请详细描述您遇到的问题或建议，我们会尽快处理"
						:maxlength="500"
					></textarea>
					<view class="char-count">{{formData.description.length}}/500</view>
				</view>

				<!-- 联系方式 -->
				<view class="form-item">
					<text class="form-label">联系方式</text>
					<input 
						class="form-input" 
						v-model="formData.contact" 
						placeholder="请输入手机号或邮箱（可选）" 
					/>
					<text class="form-hint">填写联系方式便于我们及时回复您</text>
				</view>

				<!-- 图片上传 -->
				<view class="form-item">
					<text class="form-label">相关图片</text>
					<view class="upload-section">
						<view class="image-list">
							<view class="image-item" v-for="(image, index) in uploadedImages" :key="index">
								<image :src="image" class="uploaded-image" mode="aspectFill" @click="previewImage(index)"></image>
								<button class="delete-btn" @click="deleteImage(index)">
									<Icon name="close-line" size="20rpx" color="white"></Icon>
								</button>
							</view>
							<button class="upload-btn" @click="uploadImage" v-if="uploadedImages.length < 3">
								<Icon name="add-line" size="40rpx" color="#999"></Icon>
								<text class="upload-text">添加图片</text>
							</button>
						</view>
						<text class="upload-hint">最多上传3张图片，支持jpg、png格式</text>
					</view>
				</view>

				<!-- 紧急程度 -->
				<view class="form-item">
					<text class="form-label">紧急程度</text>
					<view class="priority-options">
						<view class="priority-item" v-for="(priority, index) in priorities" :key="index"
							  :class="{ active: selectedPriorityIndex === index }" @click="selectPriority(index)">
							<view class="priority-dot" :style="{backgroundColor: priority.color}"></view>
							<text class="priority-text">{{priority.name}}</text>
						</view>
					</view>
				</view>

				<!-- 期望回复时间 -->
				<view class="form-item">
					<text class="form-label">期望回复时间</text>
					<picker :value="expectedReplyIndex" :range="expectedReplyOptions" @change="onExpectedReplyChange">
						<view class="picker-input">
							<text>{{expectedReplyOptions[expectedReplyIndex]}}</text>
							<Icon name="arrow-down-s-line" size="20rpx" color="#999"></Icon>
						</view>
					</picker>
				</view>
			</view>

			<!-- 提交按钮 -->
			<button class="submit-btn" @click="submitFeedback" :disabled="!canSubmit">提交反馈</button>
		</view>

		<!-- 历史反馈 -->
		<view class="history-section">
			<view class="section-header">
				<Icon name="history-line" size="32rpx" color="#ff8a00"></Icon>
				<text class="section-title">我的反馈</text>
				<text class="view-all" @click="viewAllFeedback">查看全部</text>
			</view>
			
			<view class="feedback-list">
				<view class="feedback-item" v-for="(feedback, index) in recentFeedback" :key="index" @click="viewFeedbackDetail(feedback)">
					<view class="feedback-header">
						<text class="feedback-type">{{getFeedbackTypeName(feedback.type)}}</text>
						<view class="feedback-status" :class="feedback.status">
							<text class="status-text">{{getStatusText(feedback.status)}}</text>
						</view>
					</view>
					<text class="feedback-content">{{feedback.description}}</text>
					<view class="feedback-footer">
						<text class="feedback-time">{{feedback.createTime}}</text>
						<text class="feedback-id">编号：{{feedback.id}}</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 联系方式 -->
		<view class="contact-section">
			<view class="section-header">
				<Icon name="customer-service-line" size="32rpx" color="#ff8a00"></Icon>
				<text class="section-title">其他联系方式</text>
			</view>
			
			<view class="contact-list">
				<view class="contact-item" @click="makeCall">
					<Icon name="phone-line" size="32rpx" color="#4caf50"></Icon>
					<view class="contact-info">
						<text class="contact-title">客服热线</text>
						<text class="contact-value">************</text>
					</view>
				</view>
				
				<view class="contact-item" @click="openChat">
					<Icon name="chat-3-line" size="32rpx" color="#2196f3"></Icon>
					<view class="contact-info">
						<text class="contact-title">在线客服</text>
						<text class="contact-value">7×24小时在线服务</text>
					</view>
				</view>
				
				<view class="contact-item" @click="sendEmail">
					<Icon name="mail-line" size="32rpx" color="#ff9800"></Icon>
					<view class="contact-info">
						<text class="contact-title">邮箱反馈</text>
						<text class="contact-value"><EMAIL></text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import Icon from '@/components/Icon/Icon.vue'
import { elderlyModeManager } from '@/utils/elderlyModeUtils.js'

export default {
	components: {
		Icon
	},
	data() {
		return {
			statusBarHeight: 0, // 状态栏高度
			isElderlyMode: false, // 适老化模式
			selectedTypeIndex: -1,
			selectedPriorityIndex: 1,
			expectedReplyIndex: 1,
			uploadedImages: [],
			formData: {
				description: '',
				contact: ''
			},
			feedbackTypes: [
				{ name: '功能建议', icon: 'lightbulb-line' },
				{ name: '问题反馈', icon: 'error-warning-line' },
				{ name: '界面优化', icon: 'palette-line' },
				{ name: '性能问题', icon: 'speed-line' },
				{ name: '服务投诉', icon: 'dislike-line' },
				{ name: '其他', icon: 'more-line' }
			],
			priorities: [
				{ name: '低', color: '#4caf50' },
				{ name: '中', color: '#ff9800' },
				{ name: '高', color: '#f44336' }
			],
			expectedReplyOptions: ['不需要回复', '1个工作日内', '3个工作日内', '1周内', '尽快回复'],
			recentFeedback: [
				{
					id: 'FB202401150001',
					type: 0,
					description: '希望能增加语音播报功能，方便老年人使用',
					status: 'processing',
					createTime: '2024-01-15 14:30'
				},
				{
					id: 'FB202401140002',
					type: 1,
					description: '应用偶尔会闪退，影响使用体验',
					status: 'resolved',
					createTime: '2024-01-14 09:20'
				},
				{
					id: 'FB202401130003',
					type: 2,
					description: '字体可以再大一些，老人看起来更清楚',
					status: 'pending',
					createTime: '2024-01-13 16:45'
				}
			]
		}
	},
	computed: {
		canSubmit() {
			return this.selectedTypeIndex >= 0 && this.formData.description.trim().length > 0;
		}
	},
	methods: {
		selectType(index) {
			this.selectedTypeIndex = index;
		},
		selectPriority(index) {
			this.selectedPriorityIndex = index;
		},
		onExpectedReplyChange(e) {
			this.expectedReplyIndex = e.detail.value;
		},
		uploadImage() {
			uni.chooseImage({
				count: 3 - this.uploadedImages.length,
				sizeType: ['compressed'],
				sourceType: ['album', 'camera'],
				success: (res) => {
					this.uploadedImages = this.uploadedImages.concat(res.tempFilePaths);
				}
			});
		},
		deleteImage(index) {
			this.uploadedImages.splice(index, 1);
		},
		previewImage(index) {
			uni.previewImage({
				urls: this.uploadedImages,
				current: index
			});
		},
		submitFeedback() {
			if (!this.canSubmit) return;
			
			uni.showLoading({
				title: '提交中...'
			});
			
			// 模拟提交
			setTimeout(() => {
				uni.hideLoading();
				uni.showToast({
					title: '反馈提交成功',
					icon: 'success'
				});
				
				// 重置表单
				this.resetForm();
			}, 2000);
		},
		resetForm() {
			this.selectedTypeIndex = -1;
			this.selectedPriorityIndex = 1;
			this.expectedReplyIndex = 1;
			this.uploadedImages = [];
			this.formData = {
				description: '',
				contact: ''
			};
		},
		getFeedbackTypeName(typeIndex) {
			return this.feedbackTypes[typeIndex]?.name || '其他';
		},
		getStatusText(status) {
			const statusMap = {
				'pending': '待处理',
				'processing': '处理中',
				'resolved': '已解决',
				'closed': '已关闭'
			};
			return statusMap[status] || '未知';
		},
		viewFeedbackDetail(feedback) {
			uni.navigateTo({
				url: `/pages/help/feedback-detail?id=${feedback.id}`
			});
		},
		viewAllFeedback() {
			uni.navigateTo({
				url: '/pages/help/feedback-list'
			});
		},
		makeCall() {
			uni.makePhoneCall({
				phoneNumber: '************'
			});
		},
		openChat() {
			uni.navigateTo({
				url: '/pages/help/service'
			});
		},
		sendEmail() {
			uni.showToast({
				title: '邮箱地址已复制',
				icon: 'success'
			});
		},

		// 返回上一页
		goBack() {
			uni.navigateBack({
				fail: () => {
					uni.switchTab({
						url: '/pages/profile/profile'
					});
				}
			});
		}
	},
	onLoad() {
		// 获取状态栏高度
		const systemInfo = uni.getSystemInfoSync();
		this.statusBarHeight = systemInfo.statusBarHeight || 44;

		// 初始化适老化模式
		this.isElderlyMode = elderlyModeManager.getElderlyMode();
		elderlyModeManager.onElderlyModeChange((isEnabled) => {
			this.isElderlyMode = isEnabled;
		});
	}
}
</script>

<style scoped>
.container {
	background: #f8f9fa;
	min-height: 100vh;
}

/* 导航栏样式 */
.custom-navbar {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 1000;
	background: rgba(255, 255, 255, 0.95);
	backdrop-filter: blur(20rpx);
	-webkit-backdrop-filter: blur(20rpx);
	border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);
}

.navbar-content {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 20rpx 32rpx;
	min-height: 88rpx;
}

.navbar-left {
	display: flex;
	align-items: center;
	gap: 12rpx;
	padding: 8rpx 16rpx 8rpx 0;
	border-radius: 20rpx;
	transition: all 0.2s ease;
}

.navbar-left:active {
	background-color: rgba(0, 0, 0, 0.04);
	transform: scale(0.96);
}

.back-text {
	font-size: 32rpx;
	color: #333;
	font-weight: 500;
}

.navbar-center {
	position: absolute;
	left: 50%;
	transform: translateX(-50%);
}

.navbar-title {
	font-size: 34rpx;
	font-weight: 600;
	color: #333;
}

.navbar-right {
	display: flex;
	align-items: center;
}

.feedback-form {
	background: white;
	margin: 20rpx;
	margin-top: 240rpx; /* 为导航栏留出空间 */
	border-radius: 30rpx;
	padding: 40rpx;
}

.form-header {
	text-align: center;
	margin-bottom: 40rpx;
}

.form-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin: 20rpx 0 15rpx;
}

.form-desc {
	font-size: 24rpx;
	color: #666;
	line-height: 1.5;
	display: block;
}

.form-item {
	margin-bottom: 40rpx;
}

.form-label {
	font-size: 28rpx;
	color: #333;
	font-weight: bold;
	display: block;
	margin-bottom: 20rpx;
}

.required {
	color: #f44336;
}

.type-grid {
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	gap: 15rpx;
}

.type-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 10rpx;
	padding: 25rpx 15rpx;
	background: #f8f9fa;
	border: 2rpx solid transparent;
	border-radius: 15rpx;
	text-align: center;
}

.type-item.active {
	background: rgba(255, 138, 0, 0.1);
	border-color: #ff8a00;
}

.type-text {
	font-size: 22rpx;
	color: #333;
}

.form-textarea {
	width: 100%;
	min-height: 200rpx;
	background: #f8f9fa;
	border: 2rpx solid #e0e0e0;
	border-radius: 15rpx;
	padding: 20rpx;
	font-size: 26rpx;
	color: #333;
}

.form-textarea:focus {
	border-color: #ff8a00;
}

.char-count {
	text-align: right;
	font-size: 22rpx;
	color: #999;
	margin-top: 10rpx;
}

.form-input {
	width: 100%;
	height: 80rpx;
	background: #f8f9fa;
	border: 2rpx solid #e0e0e0;
	border-radius: 15rpx;
	padding: 0 20rpx;
	font-size: 26rpx;
	color: #333;
}

.form-input:focus {
	border-color: #ff8a00;
}

.form-hint {
	font-size: 22rpx;
	color: #999;
	display: block;
	margin-top: 10rpx;
}

.upload-section {
	margin-top: 20rpx;
}

.image-list {
	display: flex;
	gap: 15rpx;
	margin-bottom: 15rpx;
}

.image-item {
	position: relative;
	width: 150rpx;
	height: 150rpx;
}

.uploaded-image {
	width: 100%;
	height: 100%;
	border-radius: 15rpx;
}

.delete-btn {
	position: absolute;
	top: -10rpx;
	right: -10rpx;
	width: 40rpx;
	height: 40rpx;
	background: #f44336;
	border: none;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
}

.upload-btn {
	width: 150rpx;
	height: 150rpx;
	background: #f8f9fa;
	border: 2rpx dashed #ccc;
	border-radius: 15rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	gap: 10rpx;
}

.upload-text {
	font-size: 22rpx;
	color: #999;
}

.upload-hint {
	font-size: 22rpx;
	color: #999;
}

.priority-options {
	display: flex;
	gap: 30rpx;
}

.priority-item {
	display: flex;
	align-items: center;
	gap: 10rpx;
	padding: 15rpx 25rpx;
	background: #f8f9fa;
	border: 2rpx solid transparent;
	border-radius: 20rpx;
}

.priority-item.active {
	background: rgba(255, 138, 0, 0.1);
	border-color: #ff8a00;
}

.priority-dot {
	width: 20rpx;
	height: 20rpx;
	border-radius: 50%;
}

.priority-text {
	font-size: 24rpx;
	color: #333;
}

.picker-input {
	display: flex;
	justify-content: space-between;
	align-items: center;
	height: 80rpx;
	background: #f8f9fa;
	border: 2rpx solid #e0e0e0;
	border-radius: 15rpx;
	padding: 0 20rpx;
	font-size: 26rpx;
	color: #333;
}

.submit-btn {
	width: 100%;
	height: 100rpx;
	background: linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%);
	color: white;
	border: none;
	border-radius: 20rpx;
	font-size: 32rpx;
	font-weight: bold;
	margin-top: 20rpx;
}

.submit-btn:disabled {
	background: #ccc;
}

.history-section, .contact-section {
	background: white;
	margin: 20rpx;
	border-radius: 30rpx;
	padding: 40rpx;
}

.section-header {
	display: flex;
	align-items: center;
	gap: 15rpx;
	margin-bottom: 30rpx;
}

.section-title {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
	flex: 1;
}

.view-all {
	font-size: 24rpx;
	color: #ff8a00;
}

.feedback-list {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

.feedback-item {
	padding: 25rpx;
	background: #f8f9fa;
	border-radius: 20rpx;
}

.feedback-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 15rpx;
}

.feedback-type {
	font-size: 24rpx;
	color: #ff8a00;
	background: rgba(255, 138, 0, 0.1);
	padding: 6rpx 15rpx;
	border-radius: 12rpx;
}

.feedback-status {
	padding: 6rpx 15rpx;
	border-radius: 12rpx;
	font-size: 22rpx;
}

.feedback-status.pending {
	background: rgba(158, 158, 158, 0.1);
	color: #9e9e9e;
}

.feedback-status.processing {
	background: rgba(33, 150, 243, 0.1);
	color: #2196f3;
}

.feedback-status.resolved {
	background: rgba(76, 175, 80, 0.1);
	color: #4caf50;
}

.feedback-content {
	font-size: 26rpx;
	color: #333;
	line-height: 1.5;
	display: block;
	margin-bottom: 15rpx;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.feedback-footer {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.feedback-time, .feedback-id {
	font-size: 22rpx;
	color: #999;
}

.contact-list {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

.contact-item {
	display: flex;
	align-items: center;
	gap: 20rpx;
	padding: 25rpx;
	background: #f8f9fa;
	border-radius: 20rpx;
}

.contact-info {
	flex: 1;
}

.contact-title {
	font-size: 26rpx;
	color: #333;
	font-weight: bold;
	display: block;
	margin-bottom: 5rpx;
}

.contact-value {
	font-size: 24rpx;
	color: #666;
	display: block;
}

/* ===== 适老化模式样式 ===== */
.elderly-mode .navbar-content {
	padding: 24rpx 36rpx;
	min-height: 96rpx;
}

.elderly-mode .navbar-left {
	gap: 16rpx;
	padding: 12rpx 20rpx 12rpx 0;
}

.elderly-mode .back-text {
	font-size: 36rpx;
	font-weight: 600;
}

.elderly-mode .navbar-title {
	font-size: 38rpx;
	font-weight: 700;
}

.elderly-mode .feedback-content {
	margin-top: 240rpx; /* 适老化模式下导航栏更高 */
	padding: 50rpx 30rpx;
}

.elderly-mode .section-title {
	font-size: 36rpx;
}

.elderly-mode .type-item {
	padding: 30rpx 24rpx;
}

.elderly-mode .type-text {
	font-size: 32rpx;
}

.elderly-mode .textarea {
	font-size: 32rpx;
	padding: 30rpx;
}

.elderly-mode .submit-button {
	padding: 30rpx;
	font-size: 36rpx;
}

.elderly-mode .form-label {
	font-size: 32rpx;
}
</style>
