<template>
	<view class="container">
		<!-- 头部横幅 -->
		<view class="header-banner">
			<view class="banner-content">
				<view class="banner-text">
					<text class="banner-title">智能监护</text>
					<text class="banner-subtitle">24小时智能安全守护</text>
				</view>
				<view class="banner-icon">
					<Icon name="shield-check-line" size="80rpx" color="white"></Icon>
				</view>
			</view>
		</view>

		<!-- 监护状态 -->
		<view class="section">
			<view class="section-header">
				<text class="section-title">监护状态</text>
				<text class="section-subtitle">当前设备运行状态</text>
			</view>
			<view class="status-overview">
				<view class="status-card main">
					<view class="status-icon online">
						<Icon name="wifi-line" size="40rpx" color="white"></Icon>
					</view>
					<view class="status-content">
						<text class="status-label">监护系统</text>
						<text class="status-value">正常运行</text>
						<text class="status-time">已连续运行 72 小时</text>
					</view>
				</view>
				<view class="device-grid">
					<view class="device-item" v-for="(device, index) in deviceList" :key="index">
						<view class="device-icon" :class="device.status">
							<Icon :name="device.icon" size="32rpx" color="white"></Icon>
						</view>
						<text class="device-name">{{device.name}}</text>
						<text class="device-status">{{device.statusText}}</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 监护功能 -->
		<view class="section">
			<view class="section-header">
				<text class="section-title">监护功能</text>
				<text class="section-subtitle">全方位安全监护服务</text>
			</view>
			<view class="function-list">
				<view class="function-item" v-for="(item, index) in functionList" :key="index" @click="toggleFunction(index)">
					<view class="function-icon" :style="{backgroundColor: item.color}">
						<Icon :name="item.icon" size="40rpx" color="white"></Icon>
					</view>
					<view class="function-content">
						<text class="function-title">{{item.title}}</text>
						<text class="function-desc">{{item.description}}</text>
					</view>
					<view class="function-switch">
						<switch :checked="item.enabled" @change="toggleFunction(index)" color="#ff8a00"></switch>
					</view>
				</view>
			</view>
		</view>

		<!-- 报警记录 -->
		<view class="section">
			<view class="section-header">
				<text class="section-title">报警记录</text>
				<text class="section-subtitle">最近的安全提醒</text>
			</view>
			<view class="alert-list">
				<view class="alert-item" v-for="(alert, index) in alertList" :key="index" @click="viewAlert(alert)">
					<view class="alert-icon" :class="alert.level">
						<Icon :name="alert.icon" size="32rpx" color="white"></Icon>
					</view>
					<view class="alert-content">
						<text class="alert-title">{{alert.title}}</text>
						<text class="alert-desc">{{alert.description}}</text>
						<text class="alert-time">{{alert.time}}</text>
					</view>
					<view class="alert-status" :class="alert.status">
						<text class="status-text">{{alert.statusText}}</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 紧急联系人 -->
		<view class="section">
			<view class="section-header">
				<text class="section-title">紧急联系人</text>
				<text class="section-subtitle">紧急情况联系人设置</text>
			</view>
			<view class="contact-list">
				<view class="contact-item" v-for="(contact, index) in contactList" :key="index">
					<view class="contact-avatar">
						<Icon name="user-line" size="40rpx" color="#ff8a00"></Icon>
					</view>
					<view class="contact-content">
						<text class="contact-name">{{contact.name}}</text>
						<text class="contact-relation">{{contact.relation}}</text>
						<text class="contact-phone">{{contact.phone}}</text>
					</view>
					<view class="contact-actions">
						<button class="call-btn" @click="callContact(contact)">
							<Icon name="phone-line" size="24rpx" color="white"></Icon>
						</button>
					</view>
				</view>
			</view>
		</view>

		<!-- 安全提醒 -->
		<view class="section">
			<view class="section-header">
				<text class="section-title">安全提醒</text>
				<text class="section-subtitle">日常安全注意事项</text>
			</view>
			<view class="tips-list">
				<view class="tip-item" v-for="(tip, index) in safetyTips" :key="index">
					<view class="tip-icon">
						<Icon name="information-line" size="32rpx" color="#2196f3"></Icon>
					</view>
					<text class="tip-text">{{tip}}</text>
				</view>
			</view>
		</view>

		<!-- 底部操作 -->
		<view class="bottom-actions">
			<button class="action-btn secondary" @click="manageDevices">
				<Icon name="settings-line" size="32rpx" color="#ff8a00"></Icon>
				<text>设备管理</text>
			</button>
			<button class="action-btn primary" @click="emergencyCall">
				<Icon name="alarm-warning-line" size="32rpx" color="white"></Icon>
				<text>紧急呼叫</text>
			</button>
		</view>
	</view>
</template>

<script>
import Icon from '@/components/Icon/Icon.vue'

export default {
	components: {
		Icon
	},
	data() {
		return {
			deviceList: [
				{
					name: '摄像头',
					icon: 'camera-line',
					status: 'online',
					statusText: '在线'
				},
				{
					name: '门磁感应',
					icon: 'door-line',
					status: 'online',
					statusText: '在线'
				},
				{
					name: '烟雾报警',
					icon: 'fire-line',
					status: 'online',
					statusText: '在线'
				},
				{
					name: '紧急按钮',
					icon: 'alarm-warning-line',
					status: 'online',
					statusText: '在线'
				}
			],
			functionList: [
				{
					title: '跌倒检测',
					description: '智能检测跌倒事件，及时报警',
					icon: 'user-unfollow-line',
					color: '#f44336',
					enabled: true
				},
				{
					title: '活动监测',
					description: '监测日常活动，异常时提醒',
					icon: 'walk-line',
					color: '#4caf50',
					enabled: true
				},
				{
					title: '睡眠监护',
					description: '监测睡眠质量，异常时报警',
					icon: 'hotel-bed-line',
					color: '#9c27b0',
					enabled: false
				},
				{
					title: '用药提醒',
					description: '按时提醒用药，避免遗忘',
					icon: 'capsule-line',
					color: '#ff9800',
					enabled: true
				}
			],
			alertList: [
				{
					title: '门窗异常开启',
					description: '检测到卧室窗户在深夜开启',
					time: '2024-01-15 23:30',
					level: 'warning',
					icon: 'door-open-line',
					status: 'handled',
					statusText: '已处理'
				},
				{
					title: '长时间无活动',
					description: '超过2小时未检测到活动',
					time: '2024-01-15 14:20',
					level: 'info',
					icon: 'time-line',
					status: 'handled',
					statusText: '已处理'
				},
				{
					title: '用药提醒',
					description: '该服用下午的降压药了',
					time: '2024-01-15 14:00',
					level: 'normal',
					icon: 'capsule-line',
					status: 'completed',
					statusText: '已完成'
				}
			],
			contactList: [
				{
					name: '张小明',
					relation: '儿子',
					phone: '138****1234'
				},
				{
					name: '李小红',
					relation: '女儿',
					phone: '139****5678'
				},
				{
					name: '社区医生',
					relation: '医生',
					phone: '136****9012'
				}
			],
			safetyTips: [
				'外出时请确保门窗关闭，开启安防系统',
				'夜间起床时请开灯，避免摔倒',
				'定期检查燃气阀门，确保安全',
				'保持紧急联系人电话畅通',
				'按时服药，如有不适及时联系医生'
			]
		}
	},
	methods: {
		toggleFunction(index) {
			this.functionList[index].enabled = !this.functionList[index].enabled;
		},
		viewAlert(alert) {
			uni.navigateTo({
				url: `/pages/monitoring/alert-detail?id=${alert.id}`
			});
		},
		callContact(contact) {
			uni.makePhoneCall({
				phoneNumber: contact.phone.replace(/\*/g, '')
			});
		},
		manageDevices() {
			uni.navigateTo({
				url: '/pages/monitoring/devices'
			});
		},
		emergencyCall() {
			uni.makePhoneCall({
				phoneNumber: '120'
			});
		}
	}
}
</script>

<style scoped>
.container {
	background: linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%);
	min-height: 100vh;
	padding-bottom: 200rpx;
}

.header-banner {
	padding: 40rpx;
	margin-bottom: 40rpx;
}

.banner-content {
	background: rgba(255, 255, 255, 0.15);
	border-radius: 30rpx;
	padding: 40rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.banner-text {
	flex: 1;
}

.banner-title {
	font-size: 48rpx;
	font-weight: bold;
	color: white;
	display: block;
	margin-bottom: 15rpx;
}

.banner-subtitle {
	font-size: 28rpx;
	color: rgba(255, 255, 255, 0.9);
	display: block;
}

.banner-icon {
	margin-left: 30rpx;
}

.section {
	margin-bottom: 40rpx;
}

.section-header {
	padding: 0 40rpx 30rpx;
}

.section-title {
	font-size: 36rpx;
	font-weight: bold;
	color: white;
	display: block;
	margin-bottom: 10rpx;
}

.section-subtitle {
	font-size: 26rpx;
	color: rgba(255, 255, 255, 0.8);
	display: block;
}

.status-overview {
	padding: 0 40rpx;
}

.status-card {
	background: white;
	border-radius: 30rpx;
	padding: 30rpx;
	margin-bottom: 30rpx;
	display: flex;
	align-items: center;
	gap: 20rpx;
	box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.status-icon {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
}

.status-icon.online {
	background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);
}

.status-content {
	flex: 1;
}

.status-label {
	font-size: 24rpx;
	color: #666;
	display: block;
	margin-bottom: 8rpx;
}

.status-value {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 5rpx;
}

.status-time {
	font-size: 22rpx;
	color: #999;
	display: block;
}

.device-grid {
	display: grid;
	grid-template-columns: repeat(4, 1fr);
	gap: 20rpx;
}

.device-item {
	background: white;
	border-radius: 20rpx;
	padding: 20rpx;
	text-align: center;
	box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.device-icon {
	width: 60rpx;
	height: 60rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	margin: 0 auto 15rpx;
}

.device-icon.online {
	background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);
}

.device-icon.offline {
	background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
}

.device-name {
	font-size: 24rpx;
	color: #333;
	display: block;
	margin-bottom: 5rpx;
}

.device-status {
	font-size: 20rpx;
	color: #666;
	display: block;
}

.function-list, .alert-list, .contact-list, .tips-list {
	padding: 0 40rpx;
}

.function-item, .alert-item, .contact-item {
	background: white;
	border-radius: 30rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	display: flex;
	align-items: center;
	gap: 20rpx;
	box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.function-icon, .alert-icon {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
}

.alert-icon.warning {
	background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
}

.alert-icon.info {
	background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);
}

.alert-icon.normal {
	background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);
}

.function-content, .alert-content, .contact-content {
	flex: 1;
}

.function-title, .alert-title, .contact-name {
	font-size: 30rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 8rpx;
}

.function-desc, .alert-desc {
	font-size: 26rpx;
	color: #666;
	display: block;
}

.alert-time {
	font-size: 22rpx;
	color: #999;
	display: block;
	margin-top: 8rpx;
}

.alert-status {
	padding: 8rpx 20rpx;
	border-radius: 20rpx;
	font-size: 22rpx;
}

.alert-status.handled {
	background: rgba(76, 175, 80, 0.1);
	color: #4caf50;
}

.alert-status.completed {
	background: rgba(33, 150, 243, 0.1);
	color: #2196f3;
}

.contact-avatar {
	width: 80rpx;
	height: 80rpx;
	background: rgba(255, 138, 0, 0.1);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
}

.contact-relation, .contact-phone {
	font-size: 24rpx;
	color: #666;
	display: block;
}

.call-btn {
	width: 60rpx;
	height: 60rpx;
	background: linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%);
	border: none;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
}

.tip-item {
	background: white;
	border-radius: 30rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	display: flex;
	align-items: flex-start;
	gap: 20rpx;
	box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.tip-icon {
	width: 60rpx;
	height: 60rpx;
	background: rgba(33, 150, 243, 0.1);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-top: 5rpx;
}

.tip-text {
	flex: 1;
	font-size: 28rpx;
	color: #333;
	line-height: 1.6;
}

.bottom-actions {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background: white;
	padding: 30rpx 40rpx;
	display: flex;
	gap: 20rpx;
	box-shadow: 0 -4rpx 20rpx rgba(0,0,0,0.1);
}

.action-btn {
	flex: 1;
	height: 100rpx;
	border-radius: 20rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 15rpx;
	font-size: 32rpx;
	font-weight: 500;
}

.action-btn.primary {
	background: linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%);
	color: white;
	border: none;
}

.action-btn.secondary {
	background: white;
	color: #ff8a00;
	border: 2rpx solid #ff8a00;
}
</style>
