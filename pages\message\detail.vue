<template>
	<view class="container">
		<!-- 消息头部 -->
		<view class="message-header">
			<view class="message-icon" :class="messageDetail.type">
				<Icon :name="getMessageIcon(messageDetail.type)" size="40rpx" color="white"></Icon>
			</view>
			<view class="message-info">
				<text class="message-title">{{messageDetail.title}}</text>
				<text class="message-time">{{messageDetail.time}}</text>
				<view class="message-meta">
					<text class="meta-tag" :class="messageDetail.type">{{getTypeText(messageDetail.type)}}</text>
					<text class="meta-status" v-if="messageDetail.priority">{{getPriorityText(messageDetail.priority)}}</text>
				</view>
			</view>
		</view>

		<!-- 消息内容 -->
		<view class="message-content">
			<text class="content-text">{{messageDetail.content}}</text>
			
			<!-- 消息图片 -->
			<view class="content-images" v-if="messageDetail.images && messageDetail.images.length > 0">
				<image 
					class="content-image" 
					v-for="(image, index) in messageDetail.images" 
					:key="index"
					:src="image"
					mode="aspectFill"
					@click="previewImage(image)"
				></image>
			</view>
			
			<!-- 消息附件 -->
			<view class="content-attachments" v-if="messageDetail.attachments && messageDetail.attachments.length > 0">
				<view class="attachment-item" v-for="(attachment, index) in messageDetail.attachments" :key="index" @click="downloadAttachment(attachment)">
					<view class="attachment-icon">
						<Icon :name="getFileIcon(attachment.type)" size="32rpx" color="#ff8a00"></Icon>
					</view>
					<view class="attachment-info">
						<text class="attachment-name">{{attachment.name}}</text>
						<text class="attachment-size">{{attachment.size}}</text>
					</view>
					<Icon name="download-2-line" size="24rpx" color="#999"></Icon>
				</view>
			</view>
		</view>

		<!-- 相关操作 -->
		<view class="action-section" v-if="messageDetail.actions && messageDetail.actions.length > 0">
			<view class="section-title">相关操作</view>
			<view class="action-list">
				<button 
					class="action-btn" 
					v-for="(action, index) in messageDetail.actions" 
					:key="index"
					:class="action.type"
					@click="handleAction(action)"
				>
					<Icon :name="action.icon" size="24rpx" :color="action.type === 'primary' ? 'white' : '#ff8a00'"></Icon>
					<text>{{action.text}}</text>
				</button>
			</view>
		</view>

		<!-- 消息详情 -->
		<view class="detail-section">
			<view class="section-title">消息详情</view>
			<view class="detail-list">
				<view class="detail-item">
					<text class="detail-label">发送时间</text>
					<text class="detail-value">{{messageDetail.time}}</text>
				</view>
				<view class="detail-item">
					<text class="detail-label">消息类型</text>
					<text class="detail-value">{{getTypeText(messageDetail.type)}}</text>
				</view>
				<view class="detail-item" v-if="messageDetail.sender">
					<text class="detail-label">发送方</text>
					<text class="detail-value">{{messageDetail.sender}}</text>
				</view>
				<view class="detail-item" v-if="messageDetail.expireTime">
					<text class="detail-label">有效期</text>
					<text class="detail-value">{{messageDetail.expireTime}}</text>
				</view>
			</view>
		</view>

		<!-- 相关消息 -->
		<view class="related-section" v-if="relatedMessages.length > 0">
			<view class="section-title">相关消息</view>
			<view class="related-list">
				<view class="related-item" v-for="(message, index) in relatedMessages" :key="index" @click="viewRelatedMessage(message)">
					<view class="related-icon" :class="message.type">
						<Icon :name="getMessageIcon(message.type)" size="24rpx" color="white"></Icon>
					</view>
					<view class="related-content">
						<text class="related-title">{{message.title}}</text>
						<text class="related-time">{{message.time}}</text>
					</view>
					<Icon name="arrow-right-s-line" size="20rpx" color="#999"></Icon>
				</view>
			</view>
		</view>

		<!-- 底部操作 -->
		<view class="bottom-actions">
			<button class="action-btn secondary" @click="deleteMessage">
				<Icon name="delete-line" size="32rpx" color="#f44336"></Icon>
				<text>删除</text>
			</button>
			<button class="action-btn secondary" @click="shareMessage">
				<Icon name="share-forward-line" size="32rpx" color="#ff8a00"></Icon>
				<text>分享</text>
			</button>
			<button class="action-btn primary" @click="markImportant">
				<Icon name="star-line" size="32rpx" color="white"></Icon>
				<text>{{messageDetail.isImportant ? '取消重要' : '标为重要'}}</text>
			</button>
		</view>
	</view>
</template>

<script>
import Icon from '@/components/Icon/Icon.vue'

export default {
	components: {
		Icon
	},
	data() {
		return {
			messageDetail: {
				id: 1,
				type: 'system',
				title: '系统升级通知',
				content: '尊敬的用户，智慧养老系统将于今晚22:00-24:00进行升级维护，期间可能影响部分功能使用。升级内容包括：\n\n1. 优化健康管理功能\n2. 新增智能监护模块\n3. 修复已知问题\n4. 提升系统性能\n\n升级期间如有紧急情况，请拨打客服热线：************。感谢您的理解与支持！',
				time: '2024-01-18 14:30',
				sender: '系统管理员',
				priority: 'high',
				isImportant: false,
				expireTime: '2024-01-25 23:59',
				images: [],
				attachments: [
					{
						name: '升级说明文档.pdf',
						size: '2.3MB',
						type: 'pdf',
						url: ''
					}
				],
				actions: [
					{
						text: '查看详情',
						icon: 'eye-2-line',
						type: 'primary',
						action: 'viewDetail'
					},
					{
						text: '联系客服',
						icon: 'phone-line',
						type: 'secondary',
						action: 'contactService'
					}
				]
			},
			relatedMessages: [
				{
					id: 2,
					type: 'system',
					title: '功能更新通知',
					time: '2024-01-15 20:00'
				},
				{
					id: 3,
					type: 'system',
					title: '账户安全提醒',
					time: '2024-01-16 12:10'
				}
			]
		}
	},
	onLoad(options) {
		if (options.id) {
			this.loadMessageDetail(options.id);
		}
		// 标记消息为已读
		this.markAsRead();
	},
	methods: {
		loadMessageDetail(id) {
			// 根据ID加载消息详情
			console.log('加载消息详情:', id);
		},
		markAsRead() {
			// 标记消息为已读
			console.log('标记消息为已读');
		},
		getMessageIcon(type) {
			const iconMap = {
				'system': 'settings-line',
				'service': 'heart-line',
				'health': 'health-book-line',
				'activity': 'calendar-line'
			};
			return iconMap[type] || 'mail-line';
		},
		getTypeText(type) {
			const typeMap = {
				'system': '系统消息',
				'service': '服务消息',
				'health': '健康消息',
				'activity': '活动消息'
			};
			return typeMap[type] || '未知类型';
		},
		getPriorityText(priority) {
			const priorityMap = {
				'high': '高优先级',
				'medium': '中优先级',
				'low': '低优先级'
			};
			return priorityMap[priority] || '';
		},
		getFileIcon(type) {
			const iconMap = {
				'pdf': 'file-pdf-line',
				'image': 'image-line',
				'doc': 'file-word-line',
				'excel': 'file-excel-line'
			};
			return iconMap[type] || 'file-line';
		},
		previewImage(image) {
			uni.previewImage({
				urls: this.messageDetail.images,
				current: image
			});
		},
		downloadAttachment(attachment) {
			uni.showLoading({
				title: '下载中...'
			});
			
			// 模拟下载
			setTimeout(() => {
				uni.hideLoading();
				uni.showToast({
					title: '下载完成',
					icon: 'success'
				});
			}, 2000);
		},
		handleAction(action) {
			switch (action.action) {
				case 'viewDetail':
					uni.navigateTo({
						url: '/pages/system/upgrade-detail'
					});
					break;
				case 'contactService':
					uni.makePhoneCall({
						phoneNumber: '************'
					});
					break;
				default:
					console.log('执行操作:', action);
			}
		},
		viewRelatedMessage(message) {
			uni.navigateTo({
				url: `/pages/message/detail?id=${message.id}`
			});
		},
		deleteMessage() {
			uni.showModal({
				title: '确认删除',
				content: '确定要删除这条消息吗？',
				success: (res) => {
					if (res.confirm) {
						uni.showToast({
							title: '删除成功',
							icon: 'success'
						});
						setTimeout(() => {
							uni.navigateBack();
						}, 1500);
					}
				}
			});
		},
		shareMessage() {
			uni.share({
				provider: 'weixin',
				type: 0,
				title: this.messageDetail.title,
				summary: this.messageDetail.content,
				success: () => {
					uni.showToast({
						title: '分享成功',
						icon: 'success'
					});
				}
			});
		},
		markImportant() {
			this.messageDetail.isImportant = !this.messageDetail.isImportant;
			uni.showToast({
				title: this.messageDetail.isImportant ? '已标为重要' : '已取消重要',
				icon: 'success'
			});
		}
	}
}
</script>

<style scoped>
.container {
	background: #f8f9fa;
	min-height: 100vh;
	padding-bottom: 200rpx;
}

.message-header {
	background: white;
	padding: 40rpx;
	display: flex;
	align-items: flex-start;
	gap: 30rpx;
	box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.message-icon {
	width: 100rpx;
	height: 100rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-top: 10rpx;
}

.message-icon.system {
	background: linear-gradient(135deg, #9c27b0 0%, #7b1fa2 100%);
}

.message-icon.service {
	background: linear-gradient(135deg, #e91e63 0%, #c2185b 100%);
}

.message-icon.health {
	background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);
}

.message-icon.activity {
	background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
}

.message-info {
	flex: 1;
}

.message-title {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 15rpx;
}

.message-time {
	font-size: 26rpx;
	color: #666;
	display: block;
	margin-bottom: 20rpx;
}

.message-meta {
	display: flex;
	gap: 15rpx;
}

.meta-tag {
	padding: 8rpx 20rpx;
	border-radius: 20rpx;
	font-size: 22rpx;
}

.meta-tag.system {
	background: rgba(156, 39, 176, 0.1);
	color: #9c27b0;
}

.meta-tag.service {
	background: rgba(233, 30, 99, 0.1);
	color: #e91e63;
}

.meta-tag.health {
	background: rgba(76, 175, 80, 0.1);
	color: #4caf50;
}

.meta-tag.activity {
	background: rgba(255, 152, 0, 0.1);
	color: #ff9800;
}

.meta-status {
	padding: 8rpx 20rpx;
	border-radius: 20rpx;
	font-size: 22rpx;
	background: rgba(244, 67, 54, 0.1);
	color: #f44336;
}

.message-content, .action-section, .detail-section, .related-section {
	background: white;
	margin: 40rpx;
	border-radius: 30rpx;
	padding: 40rpx;
	box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.content-text {
	font-size: 30rpx;
	color: #333;
	line-height: 1.8;
	display: block;
	margin-bottom: 30rpx;
	white-space: pre-line;
}

.content-images {
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	gap: 20rpx;
	margin-bottom: 30rpx;
}

.content-image {
	width: 100%;
	height: 200rpx;
	border-radius: 20rpx;
}

.content-attachments {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

.attachment-item {
	display: flex;
	align-items: center;
	gap: 20rpx;
	padding: 20rpx;
	background: #f8f9fa;
	border-radius: 20rpx;
}

.attachment-icon {
	width: 60rpx;
	height: 60rpx;
	background: rgba(255, 138, 0, 0.1);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
}

.attachment-info {
	flex: 1;
}

.attachment-name {
	font-size: 28rpx;
	color: #333;
	display: block;
	margin-bottom: 5rpx;
}

.attachment-size {
	font-size: 22rpx;
	color: #999;
	display: block;
}

.section-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 30rpx;
	display: block;
}

.action-list {
	display: flex;
	gap: 20rpx;
}

.action-btn {
	flex: 1;
	height: 80rpx;
	border-radius: 20rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 10rpx;
	font-size: 28rpx;
	border: none;
}

.action-btn.primary {
	background: linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%);
	color: white;
}

.action-btn.secondary {
	background: white;
	color: #ff8a00;
	border: 2rpx solid #ff8a00;
}

.detail-list, .related-list {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

.detail-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20rpx 0;
	border-bottom: 1rpx solid #f0f0f0;
}

.detail-item:last-child {
	border-bottom: none;
}

.detail-label {
	font-size: 28rpx;
	color: #666;
}

.detail-value {
	font-size: 28rpx;
	color: #333;
	text-align: right;
}

.related-item {
	display: flex;
	align-items: center;
	gap: 20rpx;
	padding: 20rpx;
	background: #f8f9fa;
	border-radius: 20rpx;
}

.related-icon {
	width: 60rpx;
	height: 60rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
}

.related-icon.system {
	background: linear-gradient(135deg, #9c27b0 0%, #7b1fa2 100%);
}

.related-content {
	flex: 1;
}

.related-title {
	font-size: 28rpx;
	color: #333;
	display: block;
	margin-bottom: 5rpx;
}

.related-time {
	font-size: 22rpx;
	color: #999;
	display: block;
}

.bottom-actions {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background: white;
	padding: 30rpx 40rpx;
	display: flex;
	gap: 20rpx;
	box-shadow: 0 -4rpx 20rpx rgba(0,0,0,0.1);
}

.bottom-actions .action-btn {
	flex: 1;
	height: 100rpx;
	font-size: 32rpx;
	font-weight: 500;
}

.bottom-actions .action-btn.secondary {
	color: #f44336;
	border-color: #f44336;
}

.bottom-actions .action-btn.secondary:nth-child(2) {
	color: #ff8a00;
	border-color: #ff8a00;
}
</style>
