<template>
	<view class="container">
		<!-- 页面头部 -->
		<PageHeader title="在线咨询">
			<template #actions>
				<InteractiveButton
					type="secondary"
					size="small"
					text="咨询记录"
					icon="message-line"
					@click="viewConsultations"
				></InteractiveButton>
			</template>
		</PageHeader>

		<!-- 服务信息 -->
		<view class="service-info" v-if="serviceInfo">
			<view class="service-header">
				<view class="service-icon">
					<Icon :name="serviceInfo.icon" size="48rpx" color="#fff"></Icon>
				</view>
				<view class="service-details">
					<text class="service-name">{{ serviceInfo.name }}</text>
					<text class="service-provider">{{ serviceInfo.provider }}</text>
					<text class="service-category">{{ serviceInfo.category }}</text>
				</view>
			</view>
		</view>

		<!-- 咨询类型选择 -->
		<view class="consultation-type">
			<text class="section-title">咨询类型</text>
			<view class="type-options">
				<view 
					v-for="(type, index) in consultationTypes" 
					:key="index"
					class="type-option"
					:class="{ active: selectedType === type.value }"
					@click="selectType(type.value)"
				>
					<Icon :name="type.icon" size="32rpx" :color="selectedType === type.value ? '#fff' : '#ff8a00'"></Icon>
					<text class="type-text">{{ type.label }}</text>
				</view>
			</view>
		</view>

		<!-- 咨询表单 -->
		<view class="consultation-form">
			<text class="form-title">咨询内容</text>
			
			<!-- 咨询主题 -->
			<view class="form-item">
				<text class="form-label">咨询主题</text>
				<input 
					class="form-input" 
					v-model="formData.subject" 
					placeholder="请简要描述您的咨询主题"
				/>
			</view>

			<!-- 详细描述 -->
			<view class="form-item">
				<text class="form-label">详细描述</text>
				<textarea 
					class="form-textarea" 
					v-model="formData.description" 
					placeholder="请详细描述您的问题或需求，我们会尽快为您解答"
				></textarea>
			</view>

			<!-- 联系方式 -->
			<view class="form-item">
				<text class="form-label">联系电话</text>
				<input 
					class="form-input" 
					v-model="formData.contactPhone" 
					placeholder="请输入您的联系电话"
					type="number"
				/>
			</view>

			<!-- 期望回复时间 -->
			<view class="form-item">
				<text class="form-label">期望回复时间</text>
				<view class="time-options">
					<view 
						v-for="(time, index) in replyTimeOptions" 
						:key="index"
						class="time-option"
						:class="{ active: selectedReplyTime === time.value }"
						@click="selectReplyTime(time.value)"
					>
						<text class="time-text">{{ time.label }}</text>
					</view>
				</view>
			</view>

			<!-- 图片上传 -->
			<view class="form-item">
				<text class="form-label">相关图片（选填）</text>
				<view class="image-upload">
					<view 
						v-for="(image, index) in uploadedImages" 
						:key="index"
						class="image-item"
					>
						<image :src="image" class="uploaded-image" mode="aspectFill"></image>
						<view class="image-delete" @click="deleteImage(index)">
							<Icon name="close-line" size="24rpx" color="#fff"></Icon>
						</view>
					</view>
					<view class="upload-btn" @click="uploadImage" v-if="uploadedImages.length < 3">
						<Icon name="add-line" size="48rpx" color="#ccc"></Icon>
						<text class="upload-text">添加图片</text>
					</view>
				</view>
				<text class="upload-tip">最多可上传3张图片，每张不超过5MB</text>
			</view>
		</view>

		<!-- 专家信息 -->
		<view class="expert-info">
			<text class="section-title">咨询专家</text>
			<view class="expert-card">
				<view class="expert-avatar">
					<Icon name="user-line" size="40rpx" color="#ff8a00"></Icon>
				</view>
				<view class="expert-details">
					<text class="expert-name">李医生</text>
					<text class="expert-title">高级护理师</text>
					<text class="expert-experience">从业15年，擅长老年护理</text>
				</view>
				<view class="expert-status online">
					<text class="status-text">在线</text>
				</view>
			</view>
		</view>

		<!-- 提交按钮 -->
		<view class="submit-section">
			<InteractiveButton
				type="primary"
				size="large"
				text="提交咨询"
				icon="send-plane-line"
				:loading="submitting"
				@click="submitConsultation"
			></InteractiveButton>
		</view>
	</view>
</template>

<script>
import InteractiveButton from '@/components/InteractiveButton/InteractiveButton.vue'
import Icon from '@/components/Icon/Icon.vue'
import PageHeader from '@/components/PageHeader/PageHeader.vue'
import FeedbackUtils from '@/utils/feedback.js'

export default {
	components: {
		InteractiveButton,
		Icon,
		PageHeader
	},
	data() {
		return {
			serviceInfo: null,
			submitting: false,
			selectedType: 'general',
			selectedReplyTime: '24h',
			uploadedImages: [],
			formData: {
				subject: '',
				description: '',
				contactPhone: ''
			},
			
			// 咨询类型
			consultationTypes: [
				{ label: '一般咨询', value: 'general', icon: 'question-line' },
				{ label: '服务咨询', value: 'service', icon: 'service-line' },
				{ label: '价格咨询', value: 'price', icon: 'money-dollar-circle-line' },
				{ label: '技术支持', value: 'technical', icon: 'tools-line' }
			],
			
			// 回复时间选项
			replyTimeOptions: [
				{ label: '24小时内', value: '24h' },
				{ label: '48小时内', value: '48h' },
				{ label: '一周内', value: '1w' },
				{ label: '不急', value: 'no_rush' }
			]
		}
	},
	onLoad(options) {
		if (options.serviceId) {
			this.loadServiceInfo(options.serviceId);
		}
	},
	methods: {
		// 加载服务信息
		loadServiceInfo(serviceId) {
			// 模拟加载服务信息
			this.serviceInfo = {
				id: serviceId,
				name: '居家护理服务',
				provider: '康护医疗',
				category: '医疗护理',
				icon: 'heart-3-line'
			};
		},

		// 选择咨询类型
		selectType(type) {
			FeedbackUtils.lightFeedback();
			this.selectedType = type;
		},

		// 选择回复时间
		selectReplyTime(time) {
			FeedbackUtils.lightFeedback();
			this.selectedReplyTime = time;
		},

		// 上传图片
		uploadImage() {
			FeedbackUtils.lightFeedback();
			uni.chooseImage({
				count: 3 - this.uploadedImages.length,
				sizeType: ['compressed'],
				sourceType: ['album', 'camera'],
				success: (res) => {
					this.uploadedImages.push(...res.tempFilePaths);
					FeedbackUtils.showSuccess('图片上传成功');
				},
				fail: () => {
					FeedbackUtils.showError('图片上传失败');
				}
			});
		},

		// 删除图片
		deleteImage(index) {
			FeedbackUtils.lightFeedback();
			this.uploadedImages.splice(index, 1);
		},

		// 查看咨询记录
		viewConsultations() {
			uni.navigateTo({
				url: '/pages/consultation/list'
			});
		},

		// 提交咨询
		async submitConsultation() {
			// 表单验证
			if (!this.validateForm()) {
				return;
			}

			this.submitting = true;
			FeedbackUtils.showLoading('提交咨询中...');

			try {
				// 模拟提交咨询
				await new Promise(resolve => setTimeout(resolve, 2000));
				
				FeedbackUtils.hideLoading();
				FeedbackUtils.showSuccess('咨询提交成功');
				
				// 跳转到咨询列表
				setTimeout(() => {
					uni.redirectTo({
						url: '/pages/consultation/list'
					});
				}, 1500);
			} catch (error) {
				FeedbackUtils.hideLoading();
				FeedbackUtils.showError('咨询提交失败，请重试');
			} finally {
				this.submitting = false;
			}
		},

		// 表单验证
		validateForm() {
			if (!this.formData.subject) {
				FeedbackUtils.showError('请输入咨询主题');
				return false;
			}
			if (!this.formData.description) {
				FeedbackUtils.showError('请输入详细描述');
				return false;
			}
			if (!this.formData.contactPhone) {
				FeedbackUtils.showError('请输入联系电话');
				return false;
			}
			return true;
		}
	}
}
</script>

<style scoped>
.container {
	background-color: #f5f5f5;
	min-height: 100vh;
	padding-bottom: 200rpx;
}

.service-info,
.consultation-type,
.consultation-form,
.expert-info {
	background: white;
	margin: 20rpx;
	border-radius: 20rpx;
	padding: 30rpx;
	box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.service-header {
	display: flex;
	align-items: center;
	gap: 20rpx;
}

.service-icon {
	width: 80rpx;
	height: 80rpx;
	background: linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
}

.service-details {
	flex: 1;
}

.service-name {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 8rpx;
}

.service-provider {
	font-size: 26rpx;
	color: #666;
	display: block;
	margin-bottom: 5rpx;
}

.service-category {
	font-size: 24rpx;
	color: #ff8a00;
}

.section-title,
.form-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 25rpx;
}

.type-options {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	gap: 15rpx;
}

.type-option {
	display: flex;
	align-items: center;
	gap: 15rpx;
	padding: 20rpx;
	border: 2rpx solid #e0e0e0;
	border-radius: 15rpx;
	background: #fff;
}

.type-option.active {
	border-color: #ff8a00;
	background: linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%);
}

.type-text {
	font-size: 26rpx;
	color: #333;
}

.type-option.active .type-text {
	color: white;
}

.form-item {
	margin-bottom: 25rpx;
}

.form-label {
	font-size: 28rpx;
	color: #333;
	display: block;
	margin-bottom: 10rpx;
}

.form-input,
.form-textarea {
	width: 100%;
	background: #f8f9fa;
	border: 1rpx solid #e0e0e0;
	border-radius: 10rpx;
	padding: 20rpx;
	font-size: 28rpx;
	color: #333;
}

.form-textarea {
	min-height: 150rpx;
}

.time-options {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	gap: 15rpx;
}

.time-option {
	padding: 15rpx 20rpx;
	border: 2rpx solid #e0e0e0;
	border-radius: 25rpx;
	text-align: center;
	background: #fff;
}

.time-option.active {
	border-color: #ff8a00;
	background: rgba(255, 138, 0, 0.1);
}

.time-text {
	font-size: 26rpx;
	color: #333;
}

.image-upload {
	display: flex;
	gap: 15rpx;
	flex-wrap: wrap;
}

.image-item {
	position: relative;
	width: 150rpx;
	height: 150rpx;
}

.uploaded-image {
	width: 100%;
	height: 100%;
	border-radius: 10rpx;
}

.image-delete {
	position: absolute;
	top: -10rpx;
	right: -10rpx;
	width: 40rpx;
	height: 40rpx;
	background: #f44336;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
}

.upload-btn {
	width: 150rpx;
	height: 150rpx;
	border: 2rpx dashed #ccc;
	border-radius: 10rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	gap: 10rpx;
}

.upload-text {
	font-size: 24rpx;
	color: #ccc;
}

.upload-tip {
	font-size: 22rpx;
	color: #999;
	margin-top: 10rpx;
}

.expert-card {
	display: flex;
	align-items: center;
	gap: 20rpx;
}

.expert-avatar {
	width: 80rpx;
	height: 80rpx;
	background: rgba(255, 138, 0, 0.1);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
}

.expert-details {
	flex: 1;
}

.expert-name {
	font-size: 30rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 8rpx;
}

.expert-title {
	font-size: 26rpx;
	color: #666;
	display: block;
	margin-bottom: 5rpx;
}

.expert-experience {
	font-size: 24rpx;
	color: #999;
}

.expert-status {
	padding: 8rpx 15rpx;
	border-radius: 15rpx;
}

.expert-status.online {
	background: #4caf50;
}

.status-text {
	font-size: 22rpx;
	color: white;
}

.submit-section {
	padding: 0 20rpx;
}
</style>
