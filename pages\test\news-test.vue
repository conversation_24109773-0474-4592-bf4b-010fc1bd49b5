<template>
	<view class="test-container">
		<view class="header">
			<text class="title">资讯模块测试</text>
		</view>
		
		<view class="test-section">
			<text class="section-title">功能测试</text>
			
			<view class="test-item">
				<text class="test-label">资讯列表页:</text>
				<button class="test-btn" @click="testNewsList">测试资讯列表</button>
			</view>
			
			<view class="test-item">
				<text class="test-label">资讯详情页:</text>
				<button class="test-btn" @click="testNewsDetail">测试资讯详情</button>
			</view>
			
			<view class="test-item">
				<text class="test-label">离线数据初始化:</text>
				<button class="test-btn" @click="initOfflineData">初始化数据</button>
			</view>
			
			<view class="test-item">
				<text class="test-label">数据状态:</text>
				<text class="test-result">{{ dataStatus }}</text>
			</view>
		</view>
		
		<view class="test-section">
			<text class="section-title">数据检查</text>
			
			<view class="data-info">
				<text class="info-label">离线资讯数量:</text>
				<text class="info-value">{{ newsCount }}条</text>
			</view>
			
			<view class="data-info">
				<text class="info-label">数据初始化状态:</text>
				<text class="info-value">{{ initStatus }}</text>
			</view>
			
			<view class="data-info">
				<text class="info-label">最新资讯:</text>
				<view class="news-preview">
					<view v-if="latestNews.length === 0" class="no-news">暂无资讯</view>
					<view v-else v-for="(item, index) in latestNews.slice(0, 3)" :key="index" class="news-item">
						<text class="news-title">{{ item.title }}</text>
						<text class="news-category">{{ item.category }}</text>
					</view>
				</view>
			</view>
		</view>
		
		<view class="test-section">
			<text class="section-title">错误日志</text>
			<view class="error-log">
				<text v-if="errors.length === 0" class="no-error">暂无错误</text>
				<view v-else v-for="(error, index) in errors" :key="index" class="error-item">
					<text class="error-time">{{ error.time }}</text>
					<text class="error-message">{{ error.message }}</text>
				</view>
			</view>
		</view>
		
		<view class="actions">
			<button class="action-btn primary" @click="clearErrors">清除错误</button>
			<button class="action-btn" @click="refreshData">刷新数据</button>
			<button class="action-btn" @click="goBack">返回</button>
		</view>
	</view>
</template>

<script>
import OfflineDataManager from '@/utils/offlineData.js'
import FeedbackUtils from '@/utils/feedback.js'

export default {
	data() {
		return {
			dataStatus: '检测中...',
			newsCount: 0,
			initStatus: '未知',
			latestNews: [],
			errors: []
		}
	},
	
	onLoad() {
		this.checkDataStatus()
	},
	
	methods: {
		checkDataStatus() {
			try {
				// 检查初始化状态
				const isInitialized = uni.getStorageSync('offline_data_initialized')
				this.initStatus = isInitialized ? '已初始化' : '未初始化'
				
				// 检查资讯数据
				const newsData = uni.getStorageSync('offline_news') || []
				this.newsCount = newsData.length
				this.latestNews = newsData
				
				if (newsData.length > 0) {
					this.dataStatus = '✅ 数据正常'
				} else {
					this.dataStatus = '❌ 数据为空'
				}
				
				this.logInfo('数据状态检查完成')
			} catch (error) {
				this.dataStatus = '❌ 检查失败'
				this.logError('数据状态检查失败: ' + error.message)
			}
		},
		
		testNewsList() {
			try {
				uni.navigateTo({
					url: '/pages/news/list',
					success: () => {
						this.logInfo('成功跳转到资讯列表页')
					},
					fail: (error) => {
						this.logError('跳转资讯列表页失败: ' + error.errMsg)
					}
				})
			} catch (error) {
				this.logError('测试资讯列表失败: ' + error.message)
			}
		},
		
		testNewsDetail() {
			try {
				uni.navigateTo({
					url: '/pages/news/detail?id=1',
					success: () => {
						this.logInfo('成功跳转到资讯详情页')
					},
					fail: (error) => {
						this.logError('跳转资讯详情页失败: ' + error.errMsg)
					}
				})
			} catch (error) {
				this.logError('测试资讯详情失败: ' + error.message)
			}
		},
		
		initOfflineData() {
			try {
				OfflineDataManager.initOfflineData()
				this.checkDataStatus()
				FeedbackUtils.showSuccess('离线数据初始化成功')
				this.logInfo('离线数据初始化成功')
			} catch (error) {
				FeedbackUtils.showError('离线数据初始化失败')
				this.logError('离线数据初始化失败: ' + error.message)
			}
		},
		
		refreshData() {
			this.checkDataStatus()
			FeedbackUtils.showSuccess('数据已刷新')
		},
		
		logInfo(message) {
			this.errors.unshift({
				time: new Date().toLocaleTimeString(),
				message: `[INFO] ${message}`,
				type: 'info'
			})
		},
		
		logError(message) {
			this.errors.unshift({
				time: new Date().toLocaleTimeString(),
				message: `[ERROR] ${message}`,
				type: 'error'
			})
		},
		
		clearErrors() {
			this.errors = []
		},
		
		goBack() {
			uni.navigateBack()
		}
	}
}
</script>

<style scoped>
.test-container {
	padding: 20rpx;
	background: #f5f5f5;
	min-height: 100vh;
}

.header {
	text-align: center;
	margin-bottom: 40rpx;
}

.title {
	font-size: 40rpx;
	font-weight: bold;
	color: #333;
}

.test-section {
	background: white;
	border-radius: 16rpx;
	padding: 24rpx;
	margin-bottom: 24rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 20rpx;
	display: block;
}

.test-item {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 16rpx;
	padding-bottom: 16rpx;
	border-bottom: 1rpx solid #eee;
}

.test-item:last-child {
	border-bottom: none;
}

.test-label {
	font-size: 28rpx;
	color: #666;
}

.test-btn {
	background: #ff8a00;
	color: white;
	border: none;
	border-radius: 12rpx;
	padding: 16rpx 32rpx;
	font-size: 26rpx;
}

.test-result {
	font-size: 26rpx;
	color: #333;
}

.data-info {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 16rpx;
}

.info-label {
	font-size: 28rpx;
	color: #666;
}

.info-value {
	font-size: 28rpx;
	color: #333;
	font-weight: 500;
}

.news-preview {
	width: 100%;
	margin-top: 16rpx;
}

.no-news {
	text-align: center;
	color: #999;
	font-size: 26rpx;
	padding: 20rpx;
}

.news-item {
	background: #f9f9f9;
	border-radius: 8rpx;
	padding: 12rpx;
	margin-bottom: 8rpx;
}

.news-title {
	font-size: 26rpx;
	color: #333;
	display: block;
	margin-bottom: 4rpx;
}

.news-category {
	font-size: 22rpx;
	color: #ff8a00;
	background: rgba(255, 138, 0, 0.1);
	padding: 2rpx 8rpx;
	border-radius: 4rpx;
}

.error-log {
	max-height: 300rpx;
	overflow-y: auto;
}

.no-error {
	font-size: 26rpx;
	color: #999;
	text-align: center;
	display: block;
	padding: 40rpx;
}

.error-item {
	border-bottom: 1rpx solid #eee;
	padding: 16rpx 0;
}

.error-time {
	font-size: 22rpx;
	color: #999;
	display: block;
	margin-bottom: 8rpx;
}

.error-message {
	font-size: 24rpx;
	color: #333;
	display: block;
}

.actions {
	display: flex;
	gap: 20rpx;
	justify-content: center;
}

.action-btn {
	background: #6b7280;
	color: white;
	border: none;
	border-radius: 12rpx;
	padding: 20rpx 40rpx;
	font-size: 28rpx;
}

.action-btn.primary {
	background: #ff8a00;
}
</style>
