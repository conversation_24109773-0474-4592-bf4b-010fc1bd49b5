<template>
	<view class="container">
		<PageHeader title="本周日程" showBack></PageHeader>
		
		<view class="content">
			<!-- 周导航 -->
			<view class="week-nav">
				<button class="nav-btn" @click="previousWeek">
					<Icon name="arrow-left-s-line" size="32rpx" color="#ff8a00"></Icon>
				</button>
				<text class="week-title">{{ weekTitle }}</text>
				<button class="nav-btn" @click="nextWeek">
					<Icon name="arrow-right-s-line" size="32rpx" color="#ff8a00"></Icon>
				</button>
			</view>

			<!-- 周视图 -->
			<view class="week-view">
				<view class="week-header">
					<view class="time-column"></view>
					<view class="day-header" v-for="day in weekDays" :key="day.date">
						<text class="day-name">{{ day.dayName }}</text>
						<text class="day-date" :class="{ today: day.isToday }">{{ day.date }}</text>
					</view>
				</view>
				
				<scroll-view class="week-content" scroll-y>
					<view class="time-slots">
						<view class="time-slot" v-for="hour in timeSlots" :key="hour">
							<view class="time-label">{{ hour }}:00</view>
							<view class="day-slots">
								<view 
									class="day-slot" 
									v-for="day in weekDays" 
									:key="`${day.date}-${hour}`"
									@click="addScheduleAtTime(day.date, hour)"
								>
									<view 
										class="schedule-block" 
										v-for="schedule in getSchedulesForDayHour(day.date, hour)" 
										:key="schedule.id"
										:style="{ backgroundColor: schedule.color }"
										@click.stop="viewSchedule(schedule)"
									>
										<text class="schedule-title">{{ schedule.title }}</text>
										<text class="schedule-time">{{ schedule.time }}</text>
									</view>
								</view>
							</view>
						</view>
					</view>
				</scroll-view>
			</view>

			<!-- 快速操作 -->
			<view class="quick-actions">
				<InteractiveButton 
					type="primary" 
					text="新建日程" 
					leftIcon="add-line"
					@click="createSchedule"
				/>
			</view>
		</view>
	</view>
</template>

<script>
import Icon from '@/components/Icon/Icon.vue'
import PageHeader from '@/components/PageHeader/PageHeader.vue'
import InteractiveButton from '@/components/InteractiveButton/InteractiveButton.vue'

export default {
	name: 'ScheduleWeek',
	components: {
		Icon,
		PageHeader,
		InteractiveButton
	},
	data() {
		return {
			currentWeekStart: new Date(),
			timeSlots: Array.from({ length: 24 }, (_, i) => i),
			scheduleData: {
				'2024-01-18': [
					{
						id: 1,
						time: '09:00',
						title: '晨练',
						color: '#4caf50',
						hour: 9
					},
					{
						id: 2,
						time: '14:30',
						title: '医生问诊',
						color: '#f44336',
						hour: 14
					}
				],
				'2024-01-19': [
					{
						id: 3,
						time: '10:00',
						title: '书法课',
						color: '#9c27b0',
						hour: 10
					}
				]
			}
		}
	},
	computed: {
		weekTitle() {
			const start = new Date(this.currentWeekStart)
			const end = new Date(start)
			end.setDate(start.getDate() + 6)
			
			const startStr = `${start.getMonth() + 1}月${start.getDate()}日`
			const endStr = `${end.getMonth() + 1}月${end.getDate()}日`
			
			return `${startStr} - ${endStr}`
		},
		weekDays() {
			const days = []
			const today = new Date()
			const todayStr = this.formatDate(today)
			
			for (let i = 0; i < 7; i++) {
				const date = new Date(this.currentWeekStart)
				date.setDate(this.currentWeekStart.getDate() + i)
				
				const dateStr = this.formatDate(date)
				const dayNames = ['日', '一', '二', '三', '四', '五', '六']
				
				days.push({
					date: dateStr,
					dayName: dayNames[date.getDay()],
					isToday: dateStr === todayStr
				})
			}
			
			return days
		}
	},
	onLoad() {
		this.setCurrentWeek()
	},
	methods: {
		setCurrentWeek() {
			const today = new Date()
			const dayOfWeek = today.getDay()
			const startOfWeek = new Date(today)
			startOfWeek.setDate(today.getDate() - dayOfWeek)
			this.currentWeekStart = startOfWeek
		},
		formatDate(date) {
			const year = date.getFullYear()
			const month = String(date.getMonth() + 1).padStart(2, '0')
			const day = String(date.getDate()).padStart(2, '0')
			return `${year}-${month}-${day}`
		},
		previousWeek() {
			const newStart = new Date(this.currentWeekStart)
			newStart.setDate(this.currentWeekStart.getDate() - 7)
			this.currentWeekStart = newStart
		},
		nextWeek() {
			const newStart = new Date(this.currentWeekStart)
			newStart.setDate(this.currentWeekStart.getDate() + 7)
			this.currentWeekStart = newStart
		},
		getSchedulesForDayHour(date, hour) {
			const daySchedules = this.scheduleData[date] || []
			return daySchedules.filter(schedule => schedule.hour === hour)
		},
		addScheduleAtTime(date, hour) {
			const time = `${String(hour).padStart(2, '0')}:00`
			uni.navigateTo({
				url: `/pages/schedule/create?date=${date}&time=${time}`
			})
		},
		viewSchedule(schedule) {
			uni.navigateTo({
				url: `/pages/schedule/detail?id=${schedule.id}`
			})
		},
		createSchedule() {
			uni.navigateTo({
				url: '/pages/schedule/create'
			})
		}
	}
}
</script>

<style scoped>
.container {
	min-height: 100vh;
	background: #f5f5f5;
}

.content {
	padding: 140rpx 0 40rpx;
}

.week-nav {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20rpx 32rpx;
	background: white;
	border-bottom: 1rpx solid #f0f0f0;
}

.nav-btn {
	width: 60rpx;
	height: 60rpx;
	background: rgba(255, 138, 0, 0.1);
	border: none;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
}

.week-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
}

.week-view {
	background: white;
	height: calc(100vh - 300rpx);
}

.week-header {
	display: flex;
	border-bottom: 1rpx solid #f0f0f0;
	background: #fafafa;
}

.time-column {
	width: 120rpx;
	height: 80rpx;
}

.day-header {
	flex: 1;
	padding: 16rpx 8rpx;
	text-align: center;
	border-left: 1rpx solid #f0f0f0;
}

.day-name {
	font-size: 24rpx;
	color: #666;
	display: block;
}

.day-date {
	font-size: 28rpx;
	color: #333;
	font-weight: 600;
	display: block;
	margin-top: 4rpx;
}

.day-date.today {
	color: #ff8a00;
}

.week-content {
	height: calc(100% - 80rpx);
}

.time-slots {
	display: flex;
	flex-direction: column;
}

.time-slot {
	display: flex;
	min-height: 120rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.time-label {
	width: 120rpx;
	padding: 16rpx;
	font-size: 24rpx;
	color: #666;
	text-align: center;
	background: #fafafa;
	border-right: 1rpx solid #f0f0f0;
}

.day-slots {
	flex: 1;
	display: flex;
}

.day-slot {
	flex: 1;
	border-left: 1rpx solid #f0f0f0;
	position: relative;
	min-height: 120rpx;
}

.schedule-block {
	margin: 4rpx;
	padding: 8rpx;
	border-radius: 8rpx;
	position: relative;
}

.schedule-title {
	font-size: 22rpx;
	color: white;
	display: block;
	font-weight: 600;
}

.schedule-time {
	font-size: 20rpx;
	color: rgba(255, 255, 255, 0.8);
	display: block;
}

.quick-actions {
	padding: 32rpx;
}
</style>
