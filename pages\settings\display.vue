<template>
	<view class="container">
		<!-- 自定义导航栏 -->
		<view class="custom-navbar">
			<view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
			<view class="navbar-content">
				<view class="navbar-left" @click="goBack">
					<Icon name="arrow-left-line" size="36rpx" color="#333"></Icon>
					<text class="back-text">返回</text>
				</view>
				<view class="navbar-center">
					<text class="navbar-title">显示设置</text>
				</view>
				<view class="navbar-right"></view>
			</view>
		</view>

		<!-- 显示设置概览 -->
		<view class="display-overview">
			<view class="overview-header">
				<Icon name="computer-line" size="60rpx" color="#ff8a00"></Icon>
				<text class="overview-title">显示设置</text>
				<text class="overview-desc">调整界面显示效果，获得更好的使用体验</text>
			</view>
		</view>

		<!-- 字体设置 -->
		<view class="display-section">
			<view class="section-header">
				<Icon name="font-size-2" size="32rpx" color="#ff8a00"></Icon>
				<text class="section-title">字体设置</text>
			</view>
			
			<view class="setting-item">
				<view class="item-info">
					<text class="item-title">字体大小</text>
					<text class="item-desc">调整应用内文字大小</text>
				</view>
				<picker :value="fontSizeIndex" :range="fontSizeOptions" @change="onFontSizeChange">
					<view class="picker-input">
						<text>{{fontSizeOptions[fontSizeIndex]}}</text>
						<Icon name="arrow-down-s-line" size="20rpx" color="#999"></Icon>
					</view>
				</picker>
			</view>
			
			<view class="setting-item">
				<view class="item-info">
					<text class="item-title">字体粗细</text>
					<text class="item-desc">调整文字粗细程度</text>
				</view>
				<picker :value="fontWeightIndex" :range="fontWeightOptions" @change="onFontWeightChange">
					<view class="picker-input">
						<text>{{fontWeightOptions[fontWeightIndex]}}</text>
						<Icon name="arrow-down-s-line" size="20rpx" color="#999"></Icon>
					</view>
				</picker>
			</view>
			
			<view class="font-preview">
				<text class="preview-title">字体预览</text>
				<text class="preview-text" :style="previewStyle">这是一段示例文字，用于预览字体效果。</text>
			</view>
		</view>

		<!-- 主题设置 -->
		<view class="display-section">
			<view class="section-header">
				<Icon name="palette-line" size="32rpx" color="#ff8a00"></Icon>
				<text class="section-title">主题设置</text>
			</view>
			
			<view class="setting-item">
				<view class="item-info">
					<text class="item-title">深色模式</text>
					<text class="item-desc">在光线较暗时保护眼睛</text>
				</view>
				<switch :checked="darkMode" @change="onDarkModeChange" color="#ff8a00" />
			</view>
			
			<view class="setting-item">
				<view class="item-info">
					<text class="item-title">跟随系统</text>
					<text class="item-desc">自动跟随系统主题设置</text>
				</view>
				<switch :checked="followSystem" @change="onFollowSystemChange" color="#ff8a00" />
			</view>
			
			<view class="theme-colors">
				<text class="colors-title">主题色彩</text>
				<view class="color-grid">
					<view class="color-item" v-for="(color, index) in themeColors" :key="index" @click="selectThemeColor(index)">
						<view class="color-circle" :style="{backgroundColor: color.primary}">
							<Icon name="check-line" size="24rpx" color="white" v-if="selectedColorIndex === index"></Icon>
						</view>
						<text class="color-name">{{color.name}}</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 布局设置 -->
		<view class="display-section">
			<view class="section-header">
				<Icon name="layout-grid-line" size="32rpx" color="#ff8a00"></Icon>
				<text class="section-title">布局设置</text>
			</view>
			
			<view class="setting-item">
				<view class="item-info">
					<text class="item-title">紧凑布局</text>
					<text class="item-desc">减少间距，显示更多内容</text>
				</view>
				<switch :checked="compactLayout" @change="onCompactLayoutChange" color="#ff8a00" />
			</view>
			
			<view class="setting-item">
				<view class="item-info">
					<text class="item-title">卡片圆角</text>
					<text class="item-desc">调整界面卡片的圆角大小</text>
				</view>
				<picker :value="borderRadiusIndex" :range="borderRadiusOptions" @change="onBorderRadiusChange">
					<view class="picker-input">
						<text>{{borderRadiusOptions[borderRadiusIndex]}}</text>
						<Icon name="arrow-down-s-line" size="20rpx" color="#999"></Icon>
					</view>
				</picker>
			</view>
			
			<view class="setting-item">
				<view class="item-info">
					<text class="item-title">动画效果</text>
					<text class="item-desc">开启界面切换动画</text>
				</view>
				<switch :checked="animations" @change="onAnimationsChange" color="#ff8a00" />
			</view>
		</view>

		<!-- 无障碍设置 -->
		<view class="display-section">
			<view class="section-header">
				<Icon name="wheelchair-line" size="32rpx" color="#ff8a00"></Icon>
				<text class="section-title">无障碍设置</text>
			</view>
			
			<view class="setting-item">
				<view class="item-info">
					<text class="item-title">高对比度</text>
					<text class="item-desc">增强文字和背景的对比度</text>
				</view>
				<switch :checked="highContrast" @change="onHighContrastChange" color="#ff8a00" />
			</view>
			
			<view class="setting-item">
				<view class="item-info">
					<text class="item-title">减少动画</text>
					<text class="item-desc">减少界面动画效果</text>
				</view>
				<switch :checked="reduceMotion" @change="onReduceMotionChange" color="#ff8a00" />
			</view>
			
			<view class="setting-item">
				<view class="item-info">
					<text class="item-title">语音朗读</text>
					<text class="item-desc">开启屏幕内容语音朗读</text>
				</view>
				<switch :checked="voiceOver" @change="onVoiceOverChange" color="#ff8a00" />
			</view>
		</view>

		<!-- 重置设置 -->
		<view class="reset-section">
			<button class="reset-btn" @click="resetSettings">
				<Icon name="refresh-line" size="32rpx" color="#666"></Icon>
				<text>恢复默认设置</text>
			</button>
		</view>
	</view>
</template>

<script>
import Icon from '@/components/Icon/Icon.vue'

export default {
	components: {
		Icon
	},
	data() {
		return {
			statusBarHeight: 0, // 状态栏高度
			fontSizeIndex: 2,
			fontSizeOptions: ['小', '较小', '标准', '较大', '大', '超大'],
			fontWeightIndex: 1,
			fontWeightOptions: ['细', '标准', '粗', '超粗'],
			darkMode: false,
			followSystem: true,
			selectedColorIndex: 0,
			themeColors: [
				{ name: '橙色', primary: '#ff8a00', secondary: '#ff6b35' },
				{ name: '蓝色', primary: '#2196f3', secondary: '#1976d2' },
				{ name: '绿色', primary: '#4caf50', secondary: '#388e3c' },
				{ name: '紫色', primary: '#9c27b0', secondary: '#7b1fa2' },
				{ name: '红色', primary: '#f44336', secondary: '#d32f2f' },
				{ name: '青色', primary: '#00bcd4', secondary: '#0097a7' }
			],
			compactLayout: false,
			borderRadiusIndex: 1,
			borderRadiusOptions: ['无圆角', '小圆角', '标准圆角', '大圆角'],
			animations: true,
			highContrast: false,
			reduceMotion: false,
			voiceOver: false
		}
	},
	computed: {
		previewStyle() {
			const fontSizes = ['24rpx', '26rpx', '28rpx', '32rpx', '36rpx', '40rpx'];
			const fontWeights = ['300', '400', '600', '800'];
			
			return {
				fontSize: fontSizes[this.fontSizeIndex],
				fontWeight: fontWeights[this.fontWeightIndex]
			};
		}
	},
	methods: {
		onFontSizeChange(e) {
			this.fontSizeIndex = e.detail.value;
			this.saveSettings();
		},
		onFontWeightChange(e) {
			this.fontWeightIndex = e.detail.value;
			this.saveSettings();
		},
		onDarkModeChange(e) {
			this.darkMode = e.detail.value;
			this.saveSettings();
		},
		onFollowSystemChange(e) {
			this.followSystem = e.detail.value;
			this.saveSettings();
		},
		selectThemeColor(index) {
			this.selectedColorIndex = index;
			this.saveSettings();
			uni.showToast({
				title: '主题色已更换',
				icon: 'success'
			});
		},
		onCompactLayoutChange(e) {
			this.compactLayout = e.detail.value;
			this.saveSettings();
		},
		onBorderRadiusChange(e) {
			this.borderRadiusIndex = e.detail.value;
			this.saveSettings();
		},
		onAnimationsChange(e) {
			this.animations = e.detail.value;
			this.saveSettings();
		},
		onHighContrastChange(e) {
			this.highContrast = e.detail.value;
			this.saveSettings();
		},
		onReduceMotionChange(e) {
			this.reduceMotion = e.detail.value;
			this.saveSettings();
		},
		onVoiceOverChange(e) {
			this.voiceOver = e.detail.value;
			this.saveSettings();
		},
		resetSettings() {
			uni.showModal({
				title: '恢复默认设置',
				content: '确定要恢复所有显示设置为默认值吗？',
				success: (res) => {
					if (res.confirm) {
						this.fontSizeIndex = 2;
						this.fontWeightIndex = 1;
						this.darkMode = false;
						this.followSystem = true;
						this.selectedColorIndex = 0;
						this.compactLayout = false;
						this.borderRadiusIndex = 1;
						this.animations = true;
						this.highContrast = false;
						this.reduceMotion = false;
						this.voiceOver = false;
						
						this.saveSettings();
						uni.showToast({
							title: '已恢复默认设置',
							icon: 'success'
						});
					}
				}
			});
		},
		saveSettings() {
			const settings = {
				fontSizeIndex: this.fontSizeIndex,
				fontWeightIndex: this.fontWeightIndex,
				darkMode: this.darkMode,
				followSystem: this.followSystem,
				selectedColorIndex: this.selectedColorIndex,
				compactLayout: this.compactLayout,
				borderRadiusIndex: this.borderRadiusIndex,
				animations: this.animations,
				highContrast: this.highContrast,
				reduceMotion: this.reduceMotion,
				voiceOver: this.voiceOver
			};

			uni.setStorageSync('displaySettings', settings);

			// 立即应用设置变更
			this.applySettingsImmediately(settings);

			// 通知其他页面设置已更改
			uni.$emit('displaySettingsChanged', settings);
		},

		// 立即应用设置变更
		applySettingsImmediately(settings) {
			try {
				// 应用字体大小
				this.applyFontSize(settings.fontSizeIndex);

				// 应用主题模式
				this.applyThemeMode(settings.darkMode, settings.followSystem);

				// 应用主题色
				this.applyThemeColor(settings.selectedColorIndex);

				// 应用高对比度
				this.applyHighContrast(settings.highContrast);

				// 应用动画设置
				this.applyAnimationSettings(settings.animations, settings.reduceMotion);

			} catch (error) {
				console.error('应用设置失败:', error);
			}
		},

		// 应用字体大小
		applyFontSize(fontSizeIndex) {
			const fontSizes = ['24rpx', '28rpx', '32rpx', '36rpx', '40rpx'];
			const fontSize = fontSizes[fontSizeIndex] || '32rpx';

			// 设置CSS变量
			const root = document.documentElement || document.body;
			if (root && root.style) {
				root.style.setProperty('--base-font-size', fontSize);
			}
		},

		// 应用主题模式
		applyThemeMode(darkMode, followSystem) {
			const body = document.body;
			if (!body) return;

			if (followSystem) {
				// 跟随系统主题
				const prefersDark = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches;
				body.classList.toggle('dark-mode', prefersDark);
			} else {
				// 手动设置
				body.classList.toggle('dark-mode', darkMode);
			}
		},

		// 应用主题色
		applyThemeColor(colorIndex) {
			const colors = ['#ff8a00', '#2196f3', '#4caf50', '#f44336', '#9c27b0', '#ff9800', '#795548', '#607d8b'];
			const color = colors[colorIndex] || '#ff8a00';

			const root = document.documentElement || document.body;
			if (root && root.style) {
				root.style.setProperty('--primary-color', color);
			}
		},

		// 应用高对比度
		applyHighContrast(highContrast) {
			const body = document.body;
			if (body) {
				body.classList.toggle('high-contrast', highContrast);
			}
		},

		// 应用动画设置
		applyAnimationSettings(animations, reduceMotion) {
			const body = document.body;
			if (body) {
				body.classList.toggle('no-animations', !animations);
				body.classList.toggle('reduce-motion', reduceMotion);
			}
		},

		// 返回上一页
		goBack() {
			uni.navigateBack({
				fail: () => {
					uni.switchTab({
						url: '/pages/profile/profile'
					});
				}
			});
		}
	},
	onLoad() {
		// 获取状态栏高度
		const systemInfo = uni.getSystemInfoSync();
		this.statusBarHeight = systemInfo.statusBarHeight || 44;

		const savedSettings = uni.getStorageSync('displaySettings');
		if (savedSettings) {
			Object.assign(this, savedSettings);
		}
	}
}
</script>

<style scoped>
.container {
	background: #f8f9fa;
	min-height: 100vh;
}

/* 导航栏样式 */
.custom-navbar {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 1000;
	background: rgba(255, 255, 255, 0.95);
	backdrop-filter: blur(20rpx);
	-webkit-backdrop-filter: blur(20rpx);
	border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);
}

.navbar-content {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 20rpx 32rpx;
	min-height: 88rpx;
}

.navbar-left {
	display: flex;
	align-items: center;
	gap: 12rpx;
	padding: 8rpx 16rpx 8rpx 0;
	border-radius: 20rpx;
	transition: all 0.2s ease;
}

.navbar-left:active {
	background-color: rgba(0, 0, 0, 0.04);
	transform: scale(0.96);
}

.back-text {
	font-size: 32rpx;
	color: #333;
	font-weight: 500;
}

.navbar-center {
	position: absolute;
	left: 50%;
	transform: translateX(-50%);
}

.navbar-title {
	font-size: 34rpx;
	font-weight: 600;
	color: #333;
}

.navbar-right {
	display: flex;
	align-items: center;
}

.display-overview {
	background: linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%);
	padding: 40rpx;
	margin-top: 220rpx; /* 为导航栏留出空间 */
	text-align: center;
	color: white;
}

.overview-title {
	font-size: 32rpx;
	font-weight: bold;
	display: block;
	margin: 20rpx 0 15rpx;
}

.overview-desc {
	font-size: 24rpx;
	opacity: 0.9;
	line-height: 1.5;
	display: block;
}

.display-section {
	background: white;
	margin: 20rpx;
	border-radius: 30rpx;
	padding: 40rpx;
}

.section-header {
	display: flex;
	align-items: center;
	gap: 15rpx;
	margin-bottom: 30rpx;
}

.section-title {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
}

.setting-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 25rpx 0;
	border-bottom: 1rpx solid #f0f0f0;
}

.setting-item:last-child {
	border-bottom: none;
}

.item-info {
	flex: 1;
	margin-right: 20rpx;
}

.item-title {
	font-size: 28rpx;
	color: #333;
	font-weight: bold;
	display: block;
	margin-bottom: 8rpx;
}

.item-desc {
	font-size: 24rpx;
	color: #666;
	line-height: 1.4;
	display: block;
}

.picker-input {
	display: flex;
	align-items: center;
	gap: 10rpx;
	padding: 15rpx 20rpx;
	background: #f8f9fa;
	border-radius: 15rpx;
	font-size: 26rpx;
	color: #333;
}

.font-preview {
	background: #f8f9fa;
	border-radius: 20rpx;
	padding: 30rpx;
	margin-top: 20rpx;
}

.preview-title {
	font-size: 24rpx;
	color: #666;
	display: block;
	margin-bottom: 20rpx;
}

.preview-text {
	color: #333;
	line-height: 1.6;
	display: block;
}

.theme-colors {
	margin-top: 30rpx;
}

.colors-title {
	font-size: 26rpx;
	color: #333;
	font-weight: bold;
	display: block;
	margin-bottom: 20rpx;
}

.color-grid {
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	gap: 20rpx;
}

.color-item {
	text-align: center;
}

.color-circle {
	width: 60rpx;
	height: 60rpx;
	border-radius: 50%;
	margin: 0 auto 10rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	border: 3rpx solid transparent;
}

.color-name {
	font-size: 22rpx;
	color: #666;
	display: block;
}

.reset-section {
	padding: 40rpx;
}

.reset-btn {
	width: 100%;
	height: 80rpx;
	background: white;
	border: 2rpx solid #e0e0e0;
	border-radius: 20rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 15rpx;
	font-size: 28rpx;
	color: #666;
}
</style>
