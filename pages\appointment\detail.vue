<template>
	<view class="container">
		<PageHeader title="预约详情" showBack></PageHeader>
		
		<view class="content">
			<!-- 预约状态卡片 -->
			<view class="status-card">
				<view class="status-header">
					<view class="service-icon" :style="{ background: getStatusGradient(appointment.status) }">
						<Icon :name="getServiceIcon(appointment.serviceType)" size="48rpx" color="#fff"></Icon>
					</view>
					<view class="status-info">
						<text class="service-name">{{ appointment.serviceName }}</text>
						<view class="status-badge" :class="appointment.status">
							<text class="status-text">{{ getStatusText(appointment.status) }}</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 预约信息 -->
			<view class="info-card">
				<text class="card-title">预约信息</text>
				<view class="info-list">
					<view class="info-item">
						<Icon name="calendar-line" size="32rpx" color="#666"></Icon>
						<text class="info-label">预约时间</text>
						<text class="info-value">{{ appointment.appointmentTime }}</text>
					</view>
					<view class="info-item">
						<Icon name="location-line" size="32rpx" color="#666"></Icon>
						<text class="info-label">服务地址</text>
						<text class="info-value">{{ appointment.address }}</text>
					</view>
					<view class="info-item">
						<Icon name="user-line" size="32rpx" color="#666"></Icon>
						<text class="info-label">服务对象</text>
						<text class="info-value">{{ appointment.customerName }}</text>
					</view>
					<view class="info-item">
						<Icon name="phone-line" size="32rpx" color="#666"></Icon>
						<text class="info-label">联系电话</text>
						<text class="info-value">{{ appointment.phone }}</text>
					</view>
				</view>
			</view>

			<!-- 服务详情 -->
			<view class="info-card">
				<text class="card-title">服务详情</text>
				<view class="service-detail">
					<text class="service-desc">{{ appointment.description }}</text>
					<view class="service-features">
						<view class="feature-item" v-for="(feature, index) in appointment.features" :key="index">
							<Icon name="check-line" size="24rpx" color="#4caf50"></Icon>
							<text class="feature-text">{{ feature }}</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 服务机构信息 -->
			<view class="info-card">
				<text class="card-title">服务机构</text>
				<view class="provider-info">
					<view class="provider-header">
						<image :src="appointment.providerLogo" class="provider-logo"></image>
						<view class="provider-detail">
							<text class="provider-name">{{ appointment.providerName }}</text>
							<view class="provider-rating">
								<Icon name="star-fill" size="24rpx" color="#ff9800"></Icon>
								<text class="rating-score">{{ appointment.providerRating }}</text>
								<text class="rating-text">分</text>
							</view>
						</view>
					</view>
					<text class="provider-desc">{{ appointment.providerDesc }}</text>
				</view>
			</view>

			<!-- 费用信息 -->
			<view class="info-card">
				<text class="card-title">费用信息</text>
				<view class="cost-info">
					<view class="cost-item">
						<text class="cost-label">服务费用</text>
						<text class="cost-value">¥{{ appointment.serviceFee }}</text>
					</view>
					<view class="cost-item" v-if="appointment.discount > 0">
						<text class="cost-label">优惠减免</text>
						<text class="cost-value discount">-¥{{ appointment.discount }}</text>
					</view>
					<view class="cost-divider"></view>
					<view class="cost-item total">
						<text class="cost-label">实付金额</text>
						<text class="cost-value">¥{{ appointment.totalFee }}</text>
					</view>
				</view>
			</view>

			<!-- 操作按钮 -->
			<view class="action-buttons">
				<InteractiveButton 
					v-if="appointment.status === 'pending'"
					type="secondary" 
					text="取消预约" 
					@click="cancelAppointment"
				/>
				<InteractiveButton 
					v-if="appointment.status === 'pending'"
					type="primary" 
					text="确认预约" 
					@click="confirmAppointment"
				/>
				<InteractiveButton 
					v-if="appointment.status === 'confirmed'"
					type="secondary" 
					text="修改预约" 
					@click="editAppointment"
				/>
				<InteractiveButton 
					v-if="appointment.status === 'completed'"
					type="primary" 
					text="评价服务" 
					@click="evaluateService"
				/>
				<InteractiveButton 
					type="secondary" 
					text="联系客服" 
					@click="contactService"
				/>
			</view>
		</view>
	</view>
</template>

<script>
import Icon from '@/components/Icon/Icon.vue'
import PageHeader from '@/components/PageHeader/PageHeader.vue'
import InteractiveButton from '@/components/InteractiveButton/InteractiveButton.vue'

export default {
	name: 'AppointmentDetail',
	components: {
		Icon,
		PageHeader,
		InteractiveButton
	},
	data() {
		return {
			appointmentId: '',
			appointment: {
				id: 1,
				serviceName: '居家护理服务',
				serviceType: 'nursing',
				appointmentTime: '2024年1月20日 14:00',
				status: 'confirmed',
				address: '北京市朝阳区建国路88号',
				customerName: '张大爷',
				phone: '138****8888',
				description: '专业护理人员上门提供日常护理服务，包括生活照料、健康监测、康复指导等。',
				features: [
					'专业护理师上门服务',
					'健康状况实时监测',
					'个性化护理方案',
					'24小时紧急响应'
				],
				providerName: '爱心护理服务中心',
				providerLogo: '/static/images/provider-logo.png',
				providerRating: 4.8,
				providerDesc: '专业的居家护理服务机构，拥有10年服务经验。',
				serviceFee: 200,
				discount: 20,
				totalFee: 180
			}
		}
	},
	onLoad(options) {
		if (options.id) {
			this.appointmentId = options.id
			this.loadAppointmentDetail()
		}
	},
	methods: {
		loadAppointmentDetail() {
			// 模拟加载预约详情数据
			console.log('加载预约详情:', this.appointmentId)
		},
		getServiceIcon(serviceType) {
			const iconMap = {
				'nursing': 'user-heart-line',
				'cleaning': 'home-line',
				'therapy': 'health-book-line',
				'medical': 'stethoscope-line',
				'companion': 'user-smile-line'
			}
			return iconMap[serviceType] || 'calendar-line'
		},
		getStatusText(status) {
			const statusMap = {
				'pending': '待确认',
				'confirmed': '已确认',
				'completed': '已完成',
				'cancelled': '已取消'
			}
			return statusMap[status] || '未知'
		},
		getStatusGradient(status) {
			const gradientMap = {
				'pending': 'linear-gradient(135deg, #ff9800 0%, #f57c00 100%)',
				'confirmed': 'linear-gradient(135deg, #4caf50 0%, #388e3c 100%)',
				'completed': 'linear-gradient(135deg, #2196f3 0%, #1976d2 100%)',
				'cancelled': 'linear-gradient(135deg, #9e9e9e 0%, #616161 100%)'
			}
			return gradientMap[status] || gradientMap.pending
		},
		confirmAppointment() {
			uni.showModal({
				title: '确认预约',
				content: '确定要确认这个预约吗？',
				success: (res) => {
					if (res.confirm) {
						this.appointment.status = 'confirmed'
						uni.showToast({
							title: '预约已确认',
							icon: 'success'
						})
					}
				}
			})
		},
		cancelAppointment() {
			uni.showModal({
				title: '取消预约',
				content: '确定要取消这个预约吗？取消后不可恢复。',
				success: (res) => {
					if (res.confirm) {
						this.appointment.status = 'cancelled'
						uni.showToast({
							title: '预约已取消',
							icon: 'success'
						})
					}
				}
			})
		},
		editAppointment() {
			uni.navigateTo({
				url: `/pages/appointment/create?id=${this.appointmentId}&mode=edit`
			})
		},
		evaluateService() {
			uni.showToast({
				title: '评价功能开发中',
				icon: 'none'
			})
		},
		contactService() {
			uni.showActionSheet({
				itemList: ['拨打电话', '在线客服'],
				success: (res) => {
					if (res.tapIndex === 0) {
						uni.makePhoneCall({
							phoneNumber: '************'
						})
					} else {
						uni.showToast({
							title: '客服功能开发中',
							icon: 'none'
						})
					}
				}
			})
		}
	}
}
</script>

<style scoped>
.container {
	min-height: 100vh;
	background: #f5f5f5;
}

.content {
	padding: 140rpx 32rpx 40rpx;
}

.status-card {
	background: white;
	border-radius: 20rpx;
	padding: 32rpx;
	margin-bottom: 24rpx;
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

.status-header {
	display: flex;
	align-items: center;
	gap: 24rpx;
}

.service-icon {
	width: 96rpx;
	height: 96rpx;
	border-radius: 24rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.status-info {
	flex: 1;
}

.service-name {
	font-size: 36rpx;
	font-weight: 600;
	color: #333;
	display: block;
	margin-bottom: 12rpx;
}

.status-badge {
	padding: 8rpx 16rpx;
	border-radius: 12rpx;
	align-self: flex-start;
}

.status-badge.pending {
	background: rgba(255, 152, 0, 0.1);
}

.status-badge.confirmed {
	background: rgba(76, 175, 80, 0.1);
}

.status-badge.completed {
	background: rgba(33, 150, 243, 0.1);
}

.status-badge.cancelled {
	background: rgba(158, 158, 158, 0.1);
}

.status-text {
	font-size: 24rpx;
	font-weight: 500;
	color: #ff9800;
}

.status-badge.confirmed .status-text {
	color: #4caf50;
}

.status-badge.completed .status-text {
	color: #2196f3;
}

.status-badge.cancelled .status-text {
	color: #9e9e9e;
}

.info-card {
	background: white;
	border-radius: 20rpx;
	padding: 32rpx;
	margin-bottom: 24rpx;
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

.card-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
	display: block;
	margin-bottom: 24rpx;
}

.info-list {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

.info-item {
	display: flex;
	align-items: center;
	gap: 16rpx;
}

.info-label {
	font-size: 28rpx;
	color: #666;
	min-width: 120rpx;
}

.info-value {
	font-size: 28rpx;
	color: #333;
	flex: 1;
}

.service-detail {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

.service-desc {
	font-size: 28rpx;
	color: #666;
	line-height: 1.6;
}

.service-features {
	display: flex;
	flex-direction: column;
	gap: 12rpx;
}

.feature-item {
	display: flex;
	align-items: center;
	gap: 12rpx;
}

.feature-text {
	font-size: 26rpx;
	color: #333;
}

.provider-info {
	display: flex;
	flex-direction: column;
	gap: 16rpx;
}

.provider-header {
	display: flex;
	align-items: center;
	gap: 16rpx;
}

.provider-logo {
	width: 80rpx;
	height: 80rpx;
	border-radius: 16rpx;
}

.provider-detail {
	flex: 1;
}

.provider-name {
	font-size: 30rpx;
	font-weight: 600;
	color: #333;
	display: block;
	margin-bottom: 8rpx;
}

.provider-rating {
	display: flex;
	align-items: center;
	gap: 8rpx;
}

.rating-score {
	font-size: 26rpx;
	color: #ff9800;
	font-weight: 600;
}

.rating-text {
	font-size: 24rpx;
	color: #999;
}

.provider-desc {
	font-size: 26rpx;
	color: #666;
	line-height: 1.5;
}

.cost-info {
	display: flex;
	flex-direction: column;
	gap: 16rpx;
}

.cost-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.cost-label {
	font-size: 28rpx;
	color: #666;
}

.cost-value {
	font-size: 28rpx;
	color: #333;
	font-weight: 600;
}

.cost-value.discount {
	color: #4caf50;
}

.cost-item.total .cost-label,
.cost-item.total .cost-value {
	font-size: 32rpx;
	font-weight: 700;
	color: #ff8a00;
}

.cost-divider {
	height: 1rpx;
	background: #f0f0f0;
	margin: 8rpx 0;
}

.action-buttons {
	display: flex;
	flex-direction: column;
	gap: 16rpx;
	margin-top: 40rpx;
}
</style>
