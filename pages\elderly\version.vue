<template>
	<view class="container">
		<!-- 页面头部 -->
		<PageHeader title="适老版设置">
			<template #actions>
				<InteractiveButton
					type="secondary"
					size="small"
					text="设置"
					icon="settings-line"
					@click="openSettings"
				></InteractiveButton>
			</template>
		</PageHeader>

		<!-- 版本切换说明 -->
		<view class="version-intro">
			<view class="intro-icon">
				<Icon name="heart-line" size="80rpx" color="#ff8a00"></Icon>
			</view>
			<text class="intro-title">适老化设计</text>
			<text class="intro-subtitle">为老年人量身定制的界面设计</text>
			<view class="intro-features">
				<view class="feature-item">
					<Icon name="font-size" size="32rpx" color="#4caf50"></Icon>
					<text class="feature-text">大字体显示</text>
				</view>
				<view class="feature-item">
					<Icon name="contrast-line" size="32rpx" color="#4caf50"></Icon>
					<text class="feature-text">高对比度</text>
				</view>
				<view class="feature-item">
					<Icon name="hand-heart-line" size="32rpx" color="#4caf50"></Icon>
					<text class="feature-text">简化操作</text>
				</view>
				<view class="feature-item">
					<Icon name="volume-up-line" size="32rpx" color="#4caf50"></Icon>
					<text class="feature-text">语音提示</text>
				</view>
			</view>
		</view>

		<!-- 版本选择 -->
		<view class="version-selection">
			<text class="section-title">选择界面版本</text>
			
			<InteractiveCard 
				class="version-card"
				:class="{ active: currentVersion === 'standard' }"
				@click="selectVersion('standard')"
			>
				<view class="version-content">
					<view class="version-preview">
						<image 
							src="/picture/v2-cf8da5bb3e3a5a87f1ce7f498397db9d_720w.jpg" 
							class="preview-image" 
							mode="aspectFill"
						></image>
						<view class="preview-overlay">
							<Icon name="smartphone-line" size="48rpx" color="#fff"></Icon>
						</view>
					</view>
					<view class="version-info">
						<text class="version-name">标准版</text>
						<text class="version-description">适合熟悉智能设备的用户，功能丰富，界面紧凑</text>
						<view class="version-features">
							<view class="feature-tag">功能完整</view>
							<view class="feature-tag">界面紧凑</view>
							<view class="feature-tag">操作灵活</view>
						</view>
					</view>
					<view class="version-status">
						<Icon 
							:name="currentVersion === 'standard' ? 'checkbox-circle-fill' : 'checkbox-blank-circle-line'" 
							size="40rpx" 
							:color="currentVersion === 'standard' ? '#ff8a00' : '#ccc'"
						></Icon>
					</view>
				</view>
			</InteractiveCard>

			<InteractiveCard 
				class="version-card"
				:class="{ active: currentVersion === 'elderly' }"
				@click="selectVersion('elderly')"
			>
				<view class="version-content">
					<view class="version-preview">
						<image 
							src="/picture/20226131655111829696_10006313.jpg" 
							class="preview-image" 
							mode="aspectFill"
						></image>
						<view class="preview-overlay">
							<Icon name="heart-line" size="48rpx" color="#fff"></Icon>
						</view>
					</view>
					<view class="version-info">
						<text class="version-name">适老版</text>
						<text class="version-description">专为老年人设计，字体更大，操作更简单，语音提示</text>
						<view class="version-features">
							<view class="feature-tag">大字体</view>
							<view class="feature-tag">简化操作</view>
							<view class="feature-tag">语音提示</view>
						</view>
					</view>
					<view class="version-status">
						<Icon 
							:name="currentVersion === 'elderly' ? 'checkbox-circle-fill' : 'checkbox-blank-circle-line'" 
							size="40rpx" 
							:color="currentVersion === 'elderly' ? '#ff8a00' : '#ccc'"
						></Icon>
					</view>
				</view>
			</InteractiveCard>
		</view>

		<!-- 适老化设置 -->
		<view class="elderly-settings" v-if="currentVersion === 'elderly'">
			<text class="section-title">适老化设置</text>
			
			<InteractiveCard class="setting-card">
				<view class="setting-item">
					<view class="setting-info">
						<Icon name="font-size" size="32rpx" color="#ff8a00"></Icon>
						<view class="setting-text">
							<text class="setting-name">字体大小</text>
							<text class="setting-desc">调整界面字体大小</text>
						</view>
					</view>
					<view class="setting-control">
						<view class="font-size-control">
							<view 
								v-for="(size, index) in fontSizes" 
								:key="index"
								class="font-size-option"
								:class="{ active: selectedFontSize === size.value }"
								@click="selectFontSize(size.value)"
							>
								<text class="size-text" :style="{ fontSize: size.preview }">A</text>
								<text class="size-label">{{ size.label }}</text>
							</view>
						</view>
					</view>
				</view>
			</InteractiveCard>

			<InteractiveCard class="setting-card">
				<view class="setting-item">
					<view class="setting-info">
						<Icon name="volume-up-line" size="32rpx" color="#ff8a00"></Icon>
						<view class="setting-text">
							<text class="setting-name">语音提示</text>
							<text class="setting-desc">开启操作语音提示</text>
						</view>
					</view>
					<view class="setting-control">
						<switch 
							:checked="voiceEnabled" 
							@change="toggleVoice"
							color="#ff8a00"
						></switch>
					</view>
				</view>
			</InteractiveCard>

			<InteractiveCard class="setting-card">
				<view class="setting-item">
					<view class="setting-info">
						<Icon name="contrast-line" size="32rpx" color="#ff8a00"></Icon>
						<view class="setting-text">
							<text class="setting-name">高对比度</text>
							<text class="setting-desc">增强界面对比度</text>
						</view>
					</view>
					<view class="setting-control">
						<switch 
							:checked="highContrast" 
							@change="toggleContrast"
							color="#ff8a00"
						></switch>
					</view>
				</view>
			</InteractiveCard>

			<InteractiveCard class="setting-card">
				<view class="setting-item">
					<view class="setting-info">
						<Icon name="hand-heart-line" size="32rpx" color="#ff8a00"></Icon>
						<view class="setting-text">
							<text class="setting-name">简化界面</text>
							<text class="setting-desc">隐藏复杂功能</text>
						</view>
					</view>
					<view class="setting-control">
						<switch 
							:checked="simplifiedUI" 
							@change="toggleSimplified"
							color="#ff8a00"
						></switch>
					</view>
				</view>
			</InteractiveCard>
		</view>

		<!-- 操作按钮 -->
		<view class="action-buttons">
			<InteractiveButton 
				type="secondary" 
				size="large" 
				text="预览效果" 
				icon="eye-line"
				@click="previewVersion"
			></InteractiveButton>
			<InteractiveButton 
				type="primary" 
				size="large" 
				text="应用设置" 
				icon="check-line"
				@click="applySettings"
			></InteractiveButton>
		</view>

		<!-- 帮助说明 -->
		<view class="help-section">
			<InteractiveCard class="help-card">
				<view class="help-content">
					<Icon name="question-line" size="32rpx" color="#ff8a00"></Icon>
					<view class="help-text">
						<text class="help-title">需要帮助？</text>
						<text class="help-desc">如果您在使用过程中遇到问题，可以联系我们的客服</text>
					</view>
					<InteractiveButton 
						type="secondary" 
						size="small" 
						text="联系客服" 
						icon="customer-service-line"
						@click="contactService"
					></InteractiveButton>
				</view>
			</InteractiveCard>
		</view>

		<!-- 设置弹窗 -->
		<uni-popup ref="settingsPopup" type="bottom" :mask-click="true">
			<view class="settings-panel">
				<view class="settings-header">
					<text class="settings-title">更多设置</text>
					<view class="settings-close" @click="closeSettings">
						<Icon name="close-line" size="32rpx" color="#666"></Icon>
					</view>
				</view>
				
				<view class="settings-content">
					<view class="settings-group">
						<text class="group-title">显示设置</text>
						<view class="settings-item">
							<text class="item-label">夜间模式</text>
							<switch :checked="nightMode" @change="toggleNightMode" color="#ff8a00"></switch>
						</view>
						<view class="settings-item">
							<text class="item-label">动画效果</text>
							<switch :checked="animations" @change="toggleAnimations" color="#ff8a00"></switch>
						</view>
					</view>
					
					<view class="settings-group">
						<text class="group-title">辅助功能</text>
						<view class="settings-item">
							<text class="item-label">震动反馈</text>
							<switch :checked="vibration" @change="toggleVibration" color="#ff8a00"></switch>
						</view>
						<view class="settings-item">
							<text class="item-label">按钮提示音</text>
							<switch :checked="buttonSound" @change="toggleButtonSound" color="#ff8a00"></switch>
						</view>
					</view>
				</view>

				<view class="settings-actions">
					<InteractiveButton 
						type="secondary" 
						size="large" 
						text="重置设置" 
						@click="resetSettings"
					></InteractiveButton>
				</view>
			</view>
		</uni-popup>
	</view>
</template>

<script>
import InteractiveCard from '@/components/InteractiveCard/InteractiveCard.vue'
import InteractiveButton from '@/components/InteractiveButton/InteractiveButton.vue'
import Icon from '@/components/Icon/Icon.vue'
import PageHeader from '@/components/PageHeader/PageHeader.vue'
import FeedbackUtils from '@/utils/feedback.js'

export default {
	components: {
		InteractiveCard,
		InteractiveButton,
		Icon,
		PageHeader
	},
	data() {
		return {
			currentVersion: 'standard',
			selectedFontSize: 'medium',
			voiceEnabled: false,
			highContrast: false,
			simplifiedUI: false,
			nightMode: false,
			animations: true,
			vibration: true,
			buttonSound: false,
			
			// 字体大小选项
			fontSizes: [
				{ label: '小', value: 'small', preview: '24rpx' },
				{ label: '中', value: 'medium', preview: '28rpx' },
				{ label: '大', value: 'large', preview: '32rpx' },
				{ label: '特大', value: 'xlarge', preview: '36rpx' }
			]
		}
	},
	onLoad() {
		this.loadSettings();
	},
	methods: {
		// 加载设置
		loadSettings() {
			try {
				const settings = uni.getStorageSync('elderlySettings');
				if (settings) {
					Object.assign(this, settings);
				}
			} catch (error) {
				console.error('加载设置失败:', error);
			}
		},

		// 保存设置
		saveSettings() {
			try {
				const settings = {
					currentVersion: this.currentVersion,
					selectedFontSize: this.selectedFontSize,
					voiceEnabled: this.voiceEnabled,
					highContrast: this.highContrast,
					simplifiedUI: this.simplifiedUI,
					nightMode: this.nightMode,
					animations: this.animations,
					vibration: this.vibration,
					buttonSound: this.buttonSound
				};
				uni.setStorageSync('elderlySettings', settings);
			} catch (error) {
				console.error('保存设置失败:', error);
			}
		},

		// 选择版本
		selectVersion(version) {
			FeedbackUtils.lightFeedback();
			this.currentVersion = version;
			
			if (this.voiceEnabled) {
				this.speakText(version === 'elderly' ? '已选择适老版' : '已选择标准版');
			}
		},

		// 选择字体大小
		selectFontSize(size) {
			FeedbackUtils.lightFeedback();
			this.selectedFontSize = size;
			
			if (this.voiceEnabled) {
				const sizeNames = {
					small: '小字体',
					medium: '中等字体',
					large: '大字体',
					xlarge: '特大字体'
				};
				this.speakText(`已选择${sizeNames[size]}`);
			}
		},

		// 切换语音提示
		toggleVoice(e) {
			FeedbackUtils.lightFeedback();
			this.voiceEnabled = e.detail.value;
			
			if (this.voiceEnabled) {
				this.speakText('语音提示已开启');
			}
		},

		// 切换高对比度
		toggleContrast(e) {
			FeedbackUtils.lightFeedback();
			this.highContrast = e.detail.value;
			
			if (this.voiceEnabled) {
				this.speakText(this.highContrast ? '高对比度已开启' : '高对比度已关闭');
			}
		},

		// 切换简化界面
		toggleSimplified(e) {
			FeedbackUtils.lightFeedback();
			this.simplifiedUI = e.detail.value;
			
			if (this.voiceEnabled) {
				this.speakText(this.simplifiedUI ? '简化界面已开启' : '简化界面已关闭');
			}
		},

		// 切换夜间模式
		toggleNightMode(e) {
			FeedbackUtils.lightFeedback();
			this.nightMode = e.detail.value;
		},

		// 切换动画效果
		toggleAnimations(e) {
			FeedbackUtils.lightFeedback();
			this.animations = e.detail.value;
		},

		// 切换震动反馈
		toggleVibration(e) {
			FeedbackUtils.lightFeedback();
			this.vibration = e.detail.value;
		},

		// 切换按钮提示音
		toggleButtonSound(e) {
			FeedbackUtils.lightFeedback();
			this.buttonSound = e.detail.value;
		},

		// 预览效果
		previewVersion() {
			FeedbackUtils.lightFeedback();
			
			if (this.voiceEnabled) {
				this.speakText('正在预览界面效果');
			}
			
			// 这里可以实现预览功能
			FeedbackUtils.showInfo('预览功能开发中');
		},

		// 应用设置
		applySettings() {
			FeedbackUtils.lightFeedback();
			
			this.saveSettings();
			
			if (this.voiceEnabled) {
				this.speakText('设置已保存并应用');
			}
			
			FeedbackUtils.showSuccess('设置已应用');
			
			// 可以在这里重新加载应用或跳转到首页
			setTimeout(() => {
				uni.reLaunch({
					url: '/pages/home/<USER>'
				});
			}, 1500);
		},

		// 打开设置
		openSettings() {
			FeedbackUtils.lightFeedback();
			this.$refs.settingsPopup.open();
		},

		// 关闭设置
		closeSettings() {
			this.$refs.settingsPopup.close();
		},

		// 重置设置
		resetSettings() {
			FeedbackUtils.lightFeedback();
			
			uni.showModal({
				title: '重置设置',
				content: '确定要重置所有设置吗？',
				success: (res) => {
					if (res.confirm) {
						this.currentVersion = 'standard';
						this.selectedFontSize = 'medium';
						this.voiceEnabled = false;
						this.highContrast = false;
						this.simplifiedUI = false;
						this.nightMode = false;
						this.animations = true;
						this.vibration = true;
						this.buttonSound = false;
						
						this.saveSettings();
						this.closeSettings();
						FeedbackUtils.showSuccess('设置已重置');
					}
				}
			});
		},

		// 联系客服
		contactService() {
			FeedbackUtils.lightFeedback();
			
			uni.showActionSheet({
				itemList: ['拨打客服电话', '在线客服', '意见反馈'],
				success: (res) => {
					switch (res.tapIndex) {
						case 0:
							uni.makePhoneCall({
								phoneNumber: '************'
							});
							break;
						case 1:
							FeedbackUtils.showInfo('在线客服功能开发中');
							break;
						case 2:
							uni.navigateTo({
								url: '/pages/feedback/feedback'
							});
							break;
					}
				}
			});
		},

		// 语音播报
		speakText(text) {
			// 这里可以集成语音合成API
			console.log('语音播报:', text);
		}
	}
}
</script>

<style scoped>
.container {
	background-color: #f5f5f5;
	min-height: 100vh;
	display: flex;
	flex-direction: column;
}

.page-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 30rpx 40rpx;
	background: white;
	border-bottom: 1rpx solid #f0f0f0;
}

.page-title {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
}

.version-intro {
	padding: 40rpx;
	background: linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%);
	text-align: center;
	margin-bottom: 30rpx;
}

.intro-icon {
	margin-bottom: 20rpx;
}

.intro-title {
	font-size: 36rpx;
	font-weight: bold;
	color: white;
	display: block;
	margin-bottom: 10rpx;
}

.intro-subtitle {
	font-size: 26rpx;
	color: rgba(255, 255, 255, 0.9);
	display: block;
	margin-bottom: 30rpx;
}

.intro-features {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	gap: 20rpx;
}

.feature-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 8rpx;
	padding: 20rpx;
	background: rgba(255, 255, 255, 0.15);
	border-radius: 15rpx;
	backdrop-filter: blur(10rpx);
}

.feature-text {
	font-size: 22rpx;
	color: white;
	font-weight: 500;
}

.version-selection {
	padding: 0 40rpx;
	margin-bottom: 30rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 20rpx;
}

.version-card {
	margin-bottom: 20rpx;
	border: 2rpx solid transparent;
	transition: all 0.3s ease;
}

.version-card.active {
	border-color: #ff8a00;
	box-shadow: 0 4rpx 20rpx rgba(255, 138, 0, 0.2);
}

.version-content {
	display: flex;
	padding: 30rpx;
	gap: 20rpx;
	align-items: center;
}

.version-preview {
	position: relative;
	width: 120rpx;
	height: 80rpx;
	border-radius: 10rpx;
	overflow: hidden;
	flex-shrink: 0;
}

.preview-image {
	width: 100%;
	height: 100%;
}

.preview-overlay {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.3);
	display: flex;
	align-items: center;
	justify-content: center;
}

.version-info {
	flex: 1;
}

.version-name {
	font-size: 30rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 8rpx;
}

.version-description {
	font-size: 24rpx;
	color: #666;
	line-height: 1.5;
	display: block;
	margin-bottom: 15rpx;
}

.version-features {
	display: flex;
	gap: 10rpx;
	flex-wrap: wrap;
}

.feature-tag {
	padding: 4rpx 12rpx;
	background: rgba(255, 138, 0, 0.1);
	border-radius: 10rpx;
	font-size: 20rpx;
	color: #ff8a00;
}

.version-status {
	flex-shrink: 0;
}

.elderly-settings {
	padding: 0 40rpx;
	margin-bottom: 30rpx;
}

.setting-card {
	margin-bottom: 15rpx;
}

.setting-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 25rpx;
}

.setting-info {
	display: flex;
	align-items: center;
	gap: 15rpx;
	flex: 1;
}

.setting-text {
	flex: 1;
}

.setting-name {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 5rpx;
}

.setting-desc {
	font-size: 22rpx;
	color: #999;
	display: block;
}

.setting-control {
	flex-shrink: 0;
}

.font-size-control {
	display: flex;
	gap: 15rpx;
}

.font-size-option {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 5rpx;
	padding: 15rpx 10rpx;
	border-radius: 10rpx;
	background: #f8f8f8;
	min-width: 60rpx;
	transition: all 0.3s ease;
}

.font-size-option.active {
	background: linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%);
}

.size-text {
	font-weight: bold;
	color: #333;
}

.font-size-option.active .size-text {
	color: white;
}

.size-label {
	font-size: 20rpx;
	color: #666;
}

.font-size-option.active .size-label {
	color: rgba(255, 255, 255, 0.9);
}

.action-buttons {
	padding: 0 40rpx;
	display: flex;
	gap: 20rpx;
	margin-bottom: 30rpx;
}

.help-section {
	padding: 0 40rpx;
	margin-bottom: 40rpx;
}

.help-card {
	background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.help-content {
	display: flex;
	align-items: center;
	padding: 25rpx;
	gap: 15rpx;
}

.help-text {
	flex: 1;
}

.help-title {
	font-size: 26rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 5rpx;
}

.help-desc {
	font-size: 22rpx;
	color: #666;
	line-height: 1.4;
	display: block;
}

/* 设置弹窗样式 */
.settings-panel {
	background: white;
	border-radius: 30rpx 30rpx 0 0;
	max-height: 80vh;
	overflow: hidden;
}

.settings-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 30rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.settings-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.settings-close {
	padding: 10rpx;
}

.settings-content {
	max-height: 50vh;
	overflow-y: auto;
	padding: 30rpx;
}

.settings-group {
	margin-bottom: 40rpx;
}

.group-title {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 20rpx;
	display: block;
}

.settings-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20rpx 0;
	border-bottom: 1rpx solid #f0f0f0;
}

.settings-item:last-child {
	border-bottom: none;
}

.item-label {
	font-size: 26rpx;
	color: #333;
}

.settings-actions {
	padding: 30rpx;
	border-top: 1rpx solid #f0f0f0;
}
</style>
