<template>
	<view class="container">
		<!-- 顶部标题 -->
		<view class="header">
			<text class="header-title">适老版设置</text>
			<text class="header-subtitle">让使用更简单、更贴心</text>
		</view>

		<!-- 当前设置状态 -->
		<view class="current-settings">
			<view class="setting-card">
				<view class="setting-icon">
					<Icon name="font-size" size="40rpx" color="#ff8a00"></Icon>
				</view>
				<view class="setting-info">
					<text class="setting-title">字体大小</text>
					<text class="setting-value">{{getFontSizeText(settings.fontSize)}}</text>
				</view>
			</view>
			<view class="setting-card">
				<view class="setting-icon">
					<Icon name="contrast-line" size="40rpx" color="#ff8a00"></Icon>
				</view>
				<view class="setting-info">
					<text class="setting-title">对比度</text>
					<text class="setting-value">{{getContrastText(settings.contrast)}}</text>
				</view>
			</view>
		</view>

		<!-- 功能设置 -->
		<view class="settings-section">
			<text class="section-title">功能设置</text>
			
			<!-- 字体大小 -->
			<view class="setting-item">
				<view class="setting-header">
					<Icon name="font-size" size="32rpx" color="#666"></Icon>
					<text class="setting-name">字体大小</text>
				</view>
				<view class="font-size-options">
					<view 
						class="font-option"
						:class="{ active: settings.fontSize === size.value }"
						v-for="size in fontSizeOptions"
						:key="size.value"
						@click="updateFontSize(size.value)"
					>
						<text :style="{ fontSize: size.demo }">{{size.label}}</text>
					</view>
				</view>
			</view>

			<!-- 对比度 -->
			<view class="setting-item">
				<view class="setting-header">
					<Icon name="contrast-line" size="32rpx" color="#666"></Icon>
					<text class="setting-name">对比度</text>
				</view>
				<view class="contrast-options">
					<view 
						class="contrast-option"
						:class="{ active: settings.contrast === contrast.value }"
						v-for="contrast in contrastOptions"
						:key="contrast.value"
						@click="updateContrast(contrast.value)"
					>
						<text>{{contrast.label}}</text>
					</view>
				</view>
			</view>

			<!-- 语音提示 -->
			<view class="setting-item">
				<view class="setting-header">
					<Icon name="volume-up-line" size="32rpx" color="#666"></Icon>
					<text class="setting-name">语音提示</text>
				</view>
				<switch 
					:checked="settings.voiceEnabled" 
					@change="updateVoiceEnabled"
					color="#ff8a00"
				/>
			</view>

			<!-- 简化模式 -->
			<view class="setting-item">
				<view class="setting-header">
					<Icon name="hand-heart-line" size="32rpx" color="#666"></Icon>
					<text class="setting-name">简化模式</text>
				</view>
				<switch 
					:checked="settings.simpleMode" 
					@change="updateSimpleMode"
					color="#ff8a00"
				/>
			</view>
		</view>

		<!-- 紧急联系人 -->
		<view class="emergency-section">
			<text class="section-title">紧急联系人</text>
			<view 
				class="contact-item"
				v-for="(contact, index) in settings.emergencyContacts"
				:key="index"
			>
				<view class="contact-info">
					<text class="contact-name">{{contact.name}}</text>
					<text class="contact-relation">{{contact.relation}}</text>
				</view>
				<view class="contact-phone">{{contact.phone}}</view>
				<InteractiveButton
					type="primary"
					size="small"
					text="呼叫"
					icon="phone-line"
					@click="callContact(contact)"
				></InteractiveButton>
			</view>
		</view>

		<!-- 快捷功能 -->
		<view class="quick-functions">
			<text class="section-title">快捷功能</text>
			<view class="function-grid">
				<view 
					class="function-item"
					v-for="(func, index) in settings.quickFunctions"
					:key="index"
					@click="executeFunction(func)"
				>
					<view class="function-icon">
						<Icon :name="func.icon" size="48rpx" color="#fff"></Icon>
					</view>
					<text class="function-name">{{func.name}}</text>
				</view>
			</view>
		</view>

		<!-- 操作按钮 -->
		<view class="actions">
			<InteractiveButton
				type="secondary"
				size="large"
				text="恢复默认"
				icon="refresh-line"
				@click="resetSettings"
			></InteractiveButton>
			<InteractiveButton
				type="primary"
				size="large"
				text="保存设置"
				icon="save-line"
				@click="saveSettings"
			></InteractiveButton>
		</view>
	</view>
</template>

<script>
import Icon from '@/components/Icon/Icon.vue'
import InteractiveButton from '@/components/InteractiveButton/InteractiveButton.vue'
import FeedbackUtils from '@/utils/feedback.js'
import OfflineDataManager from '@/utils/offlineData.js'

export default {
	components: {
		Icon,
		InteractiveButton
	},
	data() {
		return {
			settings: {
				fontSize: 'large',
				contrast: 'high',
				voiceEnabled: true,
				simpleMode: true,
				emergencyContacts: [],
				quickFunctions: []
			},
			fontSizeOptions: [
				{ value: 'small', label: '标准', demo: '28rpx' },
				{ value: 'medium', label: '中等', demo: '32rpx' },
				{ value: 'large', label: '大字', demo: '40rpx' },
				{ value: 'extra-large', label: '特大', demo: '48rpx' }
			],
			contrastOptions: [
				{ value: 'normal', label: '标准对比度' },
				{ value: 'high', label: '高对比度' }
			]
		}
	},
	onLoad() {
		// 初始化离线数据
		OfflineDataManager.initOfflineData();
		this.loadSettings();
	},
	methods: {
		// 加载设置数据 - 100%离线模式
		async loadSettings() {
			try {
				// 强制初始化离线数据
				OfflineDataManager.initOfflineData();

				// 100%使用离线设置数据，确保用户能看到内容
				const offlineSettings = OfflineDataManager.getOfflineElderlySettings();
				if (offlineSettings) {
					this.settings = { ...this.settings, ...offlineSettings };
				} else {
					// 如果没有离线数据，使用默认设置
					this.settings = {
						fontSize: 'large',
						contrast: 'high',
						voiceEnabled: true,
						simpleMode: true,
						emergencyContacts: [
							{ name: '张医生', relation: '家庭医生', phone: '138****1234' },
							{ name: '李护士', relation: '护理员', phone: '139****5678' },
							{ name: '王女士', relation: '女儿', phone: '136****9012' }
						],
						quickFunctions: [
							{ name: '紧急呼叫', icon: 'phone-line', action: 'call' },
							{ name: '健康监测', icon: 'heart-pulse-line', action: 'health' },
							{ name: '服务预约', icon: 'calendar-check-line', action: 'service' },
							{ name: '紧急求助', icon: 'alarm-warning-line', action: 'emergency' }
						]
					};
				}

				FeedbackUtils.showSuccess('设置加载完成');
			} catch (error) {
				console.error('加载设置失败:', error);
				FeedbackUtils.showError('设置加载失败，使用默认配置');
			}
		},

		updateFontSize(size) {
			FeedbackUtils.lightFeedback();
			this.settings.fontSize = size;
		},

		updateContrast(contrast) {
			FeedbackUtils.lightFeedback();
			this.settings.contrast = contrast;
		},

		updateVoiceEnabled(e) {
			FeedbackUtils.lightFeedback();
			this.settings.voiceEnabled = e.detail.value;
		},

		updateSimpleMode(e) {
			FeedbackUtils.lightFeedback();
			this.settings.simpleMode = e.detail.value;
		},

		callContact(contact) {
			FeedbackUtils.lightFeedback();
			uni.makePhoneCall({
				phoneNumber: contact.phone.replace(/\*/g, '')
			});
		},

		executeFunction(func) {
			FeedbackUtils.lightFeedback();
			switch (func.action) {
				case 'call':
					this.showEmergencyCall();
					break;
				case 'health':
					uni.navigateTo({ url: '/pages/health/monitor' });
					break;
				case 'service':
					uni.navigateTo({ url: '/pages/service/list' });
					break;
				case 'emergency':
					this.showEmergencyHelp();
					break;
			}
		},

		showEmergencyCall() {
			uni.showActionSheet({
				itemList: this.settings.emergencyContacts.map(c => c.name),
				success: (res) => {
					const contact = this.settings.emergencyContacts[res.tapIndex];
					this.callContact(contact);
				}
			});
		},

		showEmergencyHelp() {
			uni.showModal({
				title: '紧急求助',
				content: '是否立即拨打紧急救助电话？',
				confirmText: '立即拨打',
				success: (res) => {
					if (res.confirm) {
						uni.makePhoneCall({ phoneNumber: '120' });
					}
				}
			});
		},

		async resetSettings() {
			FeedbackUtils.lightFeedback();
			uni.showModal({
				title: '恢复默认设置',
				content: '确定要恢复所有设置为默认值吗？',
				success: (res) => {
					if (res.confirm) {
						this.settings = {
							fontSize: 'medium',
							contrast: 'normal',
							voiceEnabled: false,
							simpleMode: false,
							emergencyContacts: this.settings.emergencyContacts,
							quickFunctions: this.settings.quickFunctions
						};
						FeedbackUtils.showSuccess('已恢复默认设置');
					}
				}
			});
		},

		async saveSettings() {
			FeedbackUtils.lightFeedback();
			try {
				// 使用离线数据管理器保存设置
				const result = OfflineDataManager.saveElderlySettings(this.settings);
				if (result) {
					FeedbackUtils.showSuccess('设置已保存');
					// 应用设置到全局
					this.applyGlobalSettings();
				} else {
					FeedbackUtils.showError('保存失败，请重试');
				}
			} catch (error) {
				console.error('保存设置失败:', error);
				FeedbackUtils.showError('保存失败，请重试');
			}
		},

		applyGlobalSettings() {
			// 这里可以将设置应用到全局，比如修改CSS变量等
			const app = getApp();
			if (app.globalData) {
				app.globalData.elderlySettings = this.settings;
			}
		},

		getFontSizeText(size) {
			const map = {
				'small': '标准',
				'medium': '中等', 
				'large': '大字',
				'extra-large': '特大'
			};
			return map[size] || '中等';
		},

		getContrastText(contrast) {
			return contrast === 'high' ? '高对比度' : '标准对比度';
		}
	}
}
</script>

<style scoped>
.container {
	background-color: #f5f5f5;
	min-height: 100vh;
	padding: 20rpx;
}

.header {
	text-align: center;
	padding: 40rpx 20rpx;
	background: linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%);
	border-radius: 20rpx;
	margin-bottom: 30rpx;
}

.header-title {
	font-size: 48rpx;
	font-weight: bold;
	color: #fff;
	display: block;
	margin-bottom: 10rpx;
}

.header-subtitle {
	font-size: 28rpx;
	color: rgba(255, 255, 255, 0.9);
}

.current-settings {
	display: flex;
	gap: 20rpx;
	margin-bottom: 30rpx;
}

.setting-card {
	flex: 1;
	background-color: #fff;
	border-radius: 15rpx;
	padding: 25rpx;
	display: flex;
	align-items: center;
	gap: 15rpx;
}

.setting-icon {
	width: 60rpx;
	height: 60rpx;
	background: rgba(255, 138, 0, 0.1);
	border-radius: 30rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.setting-info {
	flex: 1;
}

.setting-title {
	font-size: 24rpx;
	color: #999;
	display: block;
	margin-bottom: 5rpx;
}

.setting-value {
	font-size: 28rpx;
	color: #333;
	font-weight: 500;
}

.settings-section,
.emergency-section,
.quick-functions {
	background-color: #fff;
	border-radius: 20rpx;
	padding: 30rpx;
	margin-bottom: 30rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 25rpx;
}

.setting-item {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 25rpx 0;
	border-bottom: 1rpx solid #f0f0f0;
}

.setting-item:last-child {
	border-bottom: none;
}

.setting-header {
	display: flex;
	align-items: center;
	gap: 15rpx;
}

.setting-name {
	font-size: 30rpx;
	color: #333;
}

.font-size-options,
.contrast-options {
	display: flex;
	gap: 15rpx;
}

.font-option,
.contrast-option {
	padding: 10rpx 20rpx;
	border: 2rpx solid #e0e0e0;
	border-radius: 25rpx;
	background-color: #fff;
}

.font-option.active,
.contrast-option.active {
	border-color: #ff8a00;
	background-color: rgba(255, 138, 0, 0.1);
}

.contact-item {
	display: flex;
	align-items: center;
	padding: 20rpx 0;
	border-bottom: 1rpx solid #f0f0f0;
}

.contact-item:last-child {
	border-bottom: none;
}

.contact-info {
	flex: 1;
}

.contact-name {
	font-size: 30rpx;
	color: #333;
	font-weight: 500;
	display: block;
	margin-bottom: 5rpx;
}

.contact-relation {
	font-size: 24rpx;
	color: #999;
}

.contact-phone {
	font-size: 28rpx;
	color: #666;
	margin-right: 20rpx;
}

.function-grid {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	gap: 20rpx;
}

.function-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 30rpx;
	background: linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%);
	border-radius: 20rpx;
}

.function-icon {
	width: 80rpx;
	height: 80rpx;
	background: rgba(255, 255, 255, 0.2);
	border-radius: 40rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-bottom: 15rpx;
}

.function-name {
	font-size: 28rpx;
	color: #fff;
	font-weight: 500;
}

.actions {
	display: flex;
	gap: 20rpx;
	padding: 20rpx 0;
}
</style>
