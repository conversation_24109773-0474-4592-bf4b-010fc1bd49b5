<template>
	<view class="test-container">
		<view class="header">
			<text class="title">资讯详情页测试</text>
		</view>
		
		<view class="test-section">
			<text class="section-title">快速测试</text>
			
			<view class="test-item">
				<text class="test-label">测试详情页 ID=1:</text>
				<button class="test-btn" @click="testDetail(1)">测试详情1</button>
			</view>
			
			<view class="test-item">
				<text class="test-label">测试详情页 ID=2:</text>
				<button class="test-btn" @click="testDetail(2)">测试详情2</button>
			</view>
			
			<view class="test-item">
				<text class="test-label">测试详情页 ID=3:</text>
				<button class="test-btn" @click="testDetail(3)">测试详情3</button>
			</view>
			
			<view class="test-item">
				<text class="test-label">测试无效ID:</text>
				<button class="test-btn" @click="testDetail(999)">测试无效ID</button>
			</view>
		</view>
		
		<view class="test-section">
			<text class="section-title">数据检查</text>
			
			<view class="data-info">
				<text class="info-label">离线资讯数量:</text>
				<text class="info-value">{{ newsCount }}条</text>
			</view>
			
			<view class="data-info">
				<text class="info-label">数据状态:</text>
				<text class="info-value">{{ dataStatus }}</text>
			</view>
			
			<view class="data-info">
				<text class="info-label">最后测试:</text>
				<text class="info-value">{{ lastTest }}</text>
			</view>
		</view>
		
		<view class="test-section">
			<text class="section-title">可用资讯列表</text>
			<view class="news-list">
				<view v-if="newsList.length === 0" class="no-news">暂无资讯数据</view>
				<view v-else v-for="(item, index) in newsList" :key="index" class="news-item" @click="testDetail(item.id)">
					<text class="news-id">ID: {{ item.id }}</text>
					<text class="news-title">{{ item.title }}</text>
					<text class="news-category">{{ item.category }}</text>
				</view>
			</view>
		</view>
		
		<view class="actions">
			<button class="action-btn primary" @click="initData">初始化数据</button>
			<button class="action-btn" @click="refreshData">刷新数据</button>
			<button class="action-btn" @click="goBack">返回</button>
		</view>
	</view>
</template>

<script>
import OfflineDataManager from '@/utils/offlineData.js'
import FeedbackUtils from '@/utils/feedback.js'

export default {
	data() {
		return {
			newsCount: 0,
			dataStatus: '检测中...',
			lastTest: '未测试',
			newsList: []
		}
	},
	
	onLoad() {
		this.checkData()
	},
	
	methods: {
		checkData() {
			try {
				// 检查离线数据
				const newsData = uni.getStorageSync('offline_news') || []
				this.newsCount = newsData.length
				this.newsList = newsData
				
				if (newsData.length > 0) {
					this.dataStatus = '✅ 数据正常'
				} else {
					this.dataStatus = '❌ 数据为空'
				}
			} catch (error) {
				this.dataStatus = '❌ 检查失败'
				console.error('数据检查失败:', error)
			}
		},
		
		testDetail(id) {
			try {
				this.lastTest = `测试ID: ${id} - ${new Date().toLocaleTimeString()}`
				
				uni.navigateTo({
					url: `/pages/news/detail?id=${id}`,
					success: () => {
						FeedbackUtils.showSuccess(`成功跳转到详情页 ID=${id}`)
					},
					fail: (error) => {
						FeedbackUtils.showError(`跳转失败: ${error.errMsg}`)
						console.error('跳转详情页失败:', error)
					}
				})
			} catch (error) {
				FeedbackUtils.showError('测试失败')
				console.error('测试详情页失败:', error)
			}
		},
		
		initData() {
			try {
				OfflineDataManager.initOfflineData()
				this.checkData()
				FeedbackUtils.showSuccess('数据初始化成功')
			} catch (error) {
				FeedbackUtils.showError('数据初始化失败')
				console.error('数据初始化失败:', error)
			}
		},
		
		refreshData() {
			this.checkData()
			FeedbackUtils.showSuccess('数据已刷新')
		},
		
		goBack() {
			uni.navigateBack()
		}
	}
}
</script>

<style scoped>
.test-container {
	padding: 20rpx;
	background: #f5f5f5;
	min-height: 100vh;
}

.header {
	text-align: center;
	margin-bottom: 40rpx;
}

.title {
	font-size: 40rpx;
	font-weight: bold;
	color: #333;
}

.test-section {
	background: white;
	border-radius: 16rpx;
	padding: 24rpx;
	margin-bottom: 24rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 20rpx;
	display: block;
}

.test-item {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 16rpx;
	padding-bottom: 16rpx;
	border-bottom: 1rpx solid #eee;
}

.test-item:last-child {
	border-bottom: none;
}

.test-label {
	font-size: 28rpx;
	color: #666;
}

.test-btn {
	background: #ff8a00;
	color: white;
	border: none;
	border-radius: 12rpx;
	padding: 16rpx 32rpx;
	font-size: 26rpx;
}

.data-info {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 16rpx;
}

.info-label {
	font-size: 28rpx;
	color: #666;
}

.info-value {
	font-size: 28rpx;
	color: #333;
	font-weight: 500;
}

.news-list {
	max-height: 400rpx;
	overflow-y: auto;
}

.no-news {
	text-align: center;
	color: #999;
	font-size: 26rpx;
	padding: 40rpx;
}

.news-item {
	background: #f9f9f9;
	border-radius: 12rpx;
	padding: 16rpx;
	margin-bottom: 12rpx;
	border-left: 4rpx solid #ff8a00;
}

.news-id {
	font-size: 22rpx;
	color: #ff8a00;
	font-weight: bold;
	display: block;
	margin-bottom: 8rpx;
}

.news-title {
	font-size: 28rpx;
	color: #333;
	display: block;
	margin-bottom: 8rpx;
	font-weight: 500;
}

.news-category {
	font-size: 24rpx;
	color: #666;
	background: rgba(255, 138, 0, 0.1);
	padding: 4rpx 12rpx;
	border-radius: 8rpx;
	display: inline-block;
}

.actions {
	display: flex;
	gap: 20rpx;
	justify-content: center;
}

.action-btn {
	background: #6b7280;
	color: white;
	border: none;
	border-radius: 12rpx;
	padding: 20rpx 40rpx;
	font-size: 28rpx;
}

.action-btn.primary {
	background: #ff8a00;
}
</style>
