<template>
	<view class="container">
		<!-- 头部横幅 -->
		<view class="header-banner">
			<view class="banner-content">
				<view class="banner-text">
					<text class="banner-title">家庭床位</text>
					<text class="banner-subtitle">专业护理服务，居家养老新选择</text>
				</view>
				<view class="banner-icon">
					<Icon name="hotel-bed-line" size="80rpx" color="white"></Icon>
				</view>
			</view>
		</view>

		<!-- 服务介绍 -->
		<view class="section">
			<view class="section-header">
				<text class="section-title">服务介绍</text>
				<text class="section-subtitle">什么是家庭床位服务</text>
			</view>
			<view class="intro-card">
				<view class="intro-content">
					<text class="intro-text">家庭床位是指在老年人家中设置具备专业照护功能的床位，配备必要的设施设备，由专业服务机构提供规范化、专业化的养老服务。</text>
				</view>
				<view class="intro-features">
					<view class="feature-item">
						<Icon name="home-gear-line" size="32rpx" color="#4caf50"></Icon>
						<text class="feature-text">居家环境</text>
					</view>
					<view class="feature-item">
						<Icon name="user-heart-line" size="32rpx" color="#2196f3"></Icon>
						<text class="feature-text">专业护理</text>
					</view>
					<view class="feature-item">
						<Icon name="time-line" size="32rpx" color="#ff9800"></Icon>
						<text class="feature-text">24小时服务</text>
					</view>
					<view class="feature-item">
						<Icon name="money-cny-circle-line" size="32rpx" color="#e91e63"></Icon>
						<text class="feature-text">费用合理</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 服务内容 -->
		<view class="section">
			<view class="section-header">
				<text class="section-title">服务内容</text>
				<text class="section-subtitle">全方位的专业护理服务</text>
			</view>
			<view class="service-grid">
				<view class="service-item">
					<view class="service-icon medical">
						<Icon name="health-book-line" size="48rpx" color="white"></Icon>
					</view>
					<text class="service-title">医疗护理</text>
					<text class="service-desc">专业医护人员提供基础医疗护理服务</text>
				</view>
				<view class="service-item">
					<view class="service-icon daily">
						<Icon name="heart-3-line" size="48rpx" color="white"></Icon>
					</view>
					<text class="service-title">生活照料</text>
					<text class="service-desc">协助日常生活起居，提供贴心照护</text>
				</view>
				<view class="service-item">
					<view class="service-icon rehab">
						<Icon name="run-line" size="48rpx" color="white"></Icon>
					</view>
					<text class="service-title">康复训练</text>
					<text class="service-desc">制定个性化康复计划，促进身体恢复</text>
				</view>
				<view class="service-item">
					<view class="service-icon mental">
						<Icon name="emotion-happy-line" size="48rpx" color="white"></Icon>
					</view>
					<text class="service-title">心理关怀</text>
					<text class="service-desc">提供心理疏导，关注精神健康</text>
				</view>
			</view>
		</view>

		<!-- 申请条件 -->
		<view class="section">
			<view class="section-header">
				<text class="section-title">申请条件</text>
				<text class="section-subtitle">符合以下条件可申请家庭床位</text>
			</view>
			<view class="condition-list">
				<view class="condition-item">
					<view class="condition-number">1</view>
					<view class="condition-content">
						<text class="condition-title">年龄要求</text>
						<text class="condition-desc">年满60周岁的老年人</text>
					</view>
				</view>
				<view class="condition-item">
					<view class="condition-number">2</view>
					<view class="condition-content">
						<text class="condition-title">身体状况</text>
						<text class="condition-desc">需要长期护理或失能半失能老人</text>
					</view>
				</view>
				<view class="condition-item">
					<view class="condition-number">3</view>
					<view class="condition-content">
						<text class="condition-title">居住条件</text>
						<text class="condition-desc">具备基本的居住条件和安全环境</text>
					</view>
				</view>
				<view class="condition-item">
					<view class="condition-number">4</view>
					<view class="condition-content">
						<text class="condition-title">家属配合</text>
						<text class="condition-desc">家属同意并配合护理服务工作</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 收费标准 -->
		<view class="section">
			<view class="section-header">
				<text class="section-title">收费标准</text>
				<text class="section-subtitle">透明合理的收费体系</text>
			</view>
			<view class="pricing-cards">
				<view class="pricing-card basic">
					<view class="pricing-header">
						<text class="pricing-title">基础护理</text>
						<text class="pricing-subtitle">适合轻度护理需求</text>
					</view>
					<view class="pricing-price">
						<text class="price-amount">¥120</text>
						<text class="price-unit">/天</text>
					</view>
					<view class="pricing-features">
						<text class="feature-item">• 基础生活照料</text>
						<text class="feature-item">• 健康监测</text>
						<text class="feature-item">• 紧急呼叫</text>
					</view>
				</view>
				<view class="pricing-card premium">
					<view class="pricing-header">
						<text class="pricing-title">专业护理</text>
						<text class="pricing-subtitle">适合中重度护理需求</text>
					</view>
					<view class="pricing-price">
						<text class="price-amount">¥200</text>
						<text class="price-unit">/天</text>
					</view>
					<view class="pricing-features">
						<text class="feature-item">• 专业医疗护理</text>
						<text class="feature-item">• 康复训练</text>
						<text class="feature-item">• 心理关怀</text>
						<text class="feature-item">• 24小时监护</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 底部操作 -->
		<view class="bottom-actions">
			<button class="action-btn secondary" @click="consultService">
				<Icon name="customer-service-2-line" size="32rpx" color="#ff8a00"></Icon>
				<text>咨询服务</text>
			</button>
			<button class="action-btn primary" @click="applyBed">
				<Icon name="add-line" size="32rpx" color="white"></Icon>
				<text>立即申请</text>
			</button>
		</view>
	</view>
</template>

<script>
import Icon from '@/components/Icon/Icon.vue'

export default {
	components: {
		Icon
	},
	methods: {
		consultService() {
			uni.makePhoneCall({
				phoneNumber: '************'
			});
		},
		applyBed() {
			uni.navigateTo({
				url: '/pages/bed/apply'
			});
		}
	}
}
</script>

<style scoped>
.container {
	background: linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%);
	min-height: 100vh;
	padding-bottom: 200rpx;
}

.header-banner {
	padding: 40rpx;
	margin-bottom: 40rpx;
}

.banner-content {
	background: rgba(255, 255, 255, 0.15);
	border-radius: 30rpx;
	padding: 40rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.banner-text {
	flex: 1;
}

.banner-title {
	font-size: 48rpx;
	font-weight: bold;
	color: white;
	display: block;
	margin-bottom: 15rpx;
}

.banner-subtitle {
	font-size: 28rpx;
	color: rgba(255, 255, 255, 0.9);
	display: block;
}

.banner-icon {
	margin-left: 30rpx;
}

.section {
	margin-bottom: 40rpx;
}

.section-header {
	padding: 0 40rpx 30rpx;
}

.section-title {
	font-size: 36rpx;
	font-weight: bold;
	color: white;
	display: block;
	margin-bottom: 10rpx;
}

.section-subtitle {
	font-size: 26rpx;
	color: rgba(255, 255, 255, 0.8);
	display: block;
}

.intro-card {
	background: white;
	border-radius: 30rpx;
	padding: 40rpx;
	margin: 0 40rpx;
	box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.intro-text {
	font-size: 28rpx;
	color: #333;
	line-height: 1.6;
	display: block;
	margin-bottom: 30rpx;
}

.intro-features {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	gap: 20rpx;
}

.feature-item {
	display: flex;
	align-items: center;
	gap: 15rpx;
	padding: 15rpx;
	background: #f8f9fa;
	border-radius: 15rpx;
}

.feature-text {
	font-size: 26rpx;
	color: #333;
	font-weight: 500;
}

.service-grid {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	gap: 30rpx;
	padding: 0 40rpx;
}

.service-item {
	background: white;
	border-radius: 30rpx;
	padding: 40rpx;
	text-align: center;
	box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.service-icon {
	width: 100rpx;
	height: 100rpx;
	border-radius: 20rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin: 0 auto 20rpx;
}

.service-icon.medical { background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%); }
.service-icon.daily { background: linear-gradient(135deg, #e91e63 0%, #c2185b 100%); }
.service-icon.rehab { background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%); }
.service-icon.mental { background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%); }

.service-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 10rpx;
}

.service-desc {
	font-size: 24rpx;
	color: #666;
	display: block;
	line-height: 1.4;
}

.condition-list {
	padding: 0 40rpx;
}

.condition-item {
	background: white;
	border-radius: 30rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	display: flex;
	align-items: center;
	box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.condition-number {
	width: 60rpx;
	height: 60rpx;
	background: linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	color: white;
	font-size: 28rpx;
	font-weight: bold;
	margin-right: 30rpx;
}

.condition-content {
	flex: 1;
}

.condition-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 8rpx;
}

.condition-desc {
	font-size: 26rpx;
	color: #666;
	display: block;
}

.pricing-cards {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	gap: 30rpx;
	padding: 0 40rpx;
}

.pricing-card {
	background: white;
	border-radius: 30rpx;
	padding: 40rpx;
	text-align: center;
	box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.pricing-card.premium {
	border: 3rpx solid #ff8a00;
}

.pricing-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 8rpx;
}

.pricing-subtitle {
	font-size: 24rpx;
	color: #666;
	display: block;
	margin-bottom: 30rpx;
}

.pricing-price {
	margin-bottom: 30rpx;
}

.price-amount {
	font-size: 48rpx;
	font-weight: bold;
	color: #ff8a00;
}

.price-unit {
	font-size: 24rpx;
	color: #999;
}

.pricing-features {
	text-align: left;
}

.pricing-features .feature-item {
	font-size: 26rpx;
	color: #666;
	display: block;
	margin-bottom: 10rpx;
	line-height: 1.4;
}

.bottom-actions {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background: white;
	padding: 30rpx 40rpx;
	display: flex;
	gap: 20rpx;
	box-shadow: 0 -4rpx 20rpx rgba(0,0,0,0.1);
}

.action-btn {
	flex: 1;
	height: 100rpx;
	border-radius: 20rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 15rpx;
	font-size: 32rpx;
	font-weight: 500;
}

.action-btn.primary {
	background: linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%);
	color: white;
	border: none;
}

.action-btn.secondary {
	background: white;
	color: #ff8a00;
	border: 2rpx solid #ff8a00;
}
</style>
