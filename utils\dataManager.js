/**
 * 前端数据模拟管理器
 * 提供完整的CRUD操作，使用localStorage进行数据持久化
 */

class DataManager {
	constructor() {
		this.storagePrefix = 'elderly_app_'
		this.initializeData()
	}

	/**
	 * 初始化默认数据
	 */
	initializeData() {
		// 初始化任务数据
		if (!this.getItem('tasks')) {
			this.setItem('tasks', this.getDefaultTasks())
		}

		// 初始化健康数据
		if (!this.getItem('health_records')) {
			this.setItem('health_records', this.getDefaultHealthRecords())
		}

		// 初始化用药提醒数据
		if (!this.getItem('medications')) {
			this.setItem('medications', this.getDefaultMedications())
		}

		// 初始化消息数据
		if (!this.getItem('messages')) {
			this.setItem('messages', this.getDefaultMessages())
		}

		// 初始化紧急联系人数据
		if (!this.getItem('emergency_contacts')) {
			this.setItem('emergency_contacts', this.getDefaultEmergencyContacts())
		}

		// 初始化体检预约数据
		if (!this.getItem('checkup_appointments')) {
			this.setItem('checkup_appointments', [])
		}

		// 初始化咨询记录数据
		if (!this.getItem('consultations')) {
			this.setItem('consultations', [])
		}

		// 初始化收藏数据
		if (!this.getItem('favorites')) {
			this.setItem('favorites', this.getDefaultFavorites())
		}
	}

	/**
	 * 通用存储操作
	 */
	setItem(key, value) {
		try {
			const fullKey = this.storagePrefix + key
			uni.setStorageSync(fullKey, JSON.stringify(value))
			return true
		} catch (error) {
			console.error('存储数据失败:', error)
			return false
		}
	}

	getItem(key) {
		try {
			const fullKey = this.storagePrefix + key
			const data = uni.getStorageSync(fullKey)
			return data ? JSON.parse(data) : null
		} catch (error) {
			console.error('读取数据失败:', error)
			return null
		}
	}

	removeItem(key) {
		try {
			const fullKey = this.storagePrefix + key
			uni.removeStorageSync(fullKey)
			return true
		} catch (error) {
			console.error('删除数据失败:', error)
			return false
		}
	}

	/**
	 * 生成唯一ID
	 */
	generateId() {
		return Date.now().toString(36) + Math.random().toString(36).substr(2)
	}

	/**
	 * 格式化日期
	 */
	formatDate(date = new Date()) {
		const year = date.getFullYear()
		const month = String(date.getMonth() + 1).padStart(2, '0')
		const day = String(date.getDate()).padStart(2, '0')
		return `${year}-${month}-${day}`
	}

	/**
	 * 格式化时间
	 */
	formatTime(date = new Date()) {
		const hours = String(date.getHours()).padStart(2, '0')
		const minutes = String(date.getMinutes()).padStart(2, '0')
		return `${hours}:${minutes}`
	}

	/**
	 * 格式化日期时间
	 */
	formatDateTime(date = new Date()) {
		return `${this.formatDate(date)} ${this.formatTime(date)}`
	}

	/**
	 * 默认任务数据
	 */
	getDefaultTasks() {
		const now = new Date()
		const tomorrow = new Date(now.getTime() + 24 * 60 * 60 * 1000)
		
		return [
			{
				id: this.generateId(),
				title: '晨练',
				description: '每日晨练，保持身体健康',
				category: '健康',
				priority: 'medium',
				status: 'pending',
				dueDate: this.formatDate(now),
				dueTime: '08:00',
				location: '社区公园',
				reminder: true,
				reminderTime: 15,
				createdAt: this.formatDateTime(now),
				updatedAt: this.formatDateTime(now)
			},
			{
				id: this.generateId(),
				title: '买菜',
				description: '购买今日所需食材',
				category: '生活',
				priority: 'high',
				status: 'pending',
				dueDate: this.formatDate(now),
				dueTime: '10:00',
				location: '菜市场',
				reminder: true,
				reminderTime: 30,
				createdAt: this.formatDateTime(now),
				updatedAt: this.formatDateTime(now)
			},
			{
				id: this.generateId(),
				title: '医院复查',
				description: '定期体检复查',
				category: '医疗',
				priority: 'high',
				status: 'pending',
				dueDate: this.formatDate(tomorrow),
				dueTime: '14:00',
				location: '市人民医院',
				reminder: true,
				reminderTime: 60,
				createdAt: this.formatDateTime(now),
				updatedAt: this.formatDateTime(now)
			}
		]
	}

	/**
	 * 默认健康记录数据
	 */
	getDefaultHealthRecords() {
		const now = new Date()
		const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000)
		const twoDaysAgo = new Date(now.getTime() - 2 * 24 * 60 * 60 * 1000)

		return [
			{
				id: this.generateId(),
				type: '血压',
				value: '125/82',
				systolic: 125,
				diastolic: 82,
				unit: 'mmHg',
				status: 'normal',
				date: this.formatDate(now),
				time: this.formatTime(now),
				note: '晨起测量',
				createdAt: this.formatDateTime(now)
			},
			{
				id: this.generateId(),
				type: '血糖',
				value: '5.8',
				unit: 'mmol/L',
				timing: '空腹',
				status: 'normal',
				date: this.formatDate(yesterday),
				time: '07:30',
				note: '空腹血糖',
				createdAt: this.formatDateTime(yesterday)
			},
			{
				id: this.generateId(),
				type: '体重',
				value: '65.2',
				unit: 'kg',
				status: 'normal',
				date: this.formatDate(twoDaysAgo),
				time: '19:00',
				note: '',
				createdAt: this.formatDateTime(twoDaysAgo)
			}
		]
	}

	/**
	 * 默认用药提醒数据
	 */
	getDefaultMedications() {
		return [
			{
				id: this.generateId(),
				name: '阿司匹林肠溶片',
				specification: '100mg/片',
				dosage: '每次1片',
				schedule: '每日1次，早8:00',
				times: ['08:00'],
				note: '饭后服用',
				startDate: this.formatDate(new Date()),
				endDate: '',
				reminder: true,
				createdAt: this.formatDateTime(new Date())
			},
			{
				id: this.generateId(),
				name: '降压药',
				specification: '5mg/片',
				dosage: '每次1片',
				schedule: '每日2次，早8:00、晚18:00',
				times: ['08:00', '18:00'],
				note: '饭前服用',
				startDate: this.formatDate(new Date()),
				endDate: '',
				reminder: true,
				createdAt: this.formatDateTime(new Date())
			}
		]
	}

	/**
	 * 默认消息数据
	 */
	getDefaultMessages() {
		const now = new Date()
		const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000)

		return [
			{
				id: this.generateId(),
				type: 'system',
				title: '系统升级通知',
				content: '智慧养老系统将于今晚进行升级维护，预计耗时2小时。',
				isRead: false,
				priority: 'high',
				createdAt: this.formatDateTime(now)
			},
			{
				id: this.generateId(),
				type: 'health',
				title: '健康提醒',
				content: '您今日还未记录血压数据，请及时测量并记录。',
				isRead: false,
				priority: 'medium',
				createdAt: this.formatDateTime(yesterday)
			},
			{
				id: this.generateId(),
				type: 'service',
				title: '服务通知',
				content: '您预约的体检服务将于明日进行，请提前做好准备。',
				isRead: true,
				priority: 'medium',
				createdAt: this.formatDateTime(yesterday)
			}
		]
	}

	/**
	 * 默认紧急联系人数据
	 */
	getDefaultEmergencyContacts() {
		return [
			{
				id: this.generateId(),
				name: '张女士',
				relation: '女儿',
				phone: '13888888888',
				isDefault: true,
				createdAt: this.formatDateTime(new Date())
			},
			{
				id: this.generateId(),
				name: '李先生',
				relation: '儿子',
				phone: '13999999999',
				isDefault: false,
				createdAt: this.formatDateTime(new Date())
			}
		]
	}

	/**
	 * 模拟网络延迟
	 */
	async simulateNetworkDelay(min = 300, max = 1000) {
		const delay = Math.random() * (max - min) + min
		return new Promise(resolve => setTimeout(resolve, delay))
	}

	/**
	 * 模拟网络错误
	 */
	simulateNetworkError(errorRate = 0.05) {
		return Math.random() < errorRate
	}

	/**
	 * 默认收藏数据
	 */
	getDefaultFavorites() {
		return [
			{
				id: this.generateId(),
				targetId: 'service_001',
				type: 'service',
				title: '专业居家护理服务',
				description: '24小时专业护理师上门服务，提供生活照料和医疗护理',
				image: '/picture/service_home_care.jpg',
				icon: 'heart-3-line',
				price: '120.00',
				tags: ['专业', '24小时', '上门'],
				favoriteTime: this.formatDateTime(new Date(Date.now() - 86400000)) // 1天前
			},
			{
				id: this.generateId(),
				targetId: 'news_001',
				type: 'news',
				title: '老年人健康饮食指南',
				description: '专家推荐的老年人营养搭配方案，科学饮食更健康',
				image: '/picture/news_health.jpg',
				icon: 'article-line',
				tags: ['健康', '饮食', '营养'],
				favoriteTime: this.formatDateTime(new Date(Date.now() - 172800000)) // 2天前
			},
			{
				id: this.generateId(),
				targetId: 'service_002',
				type: 'service',
				title: '康复训练服务',
				description: '专业物理治疗师指导，个性化康复训练方案',
				image: '/picture/service_rehabilitation.jpg',
				icon: 'heart-pulse-line',
				price: '200.00',
				tags: ['康复', '专业', '个性化'],
				favoriteTime: this.formatDateTime(new Date(Date.now() - 259200000)) // 3天前
			},
			{
				id: this.generateId(),
				targetId: 'institution_001',
				type: 'institution',
				title: '阳光老年服务中心',
				description: '综合性老年服务机构，提供日间照料、康复训练等服务',
				image: '/picture/nursing_home_1.jpg',
				icon: 'building-line',
				tags: ['综合', '日间照料', '康复'],
				favoriteTime: this.formatDateTime(new Date(Date.now() - 345600000)) // 4天前
			},
			{
				id: this.generateId(),
				targetId: 'equipment_001',
				type: 'equipment',
				title: '多功能护理床',
				description: '电动升降护理床，适合长期卧床老人使用',
				image: '/picture/equipment_care_bed.jpg',
				icon: 'hotel-bed-line',
				price: '300.00',
				tags: ['电动', '护理', '舒适'],
				favoriteTime: this.formatDateTime(new Date(Date.now() - 432000000)) // 5天前
			}
		]
	}
}

// 创建全局实例
const dataManager = new DataManager()

export default dataManager
