/**
 * 反馈工具类
 * 提供统一的用户反馈方法，包括提示、确认、加载等
 * 支持适老化和跨平台兼容
 */

// 反馈配置
const FEEDBACK_CONFIG = {
  // 适老化模式配置
  elderly: {
    vibrateDuration: 200,
    toastDuration: 3000,
    modalFontSize: '32rpx',
    voiceEnabled: true
  },
  // 普通模式配置
  normal: {
    vibrateDuration: 100,
    toastDuration: 2000,
    modalFontSize: '28rpx',
    voiceEnabled: false
  }
}

class FeedbackUtils {
  /**
   * 获取当前模式配置
   */
  static getCurrentConfig() {
    try {
      const isElderlyMode = uni.getStorageSync('elderlyMode') || false
      return isElderlyMode ? FEEDBACK_CONFIG.elderly : FEEDBACK_CONFIG.normal
    } catch (e) {
      return FEEDBACK_CONFIG.normal
    }
  }

  /**
   * 语音播报（适老化功能）
   */
  static speakText(text) {
    const config = this.getCurrentConfig()
    if (!config.voiceEnabled) return

    // #ifdef APP-PLUS
    try {
      if (plus.speech) {
        plus.speech.startSpeech({
          content: text,
          volume: 0.5,
          speed: 0.8
        })
      }
    } catch (e) {
      console.log('语音播报不可用:', e)
    }
    // #endif
  }
	/**
	 * 显示成功提示
	 * @param {string} title 提示文字
	 * @param {number} duration 显示时长，默认根据模式配置
	 */
	static showSuccess(title = '操作成功', duration = null) {
		const config = this.getCurrentConfig()
		const finalDuration = duration || config.toastDuration

		uni.showToast({
			title,
			icon: 'success',
			duration: finalDuration,
			mask: true
		})

		// 适老化语音播报
		this.speakText(title)

		// 适老化震动反馈
		this.vibrate('light')
	}

	/**
	 * 显示错误提示
	 * @param {string} title 提示文字
	 * @param {number} duration 显示时长，默认根据模式配置
	 */
	static showError(title = '操作失败', duration = null) {
		const config = this.getCurrentConfig()
		const finalDuration = duration || (config.toastDuration + 1000) // 错误提示显示更久

		uni.showToast({
			title,
			icon: 'error',
			duration: finalDuration,
			mask: true
		})

		// 适老化语音播报
		this.speakText(title)

		// 适老化震动反馈（更强）
		this.vibrate('medium')
	}

	/**
	 * 显示警告提示
	 * @param {string} title 提示文字
	 * @param {number} duration 显示时长，默认3000ms
	 */
	static showWarning(title = '警告', duration = 3000) {
		uni.showToast({
			title,
			icon: 'none',
			duration,
			mask: true
		})
	}

	/**
	 * 显示普通提示
	 * @param {string} title 提示文字
	 * @param {number} duration 显示时长，默认2000ms
	 */
	static showInfo(title, duration = 2000) {
		uni.showToast({
			title,
			icon: 'none',
			duration,
			mask: true
		})
	}

	/**
	 * 显示加载提示
	 * @param {string} title 加载文字
	 * @param {boolean} mask 是否显示透明蒙层
	 */
	static showLoading(title = '加载中...', mask = true) {
		uni.showLoading({
			title,
			mask
		})
	}

	/**
	 * 隐藏加载提示
	 */
	static hideLoading() {
		uni.hideLoading()
	}

	/**
	 * 显示确认对话框
	 * @param {string} content 对话框内容
	 * @param {string} title 对话框标题
	 * @param {Object} options 其他选项
	 * @returns {Promise<boolean>} 用户是否确认
	 */
	static showConfirm(content, title = '提示', options = {}) {
		return new Promise((resolve) => {
			uni.showModal({
				title,
				content,
				confirmText: options.confirmText || '确定',
				cancelText: options.cancelText || '取消',
				confirmColor: options.confirmColor || '#ff8a00',
				cancelColor: options.cancelColor || '#666666',
				success: (res) => {
					resolve(res.confirm)
				},
				fail: () => {
					resolve(false)
				}
			})
		})
	}

	/**
	 * 显示操作菜单
	 * @param {Array} itemList 菜单项列表
	 * @param {string} title 菜单标题
	 * @returns {Promise<number>} 用户选择的菜单项索引，取消返回-1
	 */
	static showActionSheet(itemList, title = '') {
		return new Promise((resolve) => {
			uni.showActionSheet({
				itemList,
				title,
				success: (res) => {
					resolve(res.tapIndex)
				},
				fail: () => {
					resolve(-1)
				}
			})
		})
	}

	/**
	 * 震动反馈
	 * @param {string} type 震动类型：'heavy', 'medium', 'light'
	 */
	static vibrate(type = 'light') {
		try {
			const config = this.getCurrentConfig()

			// 适老化模式使用更强的震动
			if (config === FEEDBACK_CONFIG.elderly) {
				if (type === 'light') type = 'medium'
				if (type === 'medium') type = 'heavy'
			}

			if (type === 'heavy') {
				uni.vibrateLong()
			} else {
				uni.vibrateShort({
					type
				})
			}
		} catch (error) {
			console.warn('设备不支持震动反馈:', error)
		}
	}

	/**
	 * 显示网络错误提示
	 * @param {string} message 自定义错误信息
	 */
	static showNetworkError(message = '网络连接异常，请检查网络设置') {
		this.showError(message, 3000)
	}

	/**
	 * 显示权限错误提示
	 * @param {string} permission 权限名称
	 */
	static showPermissionError(permission = '相关功能') {
		this.showError(`您没有${permission}的权限，请联系管理员`, 3000)
	}

	/**
	 * 显示数据为空提示
	 * @param {string} dataType 数据类型
	 */
	static showEmptyData(dataType = '数据') {
		this.showInfo(`暂无${dataType}`, 2000)
	}

	/**
	 * 显示操作成功并延迟执行回调
	 * @param {string} message 成功信息
	 * @param {Function} callback 回调函数
	 * @param {number} delay 延迟时间，默认1500ms
	 */
	static showSuccessWithCallback(message = '操作成功', callback, delay = 1500) {
		this.showSuccess(message)
		if (typeof callback === 'function') {
			setTimeout(callback, delay)
		}
	}

	/**
	 * 显示删除确认对话框
	 * @param {string} itemName 要删除的项目名称
	 * @returns {Promise<boolean>} 用户是否确认删除
	 */
	static showDeleteConfirm(itemName = '此项') {
		return this.showConfirm(
			`确定要删除${itemName}吗？删除后无法恢复。`,
			'确认删除',
			{
				confirmText: '删除',
				confirmColor: '#ff4444'
			}
		)
	}

	/**
	 * 显示退出确认对话框
	 * @param {string} action 退出的动作描述
	 * @returns {Promise<boolean>} 用户是否确认退出
	 */
	static showExitConfirm(action = '退出') {
		return this.showConfirm(
			`确定要${action}吗？未保存的内容将会丢失。`,
			'确认退出',
			{
				confirmText: '退出',
				confirmColor: '#ff8a00'
			}
		)
	}

	/**
	 * 显示保存确认对话框
	 * @returns {Promise<boolean>} 用户是否确认保存
	 */
	static showSaveConfirm() {
		return this.showConfirm(
			'是否保存当前修改？',
			'保存确认',
			{
				confirmText: '保存',
				confirmColor: '#4caf50'
			}
		)
	}

	/**
	 * 显示联系客服对话框
	 * @param {string} phone 客服电话
	 * @returns {Promise<boolean>} 用户是否选择拨打电话
	 */
	static showContactService(phone = '************') {
		return this.showConfirm(
			`如需帮助，请联系客服：${phone}`,
			'联系客服',
			{
				confirmText: '拨打电话',
				cancelText: '稍后联系'
			}
		)
	}

	/**
	 * 拨打电话
	 * @param {string} phoneNumber 电话号码
	 */
	static makePhoneCall(phoneNumber) {
		uni.makePhoneCall({
			phoneNumber,
			success: () => {
				console.log('拨打电话成功')
			},
			fail: (error) => {
				console.error('拨打电话失败:', error)
				this.showError('拨打电话失败，请手动拨打')
			}
		})
	}

	/**
	 * 复制文本到剪贴板
	 * @param {string} text 要复制的文本
	 * @param {string} successMessage 复制成功提示
	 */
	static copyToClipboard(text, successMessage = '已复制到剪贴板') {
		uni.setClipboardData({
			data: text,
			success: () => {
				this.showSuccess(successMessage)
			},
			fail: () => {
				this.showError('复制失败')
			}
		})
	}

	/**
	 * 显示分享菜单
	 * @param {Object} shareData 分享数据
	 */
	static showShareMenu(shareData = {}) {
		const defaultData = {
			title: '智慧养老',
			path: '/pages/home/<USER>',
			imageUrl: '/static/logo.png'
		}
		
		const finalData = { ...defaultData, ...shareData }
		
		// 这里可以根据平台显示不同的分享选项
		this.showActionSheet(['微信好友', '朋友圈', '复制链接'], '分享到')
			.then(index => {
				switch (index) {
					case 0:
						// 分享到微信好友
						console.log('分享到微信好友', finalData)
						break
					case 1:
						// 分享到朋友圈
						console.log('分享到朋友圈', finalData)
						break
					case 2:
						// 复制链接
						this.copyToClipboard(finalData.path || '', '链接已复制')
						break
				}
			})
	}

	/**
	 * 智能网络检测，优先尝试在线操作，失败时自动切换到离线操作
	 * @param {Function} onlineOperation 在线操作函数
	 * @param {Function} offlineOperation 离线操作函数
	 * @returns {Promise} 操作结果
	 */
	static async withNetworkCheck(onlineOperation, offlineOperation) {
		try {
			// 先检测网络状态
			const networkStatus = await NetworkUtils.checkNetworkStatus()

			if (networkStatus.isConnected) {
				// 网络连接正常，尝试在线操作
				try {
					return await onlineOperation()
				} catch (error) {
					// 在线操作失败，提示并切换到离线操作
					this.showWarning('网络请求失败，切换到离线模式')
					return await offlineOperation()
				}
			} else {
				// 网络未连接，直接使用离线操作
				this.showInfo('当前无网络连接，使用离线模式')
				return await offlineOperation()
			}
		} catch (error) {
			// 网络检测失败，使用离线操作作为备用方案
			console.error('网络检测失败:', error)
			this.showWarning('网络检测失败，使用离线模式')
			return await offlineOperation()
		}
	}

	/**
	 * 轻度震动反馈
	 */
	static lightFeedback() {
		this.vibrate('light')
	}

	/**
	 * 中度震动反馈
	 */
	static mediumFeedback() {
		this.vibrate('medium')
	}

	/**
	 * 重度震动反馈
	 */
	static heavyFeedback() {
		this.vibrate('heavy')
	}
}

/**
 * 网络工具类
 * 提供网络状态检测和模拟网络请求功能
 */
class NetworkUtils {
	/**
	 * 检测网络状态
	 * @returns {Promise<Object>} 网络状态信息
	 */
	static async checkNetworkStatus() {
		return new Promise((resolve) => {
			uni.getNetworkType({
				success: (res) => {
					resolve({
						isConnected: res.networkType !== 'none',
						networkType: res.networkType
					})
				},
				fail: () => {
					resolve({
						isConnected: false,
						networkType: 'unknown'
					})
				}
			})
		})
	}

	/**
	 * 模拟网络请求
	 * @param {number} failureRate 失败率 (0-1)
	 * @param {number} delay 延迟时间（毫秒）
	 * @returns {Promise} 请求结果
	 */
	static async simulateNetworkRequest(failureRate = 0.1, delay = 1000) {
		await new Promise(resolve => setTimeout(resolve, delay))

		if (Math.random() < failureRate) {
			throw new Error('网络请求失败')
		}

		return { success: true, data: 'Mock data' }
	}
}

export default FeedbackUtils
export { NetworkUtils }
