<template>
	<view class="container">
		<!-- 页面头部 -->
		<PageHeader title="编辑个人资料">
			<template #actions>
				<InteractiveButton
					type="primary"
					size="small"
					text="保存"
					icon="save-line"
					:loading="saving"
					@click="saveProfile"
				></InteractiveButton>
			</template>
		</PageHeader>

		<!-- 头像设置 -->
		<view class="avatar-section">
			<view class="avatar-container" @click="changeAvatar">
				<image 
					v-if="profileData.avatar" 
					:src="profileData.avatar" 
					class="avatar-image" 
					mode="aspectFill"
				></image>
				<view v-else class="avatar-placeholder">
					<Icon name="user-line" size="80rpx" color="#ccc"></Icon>
				</view>
				<view class="avatar-edit">
					<Icon name="camera-line" size="32rpx" color="#fff"></Icon>
				</view>
			</view>
			<text class="avatar-tip">点击更换头像</text>
		</view>

		<!-- 基本信息 -->
		<view class="form-section">
			<text class="section-title">基本信息</text>
			
			<view class="form-item">
				<text class="form-label">姓名</text>
				<input 
					class="form-input" 
					v-model="profileData.name" 
					placeholder="请输入姓名"
				/>
			</view>

			<view class="form-item">
				<text class="form-label">性别</text>
				<view class="gender-options">
					<view 
						class="gender-option"
						:class="{ active: profileData.gender === 'male' }"
						@click="selectGender('male')"
					>
						<Icon name="men-line" size="32rpx" :color="profileData.gender === 'male' ? '#fff' : '#ff8a00'"></Icon>
						<text class="gender-text">男</text>
					</view>
					<view 
						class="gender-option"
						:class="{ active: profileData.gender === 'female' }"
						@click="selectGender('female')"
					>
						<Icon name="women-line" size="32rpx" :color="profileData.gender === 'female' ? '#fff' : '#ff8a00'"></Icon>
						<text class="gender-text">女</text>
					</view>
				</view>
			</view>

			<view class="form-item">
				<text class="form-label">出生日期</text>
				<picker mode="date" :value="profileData.birthday" @change="onBirthdayChange">
					<view class="picker-input">
						<text class="picker-text">{{ profileData.birthday || '请选择出生日期' }}</text>
						<Icon name="calendar-line" size="24rpx" color="#999"></Icon>
					</view>
				</picker>
			</view>

			<view class="form-item">
				<text class="form-label">身份证号</text>
				<input 
					class="form-input" 
					v-model="profileData.idCard" 
					placeholder="请输入身份证号"
				/>
			</view>

			<view class="form-item">
				<text class="form-label">联系电话</text>
				<input 
					class="form-input" 
					v-model="profileData.phone" 
					placeholder="请输入联系电话"
					type="number"
				/>
			</view>
		</view>

		<!-- 居住信息 -->
		<view class="form-section">
			<text class="section-title">居住信息</text>
			
			<view class="form-item">
				<text class="form-label">居住地址</text>
				<textarea 
					class="form-textarea" 
					v-model="profileData.address" 
					placeholder="请输入详细居住地址"
				></textarea>
			</view>

			<view class="form-item">
				<text class="form-label">居住状况</text>
				<view class="living-options">
					<view 
						v-for="(option, index) in livingOptions" 
						:key="index"
						class="living-option"
						:class="{ active: profileData.livingStatus === option.value }"
						@click="selectLivingStatus(option.value)"
					>
						<text class="option-text">{{ option.label }}</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 健康信息 -->
		<view class="form-section">
			<text class="section-title">健康信息</text>
			
			<view class="form-item">
				<text class="form-label">健康状况</text>
				<view class="health-options">
					<view 
						v-for="(option, index) in healthOptions" 
						:key="index"
						class="health-option"
						:class="{ active: profileData.healthStatus === option.value }"
						@click="selectHealthStatus(option.value)"
					>
						<text class="option-text">{{ option.label }}</text>
					</view>
				</view>
			</view>

			<view class="form-item">
				<text class="form-label">慢性疾病</text>
				<textarea 
					class="form-textarea" 
					v-model="profileData.chronicDiseases" 
					placeholder="请输入慢性疾病情况（如无请填写"无"）"
				></textarea>
			</view>

			<view class="form-item">
				<text class="form-label">过敏史</text>
				<textarea 
					class="form-textarea" 
					v-model="profileData.allergies" 
					placeholder="请输入过敏史（如无请填写"无"）"
				></textarea>
			</view>
		</view>

		<!-- 其他信息 -->
		<view class="form-section">
			<text class="section-title">其他信息</text>
			
			<view class="form-item">
				<text class="form-label">个人简介</text>
				<textarea 
					class="form-textarea" 
					v-model="profileData.bio" 
					placeholder="请简单介绍一下自己"
				></textarea>
			</view>

			<view class="form-item">
				<text class="form-label">兴趣爱好</text>
				<input 
					class="form-input" 
					v-model="profileData.hobbies" 
					placeholder="请输入兴趣爱好，用逗号分隔"
				/>
			</view>
		</view>
	</view>
</template>

<script>
import InteractiveButton from '@/components/InteractiveButton/InteractiveButton.vue'
import Icon from '@/components/Icon/Icon.vue'
import PageHeader from '@/components/PageHeader/PageHeader.vue'
import FeedbackUtils from '@/utils/feedback.js'

export default {
	components: {
		InteractiveButton,
		Icon,
		PageHeader
	},
	data() {
		return {
			saving: false,
			profileData: {
				avatar: '',
				name: '',
				gender: '',
				birthday: '',
				idCard: '',
				phone: '',
				address: '',
				livingStatus: '',
				healthStatus: '',
				chronicDiseases: '',
				allergies: '',
				bio: '',
				hobbies: ''
			},
			
			// 居住状况选项
			livingOptions: [
				{ label: '独居', value: 'alone' },
				{ label: '与配偶同住', value: 'with_spouse' },
				{ label: '与子女同住', value: 'with_children' },
				{ label: '养老院', value: 'nursing_home' }
			],
			
			// 健康状况选项
			healthOptions: [
				{ label: '健康', value: 'healthy' },
				{ label: '一般', value: 'fair' },
				{ label: '较差', value: 'poor' },
				{ label: '需要护理', value: 'need_care' }
			]
		}
	},
	onLoad() {
		this.loadProfile();
	},
	methods: {
		// 加载个人资料
		loadProfile() {
			// 模拟加载个人资料数据
			this.profileData = {
				avatar: '', // 使用Icon组件显示默认头像
				name: '张大爷',
				gender: 'male',
				birthday: '1945-03-15',
				idCard: '110101194503151234',
				phone: '138****1234',
				address: '北京市朝阳区建国路88号',
				livingStatus: 'with_spouse',
				healthStatus: 'fair',
				chronicDiseases: '高血压、糖尿病',
				allergies: '无',
				bio: '退休教师，喜欢读书看报',
				hobbies: '读书,下棋,散步,听音乐'
			};
		},

		// 更换头像
		changeAvatar() {
			FeedbackUtils.lightFeedback();
			uni.chooseImage({
				count: 1,
				sizeType: ['compressed'],
				sourceType: ['album', 'camera'],
				success: (res) => {
					this.profileData.avatar = res.tempFilePaths[0];
					FeedbackUtils.showSuccess('头像更换成功');
				},
				fail: () => {
					FeedbackUtils.showError('头像更换失败');
				}
			});
		},

		// 选择性别
		selectGender(gender) {
			FeedbackUtils.lightFeedback();
			this.profileData.gender = gender;
		},

		// 出生日期选择
		onBirthdayChange(e) {
			this.profileData.birthday = e.detail.value;
		},

		// 选择居住状况
		selectLivingStatus(status) {
			FeedbackUtils.lightFeedback();
			this.profileData.livingStatus = status;
		},

		// 选择健康状况
		selectHealthStatus(status) {
			FeedbackUtils.lightFeedback();
			this.profileData.healthStatus = status;
		},

		// 保存个人资料
		async saveProfile() {
			// 表单验证
			if (!this.validateForm()) {
				return;
			}

			this.saving = true;
			FeedbackUtils.showLoading('保存中...');

			try {
				// 模拟保存个人资料
				await new Promise(resolve => setTimeout(resolve, 2000));
				
				FeedbackUtils.hideLoading();
				FeedbackUtils.showSuccess('个人资料保存成功');
				
				// 返回上一页
				setTimeout(() => {
					uni.navigateBack();
				}, 1500);
			} catch (error) {
				FeedbackUtils.hideLoading();
				FeedbackUtils.showError('保存失败，请重试');
			} finally {
				this.saving = false;
			}
		},

		// 表单验证
		validateForm() {
			if (!this.profileData.name) {
				FeedbackUtils.showError('请输入姓名');
				return false;
			}
			if (!this.profileData.gender) {
				FeedbackUtils.showError('请选择性别');
				return false;
			}
			if (!this.profileData.phone) {
				FeedbackUtils.showError('请输入联系电话');
				return false;
			}
			if (!this.profileData.address) {
				FeedbackUtils.showError('请输入居住地址');
				return false;
			}
			return true;
		}
	}
}
</script>

<style scoped>
.container {
	background-color: #f5f5f5;
	min-height: 100vh;
	padding-bottom: 100rpx;
}

.avatar-section {
	background: white;
	padding: 40rpx;
	margin: 20rpx;
	border-radius: 20rpx;
	text-align: center;
	box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.avatar-container {
	position: relative;
	width: 160rpx;
	height: 160rpx;
	margin: 0 auto 20rpx;
}

.avatar-image,
.avatar-placeholder {
	width: 100%;
	height: 100%;
	border-radius: 50%;
	background: #f0f0f0;
	display: flex;
	align-items: center;
	justify-content: center;
}

.avatar-edit {
	position: absolute;
	bottom: 0;
	right: 0;
	width: 50rpx;
	height: 50rpx;
	background: #ff8a00;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	border: 4rpx solid white;
}

.avatar-tip {
	font-size: 24rpx;
	color: #999;
}

.form-section {
	background: white;
	margin: 20rpx;
	border-radius: 20rpx;
	padding: 30rpx;
	box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.section-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 25rpx;
}

.form-item {
	margin-bottom: 25rpx;
}

.form-label {
	font-size: 28rpx;
	color: #333;
	display: block;
	margin-bottom: 10rpx;
}

.form-input,
.form-textarea {
	width: 100%;
	background: #f8f9fa;
	border: 1rpx solid #e0e0e0;
	border-radius: 10rpx;
	padding: 20rpx;
	font-size: 28rpx;
	color: #333;
}

.form-textarea {
	min-height: 120rpx;
}

.picker-input {
	display: flex;
	align-items: center;
	justify-content: space-between;
	background: #f8f9fa;
	border: 1rpx solid #e0e0e0;
	border-radius: 10rpx;
	padding: 20rpx;
}

.picker-text {
	font-size: 28rpx;
	color: #333;
}

.gender-options {
	display: flex;
	gap: 20rpx;
}

.gender-option {
	flex: 1;
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 10rpx;
	padding: 20rpx;
	border: 2rpx solid #e0e0e0;
	border-radius: 15rpx;
	background: #fff;
}

.gender-option.active {
	border-color: #ff8a00;
	background: linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%);
}

.gender-text {
	font-size: 26rpx;
	color: #333;
}

.gender-option.active .gender-text {
	color: white;
}

.living-options,
.health-options {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	gap: 15rpx;
}

.living-option,
.health-option {
	padding: 15rpx 20rpx;
	border: 2rpx solid #e0e0e0;
	border-radius: 25rpx;
	text-align: center;
	background: #fff;
}

.living-option.active,
.health-option.active {
	border-color: #ff8a00;
	background: rgba(255, 138, 0, 0.1);
}

.option-text {
	font-size: 26rpx;
	color: #333;
}
</style>
