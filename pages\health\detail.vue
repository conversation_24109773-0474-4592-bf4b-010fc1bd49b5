<template>
	<view class="container">
		<!-- 健康指标详情 -->
		<view class="health-detail-card">
			<view class="detail-header">
				<view class="indicator-icon" :style="{backgroundColor: healthDetail.color}">
					<Icon :name="healthDetail.icon" size="60rpx" color="white"></Icon>
				</view>
				<view class="indicator-info">
					<text class="indicator-name">{{healthDetail.name}}</text>
					<text class="indicator-desc">{{healthDetail.description}}</text>
				</view>
			</view>
			
			<view class="current-value">
				<text class="value-number">{{healthDetail.currentValue}}</text>
				<text class="value-unit">{{healthDetail.unit}}</text>
				<view class="value-status" :class="healthDetail.status">
					<text class="status-text">{{getStatusText(healthDetail.status)}}</text>
				</view>
			</view>
		</view>

		<!-- 数据趋势图表 -->
		<view class="chart-section">
			<view class="section-header">
				<text class="section-title">数据趋势</text>
				<view class="time-filter">
					<text 
						class="filter-item" 
						:class="{ active: activeFilter === item.key }"
						v-for="(item, index) in timeFilters" 
						:key="index"
						@click="selectTimeFilter(item.key)"
					>{{item.name}}</text>
				</view>
			</view>
			<view class="chart-container">
				<view class="chart-placeholder">
					<Icon name="bar-chart-line" size="80rpx" color="#ccc"></Icon>
					<text class="chart-text">数据图表展示区域</text>
				</view>
			</view>
		</view>

		<!-- 历史记录 -->
		<view class="history-section">
			<view class="section-header">
				<text class="section-title">历史记录</text>
				<text class="section-more" @click="viewAllHistory">查看全部</text>
			</view>
			<view class="history-list">
				<view class="history-item" v-for="(record, index) in historyRecords" :key="index">
					<view class="record-date">
						<text class="date-day">{{record.day}}</text>
						<text class="date-month">{{record.month}}</text>
					</view>
					<view class="record-content">
						<text class="record-value">{{record.value}} {{healthDetail.unit}}</text>
						<text class="record-time">{{record.time}}</text>
						<text class="record-note" v-if="record.note">{{record.note}}</text>
					</view>
					<view class="record-status" :class="record.status">
						<text class="status-text">{{getStatusText(record.status)}}</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 健康建议 -->
		<view class="advice-section">
			<view class="section-header">
				<text class="section-title">健康建议</text>
			</view>
			<view class="advice-list">
				<view class="advice-item" v-for="(advice, index) in healthAdvice" :key="index">
					<view class="advice-icon">
						<Icon name="lightbulb-line" size="32rpx" color="#ff9800"></Icon>
					</view>
					<view class="advice-content">
						<text class="advice-title">{{advice.title}}</text>
						<text class="advice-desc">{{advice.description}}</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 参考范围 -->
		<view class="reference-section">
			<view class="section-header">
				<text class="section-title">参考范围</text>
			</view>
			<view class="reference-ranges">
				<view class="range-item" v-for="(range, index) in referenceRanges" :key="index">
					<view class="range-label" :class="range.type">{{range.label}}</view>
					<text class="range-value">{{range.value}}</text>
				</view>
			</view>
		</view>

		<!-- 底部操作 -->
		<view class="bottom-actions">
			<button class="action-btn secondary" @click="recordData">
				<Icon name="add-line" size="32rpx" color="#ff8a00"></Icon>
				<text>记录数据</text>
			</button>
			<button class="action-btn primary" @click="consultDoctor">
				<Icon name="user-heart-line" size="32rpx" color="white"></Icon>
				<text>咨询医生</text>
			</button>
		</view>
	</view>
</template>

<script>
import Icon from '@/components/Icon/Icon.vue'

export default {
	components: {
		Icon
	},
	data() {
		return {
			activeFilter: 'week',
			timeFilters: [
				{ key: 'week', name: '一周' },
				{ key: 'month', name: '一月' },
				{ key: 'quarter', name: '三月' },
				{ key: 'year', name: '一年' }
			],
			healthDetail: {
				name: '血压',
				description: '收缩压/舒张压',
				currentValue: '120/80',
				unit: 'mmHg',
				icon: 'heart-pulse-line',
				color: '#e91e63',
				status: 'normal'
			},
			historyRecords: [
				{
					day: '18',
					month: '01月',
					value: '120/80',
					time: '08:30',
					status: 'normal',
					note: '晨起测量'
				},
				{
					day: '17',
					month: '01月',
					value: '125/85',
					time: '19:00',
					status: 'warning',
					note: '晚餐后测量'
				},
				{
					day: '16',
					month: '01月',
					value: '118/78',
					time: '08:15',
					status: 'normal'
				},
				{
					day: '15',
					month: '01月',
					value: '130/88',
					time: '20:30',
					status: 'warning',
					note: '运动后测量'
				}
			],
			healthAdvice: [
				{
					title: '饮食建议',
					description: '减少盐分摄入，多吃新鲜蔬菜水果，控制脂肪摄入'
				},
				{
					title: '运动建议',
					description: '每天进行30分钟有氧运动，如散步、游泳等'
				},
				{
					title: '生活习惯',
					description: '保持规律作息，避免熬夜，戒烟限酒'
				}
			],
			referenceRanges: [
				{
					label: '正常',
					value: '< 120/80 mmHg',
					type: 'normal'
				},
				{
					label: '正常高值',
					value: '120-139/80-89 mmHg',
					type: 'warning'
				},
				{
					label: '高血压',
					value: '≥ 140/90 mmHg',
					type: 'danger'
				}
			]
		}
	},
	onLoad(options) {
		if (options.type) {
			this.loadHealthDetail(options.type);
		}
	},
	methods: {
		loadHealthDetail(type) {
			// 根据类型加载不同的健康指标详情
			const detailMap = {
				'血压': {
					name: '血压',
					description: '收缩压/舒张压',
					currentValue: '120/80',
					unit: 'mmHg',
					icon: 'heart-pulse-line',
					color: '#e91e63',
					status: 'normal'
				},
				'血糖': {
					name: '血糖',
					description: '空腹血糖值',
					currentValue: '5.6',
					unit: 'mmol/L',
					icon: 'drop-line',
					color: '#2196f3',
					status: 'normal'
				},
				'心率': {
					name: '心率',
					description: '每分钟心跳次数',
					currentValue: '72',
					unit: 'bpm',
					icon: 'heart-line',
					color: '#f44336',
					status: 'normal'
				},
				'体重': {
					name: '体重',
					description: '身体重量',
					currentValue: '65.5',
					unit: 'kg',
					icon: 'scales-3-line',
					color: '#4caf50',
					status: 'normal'
				}
			};
			
			this.healthDetail = detailMap[type] || this.healthDetail;
		},
		selectTimeFilter(filter) {
			this.activeFilter = filter;
			// 重新加载图表数据
		},
		getStatusText(status) {
			const statusMap = {
				'normal': '正常',
				'warning': '偏高',
				'danger': '异常'
			};
			return statusMap[status] || '未知';
		},
		viewAllHistory() {
			uni.navigateTo({
				url: `/pages/health/history?type=${this.healthDetail.name}`
			});
		},
		recordData() {
			uni.navigateTo({
				url: `/pages/health/record?type=${this.healthDetail.name}`
			});
		},
		consultDoctor() {
			uni.navigateTo({
				url: '/pages/health/consultation'
			});
		}
	}
}
</script>

<style scoped>
.container {
	background: #f8f9fa;
	min-height: 100vh;
	padding-bottom: 200rpx;
}

.health-detail-card {
	background: white;
	margin: 40rpx;
	border-radius: 30rpx;
	padding: 40rpx;
	box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.detail-header {
	display: flex;
	align-items: center;
	margin-bottom: 40rpx;
}

.indicator-icon {
	width: 100rpx;
	height: 100rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 30rpx;
}

.indicator-info {
	flex: 1;
}

.indicator-name {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 10rpx;
}

.indicator-desc {
	font-size: 26rpx;
	color: #666;
	display: block;
}

.current-value {
	text-align: center;
	padding: 30rpx 0;
	border-top: 1rpx solid #f0f0f0;
}

.value-number {
	font-size: 72rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 10rpx;
}

.value-unit {
	font-size: 28rpx;
	color: #666;
	display: block;
	margin-bottom: 20rpx;
}

.value-status {
	display: inline-block;
	padding: 10rpx 30rpx;
	border-radius: 25rpx;
	font-size: 24rpx;
}

.value-status.normal {
	background: rgba(76, 175, 80, 0.1);
	color: #4caf50;
}

.value-status.warning {
	background: rgba(255, 152, 0, 0.1);
	color: #ff9800;
}

.value-status.danger {
	background: rgba(244, 67, 54, 0.1);
	color: #f44336;
}

.chart-section, .history-section, .advice-section, .reference-section {
	background: white;
	margin: 40rpx;
	border-radius: 30rpx;
	padding: 40rpx;
	box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.section-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 30rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.section-more {
	font-size: 26rpx;
	color: #ff8a00;
}

.time-filter {
	display: flex;
	gap: 20rpx;
}

.filter-item {
	padding: 10rpx 20rpx;
	border-radius: 20rpx;
	font-size: 24rpx;
	color: #666;
	background: #f8f9fa;
}

.filter-item.active {
	background: linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%);
	color: white;
}

.chart-container {
	height: 400rpx;
	background: #f8f9fa;
	border-radius: 20rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
}

.chart-placeholder {
	text-align: center;
}

.chart-text {
	font-size: 26rpx;
	color: #999;
	display: block;
	margin-top: 20rpx;
}

.history-list {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

.history-item {
	display: flex;
	align-items: center;
	gap: 20rpx;
	padding: 20rpx;
	background: #f8f9fa;
	border-radius: 20rpx;
}

.record-date {
	text-align: center;
	min-width: 80rpx;
}

.date-day {
	font-size: 32rpx;
	font-weight: bold;
	color: #ff8a00;
	display: block;
}

.date-month {
	font-size: 20rpx;
	color: #999;
	display: block;
}

.record-content {
	flex: 1;
}

.record-value {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 5rpx;
}

.record-time {
	font-size: 22rpx;
	color: #666;
	display: block;
	margin-bottom: 5rpx;
}

.record-note {
	font-size: 22rpx;
	color: #999;
	display: block;
}

.record-status {
	padding: 8rpx 20rpx;
	border-radius: 20rpx;
	font-size: 22rpx;
}

.record-status.normal {
	background: rgba(76, 175, 80, 0.1);
	color: #4caf50;
}

.record-status.warning {
	background: rgba(255, 152, 0, 0.1);
	color: #ff9800;
}

.advice-list {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

.advice-item {
	display: flex;
	align-items: flex-start;
	gap: 20rpx;
	padding: 20rpx;
	background: #f8f9fa;
	border-radius: 20rpx;
}

.advice-icon {
	width: 60rpx;
	height: 60rpx;
	background: rgba(255, 152, 0, 0.1);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-top: 5rpx;
}

.advice-content {
	flex: 1;
}

.advice-title {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 8rpx;
}

.advice-desc {
	font-size: 26rpx;
	color: #666;
	line-height: 1.5;
	display: block;
}

.reference-ranges {
	display: flex;
	flex-direction: column;
	gap: 15rpx;
}

.range-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20rpx;
	background: #f8f9fa;
	border-radius: 20rpx;
}

.range-label {
	padding: 8rpx 20rpx;
	border-radius: 20rpx;
	font-size: 24rpx;
}

.range-label.normal {
	background: rgba(76, 175, 80, 0.1);
	color: #4caf50;
}

.range-label.warning {
	background: rgba(255, 152, 0, 0.1);
	color: #ff9800;
}

.range-label.danger {
	background: rgba(244, 67, 54, 0.1);
	color: #f44336;
}

.range-value {
	font-size: 26rpx;
	color: #333;
	font-weight: 500;
}

.bottom-actions {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background: white;
	padding: 30rpx 40rpx;
	display: flex;
	gap: 20rpx;
	box-shadow: 0 -4rpx 20rpx rgba(0,0,0,0.1);
}

.action-btn {
	flex: 1;
	height: 100rpx;
	border-radius: 20rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 15rpx;
	font-size: 32rpx;
	font-weight: 500;
}

.action-btn.primary {
	background: linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%);
	color: white;
	border: none;
}

.action-btn.secondary {
	background: white;
	color: #ff8a00;
	border: 2rpx solid #ff8a00;
}
</style>
