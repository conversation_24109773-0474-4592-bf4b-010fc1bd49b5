<template>
	<view class="error-boundary" :class="{ 'elderly-mode': elderlyMode }">
		<!-- 错误状态显示 -->
		<view v-if="hasError" class="error-container">
			<view class="error-content">
				<!-- 错误图标 -->
				<view class="error-icon">
					<Icon :name="errorIcon" size="120rpx" color="#ff6b6b"></Icon>
				</view>
				
				<!-- 错误标题 -->
				<text class="error-title">{{ errorTitle }}</text>
				
				<!-- 错误描述 -->
				<text class="error-description">{{ errorDescription }}</text>
				
				<!-- 错误详情（开发模式下显示） -->
				<view v-if="showErrorDetails && errorMessage" class="error-details">
					<text class="error-details-title">错误详情：</text>
					<text class="error-details-content">{{ errorMessage }}</text>
				</view>
				
				<!-- 操作按钮 -->
				<view class="error-actions">
					<button 
						class="retry-btn" 
						@click="handleRetry"
						:class="{ 'elderly-btn': elderlyMode }"
					>
						<Icon name="refresh-line" size="32rpx" color="white"></Icon>
						<text class="btn-text">重试</text>
					</button>
					
					<button 
						class="back-btn" 
						@click="handleBack"
						:class="{ 'elderly-btn': elderlyMode }"
					>
						<Icon name="arrow-left-line" size="32rpx" color="#666"></Icon>
						<text class="btn-text">返回</text>
					</button>
				</view>
				
				<!-- 帮助信息 -->
				<view class="help-info">
					<text class="help-text">如果问题持续存在，请联系客服</text>
					<text class="help-phone" @click="contactSupport">************</text>
				</view>
			</view>
		</view>
		
		<!-- 正常内容 -->
		<slot v-else></slot>
	</view>
</template>

<script>
import Icon from '@/components/Icon/Icon.vue'

export default {
	name: 'ErrorBoundary',
	components: {
		Icon
	},
	props: {
		errorType: {
			type: String,
			default: 'unknown',
			validator: value => ['network', 'permission', 'data', 'system', 'unknown'].includes(value)
		},
		errorMessage: {
			type: String,
			default: ''
		},
		elderlyMode: {
			type: Boolean,
			default: false
		},
		showErrorDetails: {
			type: Boolean,
			default: false // 生产环境建议设为false
		}
	},
	data() {
		return {
			hasError: false
		}
	},
	computed: {
		errorIcon() {
			const iconMap = {
				network: 'wifi-line',
				permission: 'shield-check-line',
				data: 'database-line',
				system: 'settings-line',
				unknown: 'error-warning-line'
			}
			return iconMap[this.errorType] || 'error-warning-line'
		},
		errorTitle() {
			const titleMap = {
				network: '网络连接异常',
				permission: '权限不足',
				data: '数据加载失败',
				system: '系统错误',
				unknown: '出现了一些问题'
			}
			return titleMap[this.errorType] || '出现了一些问题'
		},
		errorDescription() {
			const descMap = {
				network: '请检查网络连接后重试',
				permission: '您没有访问此功能的权限',
				data: '数据加载失败，请稍后重试',
				system: '系统暂时不可用，请稍后重试',
				unknown: '请稍后重试或联系客服'
			}
			return descMap[this.errorType] || '请稍后重试或联系客服'
		}
	},
	watch: {
		errorMessage: {
			handler(newVal) {
				this.hasError = !!newVal
			},
			immediate: true
		}
	},
	methods: {
		handleRetry() {
			this.hasError = false
			this.$emit('retry')
		},
		handleBack() {
			// 尝试返回上一页
			const pages = getCurrentPages()
			if (pages.length > 1) {
				uni.navigateBack()
			} else {
				// 如果是第一页，跳转到首页
				uni.reLaunch({
					url: '/pages/home/<USER>'
				})
			}
		},
		contactSupport() {
			uni.showModal({
				title: '联系客服',
				content: '是否拨打客服电话：************？',
				success: (res) => {
					if (res.confirm) {
						uni.makePhoneCall({
							phoneNumber: '************',
							fail: () => {
								uni.showToast({
									title: '拨打失败',
									icon: 'none'
								})
							}
						})
					}
				}
			})
		}
	}
}
</script>

<style scoped>
.error-boundary {
	min-height: 100vh;
	background: #f5f5f5;
}

.error-container {
	display: flex;
	align-items: center;
	justify-content: center;
	min-height: 100vh;
	padding: 40rpx;
}

.error-content {
	background: white;
	border-radius: 20rpx;
	padding: 60rpx 40rpx;
	text-align: center;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
	max-width: 600rpx;
	width: 100%;
}

.error-icon {
	margin-bottom: 40rpx;
}

.error-title {
	display: block;
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 20rpx;
}

.error-description {
	display: block;
	font-size: 28rpx;
	color: #666;
	line-height: 1.6;
	margin-bottom: 40rpx;
}

.error-details {
	background: #f8f9fa;
	border-radius: 12rpx;
	padding: 20rpx;
	margin-bottom: 40rpx;
	text-align: left;
}

.error-details-title {
	display: block;
	font-size: 24rpx;
	color: #999;
	margin-bottom: 10rpx;
}

.error-details-content {
	display: block;
	font-size: 22rpx;
	color: #666;
	word-break: break-all;
	line-height: 1.4;
}

.error-actions {
	display: flex;
	gap: 20rpx;
	justify-content: center;
	margin-bottom: 40rpx;
}

.retry-btn {
	background: linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%);
	color: white;
	border: none;
	border-radius: 25rpx;
	padding: 20rpx 40rpx;
	font-size: 28rpx;
	display: flex;
	align-items: center;
	gap: 10rpx;
}

.back-btn {
	background: white;
	color: #666;
	border: 2rpx solid #e0e0e0;
	border-radius: 25rpx;
	padding: 20rpx 40rpx;
	font-size: 28rpx;
	display: flex;
	align-items: center;
	gap: 10rpx;
}

.btn-text {
	font-size: 28rpx;
}

.help-info {
	border-top: 1rpx solid #e0e0e0;
	padding-top: 30rpx;
}

.help-text {
	display: block;
	font-size: 24rpx;
	color: #999;
	margin-bottom: 10rpx;
}

.help-phone {
	display: block;
	font-size: 28rpx;
	color: #ff8a00;
	font-weight: bold;
}

/* 适老版样式 */
.elderly-mode .error-content {
	padding: 80rpx 50rpx;
}

.elderly-mode .error-title {
	font-size: 44rpx;
	margin-bottom: 30rpx;
}

.elderly-mode .error-description {
	font-size: 32rpx;
	margin-bottom: 50rpx;
}

.elderly-mode .elderly-btn {
	padding: 30rpx 50rpx;
	font-size: 32rpx;
}

.elderly-mode .elderly-btn .btn-text {
	font-size: 32rpx;
}

.elderly-mode .help-text {
	font-size: 28rpx;
}

.elderly-mode .help-phone {
	font-size: 32rpx;
}
</style>
