<template>
	<view class="container">
		<PageHeader title="在线咨询" showBack></PageHeader>
		
		<view class="content">
			<!-- 专家列表 -->
			<view class="section">
				<text class="section-title">选择专家</text>
				<view class="doctor-list">
					<view 
						class="doctor-item" 
						:class="{ active: selectedDoctor === doctor.id }"
						v-for="doctor in doctorList" 
						:key="doctor.id"
						@click="selectDoctor(doctor)"
					>
						<image class="doctor-avatar" :src="doctor.avatar" mode="aspectFill"></image>
						<view class="doctor-info">
							<text class="doctor-name">{{ doctor.name }}</text>
							<text class="doctor-title">{{ doctor.title }}</text>
							<text class="doctor-department">{{ doctor.department }}</text>
							<view class="doctor-tags">
								<text class="tag" v-for="tag in doctor.specialties" :key="tag">{{ tag }}</text>
							</view>
						</view>
						<view class="doctor-status">
							<view class="status-dot" :class="doctor.status"></view>
							<text class="status-text">{{ getStatusText(doctor.status) }}</text>
							<text class="consultation-fee">¥{{ doctor.fee }}/次</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 咨询方式 -->
			<view class="section">
				<text class="section-title">咨询方式</text>
				<view class="consultation-types">
					<view 
						class="type-item" 
						:class="{ active: consultationType === type.value }"
						v-for="type in consultationTypes" 
						:key="type.value"
						@click="selectType(type.value)"
					>
						<Icon :name="type.icon" size="40rpx" :color="consultationType === type.value ? '#ff8a00' : '#666'"></Icon>
						<text class="type-name">{{ type.name }}</text>
						<text class="type-desc">{{ type.description }}</text>
					</view>
				</view>
			</view>

			<!-- 问题描述 -->
			<view class="section">
				<text class="section-title">问题描述</text>
				<view class="form-card">
					<view class="form-item">
						<text class="form-label">主要症状</text>
						<textarea 
							class="form-textarea" 
							v-model="formData.symptoms" 
							placeholder="请详细描述您的症状和不适感受"
							maxlength="500"
						/>
						<text class="char-count">{{ formData.symptoms.length }}/500</text>
					</view>
					
					<view class="form-item">
						<text class="form-label">病史信息</text>
						<textarea 
							class="form-textarea" 
							v-model="formData.history" 
							placeholder="请描述相关病史、用药情况等（可选）"
							maxlength="300"
						/>
						<text class="char-count">{{ formData.history.length }}/300</text>
					</view>
					
					<view class="form-item">
						<text class="form-label">上传图片</text>
						<view class="image-upload">
							<view class="image-item" v-for="(image, index) in uploadedImages" :key="index">
								<image :src="image" mode="aspectFill" class="uploaded-image"></image>
								<view class="delete-btn" @click="removeImage(index)">
									<Icon name="close-line" size="20rpx" color="white"></Icon>
								</view>
							</view>
							<view class="upload-btn" @click="uploadImage" v-if="uploadedImages.length < 3">
								<Icon name="add-line" size="40rpx" color="#999"></Icon>
								<text class="upload-text">添加图片</text>
							</view>
						</view>
						<text class="upload-tip">最多上传3张图片，支持jpg、png格式</text>
					</view>
				</view>
			</view>

			<!-- 费用说明 -->
			<view class="section">
				<text class="section-title">费用说明</text>
				<view class="fee-card">
					<view class="fee-item">
						<text class="fee-label">咨询费用</text>
						<text class="fee-value">¥{{ consultationFee }}</text>
					</view>
					<view class="fee-item">
						<text class="fee-label">服务时长</text>
						<text class="fee-value">{{ serviceTime }}</text>
					</view>
					<view class="fee-note">
						<Icon name="information-line" size="20rpx" color="#ff8a00"></Icon>
						<text class="note-text">专家将在24小时内回复，如需紧急处理请拨打急救电话</text>
					</view>
				</view>
			</view>

			<!-- 底部操作 -->
			<view class="bottom-actions">
				<view class="total-fee">
					<text class="fee-label">总费用：</text>
					<text class="fee-amount">¥{{ consultationFee }}</text>
				</view>
				<InteractiveButton 
					type="primary" 
					text="提交咨询" 
					:loading="submitting"
					@click="submitConsultation"
				/>
			</view>
		</view>
	</view>
</template>

<script>
import Icon from '@/components/Icon/Icon.vue'
import PageHeader from '@/components/PageHeader/PageHeader.vue'
import InteractiveButton from '@/components/InteractiveButton/InteractiveButton.vue'

export default {
	name: 'HealthConsultation',
	components: {
		Icon,
		PageHeader,
		InteractiveButton
	},
	data() {
		return {
			submitting: false,
			selectedDoctor: null,
			consultationType: 'text',
			uploadedImages: [],
			formData: {
				symptoms: '',
				history: ''
			},
			doctorList: [
				{
					id: 1,
					name: '张主任',
					title: '主任医师',
					department: '心血管内科',
					avatar: '/picture/doctor1.jpg',
					specialties: ['高血压', '冠心病', '心律失常'],
					status: 'online',
					fee: 50
				},
				{
					id: 2,
					name: '李医生',
					title: '副主任医师',
					department: '内分泌科',
					avatar: '/picture/doctor2.jpg',
					specialties: ['糖尿病', '甲状腺', '骨质疏松'],
					status: 'busy',
					fee: 40
				},
				{
					id: 3,
					name: '王教授',
					title: '主任医师',
					department: '神经内科',
					avatar: '/picture/doctor3.jpg',
					specialties: ['脑血管病', '帕金森', '痴呆'],
					status: 'offline',
					fee: 60
				}
			],
			consultationTypes: [
				{
					value: 'text',
					name: '图文咨询',
					description: '文字+图片描述',
					icon: 'message-line'
				},
				{
					value: 'voice',
					name: '语音咨询',
					description: '语音通话咨询',
					icon: 'phone-line'
				},
				{
					value: 'video',
					name: '视频咨询',
					description: '视频通话咨询',
					icon: 'vidicon-line'
				}
			]
		}
	},
	computed: {
		consultationFee() {
			const doctor = this.doctorList.find(d => d.id === this.selectedDoctor)
			if (!doctor) return 0
			
			let baseFee = doctor.fee
			if (this.consultationType === 'voice') baseFee *= 1.5
			if (this.consultationType === 'video') baseFee *= 2
			
			return Math.round(baseFee)
		},
		serviceTime() {
			const timeMap = {
				'text': '24小时内回复',
				'voice': '15分钟通话',
				'video': '15分钟视频'
			}
			return timeMap[this.consultationType] || '24小时内回复'
		}
	},
	methods: {
		selectDoctor(doctor) {
			this.selectedDoctor = doctor.id
		},
		selectType(type) {
			this.consultationType = type
		},
		getStatusText(status) {
			const statusMap = {
				'online': '在线',
				'busy': '忙碌',
				'offline': '离线'
			}
			return statusMap[status] || '未知'
		},
		uploadImage() {
			uni.chooseImage({
				count: 3 - this.uploadedImages.length,
				sizeType: ['compressed'],
				sourceType: ['album', 'camera'],
				success: (res) => {
					this.uploadedImages.push(...res.tempFilePaths)
				}
			})
		},
		removeImage(index) {
			this.uploadedImages.splice(index, 1)
		},
		validate() {
			if (!this.selectedDoctor) {
				uni.showToast({ title: '请选择专家', icon: 'none' })
				return false
			}
			if (!this.formData.symptoms.trim()) {
				uni.showToast({ title: '请描述主要症状', icon: 'none' })
				return false
			}
			return true
		},
		async submitConsultation() {
			if (!this.validate()) return
			
			this.submitting = true
			try {
				// 模拟提交咨询
				await new Promise(resolve => setTimeout(resolve, 2000))
				
				uni.showToast({
					title: '咨询提交成功',
					icon: 'success'
				})
				
				setTimeout(() => {
					uni.navigateBack()
				}, 1500)
			} catch (error) {
				uni.showToast({
					title: '提交失败，请重试',
					icon: 'none'
				})
			} finally {
				this.submitting = false
			}
		}
	}
}
</script>

<style scoped>
.container {
	min-height: 100vh;
	background: #f5f5f5;
}

.content {
	padding: 140rpx 32rpx 200rpx;
}

.section {
	margin-bottom: 32rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 24rpx;
	display: block;
}

.doctor-list {
	display: flex;
	flex-direction: column;
	gap: 16rpx;
}

.doctor-item {
	background: white;
	border-radius: 16rpx;
	padding: 24rpx;
	border: 2rpx solid #f0f0f0;
	display: flex;
	align-items: center;
	gap: 16rpx;
}

.doctor-item.active {
	border-color: #ff8a00;
	background: rgba(255, 138, 0, 0.05);
}

.doctor-avatar {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
}

.doctor-info {
	flex: 1;
}

.doctor-name {
	font-size: 30rpx;
	font-weight: 600;
	color: #333;
	display: block;
	margin-bottom: 4rpx;
}

.doctor-title {
	font-size: 24rpx;
	color: #666;
	display: block;
	margin-bottom: 4rpx;
}

.doctor-department {
	font-size: 24rpx;
	color: #999;
	display: block;
	margin-bottom: 8rpx;
}

.doctor-tags {
	display: flex;
	gap: 8rpx;
}

.tag {
	background: #f0f0f0;
	color: #666;
	font-size: 20rpx;
	padding: 2rpx 8rpx;
	border-radius: 8rpx;
}

.doctor-status {
	text-align: right;
}

.status-dot {
	width: 12rpx;
	height: 12rpx;
	border-radius: 50%;
	display: inline-block;
	margin-right: 8rpx;
}

.status-dot.online {
	background: #4caf50;
}

.status-dot.busy {
	background: #ff9800;
}

.status-dot.offline {
	background: #999;
}

.status-text {
	font-size: 24rpx;
	color: #666;
	display: block;
	margin-bottom: 4rpx;
}

.consultation-fee {
	font-size: 28rpx;
	font-weight: 600;
	color: #ff8a00;
	display: block;
}

.consultation-types {
	display: flex;
	gap: 16rpx;
}

.type-item {
	flex: 1;
	background: white;
	border-radius: 16rpx;
	padding: 24rpx;
	text-align: center;
	border: 2rpx solid #f0f0f0;
}

.type-item.active {
	border-color: #ff8a00;
	background: rgba(255, 138, 0, 0.05);
}

.type-name {
	font-size: 28rpx;
	font-weight: 600;
	color: #333;
	display: block;
	margin: 12rpx 0 8rpx;
}

.type-desc {
	font-size: 24rpx;
	color: #666;
	display: block;
}

.form-card {
	background: white;
	border-radius: 16rpx;
	padding: 24rpx;
}

.form-item {
	margin-bottom: 24rpx;
}

.form-item:last-child {
	margin-bottom: 0;
}

.form-label {
	font-size: 28rpx;
	color: #333;
	margin-bottom: 12rpx;
	display: block;
}

.form-textarea {
	width: 100%;
	min-height: 120rpx;
	padding: 20rpx;
	border: 1rpx solid #e0e0e0;
	border-radius: 12rpx;
	font-size: 28rpx;
	background: #fafafa;
	resize: none;
}

.char-count {
	font-size: 24rpx;
	color: #999;
	text-align: right;
	display: block;
	margin-top: 8rpx;
}

.image-upload {
	display: flex;
	gap: 16rpx;
	margin-bottom: 12rpx;
}

.image-item {
	position: relative;
	width: 120rpx;
	height: 120rpx;
}

.uploaded-image {
	width: 100%;
	height: 100%;
	border-radius: 12rpx;
}

.delete-btn {
	position: absolute;
	top: -8rpx;
	right: -8rpx;
	width: 32rpx;
	height: 32rpx;
	background: #f44336;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
}

.upload-btn {
	width: 120rpx;
	height: 120rpx;
	border: 2rpx dashed #ddd;
	border-radius: 12rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	gap: 8rpx;
}

.upload-text {
	font-size: 24rpx;
	color: #999;
}

.upload-tip {
	font-size: 24rpx;
	color: #999;
}

.fee-card {
	background: white;
	border-radius: 16rpx;
	padding: 24rpx;
}

.fee-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 16rpx;
}

.fee-item:last-child {
	margin-bottom: 0;
}

.fee-label {
	font-size: 28rpx;
	color: #666;
}

.fee-value {
	font-size: 28rpx;
	font-weight: 600;
	color: #333;
}

.fee-note {
	display: flex;
	align-items: flex-start;
	gap: 8rpx;
	margin-top: 16rpx;
	padding-top: 16rpx;
	border-top: 1rpx solid #f0f0f0;
}

.note-text {
	font-size: 24rpx;
	color: #666;
	line-height: 1.5;
}

.bottom-actions {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background: white;
	padding: 24rpx 32rpx;
	border-top: 1rpx solid #f0f0f0;
	display: flex;
	align-items: center;
	gap: 24rpx;
}

.total-fee {
	flex: 1;
}

.fee-amount {
	font-size: 36rpx;
	font-weight: 600;
	color: #ff8a00;
}
</style>
