<template>
	<view class="container" :class="{ 'elderly-mode': isElderlyMode }">
		<!-- 自定义导航栏 -->
		<view class="custom-navbar" :class="{ 'elderly-mode': isElderlyMode }">
			<view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
			<view class="navbar-content">
				<view class="navbar-left" @click="goBack">
					<Icon
						name="arrow-left-line"
						:size="isElderlyMode ? '40rpx' : '36rpx'"
						color="#333"
					></Icon>
					<text class="back-text">返回</text>
				</view>
				<view class="navbar-center">
					<text class="navbar-title">我的钱包</text>
				</view>
				<view class="navbar-right"></view>
			</view>
		</view>

		<!-- 钱包余额卡片 -->
		<view class="balance-card">
			<view class="card-header">
				<text class="card-title">我的钱包</text>
				<view class="security-info">
					<Icon name="shield-check-line" size="24rpx" color="#4caf50"></Icon>
					<text class="security-text">安全保障</text>
				</view>
			</view>
			
			<view class="balance-info">
				<view class="balance-main">
					<text class="balance-label">账户余额（元）</text>
					<view class="balance-amount">
						<text class="currency">¥</text>
						<text class="amount" v-if="!hideBalance">{{walletInfo.balance}}</text>
						<text class="amount" v-else>****</text>
						<button class="toggle-btn" @click="toggleBalance">
							<Icon :name="hideBalance ? 'eye-off-line' : 'eye-line'" size="24rpx" color="#666"></Icon>
						</button>
					</view>
				</view>
				
				<view class="balance-actions">
					<button class="action-btn recharge" @click="recharge">
						<Icon name="add-line" size="32rpx" color="white"></Icon>
						<text>充值</text>
					</button>
					<button class="action-btn withdraw" @click="withdraw">
						<Icon name="subtract-line" size="32rpx" color="#ff8a00"></Icon>
						<text>提现</text>
					</button>
				</view>
			</view>
		</view>

		<!-- 快捷功能 -->
		<view class="quick-functions">
			<view class="function-item" @click="goToRecharge">
				<view class="function-icon recharge">
					<Icon name="wallet-3-line" size="40rpx" color="white"></Icon>
				</view>
				<text class="function-text">充值</text>
			</view>
			<view class="function-item" @click="goToWithdraw">
				<view class="function-icon withdraw">
					<Icon name="bank-card-line" size="40rpx" color="white"></Icon>
				</view>
				<text class="function-text">提现</text>
			</view>
			<view class="function-item" @click="goToTransfer">
				<view class="function-icon transfer">
					<Icon name="exchange-line" size="40rpx" color="white"></Icon>
				</view>
				<text class="function-text">转账</text>
			</view>
			<view class="function-item" @click="goToRedPacket">
				<view class="function-icon red-packet">
					<Icon name="gift-line" size="40rpx" color="white"></Icon>
				</view>
				<text class="function-text">红包</text>
			</view>
		</view>

		<!-- 钱包统计 -->
		<view class="wallet-stats">
			<view class="stats-header">
				<text class="stats-title">本月统计</text>
				<picker :value="selectedMonth" :range="monthOptions" @change="onMonthChange">
					<view class="month-picker">
						<text class="month-text">{{selectedMonth}}</text>
						<Icon name="arrow-down-s-line" size="20rpx" color="#999"></Icon>
					</view>
				</picker>
			</view>
			
			<view class="stats-content">
				<view class="stats-item">
					<text class="stats-label">收入</text>
					<text class="stats-value income">+¥{{monthStats.income}}</text>
				</view>
				<view class="stats-item">
					<text class="stats-label">支出</text>
					<text class="stats-value expense">-¥{{monthStats.expense}}</text>
				</view>
				<view class="stats-item">
					<text class="stats-label">结余</text>
					<text class="stats-value" :class="monthStats.balance >= 0 ? 'income' : 'expense'">
						{{monthStats.balance >= 0 ? '+' : ''}}¥{{monthStats.balance}}
					</text>
				</view>
			</view>
		</view>

		<!-- 交易记录 -->
		<view class="transaction-section">
			<view class="section-header">
				<text class="section-title">交易记录</text>
				<text class="view-all" @click="viewAllTransactions">查看全部</text>
			</view>
			
			<view class="transaction-list">
				<view class="transaction-item" v-for="(transaction, index) in recentTransactions" :key="index" @click="viewTransactionDetail(transaction)">
					<view class="transaction-icon" :class="transaction.type">
						<Icon :name="getTransactionIcon(transaction.type)" size="32rpx" color="white"></Icon>
					</view>
					
					<view class="transaction-info">
						<text class="transaction-title">{{transaction.title}}</text>
						<text class="transaction-desc">{{transaction.description}}</text>
						<text class="transaction-time">{{transaction.time}}</text>
					</view>
					
					<view class="transaction-amount">
						<text class="amount-text" :class="transaction.type">
							{{transaction.type === 'income' ? '+' : '-'}}¥{{transaction.amount}}
						</text>
						<text class="status-text" :class="transaction.status">{{getStatusText(transaction.status)}}</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 银行卡管理 -->
		<view class="card-section">
			<view class="section-header">
				<text class="section-title">银行卡管理</text>
				<text class="add-card" @click="addBankCard">
					<Icon name="add-line" size="24rpx" color="#ff8a00"></Icon>
					<text>添加</text>
				</text>
			</view>
			
			<view class="card-list">
				<view class="bank-card" v-for="(card, index) in bankCards" :key="index" @click="manageBankCard(card)">
					<view class="card-info">
						<text class="bank-name">{{card.bankName}}</text>
						<text class="card-number">****  ****  ****  {{card.lastFour}}</text>
						<view class="card-tags">
							<text class="card-tag default" v-if="card.isDefault">默认</text>
							<text class="card-tag type">{{card.type}}</text>
						</view>
					</view>
					<Icon name="arrow-right-s-line" size="24rpx" color="#999"></Icon>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import Icon from '@/components/Icon/Icon.vue'
import { elderlyModeManager } from '@/utils/elderlyModeUtils.js'

export default {
	components: {
		Icon
	},
	data() {
		return {
			statusBarHeight: 0, // 状态栏高度
			isElderlyMode: false, // 适老化模式
			hideBalance: false,
			selectedMonth: '2024年1月',
			monthOptions: ['2024年1月', '2023年12月', '2023年11月'],
			walletInfo: {
				balance: '1,258.50'
			},
			monthStats: {
				income: '2,350.00',
				expense: '1,891.50',
				balance: '458.50'
			},
			recentTransactions: [
				{
					id: 1,
					type: 'expense',
					title: '居家护理服务',
					description: '订单支付',
					amount: '120.00',
					time: '今天 14:30',
					status: 'completed'
				},
				{
					id: 2,
					type: 'income',
					title: '账户充值',
					description: '微信支付',
					amount: '500.00',
					time: '昨天 09:15',
					status: 'completed'
				},
				{
					id: 3,
					type: 'expense',
					title: '康复训练',
					description: '订单支付',
					amount: '200.00',
					time: '01-13 16:20',
					status: 'completed'
				},
				{
					id: 4,
					type: 'income',
					title: '退款',
					description: '订单取消退款',
					amount: '35.00',
					time: '01-12 11:45',
					status: 'processing'
				}
			],
			bankCards: [
				{
					id: 1,
					bankName: '中国工商银行',
					lastFour: '6688',
					type: '储蓄卡',
					isDefault: true
				},
				{
					id: 2,
					bankName: '中国建设银行',
					lastFour: '8899',
					type: '信用卡',
					isDefault: false
				}
			]
		}
	},
	onLoad() {
		// 获取状态栏高度
		const systemInfo = uni.getSystemInfoSync();
		this.statusBarHeight = systemInfo.statusBarHeight || 44;

		// 初始化适老化模式
		this.isElderlyMode = elderlyModeManager.getElderlyMode();
		elderlyModeManager.onElderlyModeChange((isEnabled) => {
			this.isElderlyMode = isEnabled;
		});
	},
	methods: {
		toggleBalance() {
			this.hideBalance = !this.hideBalance;
		},
		onMonthChange(e) {
			this.selectedMonth = this.monthOptions[e.detail.value];
			// 这里可以加载对应月份的统计数据
		},
		getTransactionIcon(type) {
			return type === 'income' ? 'arrow-down-line' : 'arrow-up-line';
		},
		getStatusText(status) {
			const statusMap = {
				'completed': '已完成',
				'processing': '处理中',
				'failed': '失败'
			};
			return statusMap[status] || '未知';
		},
		recharge() {
			uni.navigateTo({
				url: '/pages/wallet/recharge'
			});
		},
		withdraw() {
			uni.navigateTo({
				url: '/pages/wallet/withdraw'
			});
		},
		goToRecharge() {
			this.recharge();
		},
		goToWithdraw() {
			this.withdraw();
		},
		goToTransfer() {
			uni.navigateTo({
				url: '/pages/wallet/transfer'
			});
		},
		goToRedPacket() {
			uni.navigateTo({
				url: '/pages/wallet/red-packet'
			});
		},
		viewAllTransactions() {
			uni.navigateTo({
				url: '/pages/wallet/transactions'
			});
		},
		viewTransactionDetail(transaction) {
			uni.navigateTo({
				url: `/pages/wallet/transaction-detail?id=${transaction.id}`
			});
		},
		addBankCard() {
			uni.navigateTo({
				url: '/pages/wallet/add-card'
			});
		},
		manageBankCard(card) {
			uni.navigateTo({
				url: `/pages/wallet/card-detail?id=${card.id}`
			});
		},

		// 返回上一页
		goBack() {
			uni.navigateBack({
				fail: () => {
					uni.switchTab({
						url: '/pages/profile/profile'
					});
				}
			});
		}
	}
}
</script>

<style scoped>
.container {
	background: #f8f9fa;
	min-height: 100vh;
}

/* 导航栏样式 */
.custom-navbar {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 1000;
	background: rgba(255, 255, 255, 0.95);
	backdrop-filter: blur(20rpx);
	-webkit-backdrop-filter: blur(20rpx);
	border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);
}

.navbar-content {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 20rpx 32rpx;
	min-height: 88rpx;
}

.navbar-left {
	display: flex;
	align-items: center;
	gap: 12rpx;
	padding: 8rpx 16rpx 8rpx 0;
	border-radius: 20rpx;
	transition: all 0.2s ease;
}

.navbar-left:active {
	background-color: rgba(0, 0, 0, 0.04);
	transform: scale(0.96);
}

.back-text {
	font-size: 32rpx;
	color: #333;
	font-weight: 500;
}

.navbar-center {
	position: absolute;
	left: 50%;
	transform: translateX(-50%);
}

.navbar-title {
	font-size: 34rpx;
	font-weight: 600;
	color: #333;
}

.navbar-right {
	display: flex;
	align-items: center;
}

.balance-card {
	background: linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%);
	margin: 20rpx;
	margin-top: 220rpx; /* 为导航栏留出空间 */
	border-radius: 30rpx;
	padding: 40rpx;
	color: white;
}

.card-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 30rpx;
}

.card-title {
	font-size: 32rpx;
	font-weight: bold;
}

.security-info {
	display: flex;
	align-items: center;
	gap: 8rpx;
}

.security-text {
	font-size: 22rpx;
}

.balance-info {
	display: flex;
	justify-content: space-between;
	align-items: flex-end;
}

.balance-main {
	flex: 1;
}

.balance-label {
	font-size: 24rpx;
	opacity: 0.9;
	display: block;
	margin-bottom: 15rpx;
}

.balance-amount {
	display: flex;
	align-items: baseline;
	gap: 10rpx;
}

.currency {
	font-size: 32rpx;
}

.amount {
	font-size: 48rpx;
	font-weight: bold;
}

.toggle-btn {
	background: rgba(255, 255, 255, 0.2);
	border: none;
	border-radius: 50%;
	width: 50rpx;
	height: 50rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.balance-actions {
	display: flex;
	gap: 15rpx;
}

.action-btn {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 8rpx;
	padding: 20rpx;
	border-radius: 20rpx;
	border: none;
	font-size: 24rpx;
}

.action-btn.recharge {
	background: rgba(255, 255, 255, 0.2);
	color: white;
}

.action-btn.withdraw {
	background: white;
	color: #ff8a00;
}

.quick-functions {
	background: white;
	margin: 20rpx;
	border-radius: 30rpx;
	padding: 40rpx;
	display: grid;
	grid-template-columns: repeat(4, 1fr);
	gap: 30rpx;
}

.function-item {
	text-align: center;
}

.function-icon {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	margin: 0 auto 15rpx;
}

.function-icon.recharge {
	background: #4caf50;
}

.function-icon.withdraw {
	background: #2196f3;
}

.function-icon.transfer {
	background: #ff9800;
}

.function-icon.red-packet {
	background: #f44336;
}

.function-text {
	font-size: 24rpx;
	color: #333;
}

.wallet-stats {
	background: white;
	margin: 20rpx;
	border-radius: 30rpx;
	padding: 40rpx;
}

.stats-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 30rpx;
}

.stats-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.month-picker {
	display: flex;
	align-items: center;
	gap: 8rpx;
	padding: 10rpx 15rpx;
	background: #f8f9fa;
	border-radius: 15rpx;
}

.month-text {
	font-size: 24rpx;
	color: #333;
}

.stats-content {
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	gap: 30rpx;
}

.stats-item {
	text-align: center;
}

.stats-label {
	font-size: 24rpx;
	color: #666;
	display: block;
	margin-bottom: 10rpx;
}

.stats-value {
	font-size: 28rpx;
	font-weight: bold;
	display: block;
}

.stats-value.income {
	color: #4caf50;
}

.stats-value.expense {
	color: #f44336;
}

.transaction-section, .card-section {
	background: white;
	margin: 20rpx;
	border-radius: 30rpx;
	padding: 40rpx;
}

.section-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 30rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.view-all, .add-card {
	font-size: 26rpx;
	color: #ff8a00;
	display: flex;
	align-items: center;
	gap: 5rpx;
}

.transaction-list {
	display: flex;
	flex-direction: column;
	gap: 25rpx;
}

.transaction-item {
	display: flex;
	align-items: center;
	gap: 20rpx;
	padding: 20rpx;
	background: #f8f9fa;
	border-radius: 20rpx;
}

.transaction-icon {
	width: 60rpx;
	height: 60rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
}

.transaction-icon.income {
	background: #4caf50;
}

.transaction-icon.expense {
	background: #f44336;
}

.transaction-info {
	flex: 1;
}

.transaction-title {
	font-size: 28rpx;
	color: #333;
	font-weight: bold;
	display: block;
	margin-bottom: 5rpx;
}

.transaction-desc {
	font-size: 24rpx;
	color: #666;
	display: block;
	margin-bottom: 5rpx;
}

.transaction-time {
	font-size: 22rpx;
	color: #999;
	display: block;
}

.transaction-amount {
	text-align: right;
}

.amount-text {
	font-size: 28rpx;
	font-weight: bold;
	display: block;
	margin-bottom: 5rpx;
}

.amount-text.income {
	color: #4caf50;
}

.amount-text.expense {
	color: #f44336;
}

.status-text {
	font-size: 22rpx;
	display: block;
}

.status-text.completed {
	color: #4caf50;
}

.status-text.processing {
	color: #ff9800;
}

.status-text.failed {
	color: #f44336;
}

.card-list {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

.bank-card {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 25rpx;
	background: #f8f9fa;
	border-radius: 20rpx;
}

.card-info {
	flex: 1;
}

.bank-name {
	font-size: 28rpx;
	color: #333;
	font-weight: bold;
	display: block;
	margin-bottom: 8rpx;
}

.card-number {
	font-size: 24rpx;
	color: #666;
	display: block;
	margin-bottom: 10rpx;
}

.card-tags {
	display: flex;
	gap: 10rpx;
}

.card-tag {
	font-size: 20rpx;
	padding: 4rpx 10rpx;
	border-radius: 10rpx;
}

.card-tag.default {
	background: rgba(255, 138, 0, 0.1);
	color: #ff8a00;
}

.card-tag.type {
	background: #f0f0f0;
	color: #666;
}

/* ===== 适老化模式样式 ===== */
.elderly-mode .navbar-content {
	padding: 24rpx 36rpx;
	min-height: 96rpx;
}

.elderly-mode .navbar-left {
	gap: 16rpx;
	padding: 12rpx 20rpx 12rpx 0;
}

.elderly-mode .back-text {
	font-size: 36rpx;
	font-weight: 600;
}

.elderly-mode .navbar-title {
	font-size: 38rpx;
	font-weight: 700;
}

.elderly-mode .wallet-header {
	margin-top: 240rpx; /* 适老化模式下导航栏更高 */
	padding: 50rpx 30rpx;
}

.elderly-mode .balance-amount {
	font-size: 60rpx;
}

.elderly-mode .balance-label {
	font-size: 32rpx;
}

.elderly-mode .quick-action-text {
	font-size: 30rpx;
}

.elderly-mode .transaction-item {
	padding: 40rpx 30rpx;
}

.elderly-mode .transaction-title {
	font-size: 36rpx;
}

.elderly-mode .transaction-desc {
	font-size: 30rpx;
}

.elderly-mode .transaction-amount {
	font-size: 36rpx;
}

.elderly-mode .card-item {
	padding: 40rpx 30rpx;
}

.elderly-mode .card-number {
	font-size: 36rpx;
}

.elderly-mode .card-bank {
	font-size: 30rpx;
}
</style>
