<template>
	<view class="container">
		<!-- 服务图片 -->
		<image :src="serviceDetail.image" class="service-image" mode="aspectFill"></image>
		
		<!-- 基本信息 -->
		<view class="basic-info">
			<view class="info-header">
				<text class="service-name">{{serviceDetail.name}}</text>
				<view class="rating-section">
					<text class="rating-score">{{serviceDetail.rating}}</text>
					<text class="rating-text">分</text>
					<text class="review-count">({{serviceDetail.reviewCount}}条评价)</text>
				</view>
			</view>
			<view class="provider-info">
				<text class="provider-label">服务机构：</text>
				<text class="provider-name">{{serviceDetail.provider}}</text>
			</view>
			<view class="price-section">
				<text class="price-label">服务价格：</text>
				<text class="price-value">¥{{serviceDetail.price}}</text>
				<text class="price-unit">/{{serviceDetail.unit}}</text>
			</view>
			<view class="tags-section">
				<text class="tag" v-for="(tag, index) in serviceDetail.tags" :key="index">{{tag}}</text>
			</view>
		</view>
		
		<!-- 快捷操作 -->
		<view class="quick-actions">
			<view class="action-item" @click="callProvider">
				<image src="/static/icons/phone.png" class="action-icon"></image>
				<text class="action-text">电话咨询</text>
			</view>
			<view class="action-item" @click="toggleFavorite">
				<image :src="isFavorite ? '/static/icons/favorite-active.png' : '/static/icons/favorite.png'" class="action-icon"></image>
				<text class="action-text">{{isFavorite ? '已收藏' : '收藏'}}</text>
			</view>
			<view class="action-item" @click="shareService">
				<image src="/static/icons/share.png" class="action-icon"></image>
				<text class="action-text">分享</text>
			</view>
		</view>
		
		<!-- 详细信息 -->
		<view class="detail-sections">
			<!-- 服务介绍 -->
			<view class="detail-section">
				<view class="section-header">
					<text class="section-title">服务介绍</text>
				</view>
				<view class="section-content">
					<text class="description">{{serviceDetail.description}}</text>
				</view>
			</view>
			
			<!-- 服务内容 -->
			<view class="detail-section">
				<view class="section-header">
					<text class="section-title">服务内容</text>
				</view>
				<view class="section-content">
					<view class="service-items">
						<view class="service-item" v-for="(item, index) in serviceDetail.serviceItems" :key="index">
							<image src="/static/icons/check.png" class="check-icon"></image>
							<text class="item-text">{{item}}</text>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 服务流程 -->
			<view class="detail-section">
				<view class="section-header">
					<text class="section-title">服务流程</text>
				</view>
				<view class="section-content">
					<view class="process-steps">
						<view class="step-item" v-for="(step, index) in serviceDetail.process" :key="index">
							<view class="step-number">{{index + 1}}</view>
							<view class="step-content">
								<text class="step-title">{{step.title}}</text>
								<text class="step-desc">{{step.description}}</text>
							</view>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 服务时间 -->
			<view class="detail-section">
				<view class="section-header">
					<text class="section-title">服务时间</text>
				</view>
				<view class="section-content">
					<view class="time-info">
						<view class="time-item">
							<text class="time-label">服务时长：</text>
							<text class="time-value">{{serviceDetail.duration}}</text>
						</view>
						<view class="time-item">
							<text class="time-label">服务时间：</text>
							<text class="time-value">{{serviceDetail.serviceTime}}</text>
						</view>
						<view class="time-item">
							<text class="time-label">预约提前：</text>
							<text class="time-value">{{serviceDetail.advanceTime}}</text>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 用户评价 -->
			<view class="detail-section">
				<view class="section-header">
					<text class="section-title">用户评价</text>
					<text class="view-all" @click="viewAllReviews">查看全部</text>
				</view>
				<view class="section-content">
					<view class="review-list">
						<view class="review-item" v-for="(review, index) in serviceDetail.reviews" :key="index">
							<view class="review-header">
								<image :src="review.avatar" class="reviewer-avatar"></image>
								<view class="reviewer-info">
									<text class="reviewer-name">{{review.name}}</text>
									<view class="review-rating">
										<text class="rating">{{review.rating}}分</text>
										<text class="review-time">{{review.time}}</text>
									</view>
								</view>
							</view>
							<text class="review-content">{{review.content}}</text>
						</view>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 底部操作栏 -->
		<view class="bottom-actions">
			<button class="action-btn secondary" @click="callProvider">电话咨询</button>
			<button class="action-btn primary" @click="bookService">立即预约</button>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			serviceId: '',
			isFavorite: false,
			serviceDetail: {
				name: '专业护理服务',
				provider: '阳光护理中心',
				rating: 4.8,
				reviewCount: 86,
				price: 80,
				unit: '小时',
				duration: '2-8小时',
				serviceTime: '8:00-20:00',
				advanceTime: '提前2小时',
				phone: '010-12345678',
				tags: ['专业护理', '生活照料', '医疗护理', '24小时服务'],
				description: '我们提供专业的老年人护理服务，包括生活照料、医疗护理、康复训练等。我们的护理人员都经过专业培训，具有丰富的护理经验，能够为老年人提供安全、贴心的护理服务。',
				image: '/static/service/care1.jpg',
				serviceItems: [
					'日常生活照料（洗漱、穿衣、进食等）',
					'基础医疗护理（测量生命体征、用药提醒）',
					'康复训练指导',
					'心理陪护与沟通',
					'安全监护',
					'紧急情况处理'
				],
				process: [
					{
						title: '预约咨询',
						description: '电话或在线预约，了解具体需求'
					},
					{
						title: '评估服务',
						description: '专业人员上门评估老人身体状况'
					},
					{
						title: '制定方案',
						description: '根据评估结果制定个性化护理方案'
					},
					{
						title: '开始服务',
						description: '按约定时间提供专业护理服务'
					}
				],
				reviews: [
					{
						name: '张女士',
						avatar: '/static/avatar/user1.png',
						rating: 5.0,
						time: '2024-01-10',
						content: '护理人员很专业，对老人很有耐心，服务质量很好。'
					},
					{
						name: '李先生',
						avatar: '/static/avatar/user2.png',
						rating: 4.5,
						time: '2024-01-08',
						content: '服务及时，护理细致，老人很满意。'
					}
				]
			}
		}
	},
	onLoad(options) {
		if (options.id) {
			this.serviceId = options.id;
			this.loadServiceDetail();
		}
	},
	methods: {
		loadServiceDetail() {
			// 模拟加载服务详情
			console.log('加载服务详情:', this.serviceId);
			// 检查是否已收藏
			this.checkFavoriteStatus();
		},
		checkFavoriteStatus() {
			// 检查收藏状态
			const favorites = uni.getStorageSync('serviceFavorites') || [];
			this.isFavorite = favorites.includes(this.serviceId);
		},
		callProvider() {
			uni.makePhoneCall({
				phoneNumber: this.serviceDetail.phone
			});
		},
		toggleFavorite() {
			let favorites = uni.getStorageSync('serviceFavorites') || [];
			
			if (this.isFavorite) {
				// 取消收藏
				favorites = favorites.filter(id => id !== this.serviceId);
				this.isFavorite = false;
				uni.showToast({
					title: '已取消收藏',
					icon: 'success'
				});
			} else {
				// 添加收藏
				favorites.push(this.serviceId);
				this.isFavorite = true;
				uni.showToast({
					title: '收藏成功',
					icon: 'success'
				});
			}
			
			uni.setStorageSync('serviceFavorites', favorites);
		},
		shareService() {
			uni.share({
				provider: 'weixin',
				scene: 'WXSceneSession',
				type: 0,
				href: `pages/service/detail?id=${this.serviceId}`,
				title: this.serviceDetail.name,
				summary: this.serviceDetail.description,
				imageUrl: this.serviceDetail.image,
				success: () => {
					uni.showToast({
						title: '分享成功',
						icon: 'success'
					});
				}
			});
		},
		bookService() {
			uni.navigateTo({
				url: `/pages/service/book?id=${this.serviceId}`
			});
		},
		viewAllReviews() {
			uni.navigateTo({
				url: `/pages/service/reviews?id=${this.serviceId}`
			});
		}
	}
}
</script>

<style scoped>
.container {
	background-color: #f5f5f5;
	padding-bottom: 120rpx;
}

.service-image {
	width: 100%;
	height: 400rpx;
}

.basic-info {
	background-color: #fff;
	padding: 30rpx;
	margin-bottom: 20rpx;
}

.info-header {
	display: flex;
	align-items: center;
	margin-bottom: 15rpx;
}

.service-name {
	flex: 1;
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
}

.rating-section {
	display: flex;
	align-items: center;
}

.rating-score {
	font-size: 32rpx;
	color: #ff9500;
	font-weight: bold;
}

.rating-text {
	font-size: 24rpx;
	color: #999;
	margin-left: 5rpx;
	margin-right: 10rpx;
}

.review-count {
	font-size: 24rpx;
	color: #999;
}

.provider-info {
	display: flex;
	align-items: center;
	margin-bottom: 15rpx;
}

.provider-label {
	font-size: 28rpx;
	color: #666;
}

.provider-name {
	font-size: 28rpx;
	color: #4A90E2;
	font-weight: bold;
}

.price-section {
	display: flex;
	align-items: center;
	margin-bottom: 20rpx;
}

.price-label {
	font-size: 28rpx;
	color: #666;
}

.price-value {
	font-size: 36rpx;
	color: #ff6b35;
	font-weight: bold;
	margin: 0 5rpx;
}

.price-unit {
	font-size: 24rpx;
	color: #999;
}

.tags-section {
	display: flex;
	flex-wrap: wrap;
	gap: 10rpx;
}

.tag {
	padding: 8rpx 16rpx;
	background-color: #e3f2fd;
	color: #4A90E2;
	font-size: 22rpx;
	border-radius: 15rpx;
}

.quick-actions {
	background-color: #fff;
	padding: 30rpx;
	margin-bottom: 20rpx;
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	gap: 30rpx;
}

.action-item {
	display: flex;
	flex-direction: column;
	align-items: center;
}

.action-icon {
	width: 60rpx;
	height: 60rpx;
	margin-bottom: 10rpx;
}

.action-text {
	font-size: 24rpx;
	color: #666;
}

.detail-sections {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

.detail-section {
	background-color: #fff;
	padding: 30rpx;
}

.section-header {
	display: flex;
	align-items: center;
	margin-bottom: 30rpx;
}

.section-title {
	flex: 1;
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.view-all {
	font-size: 28rpx;
	color: #4A90E2;
}

.description {
	font-size: 28rpx;
	color: #666;
	line-height: 1.6;
}

.service-items {
	display: flex;
	flex-direction: column;
	gap: 15rpx;
}

.service-item {
	display: flex;
	align-items: center;
}

.check-icon {
	width: 30rpx;
	height: 30rpx;
	margin-right: 15rpx;
}

.item-text {
	font-size: 28rpx;
	color: #333;
}

.process-steps {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

.step-item {
	display: flex;
	align-items: flex-start;
}

.step-number {
	width: 60rpx;
	height: 60rpx;
	background-color: #4A90E2;
	color: #fff;
	border-radius: 30rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 24rpx;
	font-weight: bold;
	margin-right: 20rpx;
	flex-shrink: 0;
}

.step-content {
	flex: 1;
}

.step-title {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 8rpx;
}

.step-desc {
	font-size: 24rpx;
	color: #666;
}

.time-info {
	display: flex;
	flex-direction: column;
	gap: 15rpx;
}

.time-item {
	display: flex;
	align-items: center;
}

.time-label {
	width: 160rpx;
	font-size: 28rpx;
	color: #666;
}

.time-value {
	font-size: 28rpx;
	color: #333;
}

.review-list {
	display: flex;
	flex-direction: column;
	gap: 30rpx;
}

.review-item {
	padding: 20rpx;
	background-color: #f9f9f9;
	border-radius: 15rpx;
}

.review-header {
	display: flex;
	align-items: center;
	margin-bottom: 15rpx;
}

.reviewer-avatar {
	width: 60rpx;
	height: 60rpx;
	border-radius: 30rpx;
	margin-right: 15rpx;
}

.reviewer-info {
	flex: 1;
}

.reviewer-name {
	font-size: 26rpx;
	color: #333;
	display: block;
	margin-bottom: 5rpx;
}

.review-rating {
	display: flex;
	align-items: center;
	gap: 15rpx;
}

.rating {
	font-size: 22rpx;
	color: #ff9500;
}

.review-time {
	font-size: 22rpx;
	color: #999;
}

.review-content {
	font-size: 26rpx;
	color: #666;
	line-height: 1.5;
}

.bottom-actions {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background-color: #fff;
	padding: 20rpx 30rpx;
	border-top: 1rpx solid #eee;
	display: flex;
	gap: 20rpx;
}

.action-btn {
	flex: 1;
	height: 80rpx;
	border-radius: 40rpx;
	font-size: 28rpx;
	border: none;
}

.action-btn.secondary {
	background-color: #f0f0f0;
	color: #666;
}

.action-btn.primary {
	background-color: #4A90E2;
	color: #fff;
}
</style>
