<template>
	<view class="container">
		<!-- 头部横幅 -->
		<view class="header-banner">
			<view class="banner-content">
				<view class="banner-text">
					<text class="banner-title">养老服务</text>
					<text class="banner-subtitle">专业贴心，全方位养老服务</text>
				</view>
				<view class="banner-icon">
					<Icon name="heart-3-line" size="80rpx" color="white"></Icon>
				</view>
			</view>
		</view>

		<!-- 服务分类 -->
		<view class="section">
			<view class="section-header">
				<text class="section-title">服务分类</text>
				<text class="section-subtitle">选择您需要的养老服务</text>
			</view>
			<view class="category-grid">
				<view class="category-item" @click="navigateToCategory('home-care')">
					<view class="category-icon home-care">
						<Icon name="home-heart-line" size="48rpx" color="white"></Icon>
					</view>
					<text class="category-title">居家护理</text>
					<text class="category-desc">专业护理员上门服务</text>
				</view>
				<view class="category-item" @click="navigateToCategory('medical-care')">
					<view class="category-icon medical-care">
						<Icon name="health-book-line" size="48rpx" color="white"></Icon>
					</view>
					<text class="category-title">医疗护理</text>
					<text class="category-desc">医护人员专业护理</text>
				</view>
				<view class="category-item" @click="navigateToCategory('daily-care')">
					<view class="category-icon daily-care">
						<Icon name="user-heart-line" size="48rpx" color="white"></Icon>
					</view>
					<text class="category-title">生活照料</text>
					<text class="category-desc">日常生活全方位照护</text>
				</view>
				<view class="category-item" @click="navigateToCategory('companion-care')">
					<view class="category-icon companion-care">
						<Icon name="emotion-happy-line" size="48rpx" color="white"></Icon>
					</view>
					<text class="category-title">陪护服务</text>
					<text class="category-desc">心理陪伴精神慰藉</text>
				</view>
			</view>
		</view>

		<!-- 热门服务 -->
		<view class="section">
			<view class="section-header">
				<text class="section-title">热门服务</text>
				<text class="section-subtitle">最受欢迎的养老服务</text>
			</view>
			<view class="service-list">
				<view class="service-item" v-for="(item, index) in popularServices" :key="index" @click="viewService(item)">
					<view class="service-image">
						<Icon :name="item.icon" size="60rpx" :color="item.iconColor"></Icon>
					</view>
					<view class="service-content">
						<text class="service-name">{{item.name}}</text>
						<text class="service-desc">{{item.description}}</text>
						<view class="service-features">
							<text class="feature-tag" v-for="feature in item.features" :key="feature">{{feature}}</text>
						</view>
						<view class="service-meta">
							<view class="price-info">
								<text class="price">¥{{item.price}}</text>
								<text class="price-unit">/{{item.unit}}</text>
							</view>
							<view class="rating">
								<Icon name="star-fill" size="24rpx" color="#ffc107"></Icon>
								<text class="rating-text">{{item.rating}}</text>
							</view>
						</view>
					</view>
					<view class="service-action">
						<button class="book-btn" @click.stop="bookService(item)">预约</button>
					</view>
				</view>
			</view>
		</view>

		<!-- 服务流程 -->
		<view class="section">
			<view class="section-header">
				<text class="section-title">服务流程</text>
				<text class="section-subtitle">简单五步，享受专业服务</text>
			</view>
			<view class="process-timeline">
				<view class="process-step" v-for="(step, index) in serviceProcess" :key="index">
					<view class="step-number">{{index + 1}}</view>
					<view class="step-content">
						<text class="step-title">{{step.title}}</text>
						<text class="step-desc">{{step.description}}</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 服务保障 -->
		<view class="section">
			<view class="section-header">
				<text class="section-title">服务保障</text>
				<text class="section-subtitle">让您安心享受服务</text>
			</view>
			<view class="guarantee-list">
				<view class="guarantee-item" v-for="(item, index) in guaranteeList" :key="index">
					<view class="guarantee-icon">
						<Icon :name="item.icon" size="40rpx" :color="item.color"></Icon>
					</view>
					<view class="guarantee-content">
						<text class="guarantee-title">{{item.title}}</text>
						<text class="guarantee-desc">{{item.description}}</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 我的服务 -->
		<view class="section" v-if="myServices.length > 0">
			<view class="section-header">
				<text class="section-title">我的服务</text>
				<text class="section-subtitle">查看服务记录</text>
			</view>
			<view class="my-service-list">
				<view class="my-service-item" v-for="(item, index) in myServices" :key="index">
					<view class="service-header">
						<text class="service-title">{{item.serviceName}}</text>
						<view class="service-status" :class="item.status">{{item.statusText}}</view>
					</view>
					<view class="service-info">
						<text class="service-time">服务时间：{{item.serviceTime}}</text>
						<text class="service-worker">服务人员：{{item.workerName}}</text>
						<text class="service-price">服务费用：¥{{item.price}}</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 底部操作 -->
		<view class="bottom-actions">
			<button class="action-btn secondary" @click="consultService">
				<Icon name="customer-service-2-line" size="32rpx" color="#ff8a00"></Icon>
				<text>咨询客服</text>
			</button>
			<button class="action-btn primary" @click="quickBook">
				<Icon name="add-line" size="32rpx" color="white"></Icon>
				<text>立即预约</text>
			</button>
		</view>
	</view>
</template>

<script>
import Icon from '@/components/Icon/Icon.vue'

export default {
	components: {
		Icon
	},
	data() {
		return {
			popularServices: [
				{
					id: 1,
					name: '专业护理服务',
					description: '专业护理员提供日常护理服务',
					price: 120,
					unit: '小时',
					rating: 4.9,
					icon: 'user-heart-line',
					iconColor: '#e91e63',
					features: ['专业认证', '经验丰富', '贴心服务']
				},
				{
					id: 2,
					name: '医疗陪护服务',
					description: '陪同就医，协助医疗护理',
					price: 150,
					unit: '次',
					rating: 4.8,
					icon: 'health-book-line',
					iconColor: '#4caf50',
					features: ['医疗专业', '陪同就医', '用药提醒']
				},
				{
					id: 3,
					name: '生活照料服务',
					description: '协助日常生活，家务料理',
					price: 80,
					unit: '小时',
					rating: 4.7,
					icon: 'home-heart-line',
					iconColor: '#2196f3',
					features: ['生活照料', '家务协助', '营养配餐']
				},
				{
					id: 4,
					name: '心理陪护服务',
					description: '心理疏导，情感陪伴',
					price: 100,
					unit: '小时',
					rating: 4.6,
					icon: 'emotion-happy-line',
					iconColor: '#ff9800',
					features: ['心理疏导', '情感陪伴', '精神慰藉']
				}
			],
			serviceProcess: [
				{
					title: '选择服务',
					description: '根据需求选择合适的服务类型'
				},
				{
					title: '提交预约',
					description: '填写服务需求，提交预约申请'
				},
				{
					title: '匹配服务员',
					description: '系统自动匹配合适的服务人员'
				},
				{
					title: '确认服务',
					description: '确认服务时间和具体安排'
				},
				{
					title: '享受服务',
					description: '专业服务人员上门提供服务'
				}
			],
			guaranteeList: [
				{
					title: '专业认证',
					description: '所有服务人员均经过专业培训认证',
					icon: 'shield-check-line',
					color: '#4caf50'
				},
				{
					title: '保险保障',
					description: '服务过程全程保险保障',
					icon: 'shield-line',
					color: '#2196f3'
				},
				{
					title: '质量监督',
					description: '专业团队全程质量监督',
					icon: 'eye-line',
					color: '#ff9800'
				},
				{
					title: '满意保证',
					description: '不满意可申请重新服务或退款',
					icon: 'thumb-up-line',
					color: '#e91e63'
				}
			],
			myServices: [
				// 示例数据，实际应从后端获取
			]
		}
	},
	methods: {
		navigateToCategory(category) {
			uni.navigateTo({
				url: `/pages/care-service/category?type=${category}`
			});
		},
		viewService(service) {
			uni.navigateTo({
				url: `/pages/care-service/detail?id=${service.id}`
			});
		},
		bookService(service) {
			uni.navigateTo({
				url: `/pages/care-service/booking?serviceId=${service.id}`
			});
		},
		consultService() {
			uni.makePhoneCall({
				phoneNumber: '************'
			});
		},
		quickBook() {
			if (this.popularServices.length > 0) {
				this.bookService(this.popularServices[0]);
			}
		}
	}
}
</script>

<style scoped>
.container {
	background: linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%);
	min-height: 100vh;
	padding-bottom: 200rpx;
}

.header-banner {
	padding: 40rpx;
	margin-bottom: 40rpx;
}

.banner-content {
	background: rgba(255, 255, 255, 0.15);
	border-radius: 30rpx;
	padding: 40rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.banner-text {
	flex: 1;
}

.banner-title {
	font-size: 48rpx;
	font-weight: bold;
	color: white;
	display: block;
	margin-bottom: 15rpx;
}

.banner-subtitle {
	font-size: 28rpx;
	color: rgba(255, 255, 255, 0.9);
	display: block;
}

.banner-icon {
	margin-left: 30rpx;
}

.section {
	margin-bottom: 40rpx;
}

.section-header {
	padding: 0 40rpx 30rpx;
}

.section-title {
	font-size: 36rpx;
	font-weight: bold;
	color: white;
	display: block;
	margin-bottom: 10rpx;
}

.section-subtitle {
	font-size: 26rpx;
	color: rgba(255, 255, 255, 0.8);
	display: block;
}

.category-grid {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	gap: 30rpx;
	padding: 0 40rpx;
}

.category-item {
	background: white;
	border-radius: 30rpx;
	padding: 40rpx;
	text-align: center;
	box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.category-icon {
	width: 100rpx;
	height: 100rpx;
	border-radius: 20rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin: 0 auto 20rpx;
}

.category-icon.home-care { background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%); }
.category-icon.medical-care { background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%); }
.category-icon.daily-care { background: linear-gradient(135deg, #e91e63 0%, #c2185b 100%); }
.category-icon.companion-care { background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%); }

.category-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 10rpx;
}

.category-desc {
	font-size: 24rpx;
	color: #666;
	display: block;
}

.service-list, .guarantee-list, .my-service-list {
	padding: 0 40rpx;
}

.service-item, .my-service-item {
	background: white;
	border-radius: 30rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	display: flex;
	align-items: center;
	gap: 20rpx;
	box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.service-image {
	width: 100rpx;
	height: 100rpx;
	background: #f8f9fa;
	border-radius: 20rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.service-content {
	flex: 1;
}

.service-name {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 8rpx;
}

.service-desc {
	font-size: 26rpx;
	color: #666;
	display: block;
	margin-bottom: 15rpx;
}

.service-features {
	display: flex;
	gap: 10rpx;
	margin-bottom: 15rpx;
}

.feature-tag {
	background: rgba(255, 138, 0, 0.1);
	color: #ff8a00;
	font-size: 22rpx;
	padding: 5rpx 15rpx;
	border-radius: 15rpx;
}

.service-meta {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.price-info {
	display: flex;
	align-items: baseline;
	gap: 5rpx;
}

.price {
	font-size: 32rpx;
	font-weight: bold;
	color: #ff8a00;
}

.price-unit {
	font-size: 24rpx;
	color: #999;
}

.rating {
	display: flex;
	align-items: center;
	gap: 8rpx;
}

.rating-text {
	font-size: 24rpx;
	color: #666;
}

.book-btn {
	background: linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%);
	color: white;
	border: none;
	padding: 15rpx 30rpx;
	border-radius: 20rpx;
	font-size: 26rpx;
	font-weight: 500;
}

.process-timeline {
	padding: 0 40rpx;
}

.process-step {
	display: flex;
	align-items: center;
	gap: 30rpx;
	background: white;
	border-radius: 30rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.step-number {
	width: 60rpx;
	height: 60rpx;
	background: linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	color: white;
	font-size: 28rpx;
	font-weight: bold;
}

.step-content {
	flex: 1;
}

.step-title {
	font-size: 30rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 8rpx;
}

.step-desc {
	font-size: 26rpx;
	color: #666;
	display: block;
}

.guarantee-item {
	background: white;
	border-radius: 30rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	display: flex;
	align-items: center;
	gap: 20rpx;
	box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.guarantee-icon {
	width: 80rpx;
	height: 80rpx;
	background: #f8f9fa;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
}

.guarantee-content {
	flex: 1;
}

.guarantee-title {
	font-size: 30rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 8rpx;
}

.guarantee-desc {
	font-size: 26rpx;
	color: #666;
	display: block;
}

.my-service-item {
	flex-direction: column;
	align-items: stretch;
}

.service-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 15rpx;
}

.service-title {
	font-size: 30rpx;
	font-weight: bold;
	color: #333;
}

.service-status {
	padding: 8rpx 20rpx;
	border-radius: 20rpx;
	font-size: 24rpx;
}

.service-status.completed {
	background: rgba(76, 175, 80, 0.1);
	color: #4caf50;
}

.service-status.ongoing {
	background: rgba(255, 152, 0, 0.1);
	color: #ff9800;
}

.service-info {
	display: flex;
	flex-direction: column;
	gap: 8rpx;
}

.service-time, .service-worker, .service-price {
	font-size: 26rpx;
	color: #666;
}

.bottom-actions {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background: white;
	padding: 30rpx 40rpx;
	display: flex;
	gap: 20rpx;
	box-shadow: 0 -4rpx 20rpx rgba(0,0,0,0.1);
}

.action-btn {
	flex: 1;
	height: 100rpx;
	border-radius: 20rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 15rpx;
	font-size: 32rpx;
	font-weight: 500;
}

.action-btn.primary {
	background: linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%);
	color: white;
	border: none;
}

.action-btn.secondary {
	background: white;
	color: #ff8a00;
	border: 2rpx solid #ff8a00;
}
</style>
