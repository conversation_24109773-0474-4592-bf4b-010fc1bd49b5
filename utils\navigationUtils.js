/**
 * 导航工具类
 * 统一管理页面跳转，提供错误处理和用户体验优化
 * 支持跨平台兼容性和性能优化
 */

// 平台检测
const PLATFORM = {
  H5: 'h5',
  MP_WEIXIN: 'mp-weixin',
  MP_ALIPAY: 'mp-alipay',
  MP_BAIDU: 'mp-baidu',
  MP_TOUTIAO: 'mp-toutiao',
  MP_QQ: 'mp-qq',
  APP_PLUS: 'app-plus'
}

// 获取当前平台
function getCurrentPlatform() {
  // #ifdef H5
  return PLATFORM.H5
  // #endif

  // #ifdef MP-WEIXIN
  return PLATFORM.MP_WEIXIN
  // #endif

  // #ifdef MP-ALIPAY
  return PLATFORM.MP_ALIPAY
  // #endif

  // #ifdef APP-PLUS
  return PLATFORM.APP_PLUS
  // #endif

  return 'unknown'
}

// TabBar页面列表
const TAB_BAR_PAGES = [
  '/pages/home/<USER>',
  '/pages/workspace/workspace', 
  '/pages/map/map',
  '/pages/profile/profile'
]

// 已存在的页面列表
const EXISTING_PAGES = [
  '/pages/index/index',
  '/pages/home/<USER>',
  '/pages/login/login',
  '/pages/workspace/workspace',
  '/pages/map/map',
  '/pages/profile/profile',
  '/pages/institution/select',
  '/pages/institution/detail',
  '/pages/institution/list',
  '/pages/institution/manage',
  '/pages/service/find',
  '/pages/service/list',
  '/pages/service/detail',
  '/pages/service/manage',
  '/pages/subsidy/receive',
  '/pages/subsidy/list',
  '/pages/subsidy/detail',
  '/pages/elderly/settings',
  '/pages/news/list',
  '/pages/news/detail',
  '/pages/test/icons',
  '/pages/test/icons-gallery',
  '/pages/test/icon-showcase',
  '/pages/test/icon-validator'
]

// 缺失页面的替代方案
const PAGE_FALLBACKS = {
  '/pages/appointment/list': '/pages/service/list',
  '/pages/appointment/create': '/pages/service/detail',
  '/pages/notification/list': '/pages/home/<USER>',
  '/pages/hospital/detail': '/pages/institution/detail',
  '/pages/contact/service': '/pages/profile/profile',
  '/pages/payment/pay': '/pages/profile/profile',
  '/pages/order/detail': '/pages/profile/profile',
  '/pages/order/evaluate': '/pages/profile/profile',
  '/pages/subsidy/status': '/pages/subsidy/list',
  '/pages/consultation/list': '/pages/service/list'
}

/**
 * 检查页面是否存在
 */
function pageExists(url) {
  const path = url.split('?')[0] // 移除查询参数
  return EXISTING_PAGES.includes(path)
}

/**
 * 检查是否为TabBar页面
 */
function isTabBarPage(url) {
  const path = url.split('?')[0]
  return TAB_BAR_PAGES.includes(path)
}

/**
 * 获取页面的替代方案
 */
function getFallbackPage(url) {
  const path = url.split('?')[0]
  return PAGE_FALLBACKS[path] || '/pages/home/<USER>'
}

/**
 * 显示跳转加载提示
 */
function showNavigationLoading(title = '跳转中...') {
  uni.showLoading({
    title,
    mask: true
  })
  
  // 自动隐藏加载提示
  setTimeout(() => {
    uni.hideLoading()
  }, 1000)
}

/**
 * 统一的错误处理
 */
function handleNavigationError(error, url) {
  console.error('页面跳转失败:', error, 'URL:', url)
  
  uni.showToast({
    title: '页面跳转失败',
    icon: 'none',
    duration: 2000
  })
}

/**
 * 防抖处理，防止快速重复点击
 */
let navigationTimer = null
function debounceNavigation(callback, delay = 500) {
  if (navigationTimer) {
    clearTimeout(navigationTimer)
  }

  navigationTimer = setTimeout(callback, delay)
}

/**
 * 性能监控
 */
const performanceMonitor = {
  navigationStartTime: 0,

  startNavigation() {
    this.navigationStartTime = Date.now()
  },

  endNavigation(url) {
    const duration = Date.now() - this.navigationStartTime
    console.log(`页面跳转耗时: ${duration}ms, URL: ${url}`)

    // 如果跳转时间过长，记录警告
    if (duration > 2000) {
      console.warn(`页面跳转耗时过长: ${duration}ms`)
    }
  }
}

/**
 * 适老化支持
 */
function getElderlyModeSettings() {
  try {
    return uni.getStorageSync('elderlyMode') || false
  } catch (e) {
    return false
  }
}

function showElderlyFriendlyLoading(title = '正在跳转...') {
  const isElderlyMode = getElderlyModeSettings()

  if (isElderlyMode) {
    // 适老化模式：更明显的提示
    uni.showLoading({
      title: title,
      mask: true
    })

    // 语音提示（如果支持）
    // #ifdef APP-PLUS
    try {
      plus.speech && plus.speech.startSpeech({
        content: title,
        volume: 0.5
      })
    } catch (e) {
      console.log('语音提示不可用')
    }
    // #endif
  } else {
    showNavigationLoading(title)
  }
}

/**
 * 跨平台兼容性处理
 */
function getPlatformSpecificOptions(url, options = {}) {
  const platform = getCurrentPlatform()
  const platformOptions = { ...options }

  switch (platform) {
    case PLATFORM.H5:
      // H5平台特殊处理
      break

    case PLATFORM.MP_WEIXIN:
      // 微信小程序特殊处理
      break

    case PLATFORM.APP_PLUS:
      // App平台特殊处理
      platformOptions.animationType = options.animationType || 'slide-in-right'
      platformOptions.animationDuration = options.animationDuration || 300
      break
  }

  return platformOptions
}

/**
 * 安全的页面跳转 - navigateTo
 */
export function safeNavigateTo(url, options = {}) {
  return new Promise((resolve, reject) => {
    // 防抖处理
    debounceNavigation(() => {
      // 开始性能监控
      performanceMonitor.startNavigation()

      // 检查页面是否存在
      if (!pageExists(url)) {
        console.warn(`页面不存在: ${url}，使用替代页面`)
        const fallbackUrl = getFallbackPage(url)

        uni.showModal({
          title: '页面提示',
          content: '该功能正在开发中，为您跳转到相关页面',
          showCancel: false,
          success: () => {
            safeNavigateTo(fallbackUrl, options).then(resolve).catch(reject)
          }
        })
        return
      }

      // 检查是否为TabBar页面
      if (isTabBarPage(url)) {
        console.warn(`${url} 是TabBar页面，应使用 switchTab`)
        safeSwitchTab(url, options).then(resolve).catch(reject)
        return
      }

      // 显示加载提示（适老化支持）
      if (options.showLoading !== false) {
        showElderlyFriendlyLoading('正在跳转...')
      }

      // 获取平台特定选项
      const platformOptions = getPlatformSpecificOptions(url, options)

      // 执行跳转
      uni.navigateTo({
        url,
        ...platformOptions,
        success: (res) => {
          console.log('页面跳转成功:', url)
          performanceMonitor.endNavigation(url)
          uni.hideLoading()
          options.success && options.success(res)
          resolve(res)
        },
        fail: (error) => {
          performanceMonitor.endNavigation(url)
          uni.hideLoading()
          handleNavigationError(error, url)
          options.fail && options.fail(error)
          reject(error)
        }
      })
    }, options.debounce !== false ? 300 : 0)
  })
}

/**
 * 安全的TabBar跳转 - switchTab
 */
export function safeSwitchTab(url, options = {}) {
  return new Promise((resolve, reject) => {
    const path = url.split('?')[0]
    
    if (!isTabBarPage(path)) {
      console.error(`${path} 不是TabBar页面`)
      reject(new Error('不是TabBar页面'))
      return
    }
    
    // 防抖处理
    debounceNavigation(() => {
      uni.switchTab({
        url: path, // TabBar跳转不支持参数
        success: (res) => {
          console.log('TabBar跳转成功:', path)
          options.success && options.success(res)
          resolve(res)
        },
        fail: (error) => {
          handleNavigationError(error, path)
          options.fail && options.fail(error)
          reject(error)
        }
      })
    }, options.debounce !== false ? 300 : 0)
  })
}

/**
 * 安全的页面重定向 - redirectTo
 */
export function safeRedirectTo(url, options = {}) {
  return new Promise((resolve, reject) => {
    if (!pageExists(url)) {
      console.warn(`页面不存在: ${url}，使用替代页面`)
      url = getFallbackPage(url)
    }
    
    if (isTabBarPage(url)) {
      console.warn(`${url} 是TabBar页面，应使用 reLaunch`)
      safeReLaunch(url, options).then(resolve).catch(reject)
      return
    }
    
    uni.redirectTo({
      url,
      success: (res) => {
        console.log('页面重定向成功:', url)
        options.success && options.success(res)
        resolve(res)
      },
      fail: (error) => {
        handleNavigationError(error, url)
        options.fail && options.fail(error)
        reject(error)
      }
    })
  })
}

/**
 * 安全的页面重启 - reLaunch
 */
export function safeReLaunch(url, options = {}) {
  return new Promise((resolve, reject) => {
    if (!pageExists(url)) {
      console.warn(`页面不存在: ${url}，使用替代页面`)
      url = getFallbackPage(url)
    }
    
    uni.reLaunch({
      url,
      success: (res) => {
        console.log('页面重启成功:', url)
        options.success && options.success(res)
        resolve(res)
      },
      fail: (error) => {
        handleNavigationError(error, url)
        options.fail && options.fail(error)
        reject(error)
      }
    })
  })
}

/**
 * 安全的页面返回 - navigateBack
 */
export function safeNavigateBack(delta = 1, options = {}) {
  return new Promise((resolve, reject) => {
    const pages = getCurrentPages()
    
    if (pages.length <= delta) {
      console.warn('无法返回，跳转到首页')
      safeReLaunch('/pages/home/<USER>', options).then(resolve).catch(reject)
      return
    }
    
    uni.navigateBack({
      delta,
      success: (res) => {
        console.log('页面返回成功')
        options.success && options.success(res)
        resolve(res)
      },
      fail: (error) => {
        console.error('页面返回失败:', error)
        // 返回失败时跳转到首页
        safeReLaunch('/pages/home/<USER>', options).then(resolve).catch(reject)
      }
    })
  })
}

/**
 * 智能跳转 - 自动选择合适的跳转方式
 */
export function smartNavigate(url, options = {}) {
  if (isTabBarPage(url)) {
    return safeSwitchTab(url, options)
  } else {
    return safeNavigateTo(url, options)
  }
}

/**
 * 批量页面预检查
 */
export function validatePages(urls) {
  const results = {
    valid: [],
    invalid: [],
    tabBarPages: [],
    fallbacks: []
  }
  
  urls.forEach(url => {
    const path = url.split('?')[0]
    
    if (pageExists(path)) {
      results.valid.push(url)
      if (isTabBarPage(path)) {
        results.tabBarPages.push(url)
      }
    } else {
      results.invalid.push(url)
      results.fallbacks.push({
        original: url,
        fallback: getFallbackPage(url)
      })
    }
  })
  
  return results
}

// 导出工具对象
export default {
  safeNavigateTo,
  safeSwitchTab,
  safeRedirectTo,
  safeReLaunch,
  safeNavigateBack,
  smartNavigate,
  validatePages,
  pageExists,
  isTabBarPage,
  getFallbackPage
}
