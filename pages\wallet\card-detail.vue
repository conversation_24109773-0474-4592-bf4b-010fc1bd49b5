<template>
	<view class="container">
		<!-- 自定义导航栏 -->
		<view class="custom-navbar">
			<view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
			<view class="navbar-content">
				<view class="navbar-left" @click="goBack">
					<Icon name="arrow-left-line" size="36rpx" color="#333"></Icon>
					<text class="back-text">返回</text>
				</view>
				<view class="navbar-center">
					<text class="navbar-title">银行卡详情</text>
				</view>
				<view class="navbar-right"></view>
			</view>
		</view>

		<!-- 银行卡信息 -->
		<view class="card-info-section">
			<view class="card-visual">
				<view class="card-bg" :class="cardDetail.bankCode">
					<view class="card-header">
						<text class="bank-name">{{cardDetail.bankName}}</text>
						<view class="card-type-badge">
							<text class="type-text">{{cardDetail.type}}</text>
						</view>
					</view>
					<view class="card-number">
						<text class="number-text">****  ****  ****  {{cardDetail.lastFour}}</text>
					</view>
					<view class="card-footer">
						<text class="holder-name">{{cardDetail.holderName}}</text>
						<view class="default-badge" v-if="cardDetail.isDefault">
							<text class="default-text">默认</text>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 卡片操作 -->
		<view class="action-section">
			<view class="action-item" @click="setAsDefault" v-if="!cardDetail.isDefault">
				<view class="action-icon">
					<Icon name="star-line" size="32rpx" color="#ff8a00"></Icon>
				</view>
				<text class="action-text">设为默认</text>
				<Icon name="arrow-right-s-line" size="20rpx" color="#999"></Icon>
			</view>
			
			<view class="action-item" @click="updateCardInfo">
				<view class="action-icon">
					<Icon name="edit-line" size="32rpx" color="#4caf50"></Icon>
				</view>
				<text class="action-text">修改信息</text>
				<Icon name="arrow-right-s-line" size="20rpx" color="#999"></Icon>
			</view>
			
			<view class="action-item" @click="viewTransactions">
				<view class="action-icon">
					<Icon name="file-list-line" size="32rpx" color="#2196f3"></Icon>
				</view>
				<text class="action-text">交易记录</text>
				<Icon name="arrow-right-s-line" size="20rpx" color="#999"></Icon>
			</view>
			
			<view class="action-item danger" @click="removeCard">
				<view class="action-icon">
					<Icon name="delete-bin-line" size="32rpx" color="#f44336"></Icon>
				</view>
				<text class="action-text">删除银行卡</text>
				<Icon name="arrow-right-s-line" size="20rpx" color="#999"></Icon>
			</view>
		</view>

		<!-- 安全提示 -->
		<view class="security-section">
			<view class="security-title">
				<Icon name="shield-check-line" size="24rpx" color="#4caf50"></Icon>
				<text>安全提示</text>
			</view>
			<view class="security-content">
				<text class="security-item">• 请妥善保管您的银行卡信息</text>
				<text class="security-item">• 如发现异常交易，请及时联系银行</text>
				<text class="security-item">• 删除银行卡不会影响已完成的交易</text>
				<text class="security-item">• 如有疑问，请联系客服：400-123-4567</text>
			</view>
		</view>

		<!-- 功能开发中提示 -->
		<view class="dev-notice">
			<Icon name="tools-line" size="32rpx" color="#ff8a00"></Icon>
			<text class="dev-text">此功能正在开发中，敬请期待</text>
		</view>
	</view>
</template>

<script>
import Icon from '@/components/Icon/Icon.vue'

export default {
	components: {
		Icon
	},
	data() {
		return {
			statusBarHeight: 0,
			cardId: '',
			cardDetail: {
				id: 1,
				bankName: '中国工商银行',
				bankCode: 'icbc',
				lastFour: '6688',
				type: '储蓄卡',
				holderName: '张大爷',
				isDefault: true,
				addTime: '2024-01-10'
			}
		}
	},
	onLoad(options) {
		const systemInfo = uni.getSystemInfoSync();
		this.statusBarHeight = systemInfo.statusBarHeight || 44;
		
		if (options.id) {
			this.cardId = options.id;
			this.loadCardDetail();
		}
	},
	methods: {
		loadCardDetail() {
			// 模拟加载银行卡详情
			console.log('加载银行卡详情:', this.cardId);
		},
		setAsDefault() {
			uni.showModal({
				title: '设为默认',
				content: '确定要将此银行卡设为默认吗？',
				success: (res) => {
					if (res.confirm) {
						this.cardDetail.isDefault = true;
						uni.showToast({
							title: '功能开发中，敬请期待',
							icon: 'none',
							duration: 2000
						});
					}
				}
			});
		},
		updateCardInfo() {
			uni.showToast({
				title: '修改信息功能开发中',
				icon: 'none',
				duration: 2000
			});
		},
		viewTransactions() {
			uni.navigateTo({
				url: `/pages/wallet/transactions?cardId=${this.cardId}`
			});
		},
		removeCard() {
			if (this.cardDetail.isDefault) {
				uni.showToast({
					title: '默认银行卡不能删除',
					icon: 'none',
					duration: 2000
				});
				return;
			}
			
			uni.showModal({
				title: '删除银行卡',
				content: '确定要删除这张银行卡吗？删除后无法恢复。',
				confirmColor: '#f44336',
				success: (res) => {
					if (res.confirm) {
						this.processRemoveCard();
					}
				}
			});
		},
		processRemoveCard() {
			uni.showLoading({
				title: '正在删除...',
				mask: true
			});
			
			// 模拟删除流程
			setTimeout(() => {
				uni.hideLoading();
				uni.showToast({
					title: '功能开发中，敬请期待',
					icon: 'none',
					duration: 2000
				});
			}, 2000);
		},
		goBack() {
			uni.navigateBack({
				fail: () => {
					uni.navigateTo({
						url: '/pages/wallet/wallet'
					});
				}
			});
		}
	}
}
</script>

<style scoped>
.container {
	background: #f8f9fa;
	min-height: 100vh;
}

/* 导航栏样式 */
.custom-navbar {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 1000;
	background: rgba(255, 255, 255, 0.95);
	backdrop-filter: blur(20rpx);
	border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);
}

.navbar-content {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 20rpx 32rpx;
	min-height: 88rpx;
}

.navbar-left {
	display: flex;
	align-items: center;
	gap: 12rpx;
	padding: 8rpx 16rpx 8rpx 0;
	border-radius: 20rpx;
	transition: all 0.2s ease;
}

.navbar-left:active {
	background-color: rgba(0, 0, 0, 0.04);
	transform: scale(0.96);
}

.back-text {
	font-size: 32rpx;
	color: #333;
	font-weight: 500;
}

.navbar-center {
	position: absolute;
	left: 50%;
	transform: translateX(-50%);
}

.navbar-title {
	font-size: 34rpx;
	font-weight: 600;
	color: #333;
}

.navbar-right {
	display: flex;
	align-items: center;
}

/* 银行卡信息 */
.card-info-section {
	padding: 40rpx;
	margin-top: 220rpx;
}

.card-visual {
	perspective: 1000rpx;
}

.card-bg {
	width: 100%;
	height: 360rpx;
	border-radius: 30rpx;
	padding: 40rpx;
	color: white;
	position: relative;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.2);
	display: flex;
	flex-direction: column;
	justify-content: space-between;
}

.card-bg.icbc {
	background: linear-gradient(135deg, #c41e3a 0%, #8b0000 100%);
}

.card-bg.ccb {
	background: linear-gradient(135deg, #0052cc 0%, #003d99 100%);
}

.card-bg.abc {
	background: linear-gradient(135deg, #00a651 0%, #007a3d 100%);
}

.card-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.bank-name {
	font-size: 32rpx;
	font-weight: bold;
}

.card-type-badge {
	background: rgba(255, 255, 255, 0.2);
	padding: 8rpx 16rpx;
	border-radius: 15rpx;
}

.type-text {
	font-size: 22rpx;
}

.card-number {
	text-align: center;
	margin: 40rpx 0;
}

.number-text {
	font-size: 36rpx;
	font-weight: bold;
	letter-spacing: 4rpx;
}

.card-footer {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.holder-name {
	font-size: 28rpx;
	font-weight: 500;
}

.default-badge {
	background: rgba(255, 255, 255, 0.2);
	padding: 6rpx 12rpx;
	border-radius: 12rpx;
}

.default-text {
	font-size: 20rpx;
}

/* 操作区域 */
.action-section {
	background: white;
	margin: 20rpx;
	border-radius: 30rpx;
	padding: 40rpx;
}

.action-item {
	display: flex;
	align-items: center;
	padding: 25rpx 0;
	border-bottom: 1rpx solid #f0f0f0;
	transition: all 0.2s ease;
}

.action-item:last-child {
	border-bottom: none;
}

.action-item:active {
	background: rgba(0, 0, 0, 0.02);
	border-radius: 15rpx;
}

.action-item.danger:active {
	background: rgba(244, 67, 54, 0.05);
}

.action-icon {
	width: 60rpx;
	height: 60rpx;
	border-radius: 50%;
	background: #f8f9fa;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 25rpx;
}

.action-text {
	font-size: 28rpx;
	color: #333;
	flex: 1;
}

.action-item.danger .action-text {
	color: #f44336;
}

/* 安全提示 */
.security-section {
	background: white;
	margin: 20rpx;
	border-radius: 30rpx;
	padding: 40rpx;
}

.security-title {
	display: flex;
	align-items: center;
	gap: 10rpx;
	font-size: 28rpx;
	font-weight: bold;
	color: #4caf50;
	margin-bottom: 20rpx;
}

.security-content {
	display: flex;
	flex-direction: column;
	gap: 15rpx;
}

.security-item {
	font-size: 26rpx;
	color: #666;
	line-height: 1.5;
}

.dev-notice {
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 15rpx;
	padding: 30rpx;
	margin: 20rpx;
	background: rgba(255, 138, 0, 0.1);
	border-radius: 20rpx;
}

.dev-text {
	font-size: 26rpx;
	color: #ff8a00;
}
</style>
