<template>
	<view class="container">
		<!-- 顶部装饰 -->
		<view class="header-decoration">
			<view class="decoration-circle circle1"></view>
			<view class="decoration-circle circle2"></view>
			<view class="decoration-circle circle3"></view>
		</view>

		<!-- 应用Logo和标题 -->
		<view class="app-header">
			<view class="app-logo">
				<Icon name="heart-3-line" size="120rpx" color="#ff8a00"></Icon>
			</view>
			<text class="app-title">智慧养老</text>
			<text class="app-subtitle">专业的老年人智慧生活服务平台</text>
		</view>

		<!-- 登录表单 -->
		<view class="login-form">
			<view class="form-item">
				<view class="input-wrapper">
					<Icon name="user-line" size="32rpx" color="#999"></Icon>
					<input 
						class="form-input" 
						type="text" 
						placeholder="请输入手机号码"
						v-model="loginForm.phone"
						maxlength="11"
					/>
				</view>
			</view>
			
			<view class="form-item">
				<view class="input-wrapper">
					<Icon name="lock-password-line" size="32rpx" color="#999"></Icon>
					<input 
						class="form-input" 
						:type="showPassword ? 'text' : 'password'" 
						placeholder="请输入密码"
						v-model="loginForm.password"
					/>
					<Icon 
						:name="showPassword ? 'eye-line' : 'eye-off-line'" 
						size="32rpx" 
						color="#999"
						@click="togglePassword"
					></Icon>
				</view>
			</view>

			<!-- 验证码登录 -->
			<view class="form-item" v-if="loginType === 'sms'">
				<view class="input-wrapper">
					<Icon name="mail-line" size="32rpx" color="#999"></Icon>
					<input 
						class="form-input" 
						type="number" 
						placeholder="请输入验证码"
						v-model="loginForm.smsCode"
						maxlength="6"
					/>
					<button 
						class="sms-btn" 
						:disabled="smsCountdown > 0"
						@click="sendSmsCode"
					>
						{{smsCountdown > 0 ? `${smsCountdown}s` : '获取验证码'}}
					</button>
				</view>
			</view>

			<!-- 登录方式切换 -->
			<view class="login-type-switch">
				<text 
					class="switch-item" 
					:class="{ active: loginType === 'password' }"
					@click="switchLoginType('password')"
				>密码登录</text>
				<text 
					class="switch-item" 
					:class="{ active: loginType === 'sms' }"
					@click="switchLoginType('sms')"
				>验证码登录</text>
			</view>

			<!-- 记住密码和忘记密码 -->
			<view class="form-options">
				<view class="remember-password" @click="toggleRemember">
					<Icon 
						:name="rememberPassword ? 'checkbox-circle-fill' : 'checkbox-circle-line'" 
						size="32rpx" 
						:color="rememberPassword ? '#ff8a00' : '#999'"
					></Icon>
					<text class="option-text">记住密码</text>
				</view>
				<text class="forgot-password" @click="forgotPassword">忘记密码？</text>
			</view>

			<!-- 登录按钮 -->
			<button class="login-btn" @click="handleLogin" :disabled="!canLogin">
				{{loginType === 'password' ? '登录' : '验证码登录'}}
			</button>

			<!-- 快速登录 -->
			<view class="quick-login">
				<text class="quick-login-title">快速登录</text>
				<view class="quick-login-methods">
					<view class="quick-method" @click="wechatLogin">
						<Icon name="wechat-line" size="48rpx" color="#07c160"></Icon>
						<text class="method-text">微信</text>
					</view>
					<view class="quick-method" @click="biometricLogin">
						<Icon name="fingerprint-line" size="48rpx" color="#ff8a00"></Icon>
						<text class="method-text">指纹</text>
					</view>
					<view class="quick-method" @click="faceLogin">
						<Icon name="user-smile-line" size="48rpx" color="#2196f3"></Icon>
						<text class="method-text">面容</text>
					</view>
				</view>
			</view>

			<!-- 注册链接 -->
			<view class="register-link">
				<text class="register-text">还没有账号？</text>
				<text class="register-btn" @click="goRegister">立即注册</text>
			</view>
		</view>

		<!-- 底部协议 -->
		<view class="agreement-section">
			<view class="agreement-check" @click="toggleAgreement">
				<Icon 
					:name="agreeToTerms ? 'checkbox-circle-fill' : 'checkbox-circle-line'" 
					size="24rpx" 
					:color="agreeToTerms ? '#ff8a00' : '#999'"
				></Icon>
				<text class="agreement-text">我已阅读并同意</text>
			</view>
			<view class="agreement-links">
				<text class="agreement-link" @click="viewUserAgreement">《用户协议》</text>
				<text class="agreement-text">和</text>
				<text class="agreement-link" @click="viewPrivacyPolicy">《隐私政策》</text>
			</view>
		</view>
	</view>
</template>

<script>
import Icon from '@/components/Icon/Icon.vue'

export default {
	components: {
		Icon
	},
	data() {
		return {
			loginType: 'password', // password | sms
			showPassword: false,
			rememberPassword: false,
			agreeToTerms: false,
			smsCountdown: 0,
			loginForm: {
				phone: '',
				password: '',
				smsCode: ''
			}
		}
	},
	computed: {
		canLogin() {
			if (!this.agreeToTerms) return false;
			if (!this.loginForm.phone) return false;
			
			if (this.loginType === 'password') {
				return this.loginForm.password.length >= 6;
			} else {
				return this.loginForm.smsCode.length === 6;
			}
		}
	},
	onLoad() {
		this.loadSavedCredentials();
	},
	methods: {
		loadSavedCredentials() {
			// 加载保存的登录信息
			const savedPhone = uni.getStorageSync('savedPhone');
			const savedPassword = uni.getStorageSync('savedPassword');
			const rememberPassword = uni.getStorageSync('rememberPassword');
			
			if (rememberPassword && savedPhone) {
				this.loginForm.phone = savedPhone;
				this.loginForm.password = savedPassword || '';
				this.rememberPassword = true;
			}
		},
		switchLoginType(type) {
			this.loginType = type;
			// 清空相关字段
			if (type === 'password') {
				this.loginForm.smsCode = '';
			} else {
				this.loginForm.password = '';
			}
		},
		togglePassword() {
			this.showPassword = !this.showPassword;
		},
		toggleRemember() {
			this.rememberPassword = !this.rememberPassword;
		},
		toggleAgreement() {
			this.agreeToTerms = !this.agreeToTerms;
		},
		sendSmsCode() {
			if (!this.loginForm.phone || this.loginForm.phone.length !== 11) {
				uni.showToast({
					title: '请输入正确的手机号',
					icon: 'none'
				});
				return;
			}
			
			// 开始倒计时
			this.smsCountdown = 60;
			const timer = setInterval(() => {
				this.smsCountdown--;
				if (this.smsCountdown <= 0) {
					clearInterval(timer);
				}
			}, 1000);
			
			// 模拟发送验证码
			uni.showToast({
				title: '验证码已发送',
				icon: 'success'
			});
		},
		handleLogin() {
			if (!this.canLogin) return;
			
			uni.showLoading({
				title: '登录中...'
			});
			
			// 模拟登录请求
			setTimeout(() => {
				uni.hideLoading();
				
				// 保存登录信息
				if (this.rememberPassword) {
					uni.setStorageSync('savedPhone', this.loginForm.phone);
					uni.setStorageSync('savedPassword', this.loginForm.password);
					uni.setStorageSync('rememberPassword', true);
				} else {
					uni.removeStorageSync('savedPhone');
					uni.removeStorageSync('savedPassword');
					uni.removeStorageSync('rememberPassword');
				}
				
				// 保存用户信息
				uni.setStorageSync('userInfo', {
					phone: this.loginForm.phone,
					name: '张大爷',
					isLoggedIn: true
				});
				
				uni.showToast({
					title: '登录成功',
					icon: 'success'
				});
				
				// 跳转到首页
				setTimeout(() => {
					uni.reLaunch({
						url: '/pages/home/<USER>'
					});
				}, 1500);
			}, 2000);
		},
		wechatLogin() {
			uni.showToast({
				title: '微信登录功能开发中',
				icon: 'none'
			});
		},
		biometricLogin() {
			uni.showToast({
				title: '指纹登录功能开发中',
				icon: 'none'
			});
		},
		faceLogin() {
			uni.showToast({
				title: '面容登录功能开发中',
				icon: 'none'
			});
		},
		forgotPassword() {
			uni.navigateTo({
				url: '/pages/login/forgot-password'
			});
		},
		goRegister() {
			uni.navigateTo({
				url: '/pages/login/register'
			});
		},
		viewUserAgreement() {
			uni.navigateTo({
				url: '/pages/legal/user-agreement'
			});
		},
		viewPrivacyPolicy() {
			uni.navigateTo({
				url: '/pages/legal/privacy-policy'
			});
		}
	}
}
</script>

<style scoped>
.container {
	background: linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%);
	min-height: 100vh;
	position: relative;
	overflow: hidden;
}

.header-decoration {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	height: 400rpx;
}

.decoration-circle {
	position: absolute;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.1);
}

.circle1 {
	width: 200rpx;
	height: 200rpx;
	top: -100rpx;
	right: -100rpx;
}

.circle2 {
	width: 150rpx;
	height: 150rpx;
	top: 100rpx;
	left: -75rpx;
}

.circle3 {
	width: 100rpx;
	height: 100rpx;
	top: 200rpx;
	right: 100rpx;
}

.app-header {
	text-align: center;
	padding: 120rpx 40rpx 80rpx;
	position: relative;
	z-index: 1;
}

.app-logo {
	margin-bottom: 30rpx;
}

.app-title {
	font-size: 56rpx;
	font-weight: bold;
	color: white;
	display: block;
	margin-bottom: 15rpx;
}

.app-subtitle {
	font-size: 28rpx;
	color: rgba(255, 255, 255, 0.9);
	display: block;
}

.login-form {
	background: white;
	border-radius: 40rpx 40rpx 0 0;
	padding: 60rpx 40rpx 40rpx;
	margin-top: 40rpx;
	position: relative;
	z-index: 1;
}

.form-item {
	margin-bottom: 40rpx;
}

.input-wrapper {
	display: flex;
	align-items: center;
	background: #f8f9fa;
	border-radius: 25rpx;
	padding: 0 30rpx;
	height: 100rpx;
	gap: 20rpx;
}

.form-input {
	flex: 1;
	font-size: 32rpx;
	color: #333;
	border: none;
	background: transparent;
}

.sms-btn {
	background: linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%);
	color: white;
	border: none;
	padding: 15rpx 25rpx;
	border-radius: 20rpx;
	font-size: 24rpx;
	white-space: nowrap;
}

.sms-btn:disabled {
	background: #ccc;
	color: #999;
}

.login-type-switch {
	display: flex;
	justify-content: center;
	gap: 60rpx;
	margin-bottom: 40rpx;
}

.switch-item {
	font-size: 28rpx;
	color: #999;
	padding: 10rpx 0;
	position: relative;
}

.switch-item.active {
	color: #ff8a00;
	font-weight: bold;
}

.switch-item.active::after {
	content: '';
	position: absolute;
	bottom: 0;
	left: 50%;
	transform: translateX(-50%);
	width: 40rpx;
	height: 4rpx;
	background: #ff8a00;
	border-radius: 2rpx;
}

.form-options {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 50rpx;
}

.remember-password {
	display: flex;
	align-items: center;
	gap: 10rpx;
}

.option-text {
	font-size: 26rpx;
	color: #666;
}

.forgot-password {
	font-size: 26rpx;
	color: #ff8a00;
}

.login-btn {
	width: 100%;
	height: 100rpx;
	background: linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%);
	color: white;
	border: none;
	border-radius: 25rpx;
	font-size: 36rpx;
	font-weight: bold;
	margin-bottom: 50rpx;
}

.login-btn:disabled {
	background: #ccc;
	color: #999;
}

.quick-login {
	text-align: center;
	margin-bottom: 40rpx;
}

.quick-login-title {
	font-size: 26rpx;
	color: #666;
	display: block;
	margin-bottom: 30rpx;
}

.quick-login-methods {
	display: flex;
	justify-content: center;
	gap: 80rpx;
}

.quick-method {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 15rpx;
}

.method-text {
	font-size: 24rpx;
	color: #666;
}

.register-link {
	text-align: center;
	margin-bottom: 40rpx;
}

.register-text {
	font-size: 26rpx;
	color: #666;
}

.register-btn {
	font-size: 26rpx;
	color: #ff8a00;
	font-weight: bold;
}

.agreement-section {
	text-align: center;
	padding: 0 40rpx 40rpx;
}

.agreement-check {
	display: flex;
	justify-content: center;
	align-items: center;
	gap: 10rpx;
	margin-bottom: 15rpx;
}

.agreement-text {
	font-size: 22rpx;
	color: #999;
}

.agreement-links {
	display: flex;
	justify-content: center;
	align-items: center;
	gap: 10rpx;
}

.agreement-link {
	font-size: 22rpx;
	color: #ff8a00;
}
</style>
