<template>
	<view class="container">
		<!-- 头部横幅 -->
		<view class="header-banner">
			<view class="banner-content">
				<view class="banner-text">
					<text class="banner-title">政策引导</text>
					<text class="banner-subtitle">了解最新养老政策，享受政府福利</text>
				</view>
				<view class="banner-icon">
					<Icon name="government-line" size="80rpx" color="white"></Icon>
				</view>
			</view>
		</view>

		<!-- 政策分类 -->
		<view class="section">
			<view class="section-header">
				<text class="section-title">政策分类</text>
				<text class="section-subtitle">选择您关心的政策类型</text>
			</view>
			<view class="category-grid">
				<view class="category-item" @click="navigateToList('subsidy')">
					<view class="category-icon subsidy">
						<Icon name="money-cny-circle-line" size="48rpx" color="white"></Icon>
					</view>
					<text class="category-title">补贴政策</text>
					<text class="category-desc">各类养老补贴申请</text>
				</view>
				<view class="category-item" @click="navigateToList('medical')">
					<view class="category-icon medical">
						<Icon name="health-book-line" size="48rpx" color="white"></Icon>
					</view>
					<text class="category-title">医疗政策</text>
					<text class="category-desc">医保报销相关政策</text>
				</view>
				<view class="category-item" @click="navigateToList('housing')">
					<view class="category-icon housing">
						<Icon name="home-gear-line" size="48rpx" color="white"></Icon>
					</view>
					<text class="category-title">住房政策</text>
					<text class="category-desc">适老改造补贴政策</text>
				</view>
				<view class="category-item" @click="navigateToList('service')">
					<view class="category-icon service">
						<Icon name="heart-3-line" size="48rpx" color="white"></Icon>
					</view>
					<text class="category-title">服务政策</text>
					<text class="category-desc">养老服务相关政策</text>
				</view>
			</view>
		</view>

		<!-- 最新政策 -->
		<view class="section">
			<view class="section-header">
				<text class="section-title">最新政策</text>
				<text class="section-subtitle">及时了解政策动态</text>
			</view>
			<view class="policy-list">
				<view class="policy-item" v-for="(item, index) in latestPolicies" :key="index" @click="viewPolicy(item)">
					<view class="policy-badge" :class="item.type">
						<text class="badge-text">{{item.typeName}}</text>
					</view>
					<view class="policy-content">
						<text class="policy-title">{{item.title}}</text>
						<text class="policy-summary">{{item.summary}}</text>
						<view class="policy-meta">
							<text class="policy-date">{{item.publishDate}}</text>
							<text class="policy-status" :class="item.status">{{item.statusText}}</text>
						</view>
					</view>
					<view class="policy-arrow">
						<Icon name="arrow-right-s-line" size="24rpx" color="#999"></Icon>
					</view>
				</view>
			</view>
		</view>

		<!-- 热门问答 -->
		<view class="section">
			<view class="section-header">
				<text class="section-title">热门问答</text>
				<text class="section-subtitle">常见政策问题解答</text>
			</view>
			<view class="qa-list">
				<view class="qa-item" v-for="(item, index) in qaList" :key="index" @click="toggleQA(index)">
					<view class="qa-question">
						<Icon name="question-line" size="32rpx" color="#ff8a00"></Icon>
						<text class="question-text">{{item.question}}</text>
						<Icon :name="item.expanded ? 'arrow-up-s-line' : 'arrow-down-s-line'" size="24rpx" color="#999"></Icon>
					</view>
					<view class="qa-answer" v-if="item.expanded">
						<text class="answer-text">{{item.answer}}</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 底部操作 -->
		<view class="bottom-actions">
			<button class="action-btn secondary" @click="consultPolicy">
				<Icon name="customer-service-2-line" size="32rpx" color="#ff8a00"></Icon>
				<text>政策咨询</text>
			</button>
			<button class="action-btn primary" @click="applyPolicy">
				<Icon name="file-text-line" size="32rpx" color="white"></Icon>
				<text>在线申请</text>
			</button>
		</view>
	</view>
</template>

<script>
import Icon from '@/components/Icon/Icon.vue'

export default {
	components: {
		Icon
	},
	data() {
		return {
			latestPolicies: [
				{
					type: 'subsidy',
					typeName: '补贴',
					title: '2024年高龄津贴标准调整通知',
					summary: '80岁以上老人津贴标准上调至每月200元',
					publishDate: '2024-01-15',
					status: 'active',
					statusText: '正在实施'
				},
				{
					type: 'medical',
					typeName: '医疗',
					title: '老年人医疗保险报销比例提升',
					summary: '65岁以上老人住院报销比例提升至90%',
					publishDate: '2024-01-10',
					status: 'active',
					statusText: '正在实施'
				},
				{
					type: 'housing',
					typeName: '住房',
					title: '适老化改造补贴申请指南',
					summary: '符合条件的家庭可申请最高5000元改造补贴',
					publishDate: '2024-01-05',
					status: 'active',
					statusText: '正在实施'
				}
			],
			qaList: [
				{
					question: '如何申请高龄津贴？',
					answer: '年满80周岁的老人，持身份证、户口本到社区服务中心申请，审核通过后次月发放。',
					expanded: false
				},
				{
					question: '适老化改造补贴标准是多少？',
					answer: '根据改造项目不同，补贴标准为1000-5000元不等，具体以实际评估为准。',
					expanded: false
				},
				{
					question: '医疗保险如何报销？',
					answer: '持医保卡就医，出院时直接结算，个人只需支付自付部分费用。',
					expanded: false
				}
			]
		}
	},
	methods: {
		navigateToList(category) {
			uni.navigateTo({
				url: `/pages/policy/list?category=${category}`
			});
		},
		viewPolicy(policy) {
			uni.navigateTo({
				url: `/pages/policy/detail?id=${policy.id}`
			});
		},
		toggleQA(index) {
			this.qaList[index].expanded = !this.qaList[index].expanded;
		},
		consultPolicy() {
			uni.makePhoneCall({
				phoneNumber: '12345'
			});
		},
		applyPolicy() {
			uni.navigateTo({
				url: '/pages/policy/apply'
			});
		}
	}
}
</script>

<style scoped>
.container {
	background: linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%);
	min-height: 100vh;
	padding-bottom: 200rpx;
}

.header-banner {
	padding: 40rpx;
	margin-bottom: 40rpx;
}

.banner-content {
	background: rgba(255, 255, 255, 0.15);
	border-radius: 30rpx;
	padding: 40rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.banner-text {
	flex: 1;
}

.banner-title {
	font-size: 48rpx;
	font-weight: bold;
	color: white;
	display: block;
	margin-bottom: 15rpx;
}

.banner-subtitle {
	font-size: 28rpx;
	color: rgba(255, 255, 255, 0.9);
	display: block;
}

.banner-icon {
	margin-left: 30rpx;
}

.section {
	margin-bottom: 40rpx;
}

.section-header {
	padding: 0 40rpx 30rpx;
}

.section-title {
	font-size: 36rpx;
	font-weight: bold;
	color: white;
	display: block;
	margin-bottom: 10rpx;
}

.section-subtitle {
	font-size: 26rpx;
	color: rgba(255, 255, 255, 0.8);
	display: block;
}

.category-grid {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	gap: 30rpx;
	padding: 0 40rpx;
}

.category-item {
	background: white;
	border-radius: 30rpx;
	padding: 40rpx;
	text-align: center;
	box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.category-icon {
	width: 100rpx;
	height: 100rpx;
	border-radius: 20rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin: 0 auto 20rpx;
}

.category-icon.subsidy { background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%); }
.category-icon.medical { background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%); }
.category-icon.housing { background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%); }
.category-icon.service { background: linear-gradient(135deg, #e91e63 0%, #c2185b 100%); }

.category-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 10rpx;
}

.category-desc {
	font-size: 24rpx;
	color: #666;
	display: block;
}

.policy-list, .qa-list {
	padding: 0 40rpx;
}

.policy-item {
	background: white;
	border-radius: 30rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	display: flex;
	align-items: center;
	box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.policy-badge {
	padding: 8rpx 20rpx;
	border-radius: 20rpx;
	margin-right: 20rpx;
}

.policy-badge.subsidy { background: rgba(255, 152, 0, 0.1); }
.policy-badge.medical { background: rgba(76, 175, 80, 0.1); }
.policy-badge.housing { background: rgba(33, 150, 243, 0.1); }

.badge-text {
	font-size: 22rpx;
	color: #ff8a00;
	font-weight: 500;
}

.policy-content {
	flex: 1;
}

.policy-title {
	font-size: 30rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 8rpx;
}

.policy-summary {
	font-size: 26rpx;
	color: #666;
	display: block;
	margin-bottom: 15rpx;
}

.policy-meta {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.policy-date {
	font-size: 22rpx;
	color: #999;
}

.policy-status {
	font-size: 22rpx;
	padding: 4rpx 12rpx;
	border-radius: 12rpx;
}

.policy-status.active {
	background: rgba(76, 175, 80, 0.1);
	color: #4caf50;
}

.qa-item {
	background: white;
	border-radius: 30rpx;
	margin-bottom: 20rpx;
	overflow: hidden;
	box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.qa-question {
	padding: 30rpx;
	display: flex;
	align-items: center;
	gap: 20rpx;
}

.question-text {
	flex: 1;
	font-size: 30rpx;
	font-weight: 500;
	color: #333;
}

.qa-answer {
	padding: 0 30rpx 30rpx;
	border-top: 1rpx solid #f0f0f0;
}

.answer-text {
	font-size: 26rpx;
	color: #666;
	line-height: 1.6;
	display: block;
	padding-top: 20rpx;
}

.bottom-actions {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background: white;
	padding: 30rpx 40rpx;
	display: flex;
	gap: 20rpx;
	box-shadow: 0 -4rpx 20rpx rgba(0,0,0,0.1);
}

.action-btn {
	flex: 1;
	height: 100rpx;
	border-radius: 20rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 15rpx;
	font-size: 32rpx;
	font-weight: 500;
}

.action-btn.primary {
	background: linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%);
	color: white;
	border: none;
}

.action-btn.secondary {
	background: white;
	color: #ff8a00;
	border: 2rpx solid #ff8a00;
}
</style>
