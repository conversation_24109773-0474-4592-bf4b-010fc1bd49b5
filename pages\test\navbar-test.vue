<template>
	<view class="test-container">
		<view class="header">
			<text class="title">导航栏测试</text>
		</view>
		
		<view class="test-section">
			<text class="section-title">页面导航测试</text>
			
			<view class="test-item">
				<text class="test-label">资讯列表页 (PageHeader):</text>
				<button class="test-btn" @click="testNewsList">测试列表页</button>
			</view>
			
			<view class="test-item">
				<text class="test-label">资讯详情页 (自定义导航):</text>
				<button class="test-btn" @click="testNewsDetail">测试详情页</button>
			</view>
			
			<view class="test-item">
				<text class="test-label">从首页跳转:</text>
				<button class="test-btn" @click="goToHome">返回首页</button>
			</view>
		</view>
		
		<view class="test-section">
			<text class="section-title">导航栏功能说明</text>
			
			<view class="info-card">
				<view class="info-header">
					<Icon name="information-line" size="32rpx" color="#ff8a00"></Icon>
					<text class="info-title">资讯列表页</text>
				</view>
				<text class="info-content">使用 PageHeader 组件，包含：</text>
				<view class="info-list">
					<text class="info-item">• 自动状态栏适配</text>
					<text class="info-item">• iOS风格返回按钮</text>
					<text class="info-item">• 标题居中显示</text>
					<text class="info-item">• 右侧筛选按钮</text>
				</view>
			</view>
			
			<view class="info-card">
				<view class="info-header">
					<Icon name="article-line" size="32rpx" color="#ff8a00"></Icon>
					<text class="info-title">资讯详情页</text>
				</view>
				<text class="info-content">使用自定义导航栏，包含：</text>
				<view class="info-list">
					<text class="info-item">• 毛玻璃背景效果</text>
					<text class="info-item">• 返回按钮 + 文字</text>
					<text class="info-item">• 标题居中显示</text>
					<text class="info-item">• 右侧分享按钮</text>
				</view>
			</view>
		</view>
		
		<view class="test-section">
			<text class="section-title">返回功能测试</text>
			
			<view class="test-flow">
				<view class="flow-step">
					<view class="step-number">1</view>
					<text class="step-text">从首页进入资讯模块</text>
				</view>
				<view class="flow-arrow">↓</view>
				<view class="flow-step">
					<view class="step-number">2</view>
					<text class="step-text">点击"查看更多"进入列表页</text>
				</view>
				<view class="flow-arrow">↓</view>
				<view class="flow-step">
					<view class="step-number">3</view>
					<text class="step-text">点击资讯条目进入详情页</text>
				</view>
				<view class="flow-arrow">↓</view>
				<view class="flow-step">
					<view class="step-number">4</view>
					<text class="step-text">点击返回按钮逐级返回</text>
				</view>
			</view>
		</view>
		
		<view class="actions">
			<button class="action-btn primary" @click="testFullFlow">测试完整流程</button>
			<button class="action-btn" @click="goBack">返回</button>
		</view>
	</view>
</template>

<script>
import Icon from '@/components/Icon/Icon.vue'
import FeedbackUtils from '@/utils/feedback.js'

export default {
	components: {
		Icon
	},
	
	methods: {
		testNewsList() {
			try {
				uni.navigateTo({
					url: '/pages/news/list',
					success: () => {
						FeedbackUtils.showSuccess('成功跳转到资讯列表页')
					},
					fail: (error) => {
						FeedbackUtils.showError(`跳转失败: ${error.errMsg}`)
					}
				})
			} catch (error) {
				FeedbackUtils.showError('测试失败')
				console.error('测试资讯列表失败:', error)
			}
		},
		
		testNewsDetail() {
			try {
				uni.navigateTo({
					url: '/pages/news/detail?id=1',
					success: () => {
						FeedbackUtils.showSuccess('成功跳转到资讯详情页')
					},
					fail: (error) => {
						FeedbackUtils.showError(`跳转失败: ${error.errMsg}`)
					}
				})
			} catch (error) {
				FeedbackUtils.showError('测试失败')
				console.error('测试资讯详情失败:', error)
			}
		},
		
		goToHome() {
			try {
				uni.switchTab({
					url: '/pages/home/<USER>',
					success: () => {
						FeedbackUtils.showSuccess('已返回首页')
					},
					fail: (error) => {
						FeedbackUtils.showError(`跳转失败: ${error.errMsg}`)
					}
				})
			} catch (error) {
				FeedbackUtils.showError('跳转失败')
				console.error('跳转首页失败:', error)
			}
		},
		
		testFullFlow() {
			FeedbackUtils.showInfo('请按照测试流程手动操作')
			setTimeout(() => {
				this.goToHome()
			}, 1500)
		},
		
		goBack() {
			uni.navigateBack({
				fail: () => {
					this.goToHome()
				}
			})
		}
	}
}
</script>

<style scoped>
.test-container {
	padding: 20rpx;
	background: #f5f5f5;
	min-height: 100vh;
}

.header {
	text-align: center;
	margin-bottom: 40rpx;
}

.title {
	font-size: 40rpx;
	font-weight: bold;
	color: #333;
}

.test-section {
	background: white;
	border-radius: 16rpx;
	padding: 24rpx;
	margin-bottom: 24rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 20rpx;
	display: block;
}

.test-item {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 16rpx;
	padding-bottom: 16rpx;
	border-bottom: 1rpx solid #eee;
}

.test-item:last-child {
	border-bottom: none;
}

.test-label {
	font-size: 28rpx;
	color: #666;
	flex: 1;
}

.test-btn {
	background: #ff8a00;
	color: white;
	border: none;
	border-radius: 12rpx;
	padding: 16rpx 32rpx;
	font-size: 26rpx;
}

.info-card {
	background: #f9f9f9;
	border-radius: 12rpx;
	padding: 20rpx;
	margin-bottom: 16rpx;
	border-left: 4rpx solid #ff8a00;
}

.info-header {
	display: flex;
	align-items: center;
	gap: 12rpx;
	margin-bottom: 12rpx;
}

.info-title {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
}

.info-content {
	font-size: 26rpx;
	color: #666;
	margin-bottom: 12rpx;
	display: block;
}

.info-list {
	display: flex;
	flex-direction: column;
	gap: 8rpx;
}

.info-item {
	font-size: 24rpx;
	color: #666;
	display: block;
}

.test-flow {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 16rpx;
}

.flow-step {
	display: flex;
	align-items: center;
	gap: 16rpx;
	background: #f9f9f9;
	border-radius: 12rpx;
	padding: 16rpx 24rpx;
	width: 100%;
}

.step-number {
	width: 48rpx;
	height: 48rpx;
	border-radius: 50%;
	background: #ff8a00;
	color: white;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 24rpx;
	font-weight: bold;
}

.step-text {
	font-size: 26rpx;
	color: #333;
	flex: 1;
}

.flow-arrow {
	font-size: 32rpx;
	color: #ff8a00;
	font-weight: bold;
}

.actions {
	display: flex;
	gap: 20rpx;
	justify-content: center;
}

.action-btn {
	background: #6b7280;
	color: white;
	border: none;
	border-radius: 12rpx;
	padding: 20rpx 40rpx;
	font-size: 28rpx;
}

.action-btn.primary {
	background: #ff8a00;
}
</style>
