<script>
	import OfflineDataManager from '@/utils/offlineData.js'
	import { elderlyModeManager } from '@/utils/elderlyModeUtils.js'
	import { responsiveManager } from '@/utils/responsiveUtils.js'

	export default {
		onLaunch: function() {
			console.log('App Launch')

			// 初始化离线数据
			OfflineDataManager.initOfflineData()

			// 初始化适老化模式
			this.initElderlyMode()

			// 初始化响应式设计
			this.initResponsiveDesign()

			// 监听设置变更
			this.listenSettingsChanges()

			// 检查更新
			this.checkForUpdates()
		},
		onShow: function() {
			console.log('App Show')

			// 重新应用适老化设置
			this.applyElderlySettings()
		},
		onHide: function() {
			console.log('App Hide')
		},
		methods: {
			// 初始化适老化模式
			initElderlyMode() {
				try {
					// 适老化管理器会自动从存储中加载设置
					elderlyModeManager.init()
					console.log('适老化模式初始化完成')
				} catch (error) {
					console.error('适老化模式初始化失败:', error)
				}
			},

			// 初始化响应式设计
			initResponsiveDesign() {
				try {
					responsiveManager.init()
					console.log('响应式设计初始化完成')
				} catch (error) {
					console.error('响应式设计初始化失败:', error)
				}
			},

			// 监听设置变更
			listenSettingsChanges() {
				// 监听显示设置变更
				uni.$on('displaySettingsChanged', (settings) => {
					console.log('显示设置已更改:', settings)
					this.applyDisplaySettings(settings)
				})

				// 监听适老化设置变更
				uni.$on('elderlySettingsChanged', (settings) => {
					console.log('适老化设置已更改:', settings)
					this.applyElderlySettings(settings)
				})
			},

			// 应用显示设置
			applyDisplaySettings(settings) {
				try {
					// 这里可以添加全局显示设置的应用逻辑
					console.log('应用显示设置:', settings)
				} catch (error) {
					console.error('应用显示设置失败:', error)
				}
			},

			// 应用适老化设置
			applyElderlySettings(settings) {
				try {
					if (elderlyModeManager.isElderlyMode()) {
						elderlyModeManager.applyElderlyStyles()
					} else {
						elderlyModeManager.removeElderlyStyles()
					}
				} catch (error) {
					console.error('应用适老化设置失败:', error)
				}
			},

			checkForUpdates() {
				// 检查应用更新
				// #ifdef APP-PLUS
				plus.runtime.getProperty(plus.runtime.appid, (widgetInfo) => {
					console.log('当前版本：', widgetInfo.version)
				})
				// #endif
			}
		}
	}
</script>

<style>
	/* ================================
	   智慧养老 - iOS风格全局样式
	   基于iOS Human Interface Guidelines
	   ================================ */

	/* 重置样式 - iOS风格 */
	* {
		box-sizing: border-box;
		-webkit-tap-highlight-color: transparent; /* 移除iOS点击高亮 */
	}

	page {
		background-color: #F9FAFB; /* 使用iOS风格的页面背景色 */
		font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'SF Pro Text', 'Helvetica Neue', Helvetica, Arial, sans-serif;
		line-height: 1.47; /* iOS标准行高 */
		font-size: 34rpx; /* iOS Body字体大小 */
		color: #1F2937; /* iOS标准文字色 */
		-webkit-font-smoothing: antialiased; /* iOS字体平滑 */
		-moz-osx-font-smoothing: grayscale;
	}

	/* iOS风格CSS变量系统 - 优化版 */
	:root {
		/* 品牌色彩 - 增强对比度和可访问性 */
		--primary-color: #ff8a00;
		--primary-light: #ffb366;
		--primary-dark: #e67700;
		--primary-darker: #cc6600;
		--primary-tint: rgba(255, 138, 0, 0.1);
		--primary-tint-light: rgba(255, 138, 0, 0.05);
		--primary-tint-strong: rgba(255, 138, 0, 0.15);

		/* iOS系统颜色 - 增强版 */
		--ios-blue: #007AFF;
		--ios-blue-dark: #0056CC;
		--ios-green: #34C759;
		--ios-green-dark: #28A745;
		--ios-red: #FF3B30;
		--ios-red-dark: #DC3545;
		--ios-orange: #FF9500;
		--ios-orange-dark: #E68900;
		--ios-yellow: #FFCC00;
		--ios-yellow-dark: #E6B800;
		--ios-purple: #AF52DE;
		--ios-purple-dark: #9A3EC4;

		/* iOS中性色系统 - 增强对比度 */
		--gray-50: #FAFBFC;
		--gray-100: #F4F5F7;
		--gray-200: #E8EAED;
		--gray-300: #DADCE0;
		--gray-400: #9AA0A6;
		--gray-500: #5F6368;
		--gray-600: #3C4043;
		--gray-700: #202124;
		--gray-800: #1A1A1A;
		--gray-900: #0D0D0D;

		/* 语义化颜色 - 增强版 */
		--success-color: var(--ios-green);
		--success-color-dark: var(--ios-green-dark);
		--warning-color: var(--ios-orange);
		--warning-color-dark: var(--ios-orange-dark);
		--error-color: var(--ios-red);
		--error-color-dark: var(--ios-red-dark);
		--info-color: var(--ios-blue);
		--info-color-dark: var(--ios-blue-dark);

		/* 文字颜色系统 - 增强对比度 */
		--text-color: var(--gray-800);
		--text-primary: var(--gray-800);
		--text-secondary: var(--gray-600);
		--text-tertiary: var(--gray-500);
		--text-quaternary: var(--gray-400);
		--text-placeholder: var(--gray-400);
		--text-disabled: var(--gray-300);
		--text-inverse: #ffffff;

		/* 背景颜色系统 - 增强层次 */
		--background-color: var(--gray-50);
		--background-primary: #ffffff;
		--background-secondary: var(--gray-100);
		--background-tertiary: var(--gray-200);
		--card-background: #ffffff;
		--overlay-background: rgba(0, 0, 0, 0.5);

		/* 边框颜色系统 - 增强可见性 */
		--border-color: var(--gray-200);
		--border-light: var(--gray-100);
		--border-medium: var(--gray-300);
		--border-dark: var(--gray-400);
		--border-focus: var(--primary-color);
		--border-error: var(--error-color);
		--border-success: var(--success-color);

		/* iOS阴影系统 - 增强版 */
		--shadow-none: none;
		--shadow-xs: 0 2rpx 4rpx rgba(0, 0, 0, 0.02);
		--shadow-sm: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
		--shadow-md: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
		--shadow-lg: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);
		--shadow-xl: 0 16rpx 32rpx rgba(0, 0, 0, 0.12);
		--shadow-2xl: 0 24rpx 48rpx rgba(0, 0, 0, 0.16);
		--shadow-3xl: 0 32rpx 64rpx rgba(0, 0, 0, 0.20);

		/* 特殊阴影效果 */
		--shadow-inner: inset 0 2rpx 4rpx rgba(0, 0, 0, 0.06);
		--shadow-focus: 0 0 0 4rpx rgba(255, 138, 0, 0.25);
		--shadow-focus-ring: 0 0 0 2rpx rgba(255, 138, 0, 0.5);
		--shadow-error: 0 0 0 4rpx rgba(255, 59, 48, 0.25);
		--shadow-success: 0 0 0 4rpx rgba(52, 199, 89, 0.25);

		/* 分层阴影 - iOS风格 */
		--shadow-card:
			0 2rpx 4rpx rgba(0, 0, 0, 0.02),
			0 4rpx 8rpx rgba(0, 0, 0, 0.04);
		--shadow-modal:
			0 8rpx 16rpx rgba(0, 0, 0, 0.08),
			0 16rpx 32rpx rgba(0, 0, 0, 0.12);
		--shadow-dropdown:
			0 4rpx 8rpx rgba(0, 0, 0, 0.04),
			0 8rpx 24rpx rgba(0, 0, 0, 0.08);

		/* iOS圆角系统 - 增强版 */
		--radius-none: 0;
		--radius-xs: 6rpx;   /* 3pt */
		--radius-sm: 8rpx;   /* 4pt */
		--radius-md: 12rpx;  /* 6pt */
		--radius-lg: 16rpx;  /* 8pt */
		--radius-xl: 20rpx;  /* 10pt */
		--radius-2xl: 24rpx; /* 12pt */
		--radius-3xl: 32rpx; /* 16pt */
		--radius-full: 50%;

		/* 语义化圆角 */
		--radius-button: var(--radius-lg);
		--radius-card: var(--radius-xl);
		--radius-input: var(--radius-md);
		--radius-modal: var(--radius-2xl);
		--radius-avatar: var(--radius-full);

		/* iOS间距系统 - 基于8pt网格 */
		--spacing-1: 2rpx;   /* 1pt */
		--spacing-2: 4rpx;   /* 2pt */
		--spacing-3: 6rpx;   /* 3pt */
		--spacing-4: 8rpx;   /* 4pt */
		--spacing-6: 12rpx;  /* 6pt */
		--spacing-8: 16rpx;  /* 8pt */
		--spacing-10: 20rpx; /* 10pt */
		--spacing-12: 24rpx; /* 12pt */
		--spacing-16: 32rpx; /* 16pt */
		--spacing-20: 40rpx; /* 20pt */
		--spacing-24: 48rpx; /* 24pt */
		--spacing-28: 56rpx; /* 28pt */
		--spacing-32: 64rpx; /* 32pt */
		--spacing-36: 72rpx; /* 36pt */
		--spacing-40: 80rpx; /* 40pt */
		--spacing-44: 88rpx; /* 44pt - iOS最小触摸目标 */
		--spacing-48: 96rpx; /* 48pt */
		--spacing-56: 112rpx; /* 56pt */
		--spacing-64: 128rpx; /* 64pt */
		--spacing-72: 144rpx; /* 72pt */
		--spacing-80: 160rpx; /* 80pt */
		--spacing-96: 192rpx; /* 96pt */

		/* 语义化间距 */
		--spacing-xs: var(--spacing-2);
		--spacing-sm: var(--spacing-4);
		--spacing-md: var(--spacing-8);
		--spacing-lg: var(--spacing-16);
		--spacing-xl: var(--spacing-24);
		--spacing-2xl: var(--spacing-32);
		--spacing-3xl: var(--spacing-48);

		/* 触摸目标尺寸 */
		--touch-target-min: var(--spacing-44); /* 44pt最小触摸目标 */
		--touch-target-comfortable: var(--spacing-56); /* 56pt舒适触摸目标 */
		--touch-target-large: var(--spacing-64); /* 64pt大触摸目标 */

		/* 容器间距 */
		--container-padding-xs: var(--spacing-8);
		--container-padding-sm: var(--spacing-12);
		--container-padding-md: var(--spacing-16);
		--container-padding-lg: var(--spacing-24);
		--container-padding-xl: var(--spacing-32);
	}

	/* ================================
	   iOS风格工具类系统
	   ================================ */

	/* 布局工具类 - 增强版 */
	.flex {
		display: flex;
	}

	.flex-center {
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.flex-between {
		display: flex;
		align-items: center;
		justify-content: space-between;
	}

	.flex-around {
		display: flex;
		align-items: center;
		justify-content: space-around;
	}

	.flex-evenly {
		display: flex;
		align-items: center;
		justify-content: space-evenly;
	}

	.flex-start {
		display: flex;
		align-items: center;
		justify-content: flex-start;
	}

	.flex-end {
		display: flex;
		align-items: center;
		justify-content: flex-end;
	}

	.flex-column {
		display: flex;
		flex-direction: column;
	}

	.flex-column-center {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
	}

	.flex-column-between {
		display: flex;
		flex-direction: column;
		justify-content: space-between;
	}

	.flex-wrap {
		flex-wrap: wrap;
	}

	.flex-nowrap {
		flex-wrap: nowrap;
	}

	.flex-1 {
		flex: 1;
	}

	.flex-auto {
		flex: auto;
	}

	.flex-none {
		flex: none;
	}

	/* 对齐工具类 */
	.items-start { align-items: flex-start; }
	.items-center { align-items: center; }
	.items-end { align-items: flex-end; }
	.items-stretch { align-items: stretch; }
	.items-baseline { align-items: baseline; }

	.justify-start { justify-content: flex-start; }
	.justify-center { justify-content: center; }
	.justify-end { justify-content: flex-end; }
	.justify-between { justify-content: space-between; }
	.justify-around { justify-content: space-around; }
	.justify-evenly { justify-content: space-evenly; }

	/* 网格布局工具类 */
	.grid {
		display: grid;
	}

	.grid-cols-1 { grid-template-columns: repeat(1, 1fr); }
	.grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
	.grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
	.grid-cols-4 { grid-template-columns: repeat(4, 1fr); }
	.grid-cols-5 { grid-template-columns: repeat(5, 1fr); }
	.grid-cols-6 { grid-template-columns: repeat(6, 1fr); }

	.gap-1 { gap: var(--spacing-1); }
	.gap-2 { gap: var(--spacing-2); }
	.gap-3 { gap: var(--spacing-3); }
	.gap-4 { gap: var(--spacing-4); }
	.gap-6 { gap: var(--spacing-6); }
	.gap-8 { gap: var(--spacing-8); }
	.gap-12 { gap: var(--spacing-12); }
	.gap-16 { gap: var(--spacing-16); }
	.gap-24 { gap: var(--spacing-24); }

	/* 文字对齐 */
	.text-center {
		text-align: center;
	}

	.text-left {
		text-align: left;
	}

	.text-right {
		text-align: right;
	}

	/* iOS风格文字颜色类 */
	.text-primary {
		color: var(--primary-color);
	}

	.text-secondary {
		color: var(--text-secondary);
	}

	.text-tertiary {
		color: var(--text-tertiary);
	}

	.text-success {
		color: var(--success-color);
	}

	.text-warning {
		color: var(--warning-color);
	}

	.text-error {
		color: var(--error-color);
	}

	.text-info {
		color: var(--info-color);
	}

	.text-disabled {
		color: var(--text-disabled);
	}

	/* iOS风格字体大小类 - 优化版 */
	.text-caption2 {
		font-size: 22rpx;
		line-height: 1.4;
		letter-spacing: 0.01em;
	}
	.text-caption1 {
		font-size: 24rpx;
		line-height: 1.4;
		letter-spacing: 0.01em;
	}
	.text-footnote {
		font-size: 26rpx;
		line-height: 1.45;
		letter-spacing: 0.005em;
	}
	.text-subheadline {
		font-size: 30rpx;
		line-height: 1.45;
		letter-spacing: 0em;
	}
	.text-callout {
		font-size: 32rpx;
		line-height: 1.47;
		letter-spacing: -0.005em;
	}
	.text-body {
		font-size: 34rpx;
		line-height: 1.47;
		letter-spacing: -0.005em;
	}
	.text-headline {
		font-size: 36rpx;
		line-height: 1.4;
		letter-spacing: -0.01em;
	}
	.text-title3 {
		font-size: 40rpx;
		line-height: 1.35;
		letter-spacing: -0.01em;
	}
	.text-title2 {
		font-size: 44rpx;
		line-height: 1.3;
		letter-spacing: -0.015em;
	}
	.text-title1 {
		font-size: 56rpx;
		line-height: 1.25;
		letter-spacing: -0.02em;
	}
	.text-large-title {
		font-size: 68rpx;
		line-height: 1.2;
		letter-spacing: -0.025em;
	}

	/* iOS风格字体粗细类 - 增强版 */
	.font-ultralight { font-weight: 100; }
	.font-thin { font-weight: 200; }
	.font-light { font-weight: 300; }
	.font-regular { font-weight: 400; }
	.font-medium { font-weight: 500; }
	.font-semibold { font-weight: 600; }
	.font-bold { font-weight: 700; }
	.font-heavy { font-weight: 800; }
	.font-black { font-weight: 900; }

	/* 字体组合类 - 常用组合 */
	.text-display {
		font-size: 68rpx;
		font-weight: 700;
		line-height: 1.2;
		letter-spacing: -0.025em;
	}
	.text-heading {
		font-size: 44rpx;
		font-weight: 600;
		line-height: 1.3;
		letter-spacing: -0.015em;
	}
	.text-subheading {
		font-size: 36rpx;
		font-weight: 500;
		line-height: 1.4;
		letter-spacing: -0.01em;
	}
	.text-paragraph {
		font-size: 34rpx;
		font-weight: 400;
		line-height: 1.47;
		letter-spacing: -0.005em;
	}
	.text-caption {
		font-size: 26rpx;
		font-weight: 400;
		line-height: 1.45;
		letter-spacing: 0.005em;
	}

	/* iOS风格背景色类 - 增强版 */
	.bg-primary { background-color: var(--primary-color); }
	.bg-primary-light { background-color: var(--primary-light); }
	.bg-primary-dark { background-color: var(--primary-dark); }
	.bg-white { background-color: #ffffff; }
	.bg-gray-50 { background-color: var(--gray-50); }
	.bg-gray-100 { background-color: var(--gray-100); }
	.bg-gray-200 { background-color: var(--gray-200); }
	.bg-secondary { background-color: var(--background-secondary); }
	.bg-tertiary { background-color: var(--background-tertiary); }
	.bg-success { background-color: var(--success-color); }
	.bg-warning { background-color: var(--warning-color); }
	.bg-error { background-color: var(--error-color); }
	.bg-info { background-color: var(--info-color); }
	.bg-transparent { background-color: transparent; }

	/* iOS风格圆角类 - 增强版 */
	.rounded-none { border-radius: var(--radius-none); }
	.rounded-xs { border-radius: var(--radius-xs); }
	.rounded-sm { border-radius: var(--radius-sm); }
	.rounded-md { border-radius: var(--radius-md); }
	.rounded-lg { border-radius: var(--radius-lg); }
	.rounded-xl { border-radius: var(--radius-xl); }
	.rounded-2xl { border-radius: var(--radius-2xl); }
	.rounded-3xl { border-radius: var(--radius-3xl); }
	.rounded-full { border-radius: var(--radius-full); }

	/* 语义化圆角类 */
	.rounded-button { border-radius: var(--radius-button); }
	.rounded-card { border-radius: var(--radius-card); }
	.rounded-input { border-radius: var(--radius-input); }
	.rounded-modal { border-radius: var(--radius-modal); }
	.rounded-avatar { border-radius: var(--radius-avatar); }

	/* 兼容旧的圆角类 */
	.border-radius { border-radius: var(--radius-sm); }

	/* iOS风格阴影类 - 增强版 */
	.shadow-none { box-shadow: var(--shadow-none); }
	.shadow-xs { box-shadow: var(--shadow-xs); }
	.shadow-sm { box-shadow: var(--shadow-sm); }
	.shadow-md { box-shadow: var(--shadow-md); }
	.shadow-lg { box-shadow: var(--shadow-lg); }
	.shadow-xl { box-shadow: var(--shadow-xl); }
	.shadow-2xl { box-shadow: var(--shadow-2xl); }
	.shadow-3xl { box-shadow: var(--shadow-3xl); }

	/* 特殊阴影类 */
	.shadow-inner { box-shadow: var(--shadow-inner); }
	.shadow-focus { box-shadow: var(--shadow-focus); }
	.shadow-focus-ring { box-shadow: var(--shadow-focus-ring); }
	.shadow-error { box-shadow: var(--shadow-error); }
	.shadow-success { box-shadow: var(--shadow-success); }

	/* 分层阴影类 */
	.shadow-card { box-shadow: var(--shadow-card); }
	.shadow-modal { box-shadow: var(--shadow-modal); }
	.shadow-dropdown { box-shadow: var(--shadow-dropdown); }

	/* 兼容旧的阴影类 */
	.shadow { box-shadow: var(--shadow-sm); }

	/* iOS风格间距工具类 - 基于4pt网格系统 */
	.m-0 { margin: 0; }
	.m-1 { margin: var(--spacing-4); }  /* 4rpx */
	.m-2 { margin: var(--spacing-8); }  /* 8rpx */
	.m-3 { margin: var(--spacing-12); } /* 12rpx */
	.m-4 { margin: var(--spacing-16); } /* 16rpx */
	.m-5 { margin: var(--spacing-20); } /* 20rpx */
	.m-6 { margin: var(--spacing-24); } /* 24rpx */
	.m-8 { margin: var(--spacing-32); } /* 32rpx */

	.mt-0 { margin-top: 0; }
	.mt-1 { margin-top: var(--spacing-4); }
	.mt-2 { margin-top: var(--spacing-8); }
	.mt-3 { margin-top: var(--spacing-12); }
	.mt-4 { margin-top: var(--spacing-16); }
	.mt-5 { margin-top: var(--spacing-20); }
	.mt-6 { margin-top: var(--spacing-24); }
	.mt-8 { margin-top: var(--spacing-32); }

	.mb-0 { margin-bottom: 0; }
	.mb-1 { margin-bottom: var(--spacing-4); }
	.mb-2 { margin-bottom: var(--spacing-8); }
	.mb-3 { margin-bottom: var(--spacing-12); }
	.mb-4 { margin-bottom: var(--spacing-16); }
	.mb-5 { margin-bottom: var(--spacing-20); }
	.mb-6 { margin-bottom: var(--spacing-24); }
	.mb-8 { margin-bottom: var(--spacing-32); }

	.ml-0 { margin-left: 0; }
	.ml-1 { margin-left: var(--spacing-4); }
	.ml-2 { margin-left: var(--spacing-8); }
	.ml-3 { margin-left: var(--spacing-12); }
	.ml-4 { margin-left: var(--spacing-16); }

	.mr-0 { margin-right: 0; }
	.mr-1 { margin-right: var(--spacing-4); }
	.mr-2 { margin-right: var(--spacing-8); }
	.mr-3 { margin-right: var(--spacing-12); }
	.mr-4 { margin-right: var(--spacing-16); }

	.p-0 { padding: 0; }
	.p-1 { padding: var(--spacing-4); }
	.p-2 { padding: var(--spacing-8); }
	.p-3 { padding: var(--spacing-12); }
	.p-4 { padding: var(--spacing-16); }
	.p-5 { padding: var(--spacing-20); }
	.p-6 { padding: var(--spacing-24); }
	.p-8 { padding: var(--spacing-32); }

	.pt-0 { padding-top: 0; }
	.pt-1 { padding-top: var(--spacing-4); }
	.pt-2 { padding-top: var(--spacing-8); }
	.pt-3 { padding-top: var(--spacing-12); }
	.pt-4 { padding-top: var(--spacing-16); }
	.pt-5 { padding-top: var(--spacing-20); }
	.pt-6 { padding-top: var(--spacing-24); }
	.pt-8 { padding-top: var(--spacing-32); }

	.pb-0 { padding-bottom: 0; }
	.pb-1 { padding-bottom: var(--spacing-4); }
	.pb-2 { padding-bottom: var(--spacing-8); }
	.pb-3 { padding-bottom: var(--spacing-12); }
	.pb-4 { padding-bottom: var(--spacing-16); }
	.pb-5 { padding-bottom: var(--spacing-20); }
	.pb-6 { padding-bottom: var(--spacing-24); }
	.pb-8 { padding-bottom: var(--spacing-32); }

	.pl-0 { padding-left: 0; }
	.pl-1 { padding-left: var(--spacing-4); }
	.pl-2 { padding-left: var(--spacing-8); }
	.pl-3 { padding-left: var(--spacing-12); }
	.pl-4 { padding-left: var(--spacing-16); }

	.pr-0 { padding-right: 0; }
	.pr-1 { padding-right: var(--spacing-4); }
	.pr-2 { padding-right: var(--spacing-8); }
	.pr-3 { padding-right: var(--spacing-12); }
	.pr-4 { padding-right: var(--spacing-16); }

	/* ================================
	   iOS风格适老化系统 - 增强版
	   ================================ */

	/* 适老化模式基础样式 */
	.elderly-mode,
	.ios-elderly-mode {
		/* iOS风格字体增强 */
		font-size: calc(34rpx * 1.3) !important; /* 基于iOS Body字体放大1.3倍 */
		font-weight: 600 !important; /* 使用iOS Semibold字重 */
		line-height: 1.6 !important; /* 增加行高提升可读性 */

		/* iOS风格颜色对比度增强 */
		color: #000000 !important; /* 纯黑文字，最高对比度 */
		background-color: #ffffff !important; /* 纯白背景 */
	}

	/* 适老化字体层级 - 基于iOS Typography */
	.elderly-mode .text-caption,
	.ios-elderly-mode .text-caption {
		font-size: calc(22rpx * 1.3) !important; /* Caption * 1.3 */
		font-weight: 500 !important; /* iOS Medium */
	}

	.elderly-mode .text-footnote,
	.ios-elderly-mode .text-footnote {
		font-size: calc(26rpx * 1.3) !important; /* Footnote * 1.3 */
		font-weight: 500 !important;
	}

	.elderly-mode .text-subheadline,
	.ios-elderly-mode .text-subheadline {
		font-size: calc(30rpx * 1.3) !important; /* Subheadline * 1.3 */
		font-weight: 600 !important;
	}

	.elderly-mode .text-callout,
	.ios-elderly-mode .text-callout {
		font-size: calc(32rpx * 1.3) !important; /* Callout * 1.3 */
		font-weight: 600 !important;
	}

	.elderly-mode .text-body,
	.ios-elderly-mode .text-body {
		font-size: calc(34rpx * 1.3) !important; /* Body * 1.3 */
		font-weight: 600 !important;
	}

	.elderly-mode .text-headline,
	.ios-elderly-mode .text-headline {
		font-size: calc(36rpx * 1.3) !important; /* Headline * 1.3 */
		font-weight: 700 !important; /* iOS Bold */
	}

	.elderly-mode .text-title3,
	.ios-elderly-mode .text-title3 {
		font-size: calc(40rpx * 1.3) !important; /* Title 3 * 1.3 */
		font-weight: 700 !important;
	}

	.elderly-mode .text-title2,
	.ios-elderly-mode .text-title2 {
		font-size: calc(44rpx * 1.3) !important; /* Title 2 * 1.3 */
		font-weight: 700 !important;
	}

	.elderly-mode .text-title1,
	.ios-elderly-mode .text-title1 {
		font-size: calc(56rpx * 1.3) !important; /* Title 1 * 1.3 */
		font-weight: 700 !important;
	}

	.elderly-mode .text-large-title,
	.ios-elderly-mode .text-large-title {
		font-size: calc(68rpx * 1.3) !important; /* Large Title * 1.3 */
		font-weight: 700 !important;
	}

	/* 适老化交互元素 - iOS风格增强 */
	.elderly-mode .interactive-button,
	.ios-elderly-mode .interactive-button,
	.elderly-mode .interactive-card,
	.ios-elderly-mode .interactive-card {
		min-height: 112rpx !important; /* 增大触摸目标到56pt */
		padding: calc(var(--spacing-16) * 1.4) calc(var(--spacing-24) * 1.4) !important;
		border-radius: 25rpx !important; /* 更大的iOS风格圆角 */
		box-shadow:
			0 4rpx 16rpx rgba(0, 0, 0, 0.1),
			0 8rpx 24rpx rgba(0, 0, 0, 0.08) !important; /* 增强阴影 */
		border: 2rpx solid #8e8e93 !important; /* iOS标准边框 */
	}

	.elderly-mode .interactive-button,
	.ios-elderly-mode .interactive-button {
		font-size: calc(34rpx * 1.3) !important; /* Body * 1.3 */
		font-weight: 600 !important; /* iOS Semibold */
		letter-spacing: 0.5rpx !important; /* 增加字母间距 */
	}

	/* 适老化焦点状态 - iOS风格 */
	.elderly-mode .interactive-button:focus,
	.ios-elderly-mode .interactive-button:focus,
	.elderly-mode .interactive-card:focus,
	.ios-elderly-mode .interactive-card:focus {
		outline: none !important;
		box-shadow:
			0 4rpx 16rpx rgba(0, 0, 0, 0.1),
			0 8rpx 24rpx rgba(0, 0, 0, 0.08),
			0 0 0 4rpx rgba(255, 138, 0, 0.3) !important; /* 焦点环 */
		border-color: #ff8a00 !important; /* 品牌色边框 */
	}

	/* 适老化按压状态 - iOS风格 */
	.elderly-mode .interactive-button:active,
	.ios-elderly-mode .interactive-button:active,
	.elderly-mode .interactive-card:active,
	.ios-elderly-mode .interactive-card:active {
		transform: scale(0.92) !important; /* 更明显的按压效果 */
		box-shadow:
			0 2rpx 8rpx rgba(0, 0, 0, 0.12),
			0 4rpx 16rpx rgba(0, 0, 0, 0.1) !important; /* 按压时阴影 */
	}

	/* 适老化颜色增强 - iOS风格全面升级版 */
	.elderly-mode,
	.ios-elderly-mode {
		/* 文字颜色层级 - 超高对比度 */
		--text-color: var(--elderly-text-primary, #000000) !important;
		--text-primary: var(--elderly-text-primary, #000000) !important;
		--text-secondary: var(--elderly-text-secondary, #1a1a1a) !important;
		--text-tertiary: var(--elderly-text-tertiary, #333333) !important;
		--text-quaternary: #4a4a4a !important;
		--text-placeholder: #666666 !important;
		--text-disabled: #999999 !important;
		--text-inverse: #ffffff !important;

		/* 背景颜色层级 - 高对比度 */
		--background-color: var(--elderly-bg-primary, #ffffff) !important;
		--background-primary: var(--elderly-bg-primary, #ffffff) !important;
		--background-secondary: var(--elderly-bg-secondary, #f8f8f8) !important;
		--background-tertiary: #f0f0f0 !important;
		--card-background: #ffffff !important;
		--overlay-background: rgba(0, 0, 0, 0.8) !important;

		/* 边框颜色层级 - 增强可见性 */
		--border-color: var(--elderly-border-primary, #666666) !important;
		--border-light: #999999 !important;
		--border-medium: #333333 !important;
		--border-dark: #000000 !important;
		--border-focus: var(--elderly-border-focus, #ff8a00) !important;
		--border-error: #cc0000 !important;
		--border-success: #006600 !important;

		/* 语义化颜色 - 适老化版本 */
		--primary-color: #ff8a00 !important;
		--success-color: #006600 !important;
		--warning-color: #cc6600 !important;
		--error-color: #cc0000 !important;
		--info-color: #0066cc !important;

		/* 字体系统增强 */
		font-size: calc(1em * var(--elderly-font-scale, 1.4)) !important;
		line-height: var(--elderly-line-height, 1.7) !important;
		letter-spacing: var(--elderly-letter-spacing, 0.02em) !important;
		font-weight: 600 !important;

		/* 间距系统增强 */
		--spacing-scale: var(--elderly-spacing-scale, 1.6);
		--touch-target-min: var(--elderly-touch-target-min, 112rpx);
		--touch-target-comfortable: var(--elderly-touch-target-comfortable, 128rpx);
		--touch-target-large: var(--elderly-touch-target-large, 144rpx);

		/* 视觉反馈增强 */
		--focus-ring-width: var(--elderly-focus-ring-width, 4rpx);
		--animation-scale: var(--elderly-animation-scale, 1.1);
		--highlight-duration: var(--elderly-highlight-duration, 800ms);
	}

	/* 适老化边框增强 - 统一iOS风格 */
	.elderly-mode *,
	.ios-elderly-mode * {
		border-color: var(--border-primary) !important;
	}

	.elderly-mode input,
	.ios-elderly-mode input,
	.elderly-mode textarea,
	.ios-elderly-mode textarea {
		border: 2rpx solid var(--border-primary) !important;
		border-radius: 20rpx !important;
		padding: 20rpx !important;
		font-size: calc(34rpx * 1.3) !important;
		font-weight: 500 !important;
	}

	.elderly-mode input:focus,
	.ios-elderly-mode input:focus,
	.elderly-mode textarea:focus,
	.ios-elderly-mode textarea:focus {
		border-color: var(--border-focus) !important;
		box-shadow: 0 0 0 4rpx rgba(255, 138, 0, 0.3) !important;
	}

	/* 适老化图标增强 */
	.elderly-mode .icon,
	.ios-elderly-mode .icon {
		transform: scale(1.3) !important;
		filter: contrast(1.2) !important;
	}

	/* 适老化列表项增强 */
	.elderly-mode .list-item,
	.ios-elderly-mode .list-item {
		min-height: 112rpx !important;
		padding: 24rpx !important;
		border-bottom: 2rpx solid var(--border-secondary) !important;
	}

	/* 适老化导航栏增强 */
	.elderly-mode .navbar,
	.ios-elderly-mode .navbar {
		height: 120rpx !important;
		padding: 0 32rpx !important;
	}

	.elderly-mode .navbar-title,
	.ios-elderly-mode .navbar-title {
		font-size: calc(36rpx * 1.3) !important;
		font-weight: 700 !important;
	}

	/* 适老化标签页增强 */
	.elderly-mode .tab-bar,
	.ios-elderly-mode .tab-bar {
		height: 120rpx !important;
		padding: 16rpx 0 !important;
	}

	.elderly-mode .tab-item,
	.ios-elderly-mode .tab-item {
		min-height: 88rpx !important;
		padding: 12rpx 8rpx !important;
	}

	.elderly-mode .tab-text,
	.ios-elderly-mode .tab-text {
		font-size: calc(24rpx * 1.3) !important;
		font-weight: 600 !important;
		margin-top: 8rpx !important;
	}

	/* ================================
	   iOS风格响应式设计系统
	   基于iOS Human Interface Guidelines
	   ================================ */

	/* 响应式容器 */
	.responsive-container {
		width: 100%;
		margin: 0 auto;
		padding: 0 var(--container-padding, 24rpx);
	}

	/* 小屏设备 (iPhone SE) */
	@media screen and (max-width: 375px) {
		.responsive-container {
			--container-padding: 16rpx;
			max-width: 100%;
		}

		.responsive-small {
			display: block;
		}

		.responsive-medium,
		.responsive-large,
		.responsive-xlarge {
			display: none;
		}

		/* 小屏字体缩放 */
		.responsive-text {
			font-size: calc(var(--base-font-size, 32rpx) * 0.9);
		}

		/* 小屏间距缩放 */
		.responsive-spacing {
			padding: calc(var(--base-padding, 24rpx) * 0.8);
			margin: calc(var(--base-margin, 16rpx) * 0.8);
		}

		/* 小屏网格 */
		.responsive-grid {
			grid-template-columns: repeat(2, 1fr);
			gap: 12rpx;
		}

		/* 小屏按钮 */
		.responsive-button {
			height: 72rpx;
			padding: 16rpx 24rpx;
			font-size: 28rpx;
			border-radius: 12rpx;
		}

		/* 小屏卡片 */
		.responsive-card {
			padding: 24rpx;
			border-radius: 16rpx;
			min-height: 120rpx;
		}
	}

	/* 标准设备 (iPhone 12/13/14) */
	@media screen and (min-width: 376px) and (max-width: 767px) {
		.responsive-container {
			--container-padding: 24rpx;
			max-width: 100%;
		}

		.responsive-medium {
			display: block;
		}

		.responsive-small,
		.responsive-large,
		.responsive-xlarge {
			display: none;
		}

		/* 标准字体 */
		.responsive-text {
			font-size: var(--base-font-size, 32rpx);
		}

		/* 标准间距 */
		.responsive-spacing {
			padding: var(--base-padding, 24rpx);
			margin: var(--base-margin, 16rpx);
		}

		/* 标准网格 */
		.responsive-grid {
			grid-template-columns: repeat(3, 1fr);
			gap: 16rpx;
		}

		/* 标准按钮 */
		.responsive-button {
			height: 88rpx;
			padding: 20rpx 32rpx;
			font-size: 32rpx;
			border-radius: 16rpx;
		}

		/* 标准卡片 */
		.responsive-card {
			padding: 32rpx;
			border-radius: 20rpx;
			min-height: 140rpx;
		}
	}

	/* 大屏设备 (iPad) */
	@media screen and (min-width: 768px) and (max-width: 1023px) {
		.responsive-container {
			--container-padding: 32rpx;
			max-width: 1024rpx;
		}

		.responsive-large {
			display: block;
		}

		.responsive-small,
		.responsive-medium,
		.responsive-xlarge {
			display: none;
		}

		/* 大屏字体放大 */
		.responsive-text {
			font-size: calc(var(--base-font-size, 32rpx) * 1.2);
		}

		/* 大屏间距放大 */
		.responsive-spacing {
			padding: calc(var(--base-padding, 24rpx) * 1.3);
			margin: calc(var(--base-margin, 16rpx) * 1.3);
		}

		/* 大屏网格 */
		.responsive-grid {
			grid-template-columns: repeat(4, 1fr);
			gap: 24rpx;
		}

		/* 大屏按钮 */
		.responsive-button {
			height: 96rpx;
			padding: 24rpx 40rpx;
			font-size: 36rpx;
			border-radius: 20rpx;
		}

		/* 大屏卡片 */
		.responsive-card {
			padding: 40rpx;
			border-radius: 24rpx;
			min-height: 160rpx;
		}

		/* iPad专用布局 */
		.ipad-layout {
			display: flex;
			max-width: 1024rpx;
			margin: 0 auto;
		}

		.ipad-sidebar {
			width: 320rpx;
			flex-shrink: 0;
		}

		.ipad-main {
			flex: 1;
			padding-left: 32rpx;
		}

	/* 超大屏设备 (iPad Pro) */
	@media screen and (min-width: 1024px) {
		.responsive-container {
			--container-padding: 48rpx;
			max-width: 1366rpx;
		}

		.responsive-xlarge {
			display: block;
		}

		.responsive-small,
		.responsive-medium,
		.responsive-large {
			display: none;
		}

		/* 超大屏字体放大 */
		.responsive-text {
			font-size: calc(var(--base-font-size, 32rpx) * 1.4);
		}

		/* 桌面级多列布局 */
		.desktop-grid {
			display: grid;
			grid-template-columns: 320rpx 1fr;
			gap: 48rpx;
		}

		.desktop-sidebar {
			position: sticky;
			top: 0;
			height: 100vh;
			overflow-y: auto;
		}

		.desktop-main {
			min-height: 100vh;
		}
	}

		/* 超大屏间距放大 */
		.responsive-spacing {
			padding: calc(var(--base-padding, 24rpx) * 1.6);
			margin: calc(var(--base-margin, 16rpx) * 1.6);
		}

		/* 超大屏网格 */
		.responsive-grid {
			grid-template-columns: repeat(6, 1fr);
			gap: 32rpx;
		}

		/* 超大屏按钮 */
		.responsive-button {
			height: 112rpx;
			padding: 28rpx 48rpx;
			font-size: 40rpx;
			border-radius: 24rpx;
		}

		/* 超大屏卡片 */
		.responsive-card {
			padding: 48rpx;
			border-radius: 28rpx;
			min-height: 180rpx;
		}

		/* iPad Pro专用布局 */
		.ipad-pro-layout {
			display: grid;
			grid-template-columns: 320rpx 1fr 320rpx;
			gap: 48rpx;
			max-width: 1366rpx;
			margin: 0 auto;
		}

		.ipad-pro-sidebar {
			background: rgba(255, 255, 255, 0.95);
			border-radius: 28rpx;
			padding: 32rpx;
		}

		.ipad-pro-main {
			background: rgba(255, 255, 255, 0.95);
			border-radius: 28rpx;
			padding: 40rpx;
		}
	}



	/* ================================
	   iOS风格动画系统 - 增强版
	   ================================ */

	/* iOS标准动画 */
	.fade-in {
		animation: iosFadeIn 0.35s cubic-bezier(0.25, 0.46, 0.45, 0.94);
	}

	.slide-up {
		animation: iosSlideUp 0.35s cubic-bezier(0.25, 0.46, 0.45, 0.94);
	}

	.slide-in-right {
		animation: iosSlideInRight 0.35s cubic-bezier(0.25, 0.46, 0.45, 0.94);
	}

	.slide-in-left {
		animation: iosSlideInLeft 0.35s cubic-bezier(0.25, 0.46, 0.45, 0.94);
	}

	.slide-out-left {
		animation: iosSlideOutLeft 0.35s cubic-bezier(0.25, 0.46, 0.45, 0.94);
	}

	.slide-out-right {
		animation: iosSlideOutRight 0.35s cubic-bezier(0.25, 0.46, 0.45, 0.94);
	}

	.scale-in {
		animation: iosScaleIn 0.25s cubic-bezier(0.175, 0.885, 0.32, 1.275);
	}

	.scale-out {
		animation: iosScaleOut 0.2s cubic-bezier(0.42, 0, 1, 1);
	}

	.bounce-in {
		animation: iosBounceIn 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
	}

	.shake {
		animation: iosShake 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
	}

	.pulse {
		animation: iosPulse 2s cubic-bezier(0.25, 0.46, 0.45, 0.94) infinite;
	}

	/* iOS动画关键帧 - 增强版 */
	@keyframes iosFadeIn {
		from {
			opacity: 0;
		}
		to {
			opacity: 1;
		}
	}

	@keyframes iosSlideUp {
		from {
			transform: translateY(20rpx);
			opacity: 0;
		}
		to {
			transform: translateY(0);
			opacity: 1;
		}
	}

	@keyframes iosSlideInRight {
		from {
			transform: translateX(100%);
			opacity: 0;
		}
		to {
			transform: translateX(0);
			opacity: 1;
		}
	}

	@keyframes iosSlideInLeft {
		from {
			transform: translateX(-100%);
			opacity: 0;
		}
		to {
			transform: translateX(0);
			opacity: 1;
		}
	}

	@keyframes iosSlideOutLeft {
		from {
			transform: translateX(0);
			opacity: 1;
		}
		to {
			transform: translateX(-100%);
			opacity: 0;
		}
	}

	@keyframes iosSlideOutRight {
		from {
			transform: translateX(0);
			opacity: 1;
		}
		to {
			transform: translateX(100%);
			opacity: 0;
		}
	}

	@keyframes iosScaleIn {
		from {
			transform: scale(0.8);
			opacity: 0;
		}
		50% {
			transform: scale(1.05);
			opacity: 0.8;
		}
		to {
			transform: scale(1);
			opacity: 1;
		}
	}

	@keyframes iosScaleOut {
		from {
			transform: scale(1);
			opacity: 1;
		}
		to {
			transform: scale(0.8);
			opacity: 0;
		}
	}

	@keyframes iosBounceIn {
		0% {
			transform: scale(0.3);
			opacity: 0;
		}
		50% {
			transform: scale(1.1);
			opacity: 0.8;
		}
		70% {
			transform: scale(0.9);
			opacity: 0.9;
		}
		100% {
			transform: scale(1);
			opacity: 1;
		}
	}

	@keyframes iosShake {
		0%, 100% {
			transform: translateX(0);
		}
		10%, 30%, 50%, 70%, 90% {
			transform: translateX(-10rpx);
		}
		20%, 40%, 60%, 80% {
			transform: translateX(10rpx);
		}
	}

	@keyframes iosPulse {
		0%, 100% {
			transform: scale(1);
			opacity: 1;
		}
		50% {
			transform: scale(1.05);
			opacity: 0.7;
		}
	}

	/* iOS风格过渡效果 - 增强版 */
	.ios-transition {
		transition: all 0.25s cubic-bezier(0.25, 0.46, 0.45, 0.94);
	}

	.ios-transition-fast {
		transition: all 0.15s cubic-bezier(0.25, 0.46, 0.45, 0.94);
	}

	.ios-transition-slow {
		transition: all 0.35s cubic-bezier(0.25, 0.46, 0.45, 0.94);
	}

	.ios-transition-spring {
		transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
	}

	/* iOS风格微交互效果 */
	.ios-press {
		transition: transform 0.1s cubic-bezier(0.25, 0.46, 0.45, 0.94);
	}

	.ios-press:active {
		transform: scale(0.95);
	}

	.ios-press-light {
		transition: transform 0.1s cubic-bezier(0.25, 0.46, 0.45, 0.94);
	}

	.ios-press-light:active {
		transform: scale(0.98);
	}

	.ios-hover {
		transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
	}

	.ios-hover:hover {
		transform: translateY(-2rpx);
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
	}

	/* iOS风格加载动画 */
	.ios-loading {
		animation: iosLoadingRotate 1s linear infinite;
	}

	@keyframes iosLoadingRotate {
		from {
			transform: rotate(0deg);
		}
		to {
			transform: rotate(360deg);
		}
	}

	/* iOS风格心跳动画 */
	.ios-heartbeat {
		animation: iosHeartbeat 1.5s ease-in-out infinite;
	}

	@keyframes iosHeartbeat {
		0%, 100% {
			transform: scale(1);
		}
		50% {
			transform: scale(1.1);
		}
	}

	/* iOS风格闪烁动画 */
	.ios-blink {
		animation: iosBlink 1s ease-in-out infinite;
	}

	@keyframes iosBlink {
		0%, 50%, 100% {
			opacity: 1;
		}
		25%, 75% {
			opacity: 0.3;
		}
	}

	/* iOS风格弹性动画 */
	.ios-elastic {
		animation: iosElastic 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275);
	}

	@keyframes iosElastic {
		0% {
			transform: scale(1);
		}
		30% {
			transform: scale(1.25);
		}
		40% {
			transform: scale(0.75);
		}
		60% {
			transform: scale(1.15);
		}
		80% {
			transform: scale(0.95);
		}
		100% {
			transform: scale(1);
		}
	}

	/* ================================
	   iOS风格响应式设计系统
	   ================================ */

	/* iOS设备断点 */
	@media (max-width: 375px) {
		/* iPhone SE */
		.hide-mobile {
			display: none !important;
		}

		.text-responsive {
			font-size: clamp(28rpx, 4vw, 32rpx);
		}
	}

	@media (min-width: 376px) and (max-width: 414px) {
		/* 标准iPhone */
		.text-responsive {
			font-size: clamp(30rpx, 4vw, 34rpx);
		}
	}

	@media (min-width: 415px) and (max-width: 480px) {
		/* 大屏iPhone */
		.text-responsive {
			font-size: clamp(32rpx, 4vw, 36rpx);
		}
	}

	@media (min-width: 768px) {
		/* iPad */
		.hide-desktop {
			display: none !important;
		}

		.text-responsive {
			font-size: clamp(34rpx, 3vw, 40rpx);
		}

		/* iPad布局优化 */
		.container {
			max-width: 1024px;
			margin: 0 auto;
		}
	}

	/* 兼容原有响应式类 */
	@media (max-width: 750rpx) {
		.hide-mobile {
			display: none !important;
		}
	}

	@media (min-width: 751rpx) {
		.hide-desktop {
			display: none !important;
		}
	}

	/* iOS风格滚动优化 */
	.ios-scroll {
		-webkit-overflow-scrolling: touch;
		scroll-behavior: smooth;
	}

	/* iOS风格毛玻璃效果 */
	.ios-blur {
		backdrop-filter: blur(20rpx);
		-webkit-backdrop-filter: blur(20rpx);
		background: rgba(255, 255, 255, 0.9);
	}

	/* iOS风格安全区域适配 */
	.safe-area-top {
		padding-top: env(safe-area-inset-top);
	}

	.safe-area-bottom {
		padding-bottom: env(safe-area-inset-bottom);
	}

	.safe-area-left {
		padding-left: env(safe-area-inset-left);
	}

	.safe-area-right {
		padding-right: env(safe-area-inset-right);
	}

	/* ================================
	   全局页面滚动修复 - 解决底部内容被遮挡问题
	   ================================ */

	/* 全局页面容器修复 - 确保所有页面都有足够的底部间距 */
	page {
		/* 为 tabbar 预留空间，避免内容被遮挡 */
		padding-bottom: calc(100rpx + env(safe-area-inset-bottom, 20rpx)) !important;
	}

	/* 页面主容器修复 */
	.container,
	.page-container,
	.main-container {
		/* 确保容器有足够的底部间距 */
		padding-bottom: calc(120rpx + env(safe-area-inset-bottom, 20rpx)) !important;
		/* 使用 box-sizing 确保 padding 不会影响布局 */
		box-sizing: border-box;
	}

	/* 滚动容器修复 */
	.scroll-view,
	scroll-view {
		/* 为滚动容器添加底部间距 */
		padding-bottom: calc(120rpx + env(safe-area-inset-bottom, 20rpx)) !important;
	}

	/* 列表容器修复 */
	.list-container,
	.content-list {
		/* 确保列表底部有足够间距 */
		padding-bottom: calc(140rpx + env(safe-area-inset-bottom, 20rpx)) !important;
	}

	/* 适老化模式下的额外间距 */
	.elderly-mode .container,
	.elderly-mode .page-container,
	.elderly-mode .main-container {
		/* 适老化模式需要更多间距 */
		padding-bottom: calc(160rpx + env(safe-area-inset-bottom, 20rpx)) !important;
	}

	/* 修复特定页面的滚动问题 */
	.home-container,
	.workspace-container,
	.map-container,
	.profile-container {
		/* 主要页面的底部间距修复 */
		padding-bottom: calc(140rpx + env(safe-area-inset-bottom, 20rpx)) !important;
		min-height: calc(100vh - 100rpx) !important;
	}

	/* 修复所有页面的通用滚动问题 */
	.page,
	.page-content,
	.content-wrapper {
		/* 确保页面内容有足够的底部间距 */
		padding-bottom: calc(120rpx + env(safe-area-inset-bottom, 20rpx)) !important;
	}

	/* 修复列表页面的滚动问题 */
	.institution-list,
	.service-list,
	.news-list,
	.subsidy-list {
		/* 列表页面需要额外的底部间距 */
		padding-bottom: calc(160rpx + env(safe-area-inset-bottom, 20rpx)) !important;
	}

	/* 修复详情页面的滚动问题 */
	.detail-page,
	.detail-container {
		/* 详情页面的底部间距 */
		padding-bottom: calc(140rpx + env(safe-area-inset-bottom, 20rpx)) !important;
	}

	/* 确保所有 view 元素都有正确的 box-sizing */
	view {
		box-sizing: border-box;
	}

	/* 修复 tabbar 遮挡问题的最终保障 */
	.uni-tabbar {
		/* 确保 tabbar 不会遮挡内容 */
		z-index: 999 !important;
	}

	/* 为有 tabbar 的页面添加额外的底部间距 */
	.has-tabbar {
		padding-bottom: calc(100rpx + env(safe-area-inset-bottom, 20rpx)) !important;
	}
</style>
