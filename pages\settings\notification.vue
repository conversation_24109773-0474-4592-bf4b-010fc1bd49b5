<template>
	<view class="container" :class="{ 'elderly-mode': elderlyMode }">
		<!-- 自定义导航栏 -->
		<view class="custom-navbar">
			<view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
			<view class="navbar-content">
				<view class="navbar-left" @click="goBack">
					<Icon name="arrow-left-line" size="36rpx" color="#333"></Icon>
					<text class="back-text">返回</text>
				</view>
				<view class="navbar-center">
					<text class="navbar-title">通知设置</text>
				</view>
				<view class="navbar-right"></view>
			</view>
		</view>

		<!-- 通知总开关 -->
		<view class="notification-master">
			<view class="master-header">
				<Icon name="notification-3-line" size="60rpx" color="#ff8a00"></Icon>
				<text class="master-title">通知设置</text>
				<text class="master-desc">管理您接收通知的方式和时间</text>
			</view>
			<view class="master-switch">
				<text class="switch-label">接收通知</text>
				<switch :checked="masterSwitch" @change="onMasterSwitchChange" color="#ff8a00" />
			</view>
		</view>

		<!-- 通知类型设置 -->
		<view class="notification-section">
			<view class="section-header">
				<Icon name="message-line" size="32rpx" color="#ff8a00"></Icon>
				<text class="section-title">通知类型</text>
			</view>
			
			<view class="notification-item">
				<view class="item-icon">
					<Icon name="calendar-check-line" size="32rpx" color="#4caf50"></Icon>
				</view>
				<view class="item-info">
					<text class="item-title">服务提醒</text>
					<text class="item-desc">预约服务、订单状态等提醒</text>
				</view>
				<switch :checked="notifications.service" @change="onServiceChange" color="#ff8a00" :disabled="!masterSwitch" />
			</view>
			
			<view class="notification-item">
				<view class="item-icon">
					<Icon name="heart-pulse-line" size="32rpx" color="#f44336"></Icon>
				</view>
				<view class="item-info">
					<text class="item-title">健康提醒</text>
					<text class="item-desc">用药提醒、体检提醒等</text>
				</view>
				<switch :checked="notifications.health" @change="onHealthChange" color="#ff8a00" :disabled="!masterSwitch" />
			</view>
			
			<view class="notification-item">
				<view class="item-icon">
					<Icon name="alarm-warning-line" size="32rpx" color="#ff9800"></Icon>
				</view>
				<view class="item-info">
					<text class="item-title">紧急通知</text>
					<text class="item-desc">紧急情况和安全提醒</text>
				</view>
				<switch :checked="notifications.emergency" @change="onEmergencyChange" color="#ff8a00" :disabled="!masterSwitch" />
			</view>
			
			<view class="notification-item">
				<view class="item-icon">
					<Icon name="gift-line" size="32rpx" color="#9c27b0"></Icon>
				</view>
				<view class="item-info">
					<text class="item-title">活动推广</text>
					<text class="item-desc">优惠活动、新功能介绍</text>
				</view>
				<switch :checked="notifications.promotion" @change="onPromotionChange" color="#ff8a00" :disabled="!masterSwitch" />
			</view>
			
			<view class="notification-item">
				<view class="item-icon">
					<Icon name="chat-3-line" size="32rpx" color="#2196f3"></Icon>
				</view>
				<view class="item-info">
					<text class="item-title">社交消息</text>
					<text class="item-desc">好友消息、评论回复等</text>
				</view>
				<switch :checked="notifications.social" @change="onSocialChange" color="#ff8a00" :disabled="!masterSwitch" />
			</view>
		</view>

		<!-- 通知方式设置 -->
		<view class="notification-section">
			<view class="section-header">
				<Icon name="send-plane-line" size="32rpx" color="#ff8a00"></Icon>
				<text class="section-title">通知方式</text>
			</view>
			
			<view class="notification-item">
				<view class="item-icon">
					<Icon name="smartphone-line" size="32rpx" color="#4caf50"></Icon>
				</view>
				<view class="item-info">
					<text class="item-title">应用推送</text>
					<text class="item-desc">在手机通知栏显示消息</text>
				</view>
				<switch :checked="methods.push" @change="onPushChange" color="#ff8a00" :disabled="!masterSwitch" />
			</view>
			
			<view class="notification-item">
				<view class="item-icon">
					<Icon name="message-2-line" size="32rpx" color="#2196f3"></Icon>
				</view>
				<view class="item-info">
					<text class="item-title">短信通知</text>
					<text class="item-desc">发送短信到注册手机号</text>
				</view>
				<switch :checked="methods.sms" @change="onSmsChange" color="#ff8a00" :disabled="!masterSwitch" />
			</view>
			
			<view class="notification-item">
				<view class="item-icon">
					<Icon name="mail-line" size="32rpx" color="#ff9800"></Icon>
				</view>
				<view class="item-info">
					<text class="item-title">邮件通知</text>
					<text class="item-desc">发送邮件到注册邮箱</text>
				</view>
				<switch :checked="methods.email" @change="onEmailChange" color="#ff8a00" :disabled="!masterSwitch" />
			</view>
			
			<view class="notification-item">
				<view class="item-icon">
					<Icon name="phone-line" size="32rpx" color="#f44336"></Icon>
				</view>
				<view class="item-info">
					<text class="item-title">电话通知</text>
					<text class="item-desc">重要事件电话提醒</text>
				</view>
				<switch :checked="methods.call" @change="onCallChange" color="#ff8a00" :disabled="!masterSwitch" />
			</view>
		</view>

		<!-- 免打扰时间设置 -->
		<view class="notification-section">
			<view class="section-header">
				<Icon name="moon-line" size="32rpx" color="#ff8a00"></Icon>
				<text class="section-title">免打扰时间</text>
			</view>
			
			<view class="notification-item">
				<view class="item-info">
					<text class="item-title">开启免打扰</text>
					<text class="item-desc">在指定时间段内不接收通知</text>
				</view>
				<switch :checked="doNotDisturb.enabled" @change="onDoNotDisturbChange" color="#ff8a00" />
			</view>
			
			<view class="time-setting" v-if="doNotDisturb.enabled">
				<view class="time-item">
					<text class="time-label">开始时间</text>
					<picker mode="time" :value="doNotDisturb.startTime" @change="onStartTimeChange">
						<view class="time-picker">
							<text>{{doNotDisturb.startTime}}</text>
							<Icon name="time-line" size="20rpx" color="#999"></Icon>
						</view>
					</picker>
				</view>
				
				<view class="time-item">
					<text class="time-label">结束时间</text>
					<picker mode="time" :value="doNotDisturb.endTime" @change="onEndTimeChange">
						<view class="time-picker">
							<text>{{doNotDisturb.endTime}}</text>
							<Icon name="time-line" size="20rpx" color="#999"></Icon>
						</view>
					</picker>
				</view>
			</view>
		</view>

		<!-- 声音和震动设置 -->
		<view class="notification-section">
			<view class="section-header">
				<Icon name="volume-up-line" size="32rpx" color="#ff8a00"></Icon>
				<text class="section-title">声音和震动</text>
			</view>
			
			<view class="notification-item">
				<view class="item-info">
					<text class="item-title">通知声音</text>
					<text class="item-desc">播放通知提示音</text>
				</view>
				<switch :checked="soundAndVibration.sound" @change="onSoundChange" color="#ff8a00" :disabled="!masterSwitch" />
			</view>
			
			<view class="notification-item">
				<view class="item-info">
					<text class="item-title">震动提醒</text>
					<text class="item-desc">接收通知时震动</text>
				</view>
				<switch :checked="soundAndVibration.vibration" @change="onVibrationChange" color="#ff8a00" :disabled="!masterSwitch" />
			</view>
			
			<view class="notification-item" @click="selectRingtone" v-if="soundAndVibration.sound">
				<view class="item-info">
					<text class="item-title">通知铃声</text>
					<text class="item-desc">{{selectedRingtone}}</text>
				</view>
				<Icon name="arrow-right-s-line" size="20rpx" color="#999"></Icon>
			</view>
		</view>
	</view>
</template>

<script>
import Icon from '@/components/Icon/Icon.vue'
import FeedbackUtils from '@/utils/feedback.js'
import { safeNavigateBack } from '@/utils/navigationUtils.js'

export default {
	components: {
		Icon
	},
	data() {
		return {
			statusBarHeight: 0, // 状态栏高度
			elderlyMode: false, // 适老化模式
			masterSwitch: true,
			notifications: {
				service: true,
				health: true,
				emergency: true,
				promotion: false,
				social: true
			},
			methods: {
				push: true,
				sms: true,
				email: false,
				call: false
			},
			doNotDisturb: {
				enabled: false,
				startTime: '22:00',
				endTime: '08:00'
			},
			soundAndVibration: {
				sound: true,
				vibration: true
			},
			selectedRingtone: '默认铃声'
		}
	},
	methods: {
		onMasterSwitchChange(e) {
			this.masterSwitch = e.detail.value;
			FeedbackUtils.lightFeedback();

			if (this.masterSwitch) {
				FeedbackUtils.showSuccess('通知已开启');
			} else {
				FeedbackUtils.showInfo('通知已关闭');
				
				// 关闭主开关时，提示用户所有通知将被暂停
				if (this.elderlyMode) {
					// 适老化模式下，使用更明显的提示
					setTimeout(() => {
						uni.showModal({
							title: '温馨提示',
							content: '关闭通知总开关后，您将不会收到任何通知消息',
							confirmText: '知道了',
							showCancel: false
						});
					}, 500);
				}
			}

			this.saveSettings();
		},
		onServiceChange(e) {
			this.notifications.service = e.detail.value;
			FeedbackUtils.lightFeedback();
			this.saveSettings();
		},
		onHealthChange(e) {
			this.notifications.health = e.detail.value;
			FeedbackUtils.lightFeedback();
			this.saveSettings();
		},
		onEmergencyChange(e) {
			this.notifications.emergency = e.detail.value;
			FeedbackUtils.lightFeedback();
			this.saveSettings();
		},
		onPromotionChange(e) {
			this.notifications.promotion = e.detail.value;
			FeedbackUtils.lightFeedback();
			this.saveSettings();
		},
		onSocialChange(e) {
			this.notifications.social = e.detail.value;
			FeedbackUtils.lightFeedback();
			this.saveSettings();
		},
		onPushChange(e) {
			this.methods.push = e.detail.value;
			this.saveSettings();
		},
		onSmsChange(e) {
			this.methods.sms = e.detail.value;
			this.saveSettings();
		},
		onEmailChange(e) {
			this.methods.email = e.detail.value;
			this.saveSettings();
		},
		onCallChange(e) {
			this.methods.call = e.detail.value;
			this.saveSettings();
		},
		onDoNotDisturbChange(e) {
			this.doNotDisturb.enabled = e.detail.value;
			this.saveSettings();
		},
		onStartTimeChange(e) {
			this.doNotDisturb.startTime = e.detail.value;
			this.saveSettings();
		},
		onEndTimeChange(e) {
			this.doNotDisturb.endTime = e.detail.value;
			this.saveSettings();
		},
		onSoundChange(e) {
			this.soundAndVibration.sound = e.detail.value;
			this.saveSettings();
		},
		onVibrationChange(e) {
			this.soundAndVibration.vibration = e.detail.value;
			this.saveSettings();
		},
		selectRingtone() {
			const ringtones = ['默认铃声', '清脆铃声', '温和铃声', '紧急铃声'];
			uni.showActionSheet({
				itemList: ringtones,
				success: (res) => {
					this.selectedRingtone = ringtones[res.tapIndex];
					this.saveSettings();
				}
			});
		},
		saveSettings() {
			const settings = {
				masterSwitch: this.masterSwitch,
				notifications: this.notifications,
				methods: this.methods,
				doNotDisturb: this.doNotDisturb,
				soundAndVibration: this.soundAndVibration,
				selectedRingtone: this.selectedRingtone
			};

			try {
				uni.setStorageSync('notificationSettings', settings);
				// 使用更好的反馈提示
				FeedbackUtils.showSuccess('设置已保存');
			} catch (error) {
				console.error('保存设置失败:', error);
				FeedbackUtils.showError('保存失败，请重试');
			}
		},

		// 返回上一页
		goBack() {
			FeedbackUtils.lightFeedback();
			safeNavigateBack(1, {
				success: () => {
					console.log('返回成功');
				},
				fail: () => {
					console.error('返回失败，尝试返回个人页');
					// 如果返回失败，尝试跳转到个人页
					uni.switchTab({
						url: '/pages/profile/profile'
					});
				}
			});
		}
	},
	onLoad() {
		// 获取状态栏高度
		const systemInfo = uni.getSystemInfoSync();
		this.statusBarHeight = systemInfo.statusBarHeight || 44;

		// 检查适老化模式
		this.elderlyMode = uni.getStorageSync('elderlyMode') || false;

		const savedSettings = uni.getStorageSync('notificationSettings');
		if (savedSettings) {
			this.masterSwitch = savedSettings.masterSwitch;
			this.notifications = { ...this.notifications, ...savedSettings.notifications };
			this.methods = { ...this.methods, ...savedSettings.methods };
			this.doNotDisturb = { ...this.doNotDisturb, ...savedSettings.doNotDisturb };
			this.soundAndVibration = { ...this.soundAndVibration, ...savedSettings.soundAndVibration };
			this.selectedRingtone = savedSettings.selectedRingtone || '默认铃声';
		}
	}
}
</script>

<style scoped>
.container {
	background: #f8f9fa;
	min-height: 100vh;
}

/* 导航栏样式 */
.custom-navbar {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 1000;
	background: rgba(255, 255, 255, 0.95);
	backdrop-filter: blur(20rpx);
	-webkit-backdrop-filter: blur(20rpx);
	border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);
}

.navbar-content {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 20rpx 32rpx;
	min-height: 88rpx;
}

.navbar-left {
	display: flex;
	align-items: center;
	gap: 12rpx;
	padding: 8rpx 16rpx 8rpx 0;
	border-radius: 20rpx;
	transition: all 0.2s ease;
}

.navbar-left:active {
	background-color: rgba(0, 0, 0, 0.04);
	transform: scale(0.96);
}

.back-text {
	font-size: 32rpx;
	color: #333;
	font-weight: 500;
}

.navbar-center {
	position: absolute;
	left: 50%;
	transform: translateX(-50%);
}

.navbar-title {
	font-size: 34rpx;
	font-weight: 600;
	color: #333;
}

.navbar-right {
	display: flex;
	align-items: center;
}

.notification-master {
	background: linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%);
	padding: 40rpx;
	margin-top: 220rpx; /* 为导航栏留出空间 */
	color: white;
}

.master-header {
	text-align: center;
	margin-bottom: 30rpx;
}

.master-title {
	font-size: 32rpx;
	font-weight: bold;
	display: block;
	margin: 20rpx 0 15rpx;
}

.master-desc {
	font-size: 24rpx;
	opacity: 0.9;
	line-height: 1.5;
	display: block;
}

.master-switch {
	display: flex;
	justify-content: space-between;
	align-items: center;
	background: rgba(255, 255, 255, 0.1);
	border-radius: 20rpx;
	padding: 25rpx 30rpx;
}

.switch-label {
	font-size: 28rpx;
	font-weight: bold;
}

/* 添加卡片交互动画 */
@keyframes cardAppear {
	from {
		opacity: 0;
		transform: translateY(30rpx);
	}
	to {
		opacity: 1;
		transform: translateY(0);
	}
}

.notification-section {
	background: white;
	margin: 20rpx;
	border-radius: 30rpx;
	padding: 40rpx;
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
	transition: transform 0.3s ease, box-shadow 0.3s ease;
	animation: cardAppear 0.5s ease forwards;
}

.notification-section:nth-child(2) {
	animation-delay: 0.1s;
}

.notification-section:nth-child(3) {
	animation-delay: 0.2s;
}

.notification-section:nth-child(4) {
	animation-delay: 0.3s;
}

.notification-section:active {
	transform: scale(0.98);
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

.section-header {
	display: flex;
	align-items: center;
	gap: 15rpx;
	margin-bottom: 30rpx;
}

.section-title {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
}

.notification-item {
	display: flex;
	align-items: center;
	padding: 25rpx 0;
	border-bottom: 1rpx solid #f0f0f0;
	transition: all 0.2s ease;
}

.notification-item:active {
	background-color: #f9f9f9;
}

.notification-item:last-child {
	border-bottom: none;
}

/* 适老化模式样式增强 */
.elderly-mode .notification-item {
	padding: 35rpx 0;
}

.elderly-mode .item-title {
	font-size: 32rpx;
}

.elderly-mode .item-desc {
	font-size: 28rpx;
}

.item-icon {
	width: 60rpx;
	height: 60rpx;
	background: rgba(255, 138, 0, 0.1);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 20rpx;
}

.item-info {
	flex: 1;
	margin-right: 20rpx;
}

.item-title {
	font-size: 28rpx;
	color: #333;
	font-weight: bold;
	display: block;
	margin-bottom: 8rpx;
}

.item-desc {
	font-size: 24rpx;
	color: #666;
	line-height: 1.4;
	display: block;
}

.time-setting {
	background: #f8f9fa;
	border-radius: 20rpx;
	padding: 30rpx;
	margin-top: 20rpx;
}

.time-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20rpx;
}

.time-item:last-child {
	margin-bottom: 0;
}

.time-label {
	font-size: 26rpx;
	color: #333;
}

.time-picker {
	display: flex;
	align-items: center;
	gap: 10rpx;
	padding: 15rpx 20rpx;
	background: white;
	border-radius: 15rpx;
	font-size: 26rpx;
	color: #333;
}

/* 开关交互动画 */
@keyframes switchBounce {
	0%, 100% { transform: scale(1); }
	50% { transform: scale(1.1); }
}

switch:checked {
	animation: switchBounce 0.3s ease;
}
</style>
