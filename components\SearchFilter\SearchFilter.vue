<template>
	<view class="search-filter">
		<!-- 搜索栏 -->
		<view class="search-section">
			<view class="search-bar">
				<view class="search-input-wrapper">
					<Icon name="search-line" size="32rpx" color="#999" class="search-icon" />
					<input
						class="search-input"
						type="text"
						:placeholder="placeholder"
						v-model="searchKeyword"
						@input="onSearchInput"
						@confirm="onSearchConfirm"
					/>
					<Icon 
						v-if="searchKeyword" 
						name="close-line" 
						size="32rpx" 
						color="#999"
						class="clear-icon"
						@click="clearSearch"
					/>
				</view>
				<InteractiveButton
					type="primary"
					size="medium"
					text="搜索"
					@click="onSearchConfirm"
				/>
			</view>
			
			<!-- 筛选栏 -->
			<view class="filter-bar">
				<view class="filter-item" @click="showCategoryFilter">
					<text class="filter-text">{{ getCurrentCategoryText() }}</text>
					<Icon name="arrow-down-s-line" size="24rpx" color="#666" />
				</view>
				<view class="filter-item" @click="showSortFilter">
					<text class="filter-text">{{ getCurrentSortText() }}</text>
					<Icon name="arrow-down-s-line" size="24rpx" color="#666" />
				</view>
			</view>
		</view>
		
		<!-- 分类筛选弹窗 -->
		<uni-popup ref="categoryPopup" type="bottom">
			<view class="filter-popup">
				<view class="popup-header">
					<text class="popup-title">选择分类</text>
					<text class="popup-close" @click="closeCategoryFilter">取消</text>
				</view>
				<view class="filter-options">
					<view 
						class="filter-option"
						:class="{ active: currentCategory === category.value }"
						v-for="(category, index) in categories"
						:key="index"
						@click="selectCategory(category.value)"
					>
						<text>{{ category.label }}</text>
						<Icon 
							v-if="currentCategory === category.value"
							name="check-line" 
							size="32rpx" 
							color="#ff8a00" 
						/>
					</view>
				</view>
			</view>
		</uni-popup>
		
		<!-- 排序筛选弹窗 -->
		<uni-popup ref="sortPopup" type="bottom">
			<view class="filter-popup">
				<view class="popup-header">
					<text class="popup-title">排序方式</text>
					<text class="popup-close" @click="closeSortFilter">取消</text>
				</view>
				<view class="filter-options">
					<view 
						class="filter-option"
						:class="{ active: currentSort === sort.value }"
						v-for="(sort, index) in sortOptions"
						:key="index"
						@click="selectSort(sort.value)"
					>
						<text>{{ sort.label }}</text>
						<Icon 
							v-if="currentSort === sort.value"
							name="check-line" 
							size="32rpx" 
							color="#ff8a00" 
						/>
					</view>
				</view>
			</view>
		</uni-popup>
	</view>
</template>

<script>
import Icon from '@/components/Icon/Icon.vue'
import InteractiveButton from '@/components/InteractiveButton/InteractiveButton.vue'

export default {
	name: 'SearchFilter',
	components: {
		Icon,
		InteractiveButton
	},
	props: {
		placeholder: {
			type: String,
			default: '请输入搜索关键词'
		},
		categories: {
			type: Array,
			default: () => [
				{ label: '全部', value: '' }
			]
		}
	},
	data() {
		return {
			searchKeyword: '',
			currentCategory: '',
			currentSort: 'default',
			sortOptions: [
				{ label: '默认排序', value: 'default' },
				{ label: '距离最近', value: 'distance' },
				{ label: '评分最高', value: 'rating' },
				{ label: '价格最低', value: 'price_asc' },
				{ label: '价格最高', value: 'price_desc' }
			]
		}
	},
	methods: {
		// 搜索输入
		onSearchInput(e) {
			this.searchKeyword = e.detail.value
		},
		
		// 搜索确认
		onSearchConfirm() {
			this.emitSearch()
		},
		
		// 清空搜索
		clearSearch() {
			this.searchKeyword = ''
			this.emitSearch()
		},
		
		// 发送搜索事件
		emitSearch() {
			this.$emit('search', {
				keyword: this.searchKeyword
			})
		},
		
		// 显示分类筛选
		showCategoryFilter() {
			this.$refs.categoryPopup.open()
		},
		
		// 关闭分类筛选
		closeCategoryFilter() {
			this.$refs.categoryPopup.close()
		},
		
		// 选择分类
		selectCategory(value) {
			this.currentCategory = value
			this.closeCategoryFilter()
			this.emitFilter()
		},
		
		// 显示排序筛选
		showSortFilter() {
			this.$refs.sortPopup.open()
		},
		
		// 关闭排序筛选
		closeSortFilter() {
			this.$refs.sortPopup.close()
		},
		
		// 选择排序
		selectSort(value) {
			this.currentSort = value
			this.closeSortFilter()
			this.emitFilter()
		},
		
		// 发送筛选事件
		emitFilter() {
			this.$emit('filter', {
				category: this.currentCategory,
				sort: this.currentSort
			})
		},
		
		// 获取当前分类文本
		getCurrentCategoryText() {
			const category = this.categories.find(item => item.value === this.currentCategory)
			return category ? category.label : '全部分类'
		},
		
		// 获取当前排序文本
		getCurrentSortText() {
			const sort = this.sortOptions.find(item => item.value === this.currentSort)
			return sort ? sort.label : '默认排序'
		}
	}
}
</script>

<style scoped>
.search-filter {
	background: white;
	border-bottom: 1rpx solid #e0e0e0;
}

.search-section {
	padding: 20rpx;
}

.search-bar {
	display: flex;
	align-items: center;
	gap: 20rpx;
	margin-bottom: 20rpx;
}

.search-input-wrapper {
	flex: 1;
	position: relative;
	display: flex;
	align-items: center;
	background: #f5f5f5;
	border-radius: 25rpx;
	padding: 0 20rpx;
	height: 70rpx;
}

.search-icon {
	margin-right: 15rpx;
}

.search-input {
	flex: 1;
	height: 100%;
	font-size: 28rpx;
	color: #333;
	background: transparent;
	border: none;
}

.clear-icon {
	margin-left: 15rpx;
}

.filter-bar {
	display: flex;
	gap: 20rpx;
}

.filter-item {
	display: flex;
	align-items: center;
	gap: 8rpx;
	padding: 12rpx 20rpx;
	background: #f8f9fa;
	border-radius: 20rpx;
	border: 1rpx solid #e0e0e0;
}

.filter-text {
	font-size: 26rpx;
	color: #666;
}

.filter-popup {
	background: white;
	border-radius: 20rpx 20rpx 0 0;
	max-height: 60vh;
}

.popup-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 30rpx;
	border-bottom: 1rpx solid #e0e0e0;
}

.popup-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.popup-close {
	font-size: 28rpx;
	color: #666;
}

.filter-options {
	padding: 20rpx 0;
	max-height: 50vh;
	overflow-y: auto;
}

.filter-option {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 25rpx 30rpx;
	font-size: 28rpx;
	color: #333;
}

.filter-option.active {
	color: #ff8a00;
	background: #fff7f0;
}

.filter-option:active {
	background: #f5f5f5;
}
</style>
