<template>
	<view class="container">
		<PageHeader title="紧急呼叫" showBack></PageHeader>
		
		<view class="content">
			<!-- 紧急状态指示 -->
			<view class="emergency-status">
				<view class="status-icon" :class="{ active: isEmergencyMode }">
					<Icon name="alarm-warning-line" size="80rpx" color="white"></Icon>
				</view>
				<text class="status-text">{{ isEmergencyMode ? '紧急模式已激活' : '紧急呼叫服务' }}</text>
				<text class="status-desc">{{ isEmergencyMode ? '正在为您联系紧急服务' : '24小时紧急救援服务' }}</text>
			</view>

			<!-- 快速呼叫按钮 -->
			<view class="quick-call-section">
				<text class="section-title">快速呼叫</text>
				<view class="call-buttons">
					<view class="call-btn emergency" @click="callEmergency">
						<Icon name="phone-line" size="60rpx" color="white"></Icon>
						<text class="btn-text">120急救</text>
						<text class="btn-desc">医疗急救</text>
					</view>
					<view class="call-btn police" @click="callPolice">
						<Icon name="shield-line" size="60rpx" color="white"></Icon>
						<text class="btn-text">110报警</text>
						<text class="btn-desc">紧急报警</text>
					</view>
					<view class="call-btn fire" @click="callFire">
						<Icon name="fire-line" size="60rpx" color="white"></Icon>
						<text class="btn-text">119消防</text>
						<text class="btn-desc">火警救援</text>
					</view>
				</view>
			</view>

			<!-- 紧急联系人 -->
			<view class="section">
				<view class="section-header">
					<text class="section-title">紧急联系人</text>
					<InteractiveButton 
						type="primary" 
						text="添加联系人" 
						size="small"
						leftIcon="add-line"
						@click="showAddContact"
					/>
				</view>
				<view class="contact-list">
					<view class="contact-item" v-for="contact in emergencyContacts" :key="contact.id">
						<view class="contact-avatar">
							<text class="avatar-text">{{ contact.name.charAt(0) }}</text>
						</view>
						<view class="contact-info">
							<text class="contact-name">{{ contact.name }}</text>
							<text class="contact-relation">{{ contact.relation }}</text>
							<text class="contact-phone">{{ contact.phone }}</text>
						</view>
						<view class="contact-actions">
							<button class="action-btn call" @click="callContact(contact)">
								<Icon name="phone-line" size="24rpx" color="white"></Icon>
							</button>
							<button class="action-btn message" @click="messageContact(contact)">
								<Icon name="message-line" size="24rpx" color="white"></Icon>
							</button>
						</view>
					</view>
				</view>
			</view>

			<!-- 位置信息 -->
			<view class="section">
				<text class="section-title">当前位置</text>
				<view class="location-card">
					<view class="location-info">
						<Icon name="location-line" size="32rpx" color="#ff8a00"></Icon>
						<view class="location-text">
							<text class="location-address">{{ currentLocation.address }}</text>
							<text class="location-detail">{{ currentLocation.detail }}</text>
						</view>
					</view>
					<InteractiveButton 
						type="secondary" 
						text="更新位置" 
						size="small"
						leftIcon="refresh-line"
						@click="updateLocation"
					/>
				</view>
			</view>

			<!-- 健康信息 -->
			<view class="section">
				<text class="section-title">健康信息</text>
				<view class="health-info-card">
					<view class="info-item">
						<text class="info-label">血型</text>
						<text class="info-value">{{ healthInfo.bloodType }}</text>
					</view>
					<view class="info-item">
						<text class="info-label">过敏史</text>
						<text class="info-value">{{ healthInfo.allergies || '无' }}</text>
					</view>
					<view class="info-item">
						<text class="info-label">慢性疾病</text>
						<text class="info-value">{{ healthInfo.chronicDiseases || '无' }}</text>
					</view>
					<view class="info-item">
						<text class="info-label">常用药物</text>
						<text class="info-value">{{ healthInfo.medications || '无' }}</text>
					</view>
				</view>
			</view>

			<!-- 紧急指南 -->
			<view class="section">
				<text class="section-title">紧急指南</text>
				<view class="guide-list">
					<view class="guide-item" v-for="guide in emergencyGuides" :key="guide.id" @click="viewGuide(guide)">
						<Icon :name="guide.icon" size="32rpx" :color="guide.color"></Icon>
						<view class="guide-content">
							<text class="guide-title">{{ guide.title }}</text>
							<text class="guide-desc">{{ guide.description }}</text>
						</view>
						<Icon name="arrow-right-s-line" size="24rpx" color="#999"></Icon>
					</view>
				</view>
			</view>
		</view>

		<!-- 添加联系人弹窗 -->
		<uni-popup ref="contactPopup" type="bottom">
			<view class="popup-content">
				<view class="popup-header">
					<text class="popup-title">添加紧急联系人</text>
					<button class="close-btn" @click="hideAddContact">
						<Icon name="close-line" size="24rpx" color="#999"></Icon>
					</button>
				</view>
				<view class="popup-form">
					<view class="form-item">
						<text class="form-label">姓名</text>
						<input 
							class="form-input" 
							v-model="contactForm.name" 
							placeholder="请输入联系人姓名"
						/>
					</view>
					<view class="form-item">
						<text class="form-label">关系</text>
						<picker 
							:value="relationIndex" 
							:range="relationOptions" 
							@change="onRelationChange"
						>
							<view class="picker-display">
								<text class="picker-text">{{ contactForm.relation || '请选择关系' }}</text>
								<Icon name="arrow-down-s-line" size="20rpx" color="#999"></Icon>
							</view>
						</picker>
					</view>
					<view class="form-item">
						<text class="form-label">电话号码</text>
						<input 
							class="form-input" 
							v-model="contactForm.phone" 
							placeholder="请输入电话号码"
							type="number"
						/>
					</view>
				</view>
				<view class="popup-actions">
					<InteractiveButton 
						type="secondary" 
						text="取消" 
						@click="hideAddContact"
					/>
					<InteractiveButton 
						type="primary" 
						text="保存" 
						:loading="saving"
						@click="saveContact"
					/>
				</view>
			</view>
		</uni-popup>
	</view>
</template>

<script>
import Icon from '@/components/Icon/Icon.vue'
import PageHeader from '@/components/PageHeader/PageHeader.vue'
import InteractiveButton from '@/components/InteractiveButton/InteractiveButton.vue'

export default {
	name: 'HealthEmergency',
	components: {
		Icon,
		PageHeader,
		InteractiveButton
	},
	data() {
		return {
			isEmergencyMode: false,
			saving: false,
			relationIndex: 0,
			emergencyContacts: [
				{
					id: 1,
					name: '张女士',
					relation: '女儿',
					phone: '138****8888'
				},
				{
					id: 2,
					name: '李先生',
					relation: '儿子',
					phone: '139****9999'
				}
			],
			currentLocation: {
				address: '北京市朝阳区建国路88号',
				detail: '距离最近医院约2.3公里'
			},
			healthInfo: {
				bloodType: 'A型',
				allergies: '青霉素',
				chronicDiseases: '高血压',
				medications: '降压药'
			},
			emergencyGuides: [
				{
					id: 1,
					title: '心脏病发作',
					description: '胸痛、呼吸困难时的应急处理',
					icon: 'heart-line',
					color: '#f44336'
				},
				{
					id: 2,
					title: '中风症状',
					description: '识别中风症状及紧急处理',
					icon: 'brain-line',
					color: '#9c27b0'
				},
				{
					id: 3,
					title: '跌倒受伤',
					description: '跌倒后的自我检查和处理',
					icon: 'user-line',
					color: '#ff9800'
				},
				{
					id: 4,
					title: '药物过敏',
					description: '过敏反应的识别和处理',
					icon: 'capsule-line',
					color: '#4caf50'
				}
			],
			contactForm: {
				name: '',
				relation: '',
				phone: ''
			},
			relationOptions: ['子女', '配偶', '兄弟姐妹', '朋友', '邻居', '其他']
		}
	},
	methods: {
		callEmergency() {
			this.makeEmergencyCall('120', '急救中心')
		},
		callPolice() {
			this.makeEmergencyCall('110', '报警中心')
		},
		callFire() {
			this.makeEmergencyCall('119', '消防中心')
		},
		makeEmergencyCall(number, service) {
			uni.showModal({
				title: '紧急呼叫',
				content: `确定要拨打${service}电话${number}吗？`,
				confirmText: '立即拨打',
				cancelText: '取消',
				success: (res) => {
					if (res.confirm) {
						this.isEmergencyMode = true
						uni.makePhoneCall({
							phoneNumber: number,
							success: () => {
								console.log('拨打成功')
							},
							fail: () => {
								uni.showToast({
									title: '拨打失败',
									icon: 'none'
								})
							},
							complete: () => {
								this.isEmergencyMode = false
							}
						})
					}
				}
			})
		},
		callContact(contact) {
			uni.makePhoneCall({
				phoneNumber: contact.phone.replace(/\*/g, '1')
			})
		},
		messageContact(contact) {
			uni.showToast({
				title: '发送紧急短信',
				icon: 'success'
			})
		},
		updateLocation() {
			uni.showLoading({ title: '定位中...' })
			
			uni.getLocation({
				type: 'gcj02',
				success: (res) => {
					// 模拟地址解析
					this.currentLocation = {
						address: '北京市朝阳区建国路88号',
						detail: '位置已更新'
					}
					uni.hideLoading()
					uni.showToast({
						title: '位置更新成功',
						icon: 'success'
					})
				},
				fail: () => {
					uni.hideLoading()
					uni.showToast({
						title: '定位失败',
						icon: 'none'
					})
				}
			})
		},
		viewGuide(guide) {
			uni.showModal({
				title: guide.title,
				content: guide.description,
				showCancel: false
			})
		},
		showAddContact() {
			this.contactForm = {
				name: '',
				relation: '',
				phone: ''
			}
			this.$refs.contactPopup.open()
		},
		hideAddContact() {
			this.$refs.contactPopup.close()
		},
		onRelationChange(e) {
			this.relationIndex = e.detail.value
			this.contactForm.relation = this.relationOptions[this.relationIndex]
		},
		async saveContact() {
			if (!this.contactForm.name.trim()) {
				uni.showToast({ title: '请输入姓名', icon: 'none' })
				return
			}
			if (!this.contactForm.phone.trim()) {
				uni.showToast({ title: '请输入电话号码', icon: 'none' })
				return
			}
			
			this.saving = true
			try {
				await new Promise(resolve => setTimeout(resolve, 1000))
				
				this.emergencyContacts.push({
					...this.contactForm,
					id: Date.now()
				})
				
				uni.showToast({
					title: '添加成功',
					icon: 'success'
				})
				
				this.hideAddContact()
			} catch (error) {
				uni.showToast({
					title: '添加失败',
					icon: 'none'
				})
			} finally {
				this.saving = false
			}
		}
	}
}
</script>

<style scoped>
.container {
	min-height: 100vh;
	background: #f5f5f5;
}

.content {
	padding: 140rpx 32rpx 40rpx;
}

.emergency-status {
	text-align: center;
	margin-bottom: 40rpx;
}

.status-icon {
	width: 160rpx;
	height: 160rpx;
	border-radius: 50%;
	background: #ff8a00;
	display: flex;
	align-items: center;
	justify-content: center;
	margin: 0 auto 24rpx;
	transition: all 0.3s ease;
}

.status-icon.active {
	background: #f44336;
	animation: pulse 1s infinite;
}

@keyframes pulse {
	0% { transform: scale(1); }
	50% { transform: scale(1.1); }
	100% { transform: scale(1); }
}

.status-text {
	font-size: 36rpx;
	font-weight: 600;
	color: #333;
	display: block;
	margin-bottom: 8rpx;
}

.status-desc {
	font-size: 26rpx;
	color: #666;
	display: block;
}

.quick-call-section {
	margin-bottom: 40rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 24rpx;
	display: block;
}

.call-buttons {
	display: flex;
	gap: 16rpx;
}

.call-btn {
	flex: 1;
	background: white;
	border-radius: 20rpx;
	padding: 32rpx 16rpx;
	text-align: center;
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.call-btn.emergency {
	background: linear-gradient(135deg, #f44336, #d32f2f);
	color: white;
}

.call-btn.police {
	background: linear-gradient(135deg, #2196f3, #1976d2);
	color: white;
}

.call-btn.fire {
	background: linear-gradient(135deg, #ff9800, #f57c00);
	color: white;
}

.btn-text {
	font-size: 28rpx;
	font-weight: 600;
	display: block;
	margin: 16rpx 0 8rpx;
}

.btn-desc {
	font-size: 24rpx;
	opacity: 0.9;
	display: block;
}

.section {
	margin-bottom: 32rpx;
}

.section-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 24rpx;
}

.contact-list {
	display: flex;
	flex-direction: column;
	gap: 16rpx;
}

.contact-item {
	background: white;
	border-radius: 16rpx;
	padding: 24rpx;
	display: flex;
	align-items: center;
	gap: 16rpx;
}

.contact-avatar {
	width: 60rpx;
	height: 60rpx;
	border-radius: 50%;
	background: #ff8a00;
	display: flex;
	align-items: center;
	justify-content: center;
}

.avatar-text {
	font-size: 24rpx;
	font-weight: 600;
	color: white;
}

.contact-info {
	flex: 1;
}

.contact-name {
	font-size: 30rpx;
	font-weight: 600;
	color: #333;
	display: block;
	margin-bottom: 4rpx;
}

.contact-relation {
	font-size: 24rpx;
	color: #666;
	display: block;
	margin-bottom: 4rpx;
}

.contact-phone {
	font-size: 26rpx;
	color: #999;
	display: block;
}

.contact-actions {
	display: flex;
	gap: 8rpx;
}

.action-btn {
	width: 48rpx;
	height: 48rpx;
	border-radius: 50%;
	border: none;
	display: flex;
	align-items: center;
	justify-content: center;
}

.action-btn.call {
	background: #4caf50;
}

.action-btn.message {
	background: #2196f3;
}

.location-card {
	background: white;
	border-radius: 16rpx;
	padding: 24rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.location-info {
	display: flex;
	align-items: center;
	gap: 12rpx;
	flex: 1;
}

.location-text {
	flex: 1;
}

.location-address {
	font-size: 28rpx;
	color: #333;
	display: block;
	margin-bottom: 4rpx;
}

.location-detail {
	font-size: 24rpx;
	color: #666;
	display: block;
}

.health-info-card {
	background: white;
	border-radius: 16rpx;
	padding: 24rpx;
}

.info-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 16rpx;
}

.info-item:last-child {
	margin-bottom: 0;
}

.info-label {
	font-size: 28rpx;
	color: #666;
}

.info-value {
	font-size: 28rpx;
	color: #333;
	font-weight: 500;
}

.guide-list {
	display: flex;
	flex-direction: column;
	gap: 16rpx;
}

.guide-item {
	background: white;
	border-radius: 16rpx;
	padding: 24rpx;
	display: flex;
	align-items: center;
	gap: 16rpx;
}

.guide-content {
	flex: 1;
}

.guide-title {
	font-size: 28rpx;
	font-weight: 600;
	color: #333;
	display: block;
	margin-bottom: 4rpx;
}

.guide-desc {
	font-size: 24rpx;
	color: #666;
	display: block;
}

.popup-content {
	background: white;
	border-radius: 24rpx 24rpx 0 0;
	padding: 32rpx;
}

.popup-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 32rpx;
}

.popup-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
}

.close-btn {
	width: 48rpx;
	height: 48rpx;
	border-radius: 50%;
	border: none;
	background: #f5f5f5;
	display: flex;
	align-items: center;
	justify-content: center;
}

.popup-form {
	margin-bottom: 32rpx;
}

.form-item {
	margin-bottom: 24rpx;
}

.form-label {
	font-size: 28rpx;
	color: #333;
	margin-bottom: 12rpx;
	display: block;
}

.form-input {
	width: 100%;
	padding: 20rpx;
	border: 1rpx solid #e0e0e0;
	border-radius: 12rpx;
	font-size: 28rpx;
	background: #fafafa;
}

.picker-display {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20rpx;
	border: 1rpx solid #e0e0e0;
	border-radius: 12rpx;
	background: #fafafa;
}

.picker-text {
	font-size: 28rpx;
	color: #333;
}

.popup-actions {
	display: flex;
	gap: 16rpx;
}
</style>
