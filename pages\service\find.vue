<template>
	<view class="container">
		<!-- 页面头部 -->
		<PageHeader title="找服务">
			<template #actions>
				<InteractiveButton
					type="secondary"
					size="small"
					text="我的预约"
					icon="calendar-line"
					@click="viewMyAppointments"
				></InteractiveButton>
			</template>
		</PageHeader>

		<!-- 搜索栏 -->
		<view class="search-section">
			<view class="search-container">
				<Icon name="search-line" size="32rpx" color="#999" class="search-icon"></Icon>
				<input 
					class="search-input" 
					placeholder="搜索服务名称或关键词" 
					v-model="searchKeyword"
					@input="onSearchInput"
				/>
				<view v-if="searchKeyword" class="clear-btn" @click="clearSearch">
					<Icon name="close-circle-fill" size="32rpx" color="#ccc"></Icon>
				</view>
			</view>
		</view>

		<!-- 服务分类 -->
		<view class="category-section">
			<text class="section-title">服务分类</text>
			<view class="category-grid">
				<InteractiveCard 
					v-for="(category, index) in serviceCategories" 
					:key="index"
					class="category-item"
					@click="selectCategory(category)"
				>
					<view class="category-content">
						<view class="category-icon" :style="{ backgroundColor: category.bgColor }">
							<Icon :name="category.icon" size="48rpx" color="#fff"></Icon>
						</view>
						<text class="category-name">{{ category.name }}</text>
						<text class="category-count">{{ category.count }}项服务</text>
					</view>
				</InteractiveCard>
			</view>
		</view>

		<!-- 热门服务 -->
		<view class="hot-services-section">
			<view class="section-header">
				<text class="section-title">热门服务</text>
				<text class="more-link" @click="viewAllServices">查看全部</text>
			</view>
			<scroll-view scroll-x="true" class="hot-services-scroll">
				<view class="hot-services-list">
					<InteractiveCard 
						v-for="(service, index) in hotServices" 
						:key="service.id"
						class="hot-service-item"
						@click="viewServiceDetail(service)"
					>
						<view class="service-content">
							<!-- 服务图片或图标占位符 -->
							<view class="service-image-container">
								<image 
									v-if="service.image && !service.imageError" 
									:src="service.image" 
									class="service-image" 
									mode="aspectFill"
									@error="handleImageError(service)"
								></image>
								<view v-else class="service-icon-placeholder" :style="{ background: getCategoryGradient(service.category) }">
									<Icon :name="getCategoryIcon(service.category)" size="40rpx" color="#fff"></Icon>
								</view>
							</view>
							<view class="service-info">
								<text class="service-name">{{ service.name }}</text>
								<text class="service-provider">{{ service.provider }}</text>
								<view class="service-meta">
									<view class="meta-item">
										<Icon name="star-fill" size="20rpx" color="#ffc107"></Icon>
										<text class="meta-text">{{ service.rating }}</text>
									</view>
									<view class="meta-item">
										<Icon name="money-dollar-circle-line" size="20rpx" color="#ff8a00"></Icon>
										<text class="meta-text">￥{{ service.price }}</text>
									</view>
								</view>
							</view>
						</view>
					</InteractiveCard>
				</view>
			</scroll-view>
		</view>

		<!-- 推荐服务 -->
		<view class="recommended-section">
			<text class="section-title">为您推荐</text>
			<scroll-view 
				scroll-y="true" 
				class="service-list"
				@scrolltolower="loadMore"
				refresher-enabled="true"
				@refresherrefresh="refreshData"
				:refresher-triggered="refreshing"
			>
				<InteractiveCard 
					v-for="(service, index) in filteredServices" 
					:key="service.id"
					class="service-item"
					@click="viewServiceDetail(service)"
				>
					<view class="service-detail-content">
						<!-- 服务图片或图标占位符 -->
						<view class="service-image-container">
							<image 
								v-if="service.image && !service.imageError" 
								:src="service.image" 
								class="service-image" 
								mode="aspectFill"
								@error="handleImageError(service)"
							></image>
							<view v-else class="service-icon-placeholder" :style="{ background: getCategoryGradient(service.category) }">
								<Icon :name="getCategoryIcon(service.category)" size="48rpx" color="#fff"></Icon>
							</view>
						</view>

						<!-- 服务信息 -->
						<view class="service-info">
							<view class="service-header">
								<text class="service-name">{{ service.name }}</text>
								<view class="service-category" :style="{ backgroundColor: getCategoryColor(service.category) }">
									<text class="category-text">{{ service.category }}</text>
								</view>
							</view>
							
							<text class="service-description">{{ service.description }}</text>
							
							<view class="service-provider-info">
								<Icon name="store-line" size="24rpx" color="#999"></Icon>
								<text class="provider-text">{{ service.provider }}</text>
							</view>

							<view class="service-features">
								<view class="feature-item">
									<Icon name="star-fill" size="24rpx" color="#ffc107"></Icon>
									<text class="feature-text">{{ service.rating }}分</text>
								</view>
								<view class="feature-item">
									<Icon name="map-pin-line" size="24rpx" color="#999"></Icon>
									<text class="feature-text">{{ service.area }}</text>
								</view>
								<view class="feature-item">
									<Icon name="phone-line" size="24rpx" color="#999"></Icon>
									<text class="feature-text">{{ service.phone }}</text>
								</view>
							</view>

							<view class="service-tags" v-if="service.tags && service.tags.length > 0">
								<view 
									v-for="(tag, tagIndex) in service.tags.slice(0, 3)" 
									:key="tagIndex"
									class="tag-item"
								>
									<text class="tag-label">{{ tag }}</text>
								</view>
							</view>
						</view>

						<!-- 价格和操作 -->
						<view class="service-actions">
							<view class="price-section">
								<text class="price-label">价格</text>
								<text class="price-value">￥{{ service.price }}</text>
							</view>
							<view class="action-buttons">
								<InteractiveButton 
									type="secondary" 
									size="small" 
									text="咨询" 
									icon="message-line"
									@click.stop="consultService(service)"
								></InteractiveButton>
								<InteractiveButton 
									type="primary" 
									size="small" 
									text="预约" 
									icon="calendar-check-line"
									@click.stop="bookService(service)"
								></InteractiveButton>
							</view>
						</view>
					</view>
				</InteractiveCard>

				<!-- 加载状态 -->
				<view class="load-more" v-if="hasMore && loading">
					<view class="loading-spinner"></view>
					<text class="loading-text">加载中...</text>
				</view>
				<view class="no-more" v-else-if="!hasMore && filteredServices.length > 0">
					<text>没有更多服务了</text>
				</view>
				<view class="empty" v-else-if="!loading && filteredServices.length === 0">
					<Icon name="service-line" size="120rpx" color="#ccc"></Icon>
					<text class="empty-text">暂无相关服务</text>
					<text class="empty-tip">请尝试其他关键词</text>
				</view>
			</scroll-view>
		</view>
	</view>
</template>

<script>
import InteractiveCard from '@/components/InteractiveCard/InteractiveCard.vue'
import InteractiveButton from '@/components/InteractiveButton/InteractiveButton.vue'
import Icon from '@/components/Icon/Icon.vue'
import PageHeader from '@/components/PageHeader/PageHeader.vue'
import FeedbackUtils from '@/utils/feedback.js'
import OfflineDataManager from '@/utils/offlineData.js'

export default {
	components: {
		InteractiveCard,
		InteractiveButton,
		Icon,
		PageHeader
	},
	data() {
		return {
			serviceList: [],
			hotServices: [],
			loading: false,
			refreshing: false,
			hasMore: true,
			page: 1,
			pageSize: 10,
			searchKeyword: '',
			selectedCategory: '',
			
			// 服务分类
			serviceCategories: [
				{
					name: '医疗护理',
					icon: 'health-book-line',
					bgColor: '#4caf50',
					count: 25
				},
				{
					name: '生活照料',
					icon: 'home-heart-line',
					bgColor: '#2196f3',
					count: 18
				},
				{
					name: '康复训练',
					icon: 'run-line',
					bgColor: '#9c27b0',
					count: 12
				},
				{
					name: '心理关怀',
					icon: 'heart-line',
					bgColor: '#e91e63',
					count: 8
				},
				{
					name: '文娱活动',
					icon: 'music-line',
					bgColor: '#ff9800',
					count: 15
				},
				{
					name: '营养配餐',
					icon: 'restaurant-line',
					bgColor: '#795548',
					count: 10
				}
			]
		}
	},
	computed: {
		// 筛选后的服务列表
		filteredServices() {
			let list = [...this.serviceList];
			
			// 搜索筛选
			if (this.searchKeyword) {
				const keyword = this.searchKeyword.toLowerCase();
				list = list.filter(service => 
					service.name.toLowerCase().includes(keyword) ||
					service.description.toLowerCase().includes(keyword) ||
					service.provider.toLowerCase().includes(keyword)
				);
			}
			
			// 分类筛选
			if (this.selectedCategory) {
				list = list.filter(service => service.category === this.selectedCategory);
			}
			
			return list;
		}
	},
	onLoad() {
		// 初始化离线数据
		OfflineDataManager.initOfflineData();

		// 先加载热门服务（使用离线数据，不需要网络检测）
		this.loadHotServices();

		// 再加载服务列表（带网络检测）
		this.loadServices();
	},
	methods: {
		// 加载服务列表 - 100%离线模式
		async loadServices() {
			this.loading = true;

			try {
				// 强制初始化离线数据
				OfflineDataManager.initOfflineData();

				// 100%使用离线数据，确保用户能看到内容
				const params = {
					page: this.page,
					pageSize: this.pageSize,
					keyword: this.searchKeyword,
					category: this.selectedCategory
				};

				const result = OfflineDataManager.getOfflineServices(params);

				if (this.page === 1) {
					this.serviceList = result.data;
				} else {
					this.serviceList.push(...result.data);
				}

				this.hasMore = result.data.length === this.pageSize;

				// 显示成功提示
				if (this.page === 1 && result.data.length > 0) {
					FeedbackUtils.showSuccess(`已加载 ${result.data.length} 个服务`);
				}
			} catch (error) {
				console.error('加载服务数据失败:', error);
				// 使用备用数据确保页面有内容
				this.serviceList = [{
					id: 1,
					name: '居家护理服务',
					category: '医疗护理',
					provider: '康护医疗',
					rating: 4.9,
					price: 150,
					unit: '小时',
					image: '/picture/b3bc07f949264b36811e26cf01c7f50c.jpeg',
					imageError: false
				}];
				FeedbackUtils.showInfo('已加载备用数据');
			} finally {
				this.loading = false;
				this.refreshing = false;
			}
		},

		// 加载热门服务
		async loadHotServices() {
			try {
				// 直接使用离线数据，热门服务不需要网络检测
				const result = OfflineDataManager.getOfflineServices({ pageSize: 3 });
				this.hotServices = result.data;
			} catch (error) {
				console.error('加载热门服务失败:', error);
				// 如果离线数据也失败，使用备用数据
				this.hotServices = [
					{
						id: 1,
						name: '居家护理服务',
						category: '医疗护理',
						provider: '康护医疗',
						rating: 4.9,
						price: 150,
						image: '/picture/b3bc07f949264b36811e26cf01c7f50c.jpeg',
						imageError: false
					},
					{
						id: 2,
						name: '家政清洁服务',
						category: '生活照料',
						provider: '爱心家政',
						rating: 4.7,
						price: 80,
						image: '/picture/659c362e1e774324870c7a9200adc1e7.jpeg',
						imageError: false
					},
					{
						id: 3,
						name: '康复训练服务',
						category: '康复训练',
						provider: '康复中心',
						rating: 4.8,
						price: 200,
						image: '/picture/R-C.jpg',
						imageError: false
					}
				];
			}
		},

		// 搜索输入处理
		onSearchInput() {
			// 实时搜索功能
		},

		// 清空搜索
		clearSearch() {
			FeedbackUtils.lightFeedback();
			this.searchKeyword = '';
		},

		// 选择分类
		selectCategory(category) {
			FeedbackUtils.lightFeedback();
			this.selectedCategory = category.name;
			this.page = 1;
			this.hasMore = true;
			this.loadServices();
		},

		// 查看所有服务
		viewAllServices() {
			FeedbackUtils.lightFeedback();
			uni.navigateTo({
				url: '/pages/service/list'
			});
		},

		// 查看我的预约
		viewMyAppointments() {
			FeedbackUtils.lightFeedback();
			uni.showToast({
				title: '预约功能开发中',
				icon: 'none'
			});
		},

		// 查看服务详情
		viewServiceDetail(service) {
			FeedbackUtils.lightFeedback();
			uni.navigateTo({
				url: `/pages/service/detail?id=${service.id}`
			});
		},

		// 咨询服务
		consultService(service) {
			FeedbackUtils.lightFeedback();
			uni.navigateTo({
				url: `/pages/consultation/create?serviceId=${service.id}`
			});
		},

		// 预约服务
		bookService(service) {
			FeedbackUtils.lightFeedback();
			uni.navigateTo({
				url: `/pages/appointment/create?serviceId=${service.id}`
			});
		},

		// 下拉刷新
		refreshData() {
			FeedbackUtils.lightFeedback();
			this.refreshing = true;
			this.page = 1;
			this.hasMore = true;
			this.loadServices();
			this.loadHotServices();
		},

		// 上拉加载更多
		loadMore() {
			if (this.hasMore && !this.loading) {
				this.page++;
				this.loadServices();
			}
		},

		// 获取分类图标
		getCategoryIcon(category) {
			const iconMap = {
				'医疗护理': 'health-book-line',
				'生活照料': 'home-heart-line',
				'康复训练': 'run-line',
				'心理关怀': 'heart-line',
				'文娱活动': 'music-line',
				'营养配餐': 'restaurant-line'
			};
			return iconMap[category] || 'service-line';
		},

		// 获取分类颜色
		getCategoryColor(category) {
			const colorMap = {
				'医疗护理': '#4caf50',
				'生活照料': '#2196f3',
				'康复训练': '#9c27b0',
				'心理关怀': '#e91e63',
				'文娱活动': '#ff9800',
				'营养配餐': '#795548'
			};
			return colorMap[category] || '#666';
		},

		// 获取分类渐变色
		getCategoryGradient(category) {
			const gradientMap = {
				'医疗护理': 'linear-gradient(135deg, #4caf50 0%, #45a049 100%)',
				'生活照料': 'linear-gradient(135deg, #2196f3 0%, #1976d2 100%)',
				'康复训练': 'linear-gradient(135deg, #9c27b0 0%, #7b1fa2 100%)',
				'心理关怀': 'linear-gradient(135deg, #e91e63 0%, #c2185b 100%)',
				'文娱活动': 'linear-gradient(135deg, #ff9800 0%, #f57c00 100%)',
				'营养配餐': 'linear-gradient(135deg, #795548 0%, #5d4037 100%)'
			};
			return gradientMap[category] || 'linear-gradient(135deg, #666 0%, #555 100%)';
		},

		// 处理图片加载错误
		handleImageError(service) {
			service.imageError = true;
		}
	}
}
</script>

<style scoped>
.container {
	background-color: #f5f5f5;
	min-height: 100vh;
	display: flex;
	flex-direction: column;
}

.page-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 30rpx 40rpx;
	background: white;
	border-bottom: 1rpx solid #f0f0f0;
}

.page-title {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
}

.search-section {
	padding: 20rpx 40rpx;
	background: white;
	border-bottom: 1rpx solid #f0f0f0;
}

.search-container {
	position: relative;
	display: flex;
	align-items: center;
	background: #f8f8f8;
	border-radius: 25rpx;
	height: 80rpx;
}

.search-icon {
	position: absolute;
	left: 20rpx;
	z-index: 1;
}

.search-input {
	flex: 1;
	height: 100%;
	padding: 0 60rpx 0 60rpx;
	border: none;
	border-radius: 25rpx;
	font-size: 28rpx;
	background: transparent;
}

.clear-btn {
	position: absolute;
	right: 20rpx;
	padding: 10rpx;
}

.category-section {
	padding: 30rpx 40rpx;
	background: white;
	margin-bottom: 20rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 20rpx;
}

.category-grid {
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	gap: 20rpx;
}

.category-item {
	margin-bottom: 0;
}

.category-content {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 30rpx 20rpx;
	gap: 15rpx;
}

.category-icon {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
}

.category-name {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
}

.category-count {
	font-size: 22rpx;
	color: #999;
}

.hot-services-section {
	padding: 30rpx 40rpx 30rpx 40rpx;
	background: white;
	margin-bottom: 20rpx;
}

.section-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20rpx;
}

.more-link {
	font-size: 26rpx;
	color: #ff8a00;
}

.hot-services-scroll {
	white-space: nowrap;
}

.hot-services-list {
	display: flex;
	gap: 20rpx;
	padding-bottom: 10rpx;
}

.hot-service-item {
	flex-shrink: 0;
	width: 280rpx;
	margin-bottom: 0;
}

.service-content {
	padding: 20rpx;
}

.service-image-container {
	width: 100%;
	height: 120rpx;
	margin-bottom: 15rpx;
}

.service-image {
	width: 100%;
	height: 100%;
	border-radius: 10rpx;
}

.service-icon-placeholder {
	width: 100%;
	height: 100%;
	border-radius: 10rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.service-info {
	display: flex;
	flex-direction: column;
	gap: 8rpx;
}

.service-name {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
	line-height: 1.3;
	display: -webkit-box;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
	overflow: hidden;
}

.service-provider {
	font-size: 24rpx;
	color: #666;
}

.service-meta {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.meta-item {
	display: flex;
	align-items: center;
	gap: 4rpx;
}

.meta-text {
	font-size: 22rpx;
	color: #666;
}

.recommended-section {
	flex: 1;
	padding: 30rpx 40rpx 0 40rpx;
	background: white;
}

.service-list {
	margin-top: 20rpx;
}

.service-item {
	margin-bottom: 20rpx;
}

.service-detail-content {
	display: flex;
	padding: 30rpx;
	gap: 20rpx;
}

.service-detail-content .service-image-container {
	flex-shrink: 0;
	width: 160rpx;
	height: 120rpx;
	margin-bottom: 0;
}

.service-detail-content .service-info {
	flex: 1;
	gap: 12rpx;
}

.service-header {
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
	gap: 15rpx;
}

.service-detail-content .service-name {
	flex: 1;
	font-size: 30rpx;
	margin-bottom: 0;
}

.service-category {
	flex-shrink: 0;
	padding: 6rpx 12rpx;
	border-radius: 12rpx;
}

.category-text {
	font-size: 20rpx;
	color: white;
	font-weight: 500;
}

.service-description {
	font-size: 26rpx;
	color: #666;
	line-height: 1.5;
	display: -webkit-box;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
	overflow: hidden;
}

.service-provider-info {
	display: flex;
	align-items: center;
	gap: 8rpx;
}

.provider-text {
	font-size: 24rpx;
	color: #666;
}

.service-features {
	display: flex;
	gap: 20rpx;
}

.feature-item {
	display: flex;
	align-items: center;
	gap: 6rpx;
}

.feature-text {
	font-size: 24rpx;
	color: #666;
}

.service-tags {
	display: flex;
	gap: 10rpx;
	flex-wrap: wrap;
}

.tag-item {
	padding: 4rpx 10rpx;
	background: rgba(255, 138, 0, 0.1);
	border-radius: 10rpx;
}

.tag-label {
	font-size: 20rpx;
	color: #ff8a00;
}

.service-actions {
	display: flex;
	flex-direction: column;
	justify-content: space-between;
	align-items: flex-end;
	gap: 15rpx;
}

.price-section {
	text-align: right;
}

.price-label {
	font-size: 22rpx;
	color: #999;
	display: block;
}

.price-value {
	font-size: 28rpx;
	font-weight: bold;
	color: #ff8a00;
	display: block;
}

.action-buttons {
	display: flex;
	flex-direction: column;
	gap: 10rpx;
}

.load-more {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 40rpx;
	gap: 20rpx;
}

.loading-spinner {
	width: 40rpx;
	height: 40rpx;
	border: 3rpx solid rgba(255, 138, 0, 0.2);
	border-top: 3rpx solid #ff8a00;
	border-radius: 50%;
	animation: loading 1s linear infinite;
}

.loading-text {
	font-size: 26rpx;
	color: #999;
}

.no-more {
	text-align: center;
	padding: 40rpx;
	color: #999;
	font-size: 26rpx;
}

.empty {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 100rpx 40rpx;
	gap: 20rpx;
}

.empty-text {
	font-size: 28rpx;
	color: #999;
}

.empty-tip {
	font-size: 24rpx;
	color: #ccc;
}

@keyframes loading {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}
</style>
