<template>
	<view class="container">
		<!-- 自定义导航栏 -->
		<view class="custom-navbar">
			<view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
			<view class="navbar-content">
				<view class="navbar-left" @click="goBack">
					<Icon name="arrow-left-line" size="36rpx" color="#333"></Icon>
					<text class="back-text">返回</text>
				</view>
				<view class="navbar-center">
					<text class="navbar-title">语言设置</text>
				</view>
				<view class="navbar-right"></view>
			</view>
		</view>

		<!-- 语言设置概览 -->
		<view class="language-overview">
			<view class="overview-header">
				<Icon name="global-line" size="60rpx" color="#ff8a00"></Icon>
				<text class="overview-title">语言设置</text>
				<text class="overview-desc">选择您偏好的语言和地区设置</text>
			</view>
		</view>

		<!-- 应用语言 -->
		<view class="language-section">
			<view class="section-header">
				<Icon name="translate-2" size="32rpx" color="#ff8a00"></Icon>
				<text class="section-title">应用语言</text>
			</view>
			
			<view class="language-list">
				<view class="language-item" v-for="(lang, index) in languages" :key="index" @click="selectLanguage(index)">
					<view class="language-info">
						<text class="language-name">{{lang.name}}</text>
						<text class="language-native">{{lang.nativeName}}</text>
					</view>
					<view class="language-status">
						<Icon name="check-line" size="24rpx" color="#4caf50" v-if="selectedLanguageIndex === index"></Icon>
					</view>
				</view>
			</view>
		</view>

		<!-- 地区设置 -->
		<view class="language-section">
			<view class="section-header">
				<Icon name="map-pin-line" size="32rpx" color="#ff8a00"></Icon>
				<text class="section-title">地区设置</text>
			</view>
			
			<view class="setting-item">
				<view class="item-info">
					<text class="item-title">国家/地区</text>
					<text class="item-desc">影响日期、时间和货币格式</text>
				</view>
				<picker :value="regionIndex" :range="regions" range-key="name" @change="onRegionChange">
					<view class="picker-input">
						<text>{{regions[regionIndex].name}}</text>
						<Icon name="arrow-down-s-line" size="20rpx" color="#999"></Icon>
					</view>
				</picker>
			</view>
			
			<view class="setting-item">
				<view class="item-info">
					<text class="item-title">时区</text>
					<text class="item-desc">{{selectedTimezone.description}}</text>
				</view>
				<picker :value="timezoneIndex" :range="timezones" range-key="name" @change="onTimezoneChange">
					<view class="picker-input">
						<text>{{timezones[timezoneIndex].name}}</text>
						<Icon name="arrow-down-s-line" size="20rpx" color="#999"></Icon>
					</view>
				</picker>
			</view>
		</view>

		<!-- 格式设置 -->
		<view class="language-section">
			<view class="section-header">
				<Icon name="calendar-line" size="32rpx" color="#ff8a00"></Icon>
				<text class="section-title">格式设置</text>
			</view>
			
			<view class="setting-item">
				<view class="item-info">
					<text class="item-title">日期格式</text>
					<text class="item-desc">{{dateFormatPreview}}</text>
				</view>
				<picker :value="dateFormatIndex" :range="dateFormats" range-key="name" @change="onDateFormatChange">
					<view class="picker-input">
						<text>{{dateFormats[dateFormatIndex].name}}</text>
						<Icon name="arrow-down-s-line" size="20rpx" color="#999"></Icon>
					</view>
				</picker>
			</view>
			
			<view class="setting-item">
				<view class="item-info">
					<text class="item-title">时间格式</text>
					<text class="item-desc">{{timeFormatPreview}}</text>
				</view>
				<picker :value="timeFormatIndex" :range="timeFormats" range-key="name" @change="onTimeFormatChange">
					<view class="picker-input">
						<text>{{timeFormats[timeFormatIndex].name}}</text>
						<Icon name="arrow-down-s-line" size="20rpx" color="#999"></Icon>
					</view>
				</picker>
			</view>
			
			<view class="setting-item">
				<view class="item-info">
					<text class="item-title">货币格式</text>
					<text class="item-desc">{{currencyFormatPreview}}</text>
				</view>
				<picker :value="currencyIndex" :range="currencies" range-key="name" @change="onCurrencyChange">
					<view class="picker-input">
						<text>{{currencies[currencyIndex].name}}</text>
						<Icon name="arrow-down-s-line" size="20rpx" color="#999"></Icon>
					</view>
				</picker>
			</view>
		</view>

		<!-- 输入法设置 -->
		<view class="language-section">
			<view class="section-header">
				<Icon name="keyboard-line" size="32rpx" color="#ff8a00"></Icon>
				<text class="section-title">输入法设置</text>
			</view>
			
			<view class="setting-item">
				<view class="item-info">
					<text class="item-title">智能纠错</text>
					<text class="item-desc">自动纠正输入错误</text>
				</view>
				<switch :checked="inputSettings.autoCorrect" @change="onAutoCorrectChange" color="#ff8a00" />
			</view>
			
			<view class="setting-item">
				<view class="item-info">
					<text class="item-title">智能预测</text>
					<text class="item-desc">显示输入建议</text>
				</view>
				<switch :checked="inputSettings.predictive" @change="onPredictiveChange" color="#ff8a00" />
			</view>
			
			<view class="setting-item">
				<view class="item-info">
					<text class="item-title">语音输入</text>
					<text class="item-desc">启用语音转文字功能</text>
				</view>
				<switch :checked="inputSettings.voiceInput" @change="onVoiceInputChange" color="#ff8a00" />
			</view>
		</view>

		<!-- 翻译设置 -->
		<view class="language-section">
			<view class="section-header">
				<Icon name="translate" size="32rpx" color="#ff8a00"></Icon>
				<text class="section-title">翻译设置</text>
			</view>
			
			<view class="setting-item">
				<view class="item-info">
					<text class="item-title">自动翻译</text>
					<text class="item-desc">自动翻译外语内容</text>
				</view>
				<switch :checked="translationSettings.autoTranslate" @change="onAutoTranslateChange" color="#ff8a00" />
			</view>
			
			<view class="setting-item">
				<view class="item-info">
					<text class="item-title">翻译服务</text>
					<text class="item-desc">选择翻译服务提供商</text>
				</view>
				<picker :value="translationServiceIndex" :range="translationServices" @change="onTranslationServiceChange">
					<view class="picker-input">
						<text>{{translationServices[translationServiceIndex]}}</text>
						<Icon name="arrow-down-s-line" size="20rpx" color="#999"></Icon>
					</view>
				</picker>
			</view>
		</view>

		<!-- 应用重启提示 -->
		<view class="restart-notice" v-if="needRestart">
			<view class="notice-content">
				<Icon name="information-line" size="32rpx" color="#ff9800"></Icon>
				<text class="notice-text">语言更改需要重启应用才能生效</text>
				<button class="restart-btn" @click="restartApp">重启应用</button>
			</view>
		</view>
	</view>
</template>

<script>
import Icon from '@/components/Icon/Icon.vue'

export default {
	components: {
		Icon
	},
	data() {
		return {
			statusBarHeight: 0, // 状态栏高度
			selectedLanguageIndex: 0,
			needRestart: false,
			languages: [
				{ code: 'zh-CN', name: '简体中文', nativeName: '简体中文' },
				{ code: 'zh-TW', name: '繁体中文', nativeName: '繁體中文' },
				{ code: 'en-US', name: '英语', nativeName: 'English' },
				{ code: 'ja-JP', name: '日语', nativeName: '日本語' },
				{ code: 'ko-KR', name: '韩语', nativeName: '한국어' },
				{ code: 'es-ES', name: '西班牙语', nativeName: 'Español' },
				{ code: 'fr-FR', name: '法语', nativeName: 'Français' },
				{ code: 'de-DE', name: '德语', nativeName: 'Deutsch' }
			],
			regionIndex: 0,
			regions: [
				{ code: 'CN', name: '中国大陆' },
				{ code: 'TW', name: '中国台湾' },
				{ code: 'HK', name: '中国香港' },
				{ code: 'US', name: '美国' },
				{ code: 'JP', name: '日本' },
				{ code: 'KR', name: '韩国' }
			],
			timezoneIndex: 0,
			timezones: [
				{ code: 'Asia/Shanghai', name: 'GMT+8 北京时间', description: '中国标准时间' },
				{ code: 'Asia/Taipei', name: 'GMT+8 台北时间', description: '台湾标准时间' },
				{ code: 'Asia/Hong_Kong', name: 'GMT+8 香港时间', description: '香港标准时间' },
				{ code: 'America/New_York', name: 'GMT-5 纽约时间', description: '美国东部时间' },
				{ code: 'Asia/Tokyo', name: 'GMT+9 东京时间', description: '日本标准时间' },
				{ code: 'Asia/Seoul', name: 'GMT+9 首尔时间', description: '韩国标准时间' }
			],
			dateFormatIndex: 0,
			dateFormats: [
				{ code: 'YYYY-MM-DD', name: '2024-01-15', format: 'YYYY-MM-DD' },
				{ code: 'YYYY/MM/DD', name: '2024/01/15', format: 'YYYY/MM/DD' },
				{ code: 'DD/MM/YYYY', name: '15/01/2024', format: 'DD/MM/YYYY' },
				{ code: 'MM/DD/YYYY', name: '01/15/2024', format: 'MM/DD/YYYY' },
				{ code: 'DD-MM-YYYY', name: '15-01-2024', format: 'DD-MM-YYYY' }
			],
			timeFormatIndex: 0,
			timeFormats: [
				{ code: '24h', name: '24小时制', format: 'HH:mm' },
				{ code: '12h', name: '12小时制', format: 'hh:mm A' }
			],
			currencyIndex: 0,
			currencies: [
				{ code: 'CNY', name: '人民币 (¥)', symbol: '¥' },
				{ code: 'USD', name: '美元 ($)', symbol: '$' },
				{ code: 'EUR', name: '欧元 (€)', symbol: '€' },
				{ code: 'JPY', name: '日元 (¥)', symbol: '¥' },
				{ code: 'KRW', name: '韩元 (₩)', symbol: '₩' }
			],
			inputSettings: {
				autoCorrect: true,
				predictive: true,
				voiceInput: false
			},
			translationSettings: {
				autoTranslate: false
			},
			translationServiceIndex: 0,
			translationServices: ['百度翻译', '谷歌翻译', '有道翻译', '腾讯翻译']
		}
	},
	computed: {
		selectedTimezone() {
			return this.timezones[this.timezoneIndex];
		},
		dateFormatPreview() {
			const now = new Date();
			const format = this.dateFormats[this.dateFormatIndex];
			return `示例：${format.name}`;
		},
		timeFormatPreview() {
			const format = this.timeFormats[this.timeFormatIndex];
			return format.code === '24h' ? '示例：14:30' : '示例：2:30 PM';
		},
		currencyFormatPreview() {
			const currency = this.currencies[this.currencyIndex];
			return `示例：${currency.symbol}123.45`;
		}
	},
	methods: {
		selectLanguage(index) {
			if (this.selectedLanguageIndex !== index) {
				this.selectedLanguageIndex = index;
				this.needRestart = true;
				this.saveSettings();
				
				uni.showToast({
					title: '语言已更改',
					icon: 'success'
				});
			}
		},
		onRegionChange(e) {
			this.regionIndex = e.detail.value;
			this.saveSettings();
		},
		onTimezoneChange(e) {
			this.timezoneIndex = e.detail.value;
			this.saveSettings();
		},
		onDateFormatChange(e) {
			this.dateFormatIndex = e.detail.value;
			this.saveSettings();
		},
		onTimeFormatChange(e) {
			this.timeFormatIndex = e.detail.value;
			this.saveSettings();
		},
		onCurrencyChange(e) {
			this.currencyIndex = e.detail.value;
			this.saveSettings();
		},
		onAutoCorrectChange(e) {
			this.inputSettings.autoCorrect = e.detail.value;
			this.saveSettings();
		},
		onPredictiveChange(e) {
			this.inputSettings.predictive = e.detail.value;
			this.saveSettings();
		},
		onVoiceInputChange(e) {
			this.inputSettings.voiceInput = e.detail.value;
			this.saveSettings();
		},
		onAutoTranslateChange(e) {
			this.translationSettings.autoTranslate = e.detail.value;
			this.saveSettings();
		},
		onTranslationServiceChange(e) {
			this.translationServiceIndex = e.detail.value;
			this.saveSettings();
		},
		restartApp() {
			uni.showModal({
				title: '重启应用',
				content: '应用将重新启动以应用语言设置',
				showCancel: false,
				success: () => {
					// 在实际应用中，这里应该调用重启应用的方法
					uni.reLaunch({
						url: '/pages/home/<USER>'
					});
				}
			});
		},
		saveSettings() {
			const settings = {
				selectedLanguageIndex: this.selectedLanguageIndex,
				regionIndex: this.regionIndex,
				timezoneIndex: this.timezoneIndex,
				dateFormatIndex: this.dateFormatIndex,
				timeFormatIndex: this.timeFormatIndex,
				currencyIndex: this.currencyIndex,
				inputSettings: this.inputSettings,
				translationSettings: this.translationSettings,
				translationServiceIndex: this.translationServiceIndex
			};

			uni.setStorageSync('languageSettings', settings);
		},

		// 返回上一页
		goBack() {
			uni.navigateBack({
				fail: () => {
					uni.switchTab({
						url: '/pages/profile/profile'
					});
				}
			});
		}
	},
	onLoad() {
		// 获取状态栏高度
		const systemInfo = uni.getSystemInfoSync();
		this.statusBarHeight = systemInfo.statusBarHeight || 44;

		const savedSettings = uni.getStorageSync('languageSettings');
		if (savedSettings) {
			Object.assign(this, savedSettings);
		}
	}
}
</script>

<style scoped>
.container {
	background: #f8f9fa;
	min-height: 100vh;
}

/* 导航栏样式 */
.custom-navbar {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 1000;
	background: rgba(255, 255, 255, 0.95);
	backdrop-filter: blur(20rpx);
	-webkit-backdrop-filter: blur(20rpx);
	border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);
}

.navbar-content {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 20rpx 32rpx;
	min-height: 88rpx;
}

.navbar-left {
	display: flex;
	align-items: center;
	gap: 12rpx;
	padding: 8rpx 16rpx 8rpx 0;
	border-radius: 20rpx;
	transition: all 0.2s ease;
}

.navbar-left:active {
	background-color: rgba(0, 0, 0, 0.04);
	transform: scale(0.96);
}

.back-text {
	font-size: 32rpx;
	color: #333;
	font-weight: 500;
}

.navbar-center {
	position: absolute;
	left: 50%;
	transform: translateX(-50%);
}

.navbar-title {
	font-size: 34rpx;
	font-weight: 600;
	color: #333;
}

.navbar-right {
	display: flex;
	align-items: center;
}

.language-overview {
	background: linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%);
	padding: 40rpx;
	margin-top: 220rpx; /* 为导航栏留出空间 */
	text-align: center;
	color: white;
}

.overview-title {
	font-size: 32rpx;
	font-weight: bold;
	display: block;
	margin: 20rpx 0 15rpx;
}

.overview-desc {
	font-size: 24rpx;
	opacity: 0.9;
	line-height: 1.5;
	display: block;
}

.language-section {
	background: white;
	margin: 20rpx;
	border-radius: 30rpx;
	padding: 40rpx;
}

.section-header {
	display: flex;
	align-items: center;
	gap: 15rpx;
	margin-bottom: 30rpx;
}

.section-title {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
}

.language-list {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

.language-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 25rpx;
	background: #f8f9fa;
	border-radius: 20rpx;
	border: 2rpx solid transparent;
}

.language-item:active {
	background: rgba(255, 138, 0, 0.1);
	border-color: #ff8a00;
}

.language-info {
	flex: 1;
}

.language-name {
	font-size: 28rpx;
	color: #333;
	font-weight: bold;
	display: block;
	margin-bottom: 5rpx;
}

.language-native {
	font-size: 24rpx;
	color: #666;
	display: block;
}

.language-status {
	width: 40rpx;
	text-align: center;
}

.setting-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 25rpx 0;
	border-bottom: 1rpx solid #f0f0f0;
}

.setting-item:last-child {
	border-bottom: none;
}

.item-info {
	flex: 1;
	margin-right: 20rpx;
}

.item-title {
	font-size: 28rpx;
	color: #333;
	font-weight: bold;
	display: block;
	margin-bottom: 8rpx;
}

.item-desc {
	font-size: 24rpx;
	color: #666;
	line-height: 1.4;
	display: block;
}

.picker-input {
	display: flex;
	align-items: center;
	gap: 10rpx;
	padding: 15rpx 20rpx;
	background: #f8f9fa;
	border-radius: 15rpx;
	font-size: 26rpx;
	color: #333;
}

.restart-notice {
	background: white;
	margin: 20rpx;
	border-radius: 30rpx;
	padding: 40rpx;
	border: 2rpx solid #ff9800;
}

.notice-content {
	display: flex;
	align-items: center;
	gap: 15rpx;
}

.notice-text {
	flex: 1;
	font-size: 26rpx;
	color: #ff9800;
}

.restart-btn {
	background: #ff9800;
	color: white;
	border: none;
	border-radius: 20rpx;
	padding: 15rpx 30rpx;
	font-size: 24rpx;
}
</style>
