<template>
	<view class="container">
		<!-- 头部横幅 -->
		<view class="header-banner">
			<view class="banner-content">
				<view class="banner-text">
					<text class="banner-title">文娱活动</text>
					<text class="banner-subtitle">丰富多彩的老年文娱生活</text>
				</view>
				<view class="banner-icon">
					<Icon name="music-2-line" size="80rpx" color="white"></Icon>
				</view>
			</view>
		</view>

		<!-- 活动分类 -->
		<view class="section">
			<view class="section-header">
				<text class="section-title">活动分类</text>
				<text class="section-subtitle">选择您感兴趣的活动类型</text>
			</view>
			<view class="category-scroll">
				<scroll-view scroll-x="true" class="category-list">
					<view 
						class="category-item" 
						:class="{ active: activeCategory === item.key }"
						v-for="(item, index) in categoryList" 
						:key="index"
						@click="selectCategory(item.key)"
					>
						<Icon :name="item.icon" size="40rpx" :color="activeCategory === item.key ? 'white' : '#666'"></Icon>
						<text class="category-text">{{item.name}}</text>
					</view>
				</scroll-view>
			</view>
		</view>

		<!-- 推荐活动 -->
		<view class="section">
			<view class="section-header">
				<text class="section-title">推荐活动</text>
				<text class="section-subtitle">精选优质文娱活动</text>
			</view>
			<view class="activity-list">
				<view class="activity-item" v-for="(item, index) in recommendedActivities" :key="index" @click="viewActivity(item)">
					<view class="activity-image">
						<Icon :name="item.icon" size="60rpx" :color="item.iconColor"></Icon>
					</view>
					<view class="activity-content">
						<text class="activity-title">{{item.title}}</text>
						<text class="activity-desc">{{item.description}}</text>
						<view class="activity-info">
							<view class="info-item">
								<Icon name="time-line" size="20rpx" color="#999"></Icon>
								<text class="info-text">{{item.time}}</text>
							</view>
							<view class="info-item">
								<Icon name="map-pin-line" size="20rpx" color="#999"></Icon>
								<text class="info-text">{{item.location}}</text>
							</view>
							<view class="info-item">
								<Icon name="user-line" size="20rpx" color="#999"></Icon>
								<text class="info-text">{{item.participants}}/{{item.maxParticipants}}人</text>
							</view>
						</view>
						<view class="activity-tags">
							<text class="activity-tag" v-for="tag in item.tags" :key="tag">{{tag}}</text>
						</view>
					</view>
					<view class="activity-action">
						<button class="join-btn" @click.stop="joinActivity(item)" :disabled="item.participants >= item.maxParticipants">
							{{item.participants >= item.maxParticipants ? '已满' : '报名'}}
						</button>
					</view>
				</view>
			</view>
		</view>

		<!-- 我的活动 -->
		<view class="section" v-if="myActivities.length > 0">
			<view class="section-header">
				<text class="section-title">我的活动</text>
				<text class="section-subtitle">已报名的活动</text>
			</view>
			<view class="my-activity-list">
				<view class="my-activity-item" v-for="(item, index) in myActivities" :key="index">
					<view class="activity-header">
						<text class="activity-name">{{item.title}}</text>
						<view class="activity-status" :class="item.status">{{item.statusText}}</view>
					</view>
					<view class="activity-details">
						<text class="activity-time">活动时间：{{item.time}}</text>
						<text class="activity-location">活动地点：{{item.location}}</text>
						<text class="activity-organizer">主办方：{{item.organizer}}</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 活动日历 -->
		<view class="section">
			<view class="section-header">
				<text class="section-title">活动日历</text>
				<text class="section-subtitle">本月活动安排</text>
			</view>
			<view class="calendar-container">
				<view class="calendar-header">
					<text class="calendar-month">{{currentMonth}}</text>
				</view>
				<view class="calendar-grid">
					<view class="calendar-day header" v-for="day in weekDays" :key="day">{{day}}</view>
					<view 
						class="calendar-day" 
						:class="{ 'has-activity': day.hasActivity, 'today': day.isToday }"
						v-for="day in calendarDays" 
						:key="day.date"
						@click="viewDayActivities(day)"
					>
						<text class="day-number">{{day.day}}</text>
						<view class="activity-dot" v-if="day.hasActivity"></view>
					</view>
				</view>
			</view>
		</view>

		<!-- 活动管理 -->
		<view class="section">
			<view class="section-header">
				<text class="section-title">活动管理</text>
				<text class="section-subtitle">管理您的活动参与</text>
			</view>
			<view class="management-list">
				<view class="management-item" @click="manageMyActivities">
					<view class="management-icon">
						<Icon name="calendar-check-line" size="32rpx" color="#4caf50"></Icon>
					</view>
					<view class="management-content">
						<text class="management-title">我的活动</text>
						<text class="management-desc">查看和管理已报名的活动</text>
					</view>
					<Icon name="arrow-right-s-line" size="20rpx" color="#999"></Icon>
				</view>

				<view class="management-item" @click="activityHistory">
					<view class="management-icon">
						<Icon name="history-line" size="32rpx" color="#2196f3"></Icon>
					</view>
					<view class="management-content">
						<text class="management-title">活动历史</text>
						<text class="management-desc">查看参与过的活动记录</text>
					</view>
					<Icon name="arrow-right-s-line" size="20rpx" color="#999"></Icon>
				</view>

				<view class="management-item" @click="activityPreferences">
					<view class="management-icon">
						<Icon name="heart-line" size="32rpx" color="#e91e63"></Icon>
					</view>
					<view class="management-content">
						<text class="management-title">活动偏好</text>
						<text class="management-desc">设置您喜欢的活动类型</text>
					</view>
					<Icon name="arrow-right-s-line" size="20rpx" color="#999"></Icon>
				</view>
			</view>
		</view>

		<!-- 底部操作 -->
		<view class="bottom-actions">
			<button class="action-btn secondary" @click="createActivity">
				<Icon name="add-line" size="32rpx" color="#ff8a00"></Icon>
				<text>发起活动</text>
			</button>
			<button class="action-btn primary" @click="viewAllActivities">
				<Icon name="calendar-line" size="32rpx" color="white"></Icon>
				<text>查看全部</text>
			</button>
		</view>
	</view>
</template>

<script>
import Icon from '@/components/Icon/Icon.vue'

export default {
	components: {
		Icon
	},
	data() {
		return {
			activeCategory: 'all',
			currentMonth: '2024年1月',
			categoryList: [
				{ key: 'all', name: '全部', icon: 'apps-line' },
				{ key: 'music', name: '音乐', icon: 'music-2-line' },
				{ key: 'dance', name: '舞蹈', icon: 'user-star-line' },
				{ key: 'sports', name: '运动', icon: 'run-line' },
				{ key: 'art', name: '书画', icon: 'brush-line' },
				{ key: 'chess', name: '棋牌', icon: 'game-line' },
				{ key: 'lecture', name: '讲座', icon: 'book-open-line' }
			],
			recommendedActivities: [
				{
					id: 1,
					title: '太极拳晨练',
					description: '专业教练指导，适合初学者',
					time: '每周一三五 7:00-8:00',
					location: '社区广场',
					participants: 15,
					maxParticipants: 20,
					icon: 'run-line',
					iconColor: '#4caf50',
					tags: ['运动', '健身', '免费']
				},
				{
					id: 2,
					title: '书法交流会',
					description: '书法爱好者交流学习',
					time: '每周二四 14:00-16:00',
					location: '文化活动室',
					participants: 8,
					maxParticipants: 12,
					icon: 'brush-line',
					iconColor: '#9c27b0',
					tags: ['书画', '文化', '交流']
				},
				{
					id: 3,
					title: '健康养生讲座',
					description: '专家讲解老年人健康知识',
					time: '1月20日 9:00-11:00',
					location: '社区会议室',
					participants: 25,
					maxParticipants: 30,
					icon: 'book-open-line',
					iconColor: '#2196f3',
					tags: ['讲座', '健康', '免费']
				}
			],
			myActivities: [
				// 示例数据，实际应从后端获取
			],
			weekDays: ['日', '一', '二', '三', '四', '五', '六'],
			calendarDays: [
				// 生成日历数据
			],

		}
	},
	onLoad() {
		this.generateCalendar();
	},
	methods: {
		selectCategory(category) {
			this.activeCategory = category;
			this.filterActivities();
		},
		filterActivities() {
			// 筛选活动逻辑
			console.log('筛选分类:', this.activeCategory);
		},
		viewActivity(activity) {
			uni.navigateTo({
				url: `/pages/entertainment/detail?id=${activity.id}`
			});
		},
		joinActivity(activity) {
			if (activity.participants >= activity.maxParticipants) {
				uni.showToast({
					title: '活动人数已满',
					icon: 'none'
				});
				return;
			}
			
			uni.navigateTo({
				url: `/pages/entertainment/join?id=${activity.id}`
			});
		},
		viewDayActivities(day) {
			if (day.hasActivity) {
				uni.navigateTo({
					url: `/pages/entertainment/day-activities?date=${day.date}`
				});
			}
		},
		createActivity() {
			uni.navigateTo({
				url: '/pages/entertainment/create'
			});
		},
		viewAllActivities() {
			uni.navigateTo({
				url: '/pages/entertainment/all'
			});
		},
		// 活动管理功能
		manageMyActivities() {
			uni.navigateTo({
				url: '/pages/entertainment/my-activities'
			});
		},
		activityHistory() {
			uni.navigateTo({
				url: '/pages/entertainment/history'
			});
		},
		activityPreferences() {
			uni.navigateTo({
				url: '/pages/entertainment/preferences'
			});
		},
		generateCalendar() {
			// 生成日历数据的逻辑
			const today = new Date();
			const year = today.getFullYear();
			const month = today.getMonth();
			
			// 简化的日历生成逻辑
			this.calendarDays = [];
			for (let i = 1; i <= 31; i++) {
				this.calendarDays.push({
					day: i,
					date: `${year}-${month + 1}-${i}`,
					hasActivity: Math.random() > 0.7, // 随机生成有活动的日期
					isToday: i === today.getDate()
				});
			}
		}
	}
}
</script>

<style scoped>
.container {
	background: linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%);
	min-height: 100vh;
	padding-bottom: 200rpx;
}

.header-banner {
	padding: 40rpx;
	margin-bottom: 40rpx;
}

.banner-content {
	background: rgba(255, 255, 255, 0.15);
	border-radius: 30rpx;
	padding: 40rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.banner-text {
	flex: 1;
}

.banner-title {
	font-size: 48rpx;
	font-weight: bold;
	color: white;
	display: block;
	margin-bottom: 15rpx;
}

.banner-subtitle {
	font-size: 28rpx;
	color: rgba(255, 255, 255, 0.9);
	display: block;
}

.banner-icon {
	margin-left: 30rpx;
}

.section {
	margin-bottom: 40rpx;
}

.section-header {
	padding: 0 40rpx 30rpx;
}

.section-title {
	font-size: 36rpx;
	font-weight: bold;
	color: white;
	display: block;
	margin-bottom: 10rpx;
}

.section-subtitle {
	font-size: 26rpx;
	color: rgba(255, 255, 255, 0.8);
	display: block;
}

.category-scroll {
	padding: 0 40rpx;
}

.category-list {
	white-space: nowrap;
}

.category-item {
	display: inline-flex;
	flex-direction: column;
	align-items: center;
	margin-right: 40rpx;
	padding: 20rpx;
	border-radius: 20rpx;
	background: white;
	min-width: 120rpx;
}

.category-item.active {
	background: linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%);
}

.category-text {
	font-size: 24rpx;
	color: #666;
	margin-top: 10rpx;
}

.category-item.active .category-text {
	color: white;
}

.activity-list, .my-activity-list, .management-list {
	padding: 0 40rpx;
}

.management-item {
	background: white;
	border-radius: 30rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	display: flex;
	align-items: center;
	gap: 20rpx;
	box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.management-icon {
	width: 60rpx;
	height: 60rpx;
	background: rgba(255, 138, 0, 0.1);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
}

.management-content {
	flex: 1;
}

.management-title {
	font-size: 30rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 8rpx;
}

.management-desc {
	font-size: 26rpx;
	color: #666;
	display: block;
}

.activity-item, .my-activity-item {
	background: white;
	border-radius: 30rpx;
	padding: 30rpx;
	margin-bottom: 30rpx;
	display: flex;
	gap: 20rpx;
	box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.activity-image {
	width: 100rpx;
	height: 100rpx;
	background: #f8f9fa;
	border-radius: 20rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.activity-content {
	flex: 1;
}

.activity-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 8rpx;
}

.activity-desc {
	font-size: 26rpx;
	color: #666;
	display: block;
	margin-bottom: 15rpx;
}

.activity-info {
	display: flex;
	flex-direction: column;
	gap: 8rpx;
	margin-bottom: 15rpx;
}

.info-item {
	display: flex;
	align-items: center;
	gap: 8rpx;
}

.info-text {
	font-size: 24rpx;
	color: #666;
}

.activity-tags {
	display: flex;
	gap: 10rpx;
}

.activity-tag {
	background: rgba(255, 138, 0, 0.1);
	color: #ff8a00;
	font-size: 22rpx;
	padding: 5rpx 15rpx;
	border-radius: 15rpx;
}

.join-btn {
	background: linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%);
	color: white;
	border: none;
	padding: 15rpx 30rpx;
	border-radius: 20rpx;
	font-size: 26rpx;
	font-weight: 500;
	height: fit-content;
}

.join-btn:disabled {
	background: #ccc;
	color: #999;
}

.my-activity-item {
	flex-direction: column;
	align-items: stretch;
}

.activity-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 15rpx;
}

.activity-name {
	font-size: 30rpx;
	font-weight: bold;
	color: #333;
}

.activity-status {
	padding: 8rpx 20rpx;
	border-radius: 20rpx;
	font-size: 24rpx;
}

.activity-status.upcoming {
	background: rgba(255, 152, 0, 0.1);
	color: #ff9800;
}

.activity-status.ongoing {
	background: rgba(76, 175, 80, 0.1);
	color: #4caf50;
}

.activity-details {
	display: flex;
	flex-direction: column;
	gap: 8rpx;
}

.activity-time, .activity-location, .activity-organizer {
	font-size: 26rpx;
	color: #666;
}

.calendar-container {
	background: white;
	border-radius: 30rpx;
	padding: 30rpx;
	margin: 0 40rpx;
	box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.calendar-header {
	text-align: center;
	margin-bottom: 30rpx;
}

.calendar-month {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.calendar-grid {
	display: grid;
	grid-template-columns: repeat(7, 1fr);
	gap: 10rpx;
}

.calendar-day {
	aspect-ratio: 1;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	border-radius: 10rpx;
	position: relative;
}

.calendar-day.header {
	font-size: 24rpx;
	color: #666;
	font-weight: bold;
}

.calendar-day.today {
	background: rgba(255, 138, 0, 0.1);
	color: #ff8a00;
}

.calendar-day.has-activity {
	background: rgba(76, 175, 80, 0.1);
}

.day-number {
	font-size: 26rpx;
	color: #333;
}

.activity-dot {
	width: 8rpx;
	height: 8rpx;
	background: #4caf50;
	border-radius: 50%;
	position: absolute;
	bottom: 5rpx;
}

.guide-item {
	background: white;
	border-radius: 30rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	display: flex;
	align-items: flex-start;
	gap: 20rpx;
	box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.guide-icon {
	width: 60rpx;
	height: 60rpx;
	background: rgba(255, 152, 0, 0.1);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-top: 5rpx;
}

.guide-text {
	flex: 1;
	font-size: 28rpx;
	color: #333;
	line-height: 1.6;
}

.bottom-actions {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background: white;
	padding: 30rpx 40rpx;
	display: flex;
	gap: 20rpx;
	box-shadow: 0 -4rpx 20rpx rgba(0,0,0,0.1);
}

.action-btn {
	flex: 1;
	height: 100rpx;
	border-radius: 20rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 15rpx;
	font-size: 32rpx;
	font-weight: 500;
}

.action-btn.primary {
	background: linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%);
	color: white;
	border: none;
}

.action-btn.secondary {
	background: white;
	color: #ff8a00;
	border: 2rpx solid #ff8a00;
}
</style>
