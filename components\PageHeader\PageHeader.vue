<template>
	<view class="page-header" :class="{ 'elderly-mode': elderlyMode }" :style="headerStyle">
		<!-- 状态栏占位 -->
		<view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
		
		<!-- 导航栏内容 -->
		<view class="navbar" :style="{ height: navBarHeight + 'px' }">
			<!-- 左侧区域 -->
			<view class="navbar-left" @click="handleLeftClick">
				<Icon
					v-if="showBack"
					name="arrow-left-line"
					:size="iconSize"
					:color="iconColor"
				/>
				<text v-if="leftText" class="navbar-text">{{ leftText }}</text>
			</view>
			
			<!-- 中间标题区域 -->
			<view class="navbar-center">
				<text class="navbar-title" :style="titleStyle">{{ title }}</text>
				<text v-if="subtitle" class="navbar-subtitle" :style="subtitleStyle">{{ subtitle }}</text>
			</view>
			
			<!-- 右侧区域 -->
			<view class="navbar-right" @click="handleRightClick">
				<text v-if="rightText" class="navbar-text">{{ rightText }}</text>
				<Icon
					v-if="rightIcon"
					:name="rightIcon"
					:size="iconSize"
					:color="iconColor"
				/>
			</view>
		</view>
		
		<!-- 搜索栏（可选） -->
		<view v-if="showSearch" class="search-bar">
			<view class="search-input-wrapper">
				<Icon name="search-line" size="32rpx" color="#999" />
				<input 
					class="search-input"
					type="text"
					:placeholder="searchPlaceholder"
					:value="searchValue"
					@input="handleSearchInput"
					@confirm="handleSearchConfirm"
				/>
				<Icon
					v-if="searchValue"
					name="close-line"
					size="32rpx"
					color="#999"
					@click="clearSearch"
				/>
			</view>
		</view>
		
		<!-- 标签栏（可选） -->
		<view v-if="tabs && tabs.length > 0" class="tab-bar">
			<scroll-view scroll-x="true" class="tab-scroll">
				<view class="tab-list">
					<view 
						v-for="(tab, index) in tabs" 
						:key="index"
						class="tab-item"
						:class="{ 'tab-active': activeTab === index }"
						@click="handleTabClick(index)"
					>
						<text class="tab-text">{{ tab.name }}</text>
						<view v-if="tab.badge" class="tab-badge">{{ tab.badge }}</view>
					</view>
				</view>
			</scroll-view>
		</view>
	</view>
</template>

<script>
import Icon from '@/components/Icon/Icon.vue'

export default {
	name: 'PageHeader',
	components: {
		Icon
	},
	props: {
		title: {
			type: String,
			default: ''
		},
		subtitle: {
			type: String,
			default: ''
		},
		showBack: {
			type: Boolean,
			default: true
		},
		leftText: {
			type: String,
			default: ''
		},
		rightText: {
			type: String,
			default: ''
		},
		rightIcon: {
			type: String,
			default: ''
		},
		backgroundColor: {
			type: String,
			default: '#ff8a00'
		},
		textColor: {
			type: String,
			default: '#ffffff'
		},
		showSearch: {
			type: Boolean,
			default: false
		},
		searchPlaceholder: {
			type: String,
			default: '请输入搜索内容'
		},
		searchValue: {
			type: String,
			default: ''
		},
		tabs: {
			type: Array,
			default: () => []
		},
		activeTab: {
			type: Number,
			default: 0
		},
		elderlyMode: {
			type: Boolean,
			default: false
		},
		fixed: {
			type: Boolean,
			default: true
		}
	},
	data() {
		return {
			statusBarHeight: 0,
			navBarHeight: 44
		}
	},
	computed: {
		headerStyle() {
			const style = {
				backgroundColor: this.backgroundColor
			}
			
			if (this.fixed) {
				style.position = 'fixed'
				style.top = '0'
				style.left = '0'
				style.right = '0'
				style.zIndex = '1000'
			}
			
			return style
		},
		titleStyle() {
			return {
				color: this.textColor,
				fontSize: this.elderlyMode ? '36rpx' : '32rpx',
				fontWeight: 'bold'
			}
		},
		subtitleStyle() {
			return {
				color: this.textColor,
				fontSize: this.elderlyMode ? '26rpx' : '24rpx',
				opacity: '0.8'
			}
		},
		iconSize() {
			return this.elderlyMode ? '36rpx' : '32rpx'
		},
		iconColor() {
			return this.textColor
		}
	},
	mounted() {
		this.getSystemInfo()
	},
	methods: {
		getSystemInfo() {
			const systemInfo = uni.getSystemInfoSync()
			this.statusBarHeight = systemInfo.statusBarHeight || 0
			
			// 根据平台设置导航栏高度
			if (systemInfo.platform === 'ios') {
				this.navBarHeight = 44
			} else {
				this.navBarHeight = 48
			}
		},
		handleLeftClick() {
			if (this.showBack) {
				// 默认返回行为
				const pages = getCurrentPages()
				if (pages.length > 1) {
					uni.navigateBack({
						fail: (err) => {
							console.error('返回失败:', err);
							// 返回失败时跳转到首页
							uni.reLaunch({
								url: '/pages/home/<USER>',
								fail: (error) => {
									console.error('跳转首页失败:', error);
									uni.showToast({
										title: '页面跳转失败',
										icon: 'none'
									});
								}
							});
						}
					});
				} else {
					uni.reLaunch({
						url: '/pages/home/<USER>',
						fail: (error) => {
							console.error('跳转首页失败:', error);
							uni.showToast({
								title: '页面跳转失败',
								icon: 'none'
							});
						}
					});
				}
			}
			this.$emit('left-click')
		},
		handleRightClick() {
			this.$emit('right-click')
		},
		handleSearchInput(e) {
			this.$emit('search-input', e.detail.value)
		},
		handleSearchConfirm(e) {
			this.$emit('search-confirm', e.detail.value)
		},
		clearSearch() {
			this.$emit('search-input', '')
			this.$emit('search-clear')
		},
		handleTabClick(index) {
			this.$emit('tab-change', index)
		}
	}
}
</script>

<style scoped>
.page-header {
	background: #ff8a00;
	color: white;
}

.status-bar {
	width: 100%;
}

.navbar {
	display: flex;
	align-items: center;
	padding: 0 20rpx;
	position: relative;
}

.navbar-left,
.navbar-right {
	display: flex;
	align-items: center;
	gap: 10rpx;
	min-width: 100rpx;
	z-index: 10;
}

.navbar-left {
	justify-content: flex-start;
}

.navbar-right {
	justify-content: flex-end;
}

.navbar-center {
	flex: 1;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	position: absolute;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	max-width: 60%;
}

.navbar-title {
	font-size: 32rpx;
	font-weight: bold;
	color: white;
	text-align: center;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}

.navbar-subtitle {
	font-size: 24rpx;
	color: rgba(255, 255, 255, 0.8);
	text-align: center;
	margin-top: 5rpx;
}

.navbar-text {
	font-size: 28rpx;
	color: white;
}

.search-bar {
	padding: 20rpx;
	background: rgba(255, 255, 255, 0.1);
}

.search-input-wrapper {
	display: flex;
	align-items: center;
	background: white;
	border-radius: 25rpx;
	padding: 0 20rpx;
	gap: 15rpx;
}

.search-input {
	flex: 1;
	height: 70rpx;
	font-size: 28rpx;
	color: #333;
	border: none;
	background: transparent;
}

.tab-bar {
	background: rgba(255, 255, 255, 0.1);
	border-top: 1rpx solid rgba(255, 255, 255, 0.2);
}

.tab-scroll {
	white-space: nowrap;
}

.tab-list {
	display: flex;
	padding: 0 20rpx;
}

.tab-item {
	position: relative;
	padding: 25rpx 30rpx;
	white-space: nowrap;
	display: flex;
	align-items: center;
	gap: 10rpx;
}

.tab-text {
	font-size: 28rpx;
	color: rgba(255, 255, 255, 0.7);
	transition: color 0.2s ease;
}

.tab-active .tab-text {
	color: white;
	font-weight: bold;
}

.tab-active::after {
	content: '';
	position: absolute;
	bottom: 0;
	left: 50%;
	transform: translateX(-50%);
	width: 40rpx;
	height: 4rpx;
	background: white;
	border-radius: 2rpx;
}

.tab-badge {
	background: #ff4444;
	color: white;
	font-size: 20rpx;
	padding: 2rpx 8rpx;
	border-radius: 10rpx;
	min-width: 20rpx;
	text-align: center;
}

/* 适老版样式 */
.elderly-mode .navbar {
	padding: 0 30rpx;
}

.elderly-mode .navbar-title {
	font-size: 36rpx;
}

.elderly-mode .navbar-subtitle {
	font-size: 26rpx;
}

.elderly-mode .navbar-text {
	font-size: 32rpx;
}

.elderly-mode .search-input {
	height: 80rpx;
	font-size: 32rpx;
}

.elderly-mode .tab-text {
	font-size: 32rpx;
}

.elderly-mode .tab-item {
	padding: 30rpx 35rpx;
}
</style>
