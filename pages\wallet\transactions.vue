<template>
	<view class="container">
		<!-- 自定义导航栏 -->
		<view class="custom-navbar">
			<view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
			<view class="navbar-content">
				<view class="navbar-left" @click="goBack">
					<Icon name="arrow-left-line" size="36rpx" color="#333"></Icon>
					<text class="back-text">返回</text>
				</view>
				<view class="navbar-center">
					<text class="navbar-title">交易记录</text>
				</view>
				<view class="navbar-right"></view>
			</view>
		</view>

		<!-- 筛选条件 -->
		<view class="filter-section">
			<view class="filter-tabs">
				<view class="tab-item" 
					:class="{ active: activeTab === tab.key }" 
					v-for="tab in filterTabs" 
					:key="tab.key"
					@click="switchTab(tab.key)">
					<text class="tab-text">{{tab.name}}</text>
				</view>
			</view>
			
			<view class="date-filter">
				<picker mode="date" :value="startDate" @change="onStartDateChange">
					<view class="date-picker">
						<text class="date-text">{{startDate}}</text>
						<Icon name="calendar-line" size="20rpx" color="#999"></Icon>
					</view>
				</picker>
				<text class="date-separator">至</text>
				<picker mode="date" :value="endDate" @change="onEndDateChange">
					<view class="date-picker">
						<text class="date-text">{{endDate}}</text>
						<Icon name="calendar-line" size="20rpx" color="#999"></Icon>
					</view>
				</picker>
			</view>
		</view>

		<!-- 交易记录列表 -->
		<view class="transaction-list">
			<!-- 按日期分组 -->
			<view class="date-group" v-for="(group, date) in groupedTransactions" :key="date">
				<view class="date-header">
					<text class="date-title">{{formatDate(date)}}</text>
					<text class="date-count">{{group.length}}笔交易</text>
				</view>
				
				<view class="transaction-item" 
					v-for="(transaction, index) in group" 
					:key="index"
					@click="viewTransactionDetail(transaction)">
					<view class="transaction-icon" :class="transaction.type">
						<Icon :name="getTransactionIcon(transaction.type)" size="32rpx" color="white"></Icon>
					</view>
					
					<view class="transaction-info">
						<text class="transaction-title">{{transaction.title}}</text>
						<text class="transaction-desc">{{transaction.description}}</text>
						<text class="transaction-time">{{transaction.time}}</text>
					</view>
					
					<view class="transaction-amount">
						<text class="amount-text" :class="transaction.type">
							{{transaction.type === 'income' ? '+' : '-'}}¥{{transaction.amount}}
						</text>
						<text class="status-text" :class="transaction.status">{{getStatusText(transaction.status)}}</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 空状态 -->
		<view class="empty-state" v-if="filteredTransactions.length === 0">
			<Icon name="file-list-line" size="120rpx" color="#ccc"></Icon>
			<text class="empty-text">暂无交易记录</text>
			<text class="empty-desc">您的交易记录将在这里显示</text>
		</view>

		<!-- 功能开发中提示 -->
		<view class="dev-notice">
			<Icon name="tools-line" size="32rpx" color="#ff8a00"></Icon>
			<text class="dev-text">此功能正在开发中，敬请期待</text>
		</view>
	</view>
</template>

<script>
import Icon from '@/components/Icon/Icon.vue'

export default {
	components: {
		Icon
	},
	data() {
		return {
			statusBarHeight: 0,
			activeTab: 'all',
			startDate: '2024-01-01',
			endDate: '2024-01-31',
			filterTabs: [
				{ key: 'all', name: '全部' },
				{ key: 'income', name: '收入' },
				{ key: 'expense', name: '支出' }
			],
			allTransactions: [
				{
					id: 1,
					type: 'expense',
					title: '居家护理服务',
					description: '订单支付',
					amount: '120.00',
					time: '14:30',
					date: '2024-01-15',
					status: 'completed'
				},
				{
					id: 2,
					type: 'income',
					title: '账户充值',
					description: '微信支付',
					amount: '500.00',
					time: '09:15',
					date: '2024-01-14',
					status: 'completed'
				},
				{
					id: 3,
					type: 'expense',
					title: '康复训练',
					description: '订单支付',
					amount: '200.00',
					time: '16:20',
					date: '2024-01-13',
					status: 'completed'
				},
				{
					id: 4,
					type: 'income',
					title: '退款',
					description: '订单取消退款',
					amount: '35.00',
					time: '11:45',
					date: '2024-01-12',
					status: 'processing'
				},
				{
					id: 5,
					type: 'expense',
					title: '提现',
					description: '提现到银行卡',
					amount: '300.00',
					time: '10:30',
					date: '2024-01-11',
					status: 'completed'
				}
			]
		}
	},
	computed: {
		filteredTransactions() {
			let filtered = this.allTransactions;
			
			// 按类型筛选
			if (this.activeTab !== 'all') {
				filtered = filtered.filter(item => item.type === this.activeTab);
			}
			
			// 按日期筛选
			filtered = filtered.filter(item => {
				return item.date >= this.startDate && item.date <= this.endDate;
			});
			
			return filtered;
		},
		groupedTransactions() {
			const grouped = {};
			this.filteredTransactions.forEach(item => {
				if (!grouped[item.date]) {
					grouped[item.date] = [];
				}
				grouped[item.date].push(item);
			});
			
			// 按时间倒序排列每组内的项目
			Object.keys(grouped).forEach(date => {
				grouped[date].sort((a, b) => b.time.localeCompare(a.time));
			});
			
			return grouped;
		}
	},
	onLoad() {
		const systemInfo = uni.getSystemInfoSync();
		this.statusBarHeight = systemInfo.statusBarHeight || 44;
		
		// 设置默认日期范围（最近30天）
		const today = new Date();
		const thirtyDaysAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);
		
		this.endDate = this.formatDateString(today);
		this.startDate = this.formatDateString(thirtyDaysAgo);
	},
	methods: {
		switchTab(tabKey) {
			this.activeTab = tabKey;
		},
		onStartDateChange(e) {
			this.startDate = e.detail.value;
		},
		onEndDateChange(e) {
			this.endDate = e.detail.value;
		},
		formatDate(dateString) {
			const date = new Date(dateString);
			const today = new Date();
			const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);
			
			if (dateString === this.formatDateString(today)) {
				return '今天';
			} else if (dateString === this.formatDateString(yesterday)) {
				return '昨天';
			} else {
				return `${date.getMonth() + 1}月${date.getDate()}日`;
			}
		},
		formatDateString(date) {
			const year = date.getFullYear();
			const month = String(date.getMonth() + 1).padStart(2, '0');
			const day = String(date.getDate()).padStart(2, '0');
			return `${year}-${month}-${day}`;
		},
		getTransactionIcon(type) {
			return type === 'income' ? 'arrow-down-line' : 'arrow-up-line';
		},
		getStatusText(status) {
			const statusMap = {
				'completed': '已完成',
				'processing': '处理中',
				'failed': '失败'
			};
			return statusMap[status] || '未知';
		},
		viewTransactionDetail(transaction) {
			uni.navigateTo({
				url: `/pages/wallet/transaction-detail?id=${transaction.id}`
			});
		},
		goBack() {
			uni.navigateBack({
				fail: () => {
					uni.navigateTo({
						url: '/pages/wallet/wallet'
					});
				}
			});
		}
	}
}
</script>

<style scoped>
.container {
	background: #f8f9fa;
	min-height: 100vh;
}

/* 导航栏样式 */
.custom-navbar {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 1000;
	background: rgba(255, 255, 255, 0.95);
	backdrop-filter: blur(20rpx);
	border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);
}

.navbar-content {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 20rpx 32rpx;
	min-height: 88rpx;
}

.navbar-left {
	display: flex;
	align-items: center;
	gap: 12rpx;
	padding: 8rpx 16rpx 8rpx 0;
	border-radius: 20rpx;
	transition: all 0.2s ease;
}

.navbar-left:active {
	background-color: rgba(0, 0, 0, 0.04);
	transform: scale(0.96);
}

.back-text {
	font-size: 32rpx;
	color: #333;
	font-weight: 500;
}

.navbar-center {
	position: absolute;
	left: 50%;
	transform: translateX(-50%);
}

.navbar-title {
	font-size: 34rpx;
	font-weight: 600;
	color: #333;
}

.navbar-right {
	display: flex;
	align-items: center;
}

/* 筛选区域 */
.filter-section {
	background: white;
	margin: 20rpx;
	margin-top: 220rpx;
	border-radius: 30rpx;
	padding: 40rpx;
}

.filter-tabs {
	display: flex;
	background: #f8f9fa;
	border-radius: 20rpx;
	padding: 8rpx;
	margin-bottom: 30rpx;
}

.tab-item {
	flex: 1;
	text-align: center;
	padding: 20rpx;
	border-radius: 15rpx;
	transition: all 0.2s ease;
}

.tab-item.active {
	background: #ff8a00;
}

.tab-text {
	font-size: 26rpx;
	color: #666;
}

.tab-item.active .tab-text {
	color: white;
	font-weight: bold;
}

.date-filter {
	display: flex;
	align-items: center;
	gap: 20rpx;
}

.date-picker {
	display: flex;
	align-items: center;
	gap: 10rpx;
	padding: 15rpx 20rpx;
	background: #f8f9fa;
	border-radius: 15rpx;
	flex: 1;
}

.date-text {
	font-size: 26rpx;
	color: #333;
}

.date-separator {
	font-size: 26rpx;
	color: #666;
}

/* 交易记录列表 */
.transaction-list {
	padding: 0 20rpx 40rpx;
}

.date-group {
	margin-bottom: 30rpx;
}

.date-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20rpx 30rpx;
	background: rgba(255, 255, 255, 0.8);
	border-radius: 15rpx;
	margin-bottom: 15rpx;
}

.date-title {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
}

.date-count {
	font-size: 24rpx;
	color: #666;
}

.transaction-item {
	display: flex;
	align-items: center;
	gap: 20rpx;
	padding: 25rpx;
	background: white;
	border-radius: 20rpx;
	margin-bottom: 15rpx;
	transition: all 0.2s ease;
}

.transaction-item:active {
	background: rgba(255, 138, 0, 0.05);
}

.transaction-icon {
	width: 60rpx;
	height: 60rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
}

.transaction-icon.income {
	background: #4caf50;
}

.transaction-icon.expense {
	background: #f44336;
}

.transaction-info {
	flex: 1;
}

.transaction-title {
	font-size: 28rpx;
	color: #333;
	font-weight: bold;
	display: block;
	margin-bottom: 5rpx;
}

.transaction-desc {
	font-size: 24rpx;
	color: #666;
	display: block;
	margin-bottom: 5rpx;
}

.transaction-time {
	font-size: 22rpx;
	color: #999;
	display: block;
}

.transaction-amount {
	text-align: right;
}

.amount-text {
	font-size: 28rpx;
	font-weight: bold;
	display: block;
	margin-bottom: 5rpx;
}

.amount-text.income {
	color: #4caf50;
}

.amount-text.expense {
	color: #f44336;
}

.status-text {
	font-size: 22rpx;
	display: block;
}

.status-text.completed {
	color: #4caf50;
}

.status-text.processing {
	color: #ff9800;
}

.status-text.failed {
	color: #f44336;
}

/* 空状态 */
.empty-state {
	text-align: center;
	padding: 120rpx 40rpx;
}

.empty-text {
	font-size: 28rpx;
	color: #999;
	display: block;
	margin: 30rpx 0 15rpx;
}

.empty-desc {
	font-size: 24rpx;
	color: #ccc;
	display: block;
}

.dev-notice {
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 15rpx;
	padding: 30rpx;
	margin: 20rpx;
	background: rgba(255, 138, 0, 0.1);
	border-radius: 20rpx;
}

.dev-text {
	font-size: 26rpx;
	color: #ff8a00;
}
</style>
