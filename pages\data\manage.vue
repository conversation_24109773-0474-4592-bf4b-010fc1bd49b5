<template>
	<view class="container">
		<!-- 自定义导航栏 -->
		<view class="custom-navbar">
			<view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
			<view class="navbar-content">
				<view class="navbar-left" @click="goBack">
					<Icon name="arrow-left-line" size="36rpx" color="#333"></Icon>
					<text class="back-text">返回</text>
				</view>
				<view class="navbar-center">
					<text class="navbar-title">数据管理</text>
				</view>
				<view class="navbar-right"></view>
			</view>
		</view>

		<!-- 页面头部 -->
		<view class="page-header">
			<text class="page-title">数据管理中心</text>
			<text class="page-subtitle">管理您的所有数据</text>
		</view>

		<!-- 数据统计概览 -->
		<view class="stats-overview">
			<text class="section-title">数据概览</text>
			<view class="stats-grid">
				<view class="stat-item" v-for="(stat, index) in dataStats" :key="index">
					<view class="stat-icon">
						<Icon :name="stat.icon" size="48rpx" :color="stat.color"></Icon>
					</view>
					<view class="stat-info">
						<text class="stat-value">{{ stat.value }}</text>
						<text class="stat-label">{{ stat.label }}</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 管理功能菜单 -->
		<view class="management-section">
			<text class="section-title">数据管理</text>
			<view class="management-grid">
				<InteractiveCard 
					v-for="(item, index) in managementItems" 
					:key="index"
					class="management-item"
					@click="navigateToManagement(item)"
				>
					<view class="item-content">
						<view class="item-icon">
							<Icon :name="item.icon" size="64rpx" :color="item.color"></Icon>
						</view>
						<view class="item-info">
							<text class="item-title">{{ item.title }}</text>
							<text class="item-description">{{ item.description }}</text>
							<view class="item-stats">
								<text class="stats-text">{{ item.count }} 条记录</text>
							</view>
						</view>
						<view class="item-arrow">
							<Icon name="arrow-right-s-line" size="32rpx" color="#ccc"></Icon>
						</view>
					</view>
				</InteractiveCard>
			</view>
		</view>

		<!-- 数据工具 -->
		<view class="tools-section">
			<text class="section-title">数据工具</text>
			<view class="tools-grid">
				<InteractiveButton
					v-for="(tool, index) in dataTools"
					:key="index"
					type="secondary"
					size="large"
					:text="tool.title"
					:icon="tool.icon"
					class="tool-button"
					@click="handleToolAction(tool)"
				></InteractiveButton>
			</view>
		</view>

		<!-- 数据同步状态 -->
		<view class="sync-section">
			<text class="section-title">同步状态</text>
			<InteractiveCard class="sync-card">
				<view class="sync-status">
					<view class="sync-info">
						<Icon name="cloud-line" size="40rpx" :color="syncStatus.color" />
						<view class="sync-details">
							<text class="sync-title">{{ syncStatus.title }}</text>
							<text class="sync-desc">{{ syncStatus.desc }}</text>
						</view>
					</view>
					<InteractiveButton
						v-if="syncStatus.showButton"
						type="primary"
						size="small"
						text="立即同步"
						:loading="syncing"
						@click="handleSync"
					></InteractiveButton>
				</view>
				<view class="sync-progress" v-if="syncing">
					<progress :percent="syncProgress" stroke-width="6" activeColor="#ff8a00" />
					<text class="progress-text">{{ syncProgress }}%</text>
				</view>
			</InteractiveCard>
		</view>

		<!-- 存储空间信息 -->
		<view class="storage-section">
			<text class="section-title">存储空间</text>
			<InteractiveCard class="storage-card">
				<view class="storage-info">
					<view class="storage-header">
						<Icon name="hard-drive-line" size="40rpx" color="#4caf50" />
						<text class="storage-title">本地存储</text>
					</view>
					<view class="storage-details">
						<view class="storage-item">
							<text class="storage-label">已使用</text>
							<text class="storage-value">{{ storageInfo.used }}</text>
						</view>
						<view class="storage-item">
							<text class="storage-label">总容量</text>
							<text class="storage-value">{{ storageInfo.total }}</text>
						</view>
						<view class="storage-item">
							<text class="storage-label">可用空间</text>
							<text class="storage-value">{{ storageInfo.available }}</text>
						</view>
					</view>
					<view class="storage-bar">
						<view class="storage-progress" :style="{ width: storageInfo.percentage + '%' }"></view>
					</view>
					<text class="storage-percentage">{{ storageInfo.percentage }}% 已使用</text>
				</view>
			</InteractiveCard>
		</view>

		<!-- 数据分析 -->
		<view class="analytics-section">
			<text class="section-title">数据分析</text>
			<InteractiveCard class="analytics-card" :elderlyMode="elderlyMode">
				<view class="analytics-header">
					<Icon name="line-chart-line" size="40rpx" color="#2196f3" />
					<text class="analytics-title">使用趋势分析</text>
				</view>
				<view class="analytics-empty" v-if="dataStats.every(stat => stat.value === 0)">
					<text class="empty-text">暂无足够数据进行分析</text>
					<text class="empty-desc">请继续使用应用收集更多数据</text>
				</view>
				<view class="analytics-content" v-else>
					<view class="chart-placeholder">
						<view class="chart-bars">
							<view class="chart-bar-container" v-for="(stat, index) in dataStats" :key="index">
								<view class="chart-bar-label">{{stat.label}}</view>
								<view class="chart-bar-wrapper">
									<view class="chart-bar" 
										:style="{ 
											height: Math.max(40, stat.value * 10) + 'rpx', 
											backgroundColor: stat.color,
											'--final-height': Math.max(40, stat.value * 10) + 'rpx',
											'--index': index
										}">
										<text class="chart-bar-value">{{stat.value}}</text>
									</view>
								</view>
							</view>
						</view>
					</view>
					<view class="analytics-insight">
						<text class="insight-title">数据洞察:</text>
						<text class="insight-text">{{getDataInsight()}}</text>
					</view>
				</view>
				<view class="analytics-actions">
					<InteractiveButton 
						type="secondary" 
						size="small" 
						text="生成报告" 
						icon="file-list-line" 
						:elderlyMode="elderlyMode"
						@click="generateReport"
					/>
				</view>
			</InteractiveCard>
		</view>

		<!-- 数据导入导出弹窗 -->
		<uni-popup ref="dataPopup" type="center" :mask-click="false">
			<view class="data-popup">
				<view class="popup-header">
					<text class="popup-title">{{ currentTool.title }}</text>
					<view class="close-btn" @click="closePopup">
						<Icon name="close-line" size="32rpx" color="#666"></Icon>
					</view>
				</view>
				<view class="popup-content">
					<!-- 导出数据 -->
					<view v-if="currentTool.action === 'export'" class="export-content">
						<text class="content-text">将导出所有数据为JSON格式</text>
						<textarea 
							v-if="exportData" 
							class="export-textarea" 
							:value="exportData" 
							readonly
						></textarea>
						<view class="popup-actions">
							<InteractiveButton 
								type="secondary" 
								size="medium" 
								text="取消" 
								@click="closePopup"
							></InteractiveButton>
							<InteractiveButton 
								type="primary" 
								size="medium" 
								text="复制数据" 
								@click="copyExportData"
							></InteractiveButton>
						</view>
					</view>

					<!-- 导入数据 -->
					<view v-else-if="currentTool.action === 'import'" class="import-content">
						<text class="content-text">请粘贴要导入的JSON数据</text>
						<textarea 
							class="import-textarea" 
							v-model="importData" 
							placeholder="请粘贴JSON格式的数据..."
						></textarea>
						<view class="popup-actions">
							<InteractiveButton 
								type="secondary" 
								size="medium" 
								text="取消" 
								@click="closePopup"
							></InteractiveButton>
							<InteractiveButton 
								type="primary" 
								size="medium" 
								text="导入数据" 
								:loading="importing"
								@click="handleImportData"
							></InteractiveButton>
						</view>
					</view>

					<!-- 清空数据 -->
					<view v-else-if="currentTool.action === 'clear'" class="clear-content">
						<Icon name="error-warning-line" size="80rpx" color="#f44336"></Icon>
						<text class="warning-text">此操作将清空所有数据，且不可恢复！</text>
						<text class="confirm-text">请输入"确认清空"来确认操作：</text>
						<input 
							class="confirm-input" 
							v-model="clearConfirmText" 
							placeholder="请输入：确认清空"
						/>
						<view class="popup-actions">
							<InteractiveButton 
								type="secondary" 
								size="medium" 
								text="取消" 
								@click="closePopup"
							></InteractiveButton>
							<InteractiveButton 
								type="danger" 
								size="medium" 
								text="确认清空" 
								:disabled="clearConfirmText !== '确认清空'"
								:loading="clearing"
								@click="handleClearData"
							></InteractiveButton>
						</view>
					</view>
				</view>
			</view>
		</uni-popup>
	</view>
</template>

<script>
import InteractiveCard from '@/components/InteractiveCard/InteractiveCard.vue'
import InteractiveButton from '@/components/InteractiveButton/InteractiveButton.vue'
import Icon from '@/components/Icon/Icon.vue'
import FeedbackUtils from '@/utils/feedback.js'
import MockAPI, { LocalStorage } from '@/utils/mockData.js'
import { ElderlyModeManager } from '@/utils/elderlyModeUtils.js'

export default {
	components: {
		InteractiveCard,
		InteractiveButton,
		Icon
	},
	data() {
		return {
			statusBarHeight: 0,
			elderlyMode: false,
			// 数据统计
			dataStats: [
				{ label: '机构数据', value: 0, icon: 'building-line', color: '#ff8a00' },
				{ label: '服务数据', value: 0, icon: 'service-line', color: '#4caf50' },
				{ label: '收藏数据', value: 0, icon: 'heart-line', color: '#f44336' },
				{ label: '健康数据', value: 0, icon: 'heart-pulse-line', color: '#2196f3' },
				{ label: '任务数据', value: 0, icon: 'task-line', color: '#9c27b0' }
			],

			// 管理功能
			managementItems: [
				{
					title: '机构管理',
					description: '添加、编辑、删除养老机构信息',
					icon: 'building-line',
					color: '#ff8a00',
					count: 0,
					route: '/pages/institution/manage'
				},
				{
					title: '服务管理',
					description: '管理各类养老服务项目',
					icon: 'service-line',
					color: '#4caf50',
					count: 0,
					route: '/pages/service/manage'
				},
				{
					title: '收藏管理',
					description: '查看和管理收藏的内容',
					icon: 'heart-line',
					color: '#f44336',
					count: 0,
					route: '/pages/favorite/list'
				},
				{
					title: '健康数据',
					description: '记录和管理健康监测数据',
					icon: 'heart-pulse-line',
					color: '#2196f3',
					count: 0,
					route: '/pages/health/manage'
				},
				{
					title: '任务管理',
					description: '创建和管理个人任务计划',
					icon: 'task-line',
					color: '#9c27b0',
					count: 0,
					route: '/pages/task/manage'
				}
			],

			// 数据工具
			dataTools: [
				{ title: '导出数据', icon: 'download-line', action: 'export' },
				{ title: '导入数据', icon: 'upload-line', action: 'import' },
				{ title: '清空数据', icon: 'delete-bin-line', action: 'clear' }
			],

			// 弹窗相关
			currentTool: {},
			exportData: '',
			importData: '',
			importing: false,
			clearConfirmText: '',
			clearing: false,

			// 同步状态
			syncing: false,
			syncProgress: 0,
			syncStatus: {
				title: '数据已同步',
				desc: '最后同步：刚刚',
				color: '#4caf50',
				showButton: false
			},

			// 存储信息
			storageInfo: {
				used: '0 KB',
				total: '10 MB',
				available: '10 MB',
				percentage: 0
			}
		}
	},
	onLoad() {
		// 获取状态栏高度
		const systemInfo = uni.getSystemInfoSync();
		this.statusBarHeight = systemInfo.statusBarHeight || 44;

		// 检查适老化模式
		this.elderlyMode = uni.getStorageSync('elderlyMode') || false;

		this.loadDataStats();
		this.loadStorageInfo();
		this.checkSyncStatus();
	},
	onShow() {
		this.loadDataStats();
		this.loadStorageInfo();
	},
	methods: {
		// 加载数据统计
		async loadDataStats() {
			try {
				// 获取各类数据的数量
				const institutions = LocalStorage.get('institutions', []);
				const services = LocalStorage.get('services', []);
				const favorites = LocalStorage.get('favorites', []);
				const healthData = LocalStorage.get('healthData', []);
				const tasks = LocalStorage.get('tasks', []);

				// 更新统计数据
				this.dataStats[0].value = institutions.length;
				this.dataStats[1].value = services.length;
				this.dataStats[2].value = favorites.length;
				this.dataStats[3].value = healthData.length;
				this.dataStats[4].value = tasks.length;

				// 更新管理项目的计数
				this.managementItems[0].count = institutions.length;
				this.managementItems[1].count = services.length;
				this.managementItems[2].count = favorites.length;
				this.managementItems[3].count = healthData.length;
				this.managementItems[4].count = tasks.length;

				// 添加数据处理标记，检查是否首次使用
				const isFirstUse = !LocalStorage.get('dataLastUpdate');
				if (isFirstUse) {
					// 首次使用提示
					setTimeout(() => {
						uni.showModal({
							title: '数据管理说明',
							content: '欢迎使用数据管理功能，您可以在这里管理所有个人数据和应用数据。',
							confirmText: '知道了',
							showCancel: false
						});
						// 记录首次使用时间
						LocalStorage.set('dataLastUpdate', Date.now());
					}, 500);
				}
			} catch (error) {
				console.error('加载数据统计失败:', error);
				FeedbackUtils.showError('数据加载失败');
			}
		},

		// 导航到管理页面
		navigateToManagement(item) {
			FeedbackUtils.lightFeedback();
			uni.navigateTo({
				url: item.route,
				success: () => {
					console.log('跳转到管理页面:', item.title);
				},
				fail: (err) => {
					console.error('跳转失败:', err);
					FeedbackUtils.showError('页面跳转失败');
				}
			});
		},

		// 处理工具操作
		async handleToolAction(tool) {
			FeedbackUtils.lightFeedback();
			this.currentTool = tool;

			if (tool.action === 'export') {
				await this.handleExportData();
			} else if (tool.action === 'import') {
				this.importData = '';
				this.$refs.dataPopup.open();
			} else if (tool.action === 'clear') {
				this.clearConfirmText = '';
				this.$refs.dataPopup.open();
			}
		},

		// 处理数据导出
		async handleExportData() {
			try {
				FeedbackUtils.showLoading('正在导出数据...');
				
				const result = await MockAPI.exportData();
				
				FeedbackUtils.hideLoading();
				
				if (result.success) {
					this.exportData = result.data;
					this.$refs.dataPopup.open();
				} else {
					FeedbackUtils.showError(result.message || '导出失败');
				}
			} catch (error) {
				FeedbackUtils.hideLoading();
				console.error('导出数据失败:', error);
				FeedbackUtils.showError('导出失败，请重试');
			}
		},

		// 复制导出数据
		copyExportData() {
			uni.setClipboardData({
				data: this.exportData,
				success: () => {
					FeedbackUtils.showSuccess('数据已复制到剪贴板');
					this.closePopup();
				},
				fail: () => {
					FeedbackUtils.showError('复制失败');
				}
			});
		},

		// 处理数据导入
		async handleImportData() {
			if (!this.importData.trim()) {
				FeedbackUtils.showError('请输入要导入的数据');
				return;
			}

			try {
				this.importing = true;
				
				const result = await MockAPI.importData(this.importData);
				
				if (result.success) {
					FeedbackUtils.showSuccess('数据导入成功');
					this.closePopup();
					this.loadDataStats();
				} else {
					FeedbackUtils.showError(result.message || '导入失败');
				}
			} catch (error) {
				console.error('导入数据失败:', error);
				FeedbackUtils.showError('导入失败，请检查数据格式');
			} finally {
				this.importing = false;
			}
		},

		// 处理数据清空
		async handleClearData() {
			if (this.clearConfirmText !== '确认清空') {
				FeedbackUtils.showError('请输入正确的确认文字');
				return;
			}

			try {
				this.clearing = true;
				
				const result = await MockAPI.clearAllData();
				
				if (result.success) {
					FeedbackUtils.showSuccess('数据清空成功');
					this.closePopup();
					this.loadDataStats();
				} else {
					FeedbackUtils.showError(result.message || '清空失败');
				}
			} catch (error) {
				console.error('清空数据失败:', error);
				FeedbackUtils.showError('清空失败，请重试');
			} finally {
				this.clearing = false;
			}
		},

		// 关闭弹窗
		closePopup() {
			this.$refs.dataPopup.close();
			this.currentTool = {};
			this.exportData = '';
			this.importData = '';
			this.clearConfirmText = '';
		},

		// 加载存储信息
		loadStorageInfo() {
			try {
				const storageInfo = LocalStorage.getInfo();
				const usedKB = Math.round(storageInfo.currentSize / 1024);
				const totalKB = Math.round(storageInfo.limitSize / 1024);
				const availableKB = totalKB - usedKB;
				const percentage = Math.round((usedKB / totalKB) * 100);

				this.storageInfo = {
					used: usedKB > 1024 ? `${(usedKB / 1024).toFixed(1)} MB` : `${usedKB} KB`,
					total: totalKB > 1024 ? `${(totalKB / 1024).toFixed(1)} MB` : `${totalKB} KB`,
					available: availableKB > 1024 ? `${(availableKB / 1024).toFixed(1)} MB` : `${availableKB} KB`,
					percentage: percentage || 0
				};
			} catch (error) {
				console.error('获取存储信息失败:', error);
			}
		},

		// 检查同步状态
		checkSyncStatus() {
			const lastSync = LocalStorage.get('lastSyncTime', null);
			if (lastSync) {
				const now = Date.now();
				const diff = now - lastSync;
				const hours = Math.floor(diff / (1000 * 60 * 60));

				if (hours < 1) {
					this.syncStatus = {
						title: '数据已同步',
						desc: '最后同步：刚刚',
						color: '#4caf50',
						showButton: false
					};
				} else if (hours < 24) {
					this.syncStatus = {
						title: '数据已同步',
						desc: `最后同步：${hours}小时前`,
						color: '#4caf50',
						showButton: true
					};
				} else {
					this.syncStatus = {
						title: '需要同步',
						desc: '数据可能已过期',
						color: '#ff9800',
						showButton: true
					};
				}
			} else {
				this.syncStatus = {
					title: '未同步',
					desc: '建议立即同步数据',
					color: '#f44336',
					showButton: true
				};
			}
		},

		// 处理数据同步
		async handleSync() {
			try {
				this.syncing = true;
				this.syncProgress = 0;

				// 模拟同步进度
				const progressInterval = setInterval(() => {
					if (this.syncProgress < 90) {
						this.syncProgress += Math.random() * 20;
					}
				}, 200);

				// 模拟同步请求
				await new Promise(resolve => setTimeout(resolve, 2000));

				clearInterval(progressInterval);
				this.syncProgress = 100;

				// 更新同步时间
				LocalStorage.set('lastSyncTime', Date.now());

				setTimeout(() => {
					this.syncing = false;
					this.syncProgress = 0;
					this.checkSyncStatus();
					FeedbackUtils.showSuccess('数据同步成功');
				}, 500);

			} catch (error) {
				this.syncing = false;
				this.syncProgress = 0;
				console.error('数据同步失败:', error);
				FeedbackUtils.showError('数据同步失败，请重试');
			}
		},

		// 返回上一页
		goBack() {
			FeedbackUtils.lightFeedback();
			uni.navigateBack({
				fail: () => {
					uni.switchTab({
						url: '/pages/profile/profile'
					});
				}
			});
		},

		// 生成数据分析报告
		generateReport() {
			FeedbackUtils.lightFeedback();
			
			// 如果没有数据，提示用户
			if (this.dataStats.every(stat => stat.value === 0)) {
				FeedbackUtils.showInfo('暂无数据可生成报告');
				return;
			}
			
			FeedbackUtils.showLoading('正在生成报告...');
			
			// 模拟生成报告的延迟
			setTimeout(() => {
				FeedbackUtils.hideLoading();
				
				uni.showModal({
					title: '数据分析报告',
					content: '数据报告已生成，是否查看详情？',
					confirmText: '立即查看',
					cancelText: '稍后查看',
					success: (res) => {
						if (res.confirm) {
							// 模拟查看报告详情
							uni.navigateTo({
								url: '/pages/data/report',
								fail: () => {
									FeedbackUtils.showInfo('报告详情页面开发中');
								}
							});
						}
					}
				});
			}, 1500);
		},

		// 获取数据洞察
		getDataInsight() {
			// 找出最多的数据类型
			const maxStat = this.dataStats.reduce((prev, current) => {
				return (prev.value > current.value) ? prev : current;
			});
			
			// 找出最少的数据类型
			const minStat = this.dataStats.filter(stat => stat.value > 0).reduce((prev, current) => {
				return (prev.value < current.value) ? prev : current;
			}, maxStat);
			
			// 计算总数据量
			const totalData = this.dataStats.reduce((sum, stat) => sum + stat.value, 0);
			
			// 如果没有数据
			if (totalData === 0) {
				return '暂无数据可供分析，请先添加一些数据。';
			}
			
			// 如果只有一种数据
			if (this.dataStats.filter(stat => stat.value > 0).length === 1) {
				return `您目前只有${maxStat.label}，建议多使用其他功能丰富您的数据。`;
			}
			
			// 正常情况
			return `您的${maxStat.label}最丰富(${maxStat.value}条)，${minStat.label}较少(${minStat.value}条)。建议多关注${minStat.label}以获得更全面的服务体验。`;
		}
	}
}
</script>

<style scoped>
.container {
	background-color: #f5f5f5;
	min-height: 100vh;
	padding: 20rpx;
}

/* 导航栏样式 */
.custom-navbar {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 1000;
	background: rgba(255, 255, 255, 0.95);
	backdrop-filter: blur(20rpx);
	-webkit-backdrop-filter: blur(20rpx);
	border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);
}

.navbar-content {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 20rpx 32rpx;
	min-height: 88rpx;
}

.navbar-left {
	display: flex;
	align-items: center;
	gap: 12rpx;
	padding: 8rpx 16rpx 8rpx 0;
	border-radius: 20rpx;
	transition: all 0.2s ease;
}

.navbar-left:active {
	background-color: rgba(0, 0, 0, 0.04);
	transform: scale(0.96);
}

.back-text {
	font-size: 32rpx;
	color: #333;
	font-weight: 500;
}

.navbar-center {
	position: absolute;
	left: 50%;
	transform: translateX(-50%);
}

.navbar-title {
	font-size: 34rpx;
	font-weight: 600;
	color: #333;
}

.page-header {
	text-align: center;
	padding: 40rpx 20rpx;
	background: linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%);
	border-radius: 30rpx;
	margin-bottom: 30rpx;
	margin-top: 200rpx; /* 为导航栏留出空间 */
}

.page-title {
	font-size: 40rpx;
	font-weight: bold;
	color: white;
	display: block;
	margin-bottom: 10rpx;
}

.page-subtitle {
	font-size: 26rpx;
	color: rgba(255, 255, 255, 0.8);
	display: block;
}

.section-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 20rpx;
}

.stats-overview {
	margin-bottom: 40rpx;
}

.stats-grid {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 20rpx;
}

.stat-item {
	background: white;
	border-radius: 20rpx;
	padding: 30rpx;
	display: flex;
	align-items: center;
	gap: 20rpx;
	box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.stat-icon {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	background: rgba(255, 138, 0, 0.1);
	display: flex;
	align-items: center;
	justify-content: center;
}

.stat-info {
	flex: 1;
}

.stat-value {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
	display: block;
}

.stat-label {
	font-size: 24rpx;
	color: #666;
	display: block;
	margin-top: 5rpx;
}

.management-section {
	margin-bottom: 40rpx;
}

.management-grid {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

.management-item {
	margin-bottom: 0;
}

.item-content {
	display: flex;
	align-items: center;
	padding: 30rpx;
	gap: 20rpx;
}

.item-icon {
	width: 100rpx;
	height: 100rpx;
	border-radius: 50%;
	background: rgba(255, 138, 0, 0.1);
	display: flex;
	align-items: center;
	justify-content: center;
	flex-shrink: 0;
}

.item-info {
	flex: 1;
}

.item-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 8rpx;
}

.item-description {
	font-size: 26rpx;
	color: #666;
	display: block;
	margin-bottom: 10rpx;
}

.item-stats {
	display: flex;
	align-items: center;
}

.stats-text {
	font-size: 24rpx;
	color: #ff8a00;
	background: rgba(255, 138, 0, 0.1);
	padding: 4rpx 12rpx;
	border-radius: 12rpx;
}

.item-arrow {
	flex-shrink: 0;
}

.tools-section {
	margin-bottom: 40rpx;
}

.tools-grid {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

.tool-button {
	width: 100%;
}

.data-popup {
	background: white;
	border-radius: 30rpx;
	width: 600rpx;
	max-height: 80vh;
	overflow: hidden;
}

.popup-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 30rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.popup-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.close-btn {
	padding: 10rpx;
}

.popup-content {
	padding: 30rpx;
	max-height: 60vh;
	overflow-y: auto;
}

.export-content,
.import-content,
.clear-content {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
	align-items: center;
}

.content-text {
	font-size: 28rpx;
	color: #666;
	text-align: center;
}

.export-textarea,
.import-textarea {
	width: 100%;
	height: 300rpx;
	border: 2rpx solid #e0e0e0;
	border-radius: 15rpx;
	padding: 20rpx;
	font-size: 24rpx;
	color: #333;
	box-sizing: border-box;
}

.warning-text {
	font-size: 28rpx;
	color: #f44336;
	text-align: center;
	font-weight: bold;
}

.confirm-text {
	font-size: 26rpx;
	color: #666;
	text-align: center;
}

.confirm-input {
	width: 100%;
	height: 80rpx;
	border: 2rpx solid #e0e0e0;
	border-radius: 15rpx;
	padding: 0 20rpx;
	font-size: 28rpx;
	color: #333;
	box-sizing: border-box;
}

.popup-actions {
	display: flex;
	gap: 20rpx;
	margin-top: 30rpx;
}

/* 同步状态样式 */
.sync-section {
	margin-bottom: 40rpx;
}

.sync-card {
	margin-bottom: 0;
}

.sync-status {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 30rpx;
}

.sync-info {
	display: flex;
	align-items: center;
	gap: 20rpx;
	flex: 1;
}

.sync-details {
	flex: 1;
}

.sync-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 8rpx;
}

.sync-desc {
	font-size: 26rpx;
	color: #666;
	display: block;
}

.sync-progress {
	padding: 0 30rpx 30rpx;
	display: flex;
	align-items: center;
	gap: 20rpx;
}

.progress-text {
	font-size: 24rpx;
	color: #ff8a00;
	font-weight: bold;
}

/* 存储空间样式 */
.storage-section {
	margin-bottom: 40rpx;
}

.storage-card {
	margin-bottom: 0;
}

.storage-info {
	padding: 30rpx;
}

.storage-header {
	display: flex;
	align-items: center;
	gap: 15rpx;
	margin-bottom: 25rpx;
}

.storage-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.storage-details {
	display: flex;
	flex-direction: column;
	gap: 15rpx;
	margin-bottom: 25rpx;
}

.storage-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.storage-label {
	font-size: 28rpx;
	color: #666;
}

.storage-value {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
}

.storage-bar {
	height: 12rpx;
	background: #f0f0f0;
	border-radius: 6rpx;
	overflow: hidden;
	margin-bottom: 15rpx;
}

.storage-progress {
	height: 100%;
	background: linear-gradient(90deg, #4caf50 0%, #8bc34a 100%);
	border-radius: 6rpx;
	transition: width 0.3s ease;
}

.storage-percentage {
	font-size: 24rpx;
	color: #666;
	text-align: center;
	display: block;
}

/* 数据分析样式 */
.analytics-section {
	margin-bottom: 40rpx;
}

.analytics-card {
	margin-bottom: 0;
}

.analytics-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 30rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.analytics-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.analytics-empty {
	padding: 30rpx;
	text-align: center;
}

.empty-text {
	font-size: 28rpx;
	color: #666;
	font-weight: bold;
	display: block;
	margin-bottom: 10rpx;
}

.empty-desc {
	font-size: 26rpx;
	color: #999;
	display: block;
}

.analytics-content {
	padding: 30rpx;
}

.chart-placeholder {
	margin-bottom: 20rpx;
	padding: 20rpx 0;
}

.chart-bars {
	display: flex;
	justify-content: space-around;
	align-items: flex-end;
	height: 300rpx;
}

.chart-bar-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	width: 16%;
}

.chart-bar-label {
	font-size: 24rpx;
	color: #666;
	margin-top: 15rpx;
	text-align: center;
}

.chart-bar-wrapper {
	width: 40rpx;
	height: 200rpx;
	position: relative;
	background-color: #f5f5f5;
	border-radius: 20rpx;
	overflow: hidden;
}

.chart-bar {
	position: absolute;
	bottom: 0;
	left: 0;
	width: 100%;
	min-height: 40rpx;
	border-radius: 20rpx;
	display: flex;
	align-items: flex-start;
	justify-content: center;
	animation: barGrow 1s ease forwards;
	animation-delay: calc(var(--index) * 0.1s + 0.7s);
	--final-height: 40rpx;
}

.chart-bar-value {
	margin-top: 10rpx;
	font-size: 24rpx;
	color: white;
	font-weight: bold;
}

.elderly-mode .chart-bar-wrapper {
	width: 60rpx;
}

.elderly-mode .chart-bar-label {
	font-size: 28rpx;
	font-weight: bold;
}

.elderly-mode .chart-bar-value {
	font-size: 28rpx;
}

.analytics-insight {
	margin-bottom: 20rpx;
}

.insight-title {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 10rpx;
}

.insight-text {
	font-size: 26rpx;
	color: #666;
	display: block;
}

.analytics-actions {
	display: flex;
	justify-content: flex-end;
	margin-top: 20rpx;
}

/* 添加卡片交互动画 */
@keyframes sectionAppear {
	from {
		opacity: 0;
		transform: translateY(30rpx);
	}
	to {
		opacity: 1;
		transform: translateY(0);
	}
}

.stats-overview,
.management-section,
.tools-section,
.sync-section,
.storage-section,
.analytics-section {
	animation: sectionAppear 0.5s ease forwards;
}

.stats-overview {
	animation-delay: 0.1s;
}

.management-section {
	animation-delay: 0.2s;
}

.tools-section {
	animation-delay: 0.3s;
}

.sync-section {
	animation-delay: 0.4s;
}

.storage-section {
	animation-delay: 0.5s;
}

.analytics-section {
	animation-delay: 0.6s;
}

/* 图表动画效果 */
@keyframes barGrow {
	from {
		height: 0;
	}
	to {
		height: var(--final-height);
	}
}

/* 交互效果增强 */
.management-item:active,
.tool-button:active,
.sync-card:active,
.storage-card:active,
.analytics-card:active {
	transform: scale(0.98);
}

.elderly-mode .management-item:active,
.elderly-mode .tool-button:active,
.elderly-mode .sync-card:active,
.elderly-mode .storage-card:active,
.elderly-mode .analytics-card:active {
	transform: scale(0.95);
	transition: transform 0.3s ease;
}
</style>
