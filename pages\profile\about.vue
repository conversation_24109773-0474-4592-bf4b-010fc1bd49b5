<template>
	<view class="container">
		<!-- 应用信息 -->
		<view class="app-info-section">
			<view class="app-logo">
				<Icon name="heart-3-line" size="120rpx" color="#ff8a00"></Icon>
			</view>
			<text class="app-name">智慧养老</text>
			<text class="app-slogan">专业的老年人智慧生活服务平台</text>
			<view class="app-version">
				<text class="version-text">版本 {{appInfo.version}}</text>
				<text class="build-text">构建号 {{appInfo.buildNumber}}</text>
			</view>
		</view>

		<!-- 个人信息管理 -->
		<view class="profile-section">
			<view class="section-header">
				<Icon name="user-settings-line" size="32rpx" color="#ff8a00"></Icon>
				<text class="section-title">个人信息管理</text>
			</view>
			<view class="profile-list">
				<view class="profile-item" @click="editProfile">
					<Icon name="user-line" size="32rpx" color="#4caf50"></Icon>
					<view class="profile-info">
						<text class="profile-title">编辑个人资料</text>
						<text class="profile-desc">修改姓名、头像等基本信息</text>
					</view>
					<Icon name="arrow-right-s-line" size="20rpx" color="#999"></Icon>
				</view>

				<view class="profile-item" @click="manageContacts">
					<Icon name="contacts-line" size="32rpx" color="#2196f3"></Icon>
					<view class="profile-info">
						<text class="profile-title">紧急联系人</text>
						<text class="profile-desc">管理紧急联系人信息</text>
					</view>
					<Icon name="arrow-right-s-line" size="20rpx" color="#999"></Icon>
				</view>

				<view class="profile-item" @click="healthProfile">
					<Icon name="heart-line" size="32rpx" color="#e91e63"></Icon>
					<view class="profile-info">
						<text class="profile-title">健康档案</text>
						<text class="profile-desc">管理个人健康信息</text>
					</view>
					<Icon name="arrow-right-s-line" size="20rpx" color="#999"></Icon>
				</view>

				<view class="profile-item" @click="preferences">
					<Icon name="settings-3-line" size="32rpx" color="#ff9800"></Icon>
					<view class="profile-info">
						<text class="profile-title">偏好设置</text>
						<text class="profile-desc">设置个人使用偏好</text>
					</view>
					<Icon name="arrow-right-s-line" size="20rpx" color="#999"></Icon>
				</view>
			</view>
		</view>

		<!-- 联系我们 -->
		<view class="contact-section">
			<view class="section-header">
				<Icon name="phone-line" size="32rpx" color="#ff8a00"></Icon>
				<text class="section-title">联系我们</text>
			</view>
			<view class="contact-list">
				<view class="contact-item" @click="makeCall">
					<view class="contact-icon phone">
						<Icon name="phone-line" size="32rpx" color="white"></Icon>
					</view>
					<view class="contact-info">
						<text class="contact-label">客服热线</text>
						<text class="contact-value">{{contactInfo.phone}}</text>
					</view>
					<Icon name="arrow-right-s-line" size="20rpx" color="#999"></Icon>
				</view>
				<view class="contact-item" @click="sendEmail">
					<view class="contact-icon email">
						<Icon name="mail-line" size="32rpx" color="white"></Icon>
					</view>
					<view class="contact-info">
						<text class="contact-label">邮箱地址</text>
						<text class="contact-value">{{contactInfo.email}}</text>
					</view>
					<Icon name="arrow-right-s-line" size="20rpx" color="#999"></Icon>
				</view>
				<view class="contact-item" @click="viewAddress">
					<view class="contact-icon address">
						<Icon name="map-pin-line" size="32rpx" color="white"></Icon>
					</view>
					<view class="contact-info">
						<text class="contact-label">公司地址</text>
						<text class="contact-value">{{contactInfo.address}}</text>
					</view>
					<Icon name="arrow-right-s-line" size="20rpx" color="#999"></Icon>
				</view>
				<view class="contact-item" @click="viewWebsite">
					<view class="contact-icon website">
						<Icon name="global-line" size="32rpx" color="white"></Icon>
					</view>
					<view class="contact-info">
						<text class="contact-label">官方网站</text>
						<text class="contact-value">{{contactInfo.website}}</text>
					</view>
					<Icon name="arrow-right-s-line" size="20rpx" color="#999"></Icon>
				</view>
			</view>
		</view>

		<!-- 法律信息 -->
		<view class="legal-section">
			<view class="section-header">
				<Icon name="file-text-line" size="32rpx" color="#ff8a00"></Icon>
				<text class="section-title">法律信息</text>
			</view>
			<view class="legal-list">
				<view class="legal-item" @click="viewPrivacyPolicy">
					<text class="legal-title">隐私政策</text>
					<Icon name="arrow-right-s-line" size="20rpx" color="#999"></Icon>
				</view>
				<view class="legal-item" @click="viewUserAgreement">
					<text class="legal-title">用户协议</text>
					<Icon name="arrow-right-s-line" size="20rpx" color="#999"></Icon>
				</view>
				<view class="legal-item" @click="viewCopyright">
					<text class="legal-title">版权声明</text>
					<Icon name="arrow-right-s-line" size="20rpx" color="#999"></Icon>
				</view>
			</view>
		</view>

		<!-- 版权信息 -->
		<view class="copyright-section">
			<text class="copyright-text">© 2024 智慧养老科技有限公司</text>
			<text class="copyright-text">保留所有权利</text>
		</view>
	</view>
</template>

<script>
import Icon from '@/components/Icon/Icon.vue'

export default {
	components: {
		Icon
	},
	data() {
		return {
			appInfo: {
				version: '1.0.0',
				buildNumber: '20240118001'
			},
			contactInfo: {
				phone: '************',
				email: '<EMAIL>',
				address: '北京市朝阳区智慧养老大厦8层',
				website: 'www.yanglaofuwu.com'
			}
		}
	},
	methods: {
		// 个人信息管理功能
		editProfile() {
			uni.navigateTo({
				url: '/pages/profile/edit'
			});
		},
		manageContacts() {
			uni.navigateTo({
				url: '/pages/profile/emergency-contacts'
			});
		},
		healthProfile() {
			uni.navigateTo({
				url: '/pages/profile/health-profile'
			});
		},
		preferences() {
			uni.navigateTo({
				url: '/pages/profile/preferences'
			});
		},
		// 联系功能
		makeCall() {
			uni.makePhoneCall({
				phoneNumber: this.contactInfo.phone
			});
		},
		sendEmail() {
			// 复制邮箱地址到剪贴板
			uni.setClipboardData({
				data: this.contactInfo.email,
				success: () => {
					uni.showToast({
						title: '邮箱地址已复制',
						icon: 'success'
					});
				}
			});
		},
		viewAddress() {
			// 打开地图查看地址
			uni.openLocation({
				latitude: 39.9042,
				longitude: 116.4074,
				name: '智慧养老科技有限公司',
				address: this.contactInfo.address
			});
		},
		viewWebsite() {
			// 复制网站地址到剪贴板
			uni.setClipboardData({
				data: this.contactInfo.website,
				success: () => {
					uni.showToast({
						title: '网站地址已复制',
						icon: 'success'
					});
				}
			});
		},
		viewPrivacyPolicy() {
			uni.navigateTo({
				url: '/pages/legal/privacy-policy'
			});
		},
		viewUserAgreement() {
			uni.navigateTo({
				url: '/pages/legal/user-agreement'
			});
		},
		viewCopyright() {
			uni.navigateTo({
				url: '/pages/legal/copyright'
			});
		}
	}
}
</script>

<style scoped>
.container {
	background: #f8f9fa;
	min-height: 100vh;
	padding: 40rpx 0;
}

.app-info-section {
	background: white;
	margin: 0 40rpx 40rpx;
	border-radius: 30rpx;
	padding: 60rpx 40rpx;
	text-align: center;
	box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.app-logo {
	margin-bottom: 30rpx;
}

.app-name {
	font-size: 48rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 15rpx;
}

.app-slogan {
	font-size: 28rpx;
	color: #666;
	display: block;
	margin-bottom: 30rpx;
}

.app-version {
	display: flex;
	justify-content: center;
	gap: 30rpx;
}

.version-text, .build-text {
	font-size: 24rpx;
	color: #999;
	padding: 8rpx 20rpx;
	background: #f8f9fa;
	border-radius: 15rpx;
}

.profile-section, .contact-section, .legal-section {
	background: white;
	margin: 0 40rpx 40rpx;
	border-radius: 30rpx;
	padding: 40rpx;
	box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.section-header {
	display: flex;
	align-items: center;
	gap: 20rpx;
	margin-bottom: 30rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.profile-list {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

.profile-item {
	display: flex;
	align-items: center;
	gap: 20rpx;
	padding: 20rpx;
	background: #f8f9fa;
	border-radius: 20rpx;
}

.profile-info {
	flex: 1;
}

.profile-title {
	font-size: 28rpx;
	color: #333;
	font-weight: bold;
	display: block;
	margin-bottom: 5rpx;
}

.profile-desc {
	font-size: 24rpx;
	color: #666;
	display: block;
}

.contact-list, .legal-list {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

.contact-item, .legal-item {
	display: flex;
	align-items: center;
	gap: 20rpx;
	padding: 20rpx;
	background: #f8f9fa;
	border-radius: 20rpx;
}

.contact-icon {
	width: 60rpx;
	height: 60rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
}

.contact-icon.phone {
	background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);
}

.contact-icon.email {
	background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);
}

.contact-icon.address {
	background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
}

.contact-icon.website {
	background: linear-gradient(135deg, #9c27b0 0%, #7b1fa2 100%);
}

.contact-info {
	flex: 1;
}

.contact-label {
	font-size: 26rpx;
	color: #666;
	display: block;
	margin-bottom: 5rpx;
}

.contact-value {
	font-size: 28rpx;
	color: #333;
	display: block;
}

.legal-title {
	font-size: 28rpx;
	color: #333;
	flex: 1;
}

.copyright-section {
	text-align: center;
	padding: 40rpx;
}

.copyright-text {
	font-size: 24rpx;
	color: #999;
	display: block;
	margin-bottom: 10rpx;
}
</style>
