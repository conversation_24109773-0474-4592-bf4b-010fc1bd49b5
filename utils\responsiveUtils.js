/**
 * iOS风格响应式设计工具类
 * 基于iOS Human Interface Guidelines的设备适配
 * 支持iPhone、iPad等不同尺寸设备
 */

// iOS设备断点定义
export const IOS_BREAKPOINTS = {
  // iPhone设备
  IPHONE_SE: 375,           // iPhone SE (1st/2nd/3rd gen)
  IPHONE_STANDARD: 390,     // iPhone 12/13/14
  IPHONE_PLUS: 414,         // iPhone 6/7/8 Plus
  IPHONE_PRO: 393,          // iPhone 14 Pro
  IPHONE_PRO_MAX: 430,      // iPhone 14 Pro Max
  
  // iPad设备
  IPAD_MINI: 768,           // iPad Mini
  IPAD: 820,                // iPad (9th/10th gen)
  IPAD_AIR: 834,            // iPad Air
  IPAD_PRO_11: 834,         // iPad Pro 11"
  IPAD_PRO_12: 1024,        // iPad Pro 12.9"
  
  // 自定义断点
  SMALL: 375,               // 小屏设备
  MEDIUM: 768,              // 中等设备
  LARGE: 1024,              // 大屏设备
  XLARGE: 1366              // 超大屏设备
}

// iOS风格响应式配置
export const IOS_RESPONSIVE_CONFIG = {
  // 字体缩放配置
  typography: {
    // 小屏设备 (iPhone SE)
    small: {
      scaleRatio: 0.9,
      minFontSize: 28,      // rpx
      lineHeight: 1.4
    },
    // 标准设备 (iPhone 12/13/14)
    medium: {
      scaleRatio: 1.0,
      minFontSize: 32,      // rpx
      lineHeight: 1.5
    },
    // 大屏设备 (iPad)
    large: {
      scaleRatio: 1.2,
      minFontSize: 36,      // rpx
      lineHeight: 1.6
    },
    // 超大屏设备 (iPad Pro)
    xlarge: {
      scaleRatio: 1.4,
      minFontSize: 40,      // rpx
      lineHeight: 1.7
    }
  },

  // 间距缩放配置
  spacing: {
    small: {
      scaleRatio: 0.8,
      containerPadding: 16,  // rpx
      cardGap: 12           // rpx
    },
    medium: {
      scaleRatio: 1.0,
      containerPadding: 24,  // rpx
      cardGap: 16           // rpx
    },
    large: {
      scaleRatio: 1.3,
      containerPadding: 32,  // rpx
      cardGap: 24           // rpx
    },
    xlarge: {
      scaleRatio: 1.6,
      containerPadding: 48,  // rpx
      cardGap: 32           // rpx
    }
  },

  // 布局配置
  layout: {
    // 网格列数
    gridColumns: {
      small: 2,             // iPhone SE: 2列
      medium: 3,            // iPhone标准: 3列
      large: 4,             // iPad: 4列
      xlarge: 6             // iPad Pro: 6列
    },
    
    // 容器最大宽度
    maxWidth: {
      small: '100%',
      medium: '100%',
      large: '1024rpx',
      xlarge: '1366rpx'
    },

    // 导航栏高度
    navHeight: {
      small: 88,            // rpx
      medium: 88,           // rpx
      large: 96,            // rpx
      xlarge: 104           // rpx
    }
  },

  // 组件尺寸配置
  components: {
    // 按钮尺寸
    button: {
      small: {
        height: 72,         // rpx
        padding: '16rpx 24rpx',
        fontSize: 28,       // rpx
        borderRadius: 12    // rpx
      },
      medium: {
        height: 88,         // rpx
        padding: '20rpx 32rpx',
        fontSize: 32,       // rpx
        borderRadius: 16    // rpx
      },
      large: {
        height: 96,         // rpx
        padding: '24rpx 40rpx',
        fontSize: 36,       // rpx
        borderRadius: 20    // rpx
      },
      xlarge: {
        height: 112,        // rpx
        padding: '28rpx 48rpx',
        fontSize: 40,       // rpx
        borderRadius: 24    // rpx
      }
    },

    // 卡片尺寸
    card: {
      small: {
        padding: 24,        // rpx
        borderRadius: 16,   // rpx
        minHeight: 120      // rpx
      },
      medium: {
        padding: 32,        // rpx
        borderRadius: 20,   // rpx
        minHeight: 140      // rpx
      },
      large: {
        padding: 40,        // rpx
        borderRadius: 24,   // rpx
        minHeight: 160      // rpx
      },
      xlarge: {
        padding: 48,        // rpx
        borderRadius: 28,   // rpx
        minHeight: 180      // rpx
      }
    },

    // 图标尺寸
    icon: {
      small: {
        container: 48,      // rpx
        icon: 24,          // rpx
        borderRadius: 12   // rpx
      },
      medium: {
        container: 64,      // rpx
        icon: 32,          // rpx
        borderRadius: 16   // rpx
      },
      large: {
        container: 80,      // rpx
        icon: 40,          // rpx
        borderRadius: 20   // rpx
      },
      xlarge: {
        container: 96,      // rpx
        icon: 48,          // rpx
        borderRadius: 24   // rpx
      }
    }
  }
}

/**
 * 响应式设计管理类
 */
export class ResponsiveManager {
  constructor() {
    this.currentBreakpoint = 'medium'
    this.screenWidth = 375
    this.screenHeight = 667
    this.init()
  }

  /**
   * 初始化响应式管理器
   */
  init() {
    this.updateScreenInfo()
    this.updateBreakpoint()
  }

  /**
   * 更新屏幕信息
   */
  updateScreenInfo() {
    try {
      const systemInfo = uni.getSystemInfoSync()
      this.screenWidth = systemInfo.screenWidth
      this.screenHeight = systemInfo.screenHeight
      this.pixelRatio = systemInfo.pixelRatio || 2
      this.platform = systemInfo.platform
    } catch (error) {
      console.warn('获取屏幕信息失败:', error)
    }
  }

  /**
   * 更新当前断点
   */
  updateBreakpoint() {
    const width = this.screenWidth
    
    if (width < IOS_BREAKPOINTS.SMALL) {
      this.currentBreakpoint = 'small'
    } else if (width < IOS_BREAKPOINTS.MEDIUM) {
      this.currentBreakpoint = 'medium'
    } else if (width < IOS_BREAKPOINTS.LARGE) {
      this.currentBreakpoint = 'large'
    } else {
      this.currentBreakpoint = 'xlarge'
    }
  }

  /**
   * 获取当前断点
   */
  getCurrentBreakpoint() {
    return this.currentBreakpoint
  }

  /**
   * 检查是否为指定断点
   */
  isBreakpoint(breakpoint) {
    return this.currentBreakpoint === breakpoint
  }

  /**
   * 检查是否为小屏设备
   */
  isSmallScreen() {
    return this.currentBreakpoint === 'small'
  }

  /**
   * 检查是否为iPad设备
   */
  isTablet() {
    return this.currentBreakpoint === 'large' || this.currentBreakpoint === 'xlarge'
  }

  /**
   * 获取响应式配置
   */
  getConfig(category = null) {
    const config = IOS_RESPONSIVE_CONFIG
    const breakpoint = this.currentBreakpoint

    if (category) {
      return config[category] ? config[category][breakpoint] : null
    }

    return {
      typography: config.typography[breakpoint],
      spacing: config.spacing[breakpoint],
      layout: config.layout,
      components: config.components
    }
  }

  /**
   * 获取响应式字体大小
   */
  getResponsiveFontSize(baseFontSize) {
    const config = this.getConfig('typography')
    if (!config) return baseFontSize

    const scaledSize = baseFontSize * config.scaleRatio
    return Math.max(scaledSize, config.minFontSize)
  }

  /**
   * 获取响应式间距
   */
  getResponsiveSpacing(baseSpacing) {
    const config = this.getConfig('spacing')
    if (!config) return baseSpacing

    return baseSpacing * config.scaleRatio
  }

  /**
   * 获取网格列数
   */
  getGridColumns() {
    const config = this.getConfig('layout')
    return config ? config.gridColumns[this.currentBreakpoint] : 3
  }

  /**
   * 获取容器最大宽度
   */
  getMaxWidth() {
    const config = this.getConfig('layout')
    return config ? config.maxWidth[this.currentBreakpoint] : '100%'
  }

  /**
   * 获取组件尺寸配置
   */
  getComponentSize(component, property = null) {
    const config = this.getConfig('components')
    if (!config || !config[component]) return null

    const componentConfig = config[component][this.currentBreakpoint]
    
    if (property) {
      return componentConfig ? componentConfig[property] : null
    }

    return componentConfig
  }

  /**
   * 生成响应式CSS类名
   */
  getResponsiveClass(baseClass = '') {
    return `${baseClass} responsive-${this.currentBreakpoint}`
  }

  /**
   * 监听屏幕变化
   */
  onResize(callback) {
    // 在小程序中，屏幕尺寸通常不会动态变化
    // 但可以在页面显示时重新检查
    if (typeof callback === 'function') {
      this.updateScreenInfo()
      this.updateBreakpoint()
      callback(this.currentBreakpoint)
    }
  }
}

// 创建全局响应式管理器实例
export const responsiveManager = new ResponsiveManager()

// 导出工具函数
export const ResponsiveUtils = {
  /**
   * 获取当前断点
   */
  getCurrentBreakpoint() {
    return responsiveManager.getCurrentBreakpoint()
  },

  /**
   * 检查断点
   */
  isBreakpoint(breakpoint) {
    return responsiveManager.isBreakpoint(breakpoint)
  },

  /**
   * 获取响应式配置
   */
  getConfig(category) {
    return responsiveManager.getConfig(category)
  },

  /**
   * 获取响应式类名
   */
  getResponsiveClass(baseClass) {
    return responsiveManager.getResponsiveClass(baseClass)
  }
}

// 导出默认对象
export default {
  IOS_BREAKPOINTS,
  IOS_RESPONSIVE_CONFIG,
  ResponsiveManager,
  responsiveManager,
  ResponsiveUtils
}
