<template>
	<view class="container">
		<!-- 自定义导航栏 -->
		<view class="custom-navbar">
			<view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
			<view class="navbar-content">
				<view class="navbar-left" @click="goBack">
					<Icon name="arrow-left-line" size="36rpx" color="#333"></Icon>
					<text class="back-text">返回</text>
				</view>
				<view class="navbar-center">
					<text class="navbar-title">联系方式</text>
				</view>
				<view class="navbar-right">
					<view class="nav-action" @click="saveContactInfo">
						<text class="save-text">保存</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 主要联系方式 -->
		<view class="contact-section">
			<view class="section-header">
				<Icon name="phone-line" size="32rpx" color="#ff8a00"></Icon>
				<text class="section-title">主要联系方式</text>
			</view>
			
			<view class="contact-item">
				<text class="contact-label">手机号码</text>
				<view class="contact-value">
					<input class="contact-input" v-model="contactInfo.phone" placeholder="请输入手机号码" />
					<button class="verify-btn" @click="verifyPhone" :disabled="phoneVerified">
						{{phoneVerified ? '已验证' : '验证'}}
					</button>
				</view>
			</view>
			
			<view class="contact-item">
				<text class="contact-label">座机号码</text>
				<input class="contact-input" v-model="contactInfo.landline" placeholder="请输入座机号码（可选）" />
			</view>
			
			<view class="contact-item">
				<text class="contact-label">电子邮箱</text>
				<input class="contact-input" v-model="contactInfo.email" placeholder="请输入邮箱地址（可选）" />
			</view>
		</view>

		<!-- 居住地址 -->
		<view class="address-section">
			<view class="section-header">
				<Icon name="map-pin-line" size="32rpx" color="#ff8a00"></Icon>
				<text class="section-title">居住地址</text>
			</view>
			
			<view class="address-item">
				<text class="address-label">详细地址</text>
				<textarea class="address-textarea" v-model="contactInfo.address" placeholder="请输入详细居住地址"></textarea>
			</view>
			
			<view class="address-item">
				<text class="address-label">地址标签</text>
				<view class="address-tags">
					<view class="tag-item" :class="{ active: contactInfo.addressTag === tag }" v-for="tag in addressTags" :key="tag" @click="selectAddressTag(tag)">
						<text class="tag-text">{{tag}}</text>
					</view>
				</view>
			</view>
			
			<button class="location-btn" @click="getCurrentLocation">
				<Icon name="map-pin-2-line" size="32rpx" color="#ff8a00"></Icon>
				<text>获取当前位置</text>
			</button>
		</view>

		<!-- 紧急联系人 -->
		<view class="emergency-section">
			<view class="section-header">
				<Icon name="alarm-warning-line" size="32rpx" color="#f44336"></Icon>
				<text class="section-title">紧急联系人</text>
				<button class="add-btn" @click="addEmergencyContact">
					<Icon name="add-line" size="24rpx" color="#ff8a00"></Icon>
				</button>
			</view>
			
			<view class="emergency-list">
				<view class="emergency-item" v-for="(contact, index) in emergencyContacts" :key="index">
					<view class="emergency-info">
						<text class="emergency-name">{{contact.name}}</text>
						<text class="emergency-relation">{{contact.relation}}</text>
						<text class="emergency-phone">{{contact.phone}}</text>
					</view>
					<view class="emergency-actions">
						<button class="action-btn call" @click="callEmergencyContact(contact)">
							<Icon name="phone-line" size="24rpx" color="white"></Icon>
						</button>
						<button class="action-btn edit" @click="editEmergencyContact(index)">
							<Icon name="edit-line" size="24rpx" color="white"></Icon>
						</button>
						<button class="action-btn delete" @click="deleteEmergencyContact(index)">
							<Icon name="delete-bin-line" size="24rpx" color="white"></Icon>
						</button>
					</view>
				</view>
			</view>
		</view>

		<!-- 通知偏好 -->
		<view class="notification-section">
			<view class="section-header">
				<Icon name="notification-3-line" size="32rpx" color="#ff8a00"></Icon>
				<text class="section-title">通知偏好</text>
			</view>
			
			<view class="notification-item">
				<text class="notification-label">短信通知</text>
				<switch :checked="notificationSettings.sms" @change="onSmsChange" color="#ff8a00" />
			</view>
			
			<view class="notification-item">
				<text class="notification-label">电话通知</text>
				<switch :checked="notificationSettings.call" @change="onCallChange" color="#ff8a00" />
			</view>
			
			<view class="notification-item">
				<text class="notification-label">应用推送</text>
				<switch :checked="notificationSettings.push" @change="onPushChange" color="#ff8a00" />
			</view>
		</view>

		<!-- 保存按钮 -->
		<view class="save-section">
			<button class="save-btn" @click="saveContactInfo">保存联系信息</button>
		</view>
	</view>
</template>

<script>
import Icon from '@/components/Icon/Icon.vue'

export default {
	components: {
		Icon
	},
	data() {
		return {
			statusBarHeight: 0,
			phoneVerified: false,
			contactInfo: {
				phone: '138****5678',
				landline: '',
				email: '',
				address: '北京市朝阳区建国路88号',
				addressTag: '家'
			},
			addressTags: ['家', '单位', '其他'],
			emergencyContacts: [
				{
					name: '张小明',
					relation: '儿子',
					phone: '139****1234'
				},
				{
					name: '李小红',
					relation: '女儿',
					phone: '137****5678'
				}
			],
			notificationSettings: {
				sms: true,
				call: true,
				push: true
			}
		}
	},
	onLoad() {
		// 获取状态栏高度
		const systemInfo = uni.getSystemInfoSync();
		this.statusBarHeight = systemInfo.statusBarHeight || 44;

		this.loadContactInfo();
	},
	methods: {
		loadContactInfo() {
			// 从本地存储加载联系信息
			const savedInfo = uni.getStorageSync('contactInfo');
			if (savedInfo) {
				this.contactInfo = { ...this.contactInfo, ...savedInfo };
			}
		},
		verifyPhone() {
			if (!this.contactInfo.phone) {
				uni.showToast({
					title: '请先输入手机号码',
					icon: 'none'
				});
				return;
			}
			
			uni.showToast({
				title: '验证码已发送',
				icon: 'success'
			});
			
			// 模拟验证成功
			setTimeout(() => {
				this.phoneVerified = true;
				uni.showToast({
					title: '手机号验证成功',
					icon: 'success'
				});
			}, 2000);
		},
		selectAddressTag(tag) {
			this.contactInfo.addressTag = tag;
		},
		getCurrentLocation() {
			uni.getLocation({
				type: 'gcj02',
				success: (res) => {
					// 这里可以调用地图API获取详细地址
					uni.showToast({
						title: '位置获取成功',
						icon: 'success'
					});
				},
				fail: () => {
					uni.showToast({
						title: '位置获取失败',
						icon: 'none'
					});
				}
			});
		},
		addEmergencyContact() {
			uni.navigateTo({
				url: '/pages/profile/emergency-edit'
			});
		},
		editEmergencyContact(index) {
			uni.navigateTo({
				url: `/pages/profile/emergency-edit?index=${index}`
			});
		},
		deleteEmergencyContact(index) {
			uni.showModal({
				title: '确认删除',
				content: '确定要删除这个紧急联系人吗？',
				success: (res) => {
					if (res.confirm) {
						this.emergencyContacts.splice(index, 1);
						uni.showToast({
							title: '删除成功',
							icon: 'success'
						});
					}
				}
			});
		},
		callEmergencyContact(contact) {
			uni.makePhoneCall({
				phoneNumber: contact.phone.replace(/\*/g, '')
			});
		},
		onSmsChange(e) {
			this.notificationSettings.sms = e.detail.value;
		},
		onCallChange(e) {
			this.notificationSettings.call = e.detail.value;
		},
		onPushChange(e) {
			this.notificationSettings.push = e.detail.value;
		},
		saveContactInfo() {
			// 验证必填字段
			if (!this.contactInfo.phone) {
				uni.showToast({
					title: '请输入手机号码',
					icon: 'none'
				});
				return;
			}
			
			// 保存到本地存储
			uni.setStorageSync('contactInfo', this.contactInfo);
			uni.setStorageSync('emergencyContacts', this.emergencyContacts);
			uni.setStorageSync('notificationSettings', this.notificationSettings);
			
			uni.showToast({
				title: '保存成功',
				icon: 'success'
			});
			
			setTimeout(() => {
				this.goBack();
			}, 1500);
		},

		// 返回上一页
		goBack() {
			uni.navigateBack({
				fail: () => {
					uni.switchTab({
						url: '/pages/profile/profile'
					});
				}
			});
		}
	}
}
</script>

<style scoped>
.container {
	background: #f8f9fa;
	min-height: 100vh;
	padding-bottom: 200rpx;
}

/* 导航栏样式 */
.custom-navbar {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 1000;
	background: rgba(255, 255, 255, 0.95);
	backdrop-filter: blur(20rpx);
	-webkit-backdrop-filter: blur(20rpx);
	border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);
}

.navbar-content {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 20rpx 32rpx;
	min-height: 88rpx;
}

.navbar-left {
	display: flex;
	align-items: center;
	gap: 12rpx;
	padding: 8rpx 16rpx 8rpx 0;
	border-radius: 20rpx;
	transition: all 0.2s ease;
}

.navbar-left:active {
	background-color: rgba(0, 0, 0, 0.04);
	transform: scale(0.96);
}

.back-text {
	font-size: 32rpx;
	color: #333;
	font-weight: 500;
}

.navbar-center {
	position: absolute;
	left: 50%;
	transform: translateX(-50%);
}

.navbar-title {
	font-size: 34rpx;
	font-weight: 600;
	color: #333;
}

.navbar-right {
	display: flex;
	align-items: center;
}

.nav-action {
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
	transition: all 0.2s ease;
}

.nav-action:active {
	background-color: rgba(255, 138, 0, 0.1);
	transform: scale(0.96);
}

.save-text {
	font-size: 32rpx;
	color: #ff8a00;
	font-weight: 600;
}

.contact-section, .address-section, .emergency-section, .notification-section {
	background: white;
	margin: 20rpx;
	border-radius: 30rpx;
	padding: 40rpx;
}

.contact-section {
	margin-top: 220rpx; /* 为导航栏留出空间 */
}

.section-header {
	display: flex;
	align-items: center;
	gap: 15rpx;
	margin-bottom: 30rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	flex: 1;
}

.add-btn {
	width: 60rpx;
	height: 60rpx;
	background: rgba(255, 138, 0, 0.1);
	border: 2rpx solid #ff8a00;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
}

.contact-item, .address-item, .notification-item {
	margin-bottom: 30rpx;
}

.contact-item:last-child, .address-item:last-child, .notification-item:last-child {
	margin-bottom: 0;
}

.contact-label, .address-label, .notification-label {
	font-size: 28rpx;
	color: #333;
	font-weight: bold;
	display: block;
	margin-bottom: 15rpx;
}

.contact-value {
	display: flex;
	gap: 15rpx;
	align-items: center;
}

.contact-input {
	flex: 1;
	background: #f8f9fa;
	border: 2rpx solid #e0e0e0;
	border-radius: 15rpx;
	padding: 25rpx 20rpx;
	font-size: 28rpx;
	color: #333;
}

.verify-btn {
	background: #ff8a00;
	color: white;
	border: none;
	border-radius: 15rpx;
	padding: 25rpx 30rpx;
	font-size: 24rpx;
}

.verify-btn:disabled {
	background: #4caf50;
}

.address-textarea {
	background: #f8f9fa;
	border: 2rpx solid #e0e0e0;
	border-radius: 15rpx;
	padding: 25rpx 20rpx;
	font-size: 28rpx;
	color: #333;
	min-height: 120rpx;
}

.address-tags {
	display: flex;
	gap: 15rpx;
}

.tag-item {
	padding: 15rpx 30rpx;
	background: #f8f9fa;
	border: 2rpx solid #e0e0e0;
	border-radius: 20rpx;
}

.tag-item.active {
	background: rgba(255, 138, 0, 0.1);
	border-color: #ff8a00;
}

.tag-text {
	font-size: 26rpx;
	color: #666;
}

.tag-item.active .tag-text {
	color: #ff8a00;
}

.location-btn {
	width: 100%;
	height: 80rpx;
	background: rgba(255, 138, 0, 0.1);
	border: 2rpx solid #ff8a00;
	border-radius: 15rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 10rpx;
	font-size: 28rpx;
	color: #ff8a00;
	margin-top: 20rpx;
}

.emergency-list {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

.emergency-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 25rpx;
	background: #f8f9fa;
	border-radius: 20rpx;
}

.emergency-info {
	flex: 1;
}

.emergency-name {
	font-size: 28rpx;
	color: #333;
	font-weight: bold;
	display: block;
	margin-bottom: 5rpx;
}

.emergency-relation {
	font-size: 24rpx;
	color: #666;
	display: block;
	margin-bottom: 5rpx;
}

.emergency-phone {
	font-size: 26rpx;
	color: #ff8a00;
	font-weight: bold;
	display: block;
}

.emergency-actions {
	display: flex;
	gap: 10rpx;
}

.action-btn {
	width: 60rpx;
	height: 60rpx;
	border-radius: 50%;
	border: none;
	display: flex;
	align-items: center;
	justify-content: center;
}

.action-btn.call {
	background: #4caf50;
}

.action-btn.edit {
	background: #ff9800;
}

.action-btn.delete {
	background: #f44336;
}

.notification-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20rpx 0;
}

.save-section {
	padding: 40rpx;
}

.save-btn {
	width: 100%;
	height: 100rpx;
	background: linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%);
	color: white;
	border: none;
	border-radius: 20rpx;
	font-size: 32rpx;
	font-weight: bold;
}
</style>
