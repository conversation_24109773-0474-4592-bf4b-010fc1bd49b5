<template>
	<view class="container">
		<!-- 页面头部 -->
		<PageHeader title="选机构" />

		<!-- 搜索和可折叠筛选 -->
		<view class="search-filter-section">
			<!-- 搜索栏 -->
			<view class="search-bar">
				<view class="search-input-wrapper">
					<Icon name="search-line" size="32rpx" color="#999" class="search-icon" />
					<input
						class="search-input"
						type="text"
						placeholder="搜索养老机构"
						v-model="searchKeyword"
						@input="onSearchInput"
						@confirm="performSearch"
					/>
					<Icon
						v-if="searchKeyword"
						name="close-line"
						size="32rpx"
						color="#999"
						class="clear-icon"
						@click="clearSearch"
					/>
				</view>
				<InteractiveButton
					type="primary"
					size="medium"
					text="搜索"
					@click="performSearch"
				/>
			</view>

			<!-- 筛选按钮 -->
			<view class="filter-toggle" @click="toggleFilter">
				<Icon name="filter-line" size="28rpx" color="#666" />
				<text class="filter-toggle-text">筛选</text>
				<Icon
					:name="showFilter ? 'arrow-up-s-line' : 'arrow-down-s-line'"
					size="24rpx"
					color="#666"
					class="filter-arrow"
				/>
				<view v-if="hasActiveFilters" class="filter-badge"></view>
			</view>

			<!-- 可折叠筛选面板 -->
			<view class="filter-panel" :class="{ 'filter-panel-show': showFilter }">
				<view class="filter-content">
					<!-- 机构类型 -->
					<view class="filter-group">
						<text class="filter-group-title">机构类型</text>
						<view class="filter-options">
							<view
								class="filter-option"
								:class="{ active: filterData.type === type.value }"
								v-for="(type, index) in typeOptions"
								:key="index"
								@click="selectFilter('type', type.value)"
							>
								<text>{{type.label}}</text>
							</view>
						</view>
					</view>

					<!-- 价格范围 -->
					<view class="filter-group">
						<text class="filter-group-title">价格范围</text>
						<view class="filter-options">
							<view
								class="filter-option"
								:class="{ active: filterData.price === price.value }"
								v-for="(price, index) in priceOptions"
								:key="index"
								@click="selectFilter('price', price.value)"
							>
								<text>{{price.label}}</text>
							</view>
						</view>
					</view>

					<!-- 距离 -->
					<view class="filter-group">
						<text class="filter-group-title">距离</text>
						<view class="filter-options">
							<view
								class="filter-option"
								:class="{ active: filterData.distance === distance.value }"
								v-for="(distance, index) in distanceOptions"
								:key="index"
								@click="selectFilter('distance', distance.value)"
							>
								<text>{{distance.label}}</text>
							</view>
						</view>
					</view>

					<!-- 排序方式 -->
					<view class="filter-group">
						<text class="filter-group-title">排序方式</text>
						<view class="filter-options">
							<view
								class="filter-option"
								:class="{ active: sortType === sort.value }"
								v-for="(sort, index) in sortOptions"
								:key="index"
								@click="selectSort(sort.value)"
							>
								<text>{{sort.label}}</text>
							</view>
						</view>
					</view>

					<!-- 筛选操作按钮 -->
					<view class="filter-actions">
						<InteractiveButton
							type="secondary"
							size="medium"
							text="重置"
							@click="resetAllFilters"
						/>
						<InteractiveButton
							type="primary"
							size="medium"
							text="确定"
							@click="applyFilters"
						/>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 机构列表 -->
		<scroll-view
			scroll-y="true"
			class="institution-list"
			@scrolltolower="loadMore"
			refresher-enabled="true"
			@refresherrefresh="refreshData"
			:refresher-triggered="refreshing"
		>
			<InteractiveCard
				v-for="(item, index) in institutionList"
				:key="index"
				class="institution-item"
				:loading="false"
				@click="viewDetail(item)"
			>
				<view class="institution-image-container">
					<image
						:src="item.image"
						class="institution-image"
						mode="aspectFill"
						@error="handleImageError"
						@load="handleImageLoad"
					/>
					<view class="image-overlay" v-if="!item.image">
						<Icon name="building-line" size="60rpx" color="#ccc" />
					</view>
				</view>
				<view class="institution-content">
					<view class="institution-header">
						<view class="header-left">
							<text class="institution-name">{{item.name}}</text>
							<view class="institution-type" v-if="item.type">
								<text class="type-text">{{item.type}}</text>
							</view>
						</view>
						<view class="institution-rating">
							<Icon name="star-fill" size="28rpx" color="#ff8a00" />
							<text class="rating-score">{{item.rating}}</text>
						</view>
					</view>
					<text class="institution-address">{{item.address}}</text>
					<view class="institution-tags" v-if="item.tags && item.tags.length">
						<text
							class="tag"
							v-for="(tag, tagIndex) in item.tags"
							:key="tagIndex"
						>{{tag}}</text>
					</view>
					<view class="institution-info">
						<view class="info-row">
							<text class="info-item beds">
								<Icon name="hotel-bed-line" size="24rpx" color="#666" />
								床位: {{item.availableBeds}}/{{item.beds}}张
							</text>
							<text class="info-item distance">
								<Icon name="location-line" size="24rpx" color="#666" />
								{{item.distance}}
							</text>
						</view>
						<view class="info-row">
							<text class="info-item price">
								<Icon name="money-cny-circle-line" size="24rpx" color="#ff8a00" />
								{{item.price || '¥3000-5000/月起'}}
							</text>
							<text class="info-item level" v-if="item.level">
								<Icon name="award-line" size="24rpx" color="#4caf50" />
								{{item.level}}机构
							</text>
						</view>
					</view>
					<view class="institution-actions">
						<InteractiveButton
							type="secondary"
							size="medium"
							text="电话咨询"
							icon="phone-line"
							@click.stop="callInstitution(item)"
						/>
						<InteractiveButton
							type="primary"
							size="medium"
							text="预约参观"
							icon="calendar-line"
							@click.stop="bookVisit(item)"
						/>
					</view>
				</view>
			</InteractiveCard>
			
			<!-- 加载更多 -->
			<view class="load-more" v-if="hasMore">
				<text v-if="loading">加载中...</text>
				<text v-else>上拉加载更多</text>
			</view>
			<view class="no-more" v-else-if="institutionList.length > 0">
				<text>没有更多数据了</text>
			</view>
			<view class="empty" v-else-if="!loading">
				<view class="empty-icon">
					<Icon name="building-line" size="120rpx" color="#ccc" />
				</view>
				<text class="empty-text">暂无机构信息</text>
			</view>
		</scroll-view>
		

	</view>
</template>

<script>
import InteractiveCard from '@/components/InteractiveCard/InteractiveCard.vue'
import InteractiveButton from '@/components/InteractiveButton/InteractiveButton.vue'
import Icon from '@/components/Icon/Icon.vue'
import PageHeader from '@/components/PageHeader/PageHeader.vue'
import FeedbackUtils from '@/utils/feedback.js'
import MockAPI from '@/utils/mockData.js'
import OfflineDataManager from '@/utils/offlineData.js'
import InteractionUtils from '@/utils/interactionUtils.js'

export default {
	components: {
		InteractiveCard,
		InteractiveButton,
		Icon,
		PageHeader
	},
	data() {
		return {
			searchKeyword: '',
			institutionList: [],
			loading: false,
			refreshing: false,
			hasMore: true,
			page: 1,
			pageSize: 10,
			sortType: 'default',
			showFilter: false, // 控制筛选面板显示
			filterData: {
				type: '',
				price: '',
				distance: ''
			},
			// 搜索筛选组件的分类选项
			categories: [
				{ label: '全部', value: '' },
				{ label: '养老院', value: 'nursing_home' },
				{ label: '老年公寓', value: 'apartment' },
				{ label: '护理院', value: 'care_home' },
				{ label: '日间照料', value: 'day_care' }
			],
			typeOptions: [
				{ label: '全部', value: '' },
				{ label: '养老院', value: 'nursing_home' },
				{ label: '老年公寓', value: 'apartment' },
				{ label: '护理院', value: 'care_home' },
				{ label: '日间照料', value: 'day_care' }
			],
			priceOptions: [
				{ label: '全部', value: '' },
				{ label: '2000以下', value: '0-2000' },
				{ label: '2000-5000', value: '2000-5000' },
				{ label: '5000-8000', value: '5000-8000' },
				{ label: '8000以上', value: '8000-' }
			],
			distanceOptions: [
				{ label: '全部', value: '' },
				{ label: '1公里内', value: '0-1' },
				{ label: '3公里内', value: '0-3' },
				{ label: '5公里内', value: '0-5' },
				{ label: '10公里内', value: '0-10' }
			],
			sortOptions: [
				{ label: '默认排序', value: 'default' },
				{ label: '距离最近', value: 'distance' },
				{ label: '评分最高', value: 'rating' },
				{ label: '价格最低', value: 'price_asc' },
				{ label: '价格最高', value: 'price_desc' }
			]
		}
	},
	computed: {
		// 检查是否有活跃的筛选条件
		hasActiveFilters() {
			return this.filterData.type ||
				   this.filterData.price ||
				   this.filterData.distance ||
				   this.sortType !== 'default'
		}
	},
	onLoad() {
		// 初始化离线数据
		OfflineDataManager.initOfflineData();
		this.loadInstitutions();
	},
	methods: {
		async loadInstitutions() {
			if (this.loading) return;

			// 直接使用离线数据，确保100%可用性
			try {
				const params = {
					page: this.page,
					pageSize: this.pageSize,
					keyword: this.searchKeyword,
					sort: this.sortType,
					...this.filterData
				};

				const result = OfflineDataManager.getOfflineInstitutions(params);

				if (this.page === 1) {
					this.institutionList = result.data;
				} else {
					this.institutionList.push(...result.data);
				}

				this.hasMore = result.data.length === this.pageSize;
				this.loading = false;
				this.refreshing = false;

				// 显示成功提示
				if (this.page === 1 && result.data.length > 0) {
					FeedbackUtils.showSuccess(`已加载 ${result.data.length} 个机构`);
				}
			} catch (error) {
				this.loading = false;
				this.refreshing = false;
				FeedbackUtils.showError('数据加载失败，请重试');
				console.error('加载机构数据失败:', error);
			}
		},

		// 搜索输入处理
		onSearchInput(e) {
			this.searchKeyword = e.detail.value;
		},

		// 执行搜索
		performSearch() {
			FeedbackUtils.lightFeedback();
			this.resetAndLoad();
		},

		// 清空搜索
		clearSearch() {
			this.searchKeyword = '';
			this.resetAndLoad();
		},

		// 切换筛选面板
		toggleFilter() {
			FeedbackUtils.lightFeedback();
			this.showFilter = !this.showFilter;
		},

		// 图片加载错误处理
		handleImageError(e) {
			console.log('图片加载失败:', e);
		},

		// 图片加载成功处理
		handleImageLoad(e) {
			console.log('图片加载成功:', e);
		},
		// 选择筛选条件
		selectFilter(key, value) {
			FeedbackUtils.lightFeedback();
			this.filterData[key] = this.filterData[key] === value ? '' : value;
		},

		// 选择排序方式
		selectSort(value) {
			FeedbackUtils.lightFeedback();
			this.sortType = value;
		},

		// 重置所有筛选条件
		resetAllFilters() {
			FeedbackUtils.lightFeedback();
			this.filterData = {
				type: '',
				price: '',
				distance: ''
			};
			this.sortType = 'default';
		},

		// 应用筛选条件
		applyFilters() {
			return InteractionUtils.handleAsyncOperation({
				operation: async () => {
					this.showFilter = false;
					this.resetAndLoad();
					return true;
				},
				loadingText: '应用筛选条件...',
				successText: '筛选完成',
				errorText: '筛选失败，请重试',
				operationId: 'apply_filters'
			});
		},

		// 重置并加载
		resetAndLoad() {
			this.page = 1;
			this.hasMore = true;
			this.institutionList = [];
			this.loadInstitutions();
		},

		// 下拉刷新
		refreshData() {
			FeedbackUtils.lightFeedback();
			this.refreshing = true;
			this.resetAndLoad();
		},

		// 上拉加载更多
		loadMore() {
			if (this.hasMore && !this.loading) {
				this.page++;
				this.loadInstitutions();
			}
		},

		// 查看详情
		viewDetail(item) {
			return InteractionUtils.handleNavigation({
				url: `/pages/institution/detail`,
				params: { id: item.id },
				loadingText: '加载机构详情...',
				beforeNavigate: () => {
					console.log('准备跳转到机构详情页:', item.name);
					return true;
				},
				afterNavigate: () => {
					console.log('成功跳转到机构详情页');
				}
			});
		},

		// 拨打电话
		async callInstitution(item) {
			try {
				await FeedbackUtils.showConfirm({
					title: '拨打电话',
					content: `即将拨打 ${item.name} 的电话：${item.phone}`,
					confirmText: '拨打',
					cancelText: '取消'
				});

				FeedbackUtils.mediumFeedback();
				uni.makePhoneCall({
					phoneNumber: item.phone,
					success: () => {
						console.log('拨打电话成功');
					},
					fail: (err) => {
						console.error('拨打电话失败:', err);
						FeedbackUtils.showError('拨打电话失败');
					}
				});
			} catch (error) {
				console.log('用户取消拨打');
			}
		},

		// 预约参观
		bookVisit(item) {
			FeedbackUtils.lightFeedback();
			FeedbackUtils.showLoading('正在预约...');

			// 模拟预约过程
			setTimeout(() => {
				FeedbackUtils.hideLoading();
				FeedbackUtils.showSuccess('预约成功，工作人员将联系您');

				// 这里可以跳转到预约详情页面
				// uni.navigateTo({
				//     url: `/pages/institution/book?id=${item.id}`
				// });
			}, 1500);
		},
		showFilterModal() {
			this.$refs.filterPopup.open();
		},
		closeFilterModal() {
			this.$refs.filterPopup.close();
		},
		showSortModal() {
			this.$refs.sortPopup.open();
		},
		closeSortModal() {
			this.$refs.sortPopup.close();
		},
		selectFilter(key, value) {
			this.filterData[key] = this.filterData[key] === value ? '' : value;
		},
		resetFilter() {
			this.filterData = {
				type: '',
				price: '',
				distance: ''
			};
		},
		applyFilter() {
			return InteractionUtils.handleAsyncOperation({
				operation: async () => {
					this.page = 1;
					this.hasMore = true;
					await this.loadInstitutions();
					this.closeFilterModal();
					return true;
				},
				loadingText: '应用筛选条件...',
				successText: '筛选完成',
				errorText: '筛选失败，请重试',
				operationId: 'apply_filter'
			});
		},
		selectSort(value) {
			this.sortType = value;
			this.page = 1;
			this.hasMore = true;
			this.loadInstitutions();
			this.closeSortModal();
		},
		getSortText() {
			const option = this.sortOptions.find(item => item.value === this.sortType);
			return option ? option.label : '默认排序';
		}
	}
}
</script>

<style scoped>
.container {
	background-color: #f5f5f5;
	height: 100vh;
	display: flex;
	flex-direction: column;
}

/* 搜索和筛选区域 */
.search-filter-section {
	background-color: #fff;
	border-bottom: 1rpx solid #eee;
}

.search-bar {
	display: flex;
	align-items: center;
	padding: 20rpx;
	gap: 20rpx;
}

.search-input-wrapper {
	flex: 1;
	position: relative;
	display: flex;
	align-items: center;
	background-color: #f8f9fa;
	border-radius: 50rpx;
	padding: 0 30rpx;
	height: 80rpx;
}

.search-icon {
	margin-right: 20rpx;
}

.search-input {
	flex: 1;
	border: none;
	background: transparent;
	font-size: 30rpx;
	color: #333;
}

.search-input::placeholder {
	color: #999;
	font-size: 28rpx;
}

.clear-icon {
	margin-left: 20rpx;
	cursor: pointer;
}

/* 筛选切换按钮 */
.filter-toggle {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 20rpx;
	border-top: 1rpx solid #f0f0f0;
	background-color: #fafafa;
	position: relative;
	cursor: pointer;
	transition: background-color 0.3s ease;
}

.filter-toggle:active {
	background-color: #f0f0f0;
}

.filter-toggle-text {
	margin: 0 15rpx;
	font-size: 28rpx;
	color: #666;
}

.filter-arrow {
	transition: transform 0.3s ease;
}

.filter-badge {
	position: absolute;
	top: 15rpx;
	right: 15rpx;
	width: 16rpx;
	height: 16rpx;
	background-color: #ff8a00;
	border-radius: 50%;
}

/* 筛选面板 */
.filter-panel {
	max-height: 0;
	overflow: hidden;
	transition: max-height 0.3s ease;
	background-color: #fff;
}

.filter-panel-show {
	max-height: 1000rpx;
}

.filter-content {
	padding: 30rpx;
}

.filter-group {
	margin-bottom: 40rpx;
}

.filter-group:last-child {
	margin-bottom: 0;
}

.filter-group-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 20rpx;
	display: block;
}

.filter-options {
	display: flex;
	flex-wrap: wrap;
	gap: 20rpx;
}

.filter-option {
	padding: 16rpx 32rpx;
	border: 2rpx solid #e0e0e0;
	border-radius: 50rpx;
	font-size: 28rpx;
	color: #666;
	background-color: #fff;
	cursor: pointer;
	transition: all 0.3s ease;
}

.filter-option.active {
	border-color: #ff8a00;
	background-color: #ff8a00;
	color: #fff;
}

.filter-option:active {
	transform: scale(0.95);
}

.filter-actions {
	display: flex;
	gap: 20rpx;
	margin-top: 40rpx;
	padding-top: 30rpx;
	border-top: 1rpx solid #f0f0f0;
}

.institution-list {
	flex: 1;
	padding: 20rpx;
}

.institution-item {
	background-color: #fff;
	border-radius: 20rpx;
	margin-bottom: 20rpx;
	overflow: hidden;
	box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.08);
	transition: all 0.3s ease;
}

.institution-item:active {
	transform: translateY(2rpx);
	box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

/* 图片容器优化 */
.institution-image-container {
	position: relative;
	width: 100%;
	height: 300rpx;
	background-color: #f5f5f5;
}

.institution-image {
	width: 100%;
	height: 100%;
	object-fit: cover;
}

.image-overlay {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	display: flex;
	align-items: center;
	justify-content: center;
	background-color: #f8f9fa;
}

.institution-content {
	padding: 30rpx;
}

.institution-header {
	display: flex;
	align-items: flex-start;
	justify-content: space-between;
	margin-bottom: 20rpx;
}

.header-left {
	flex: 1;
	display: flex;
	flex-direction: column;
	gap: 12rpx;
}

/* 增强机构名称显示 */
.institution-name {
	font-size: 36rpx;
	font-weight: 700;
	color: #1a1a1a;
	line-height: 1.4;
	letter-spacing: 0.5rpx;
}

.institution-type {
	align-self: flex-start;
	padding: 6rpx 16rpx;
	background: rgba(255, 138, 0, 0.12);
	border-radius: 12rpx;
	border: 1rpx solid rgba(255, 138, 0, 0.2);
}

.type-text {
	font-size: 22rpx;
	color: #ff8a00;
	font-weight: 600;
}

/* 增强评分显示 */
.institution-rating {
	display: flex;
	align-items: center;
	gap: 6rpx;
	flex-shrink: 0;
	padding: 8rpx 12rpx;
	background: rgba(255, 138, 0, 0.08);
	border-radius: 20rpx;
}

.rating-score {
	font-size: 32rpx;
	color: #ff8a00;
	font-weight: 700;
}

/* 增强地址显示 */
.institution-address {
	font-size: 28rpx;
	color: #555;
	margin-bottom: 18rpx;
	line-height: 1.5;
}

.institution-tags {
	display: flex;
	gap: 10rpx;
	margin-bottom: 20rpx;
}

.tag {
	padding: 8rpx 16rpx;
	background-color: rgba(255, 138, 0, 0.1);
	color: #ff8a00;
	font-size: 22rpx;
	border-radius: 15rpx;
}

/* 增强信息显示区域 */
.institution-info {
	display: flex;
	flex-direction: column;
	gap: 12rpx;
	margin-bottom: 25rpx;
	padding: 20rpx;
	background-color: #fafbfc;
	border-radius: 16rpx;
	border: 1rpx solid #f0f0f0;
}

.info-row {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.info-item {
	display: flex;
	align-items: center;
	gap: 8rpx;
	font-size: 26rpx;
	color: #666;
	font-weight: 500;
}

/* 增强床位信息显示 */
.info-item.beds {
	color: #555;
	font-weight: 600;
}

/* 增强距离信息显示 */
.info-item.distance {
	color: #666;
	font-weight: 600;
}

/* 增强价格信息显示 */
.info-item.price {
	color: #ff8a00;
	font-weight: 700;
	font-size: 30rpx;
}

/* 增强等级信息显示 */
.info-item.level {
	color: #4caf50;
	font-weight: 600;
}

.institution-actions {
	display: flex;
	gap: 20rpx;
}

.action-btn {
	flex: 1;
	height: 70rpx;
	border-radius: 35rpx;
	font-size: 28rpx;
	border: 1rpx solid #ddd;
	background-color: #fff;
	color: #666;
}

.action-btn.primary {
	background: linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%);
	color: #fff;
	border-color: #ff8a00;
}

.load-more, .no-more {
	text-align: center;
	padding: 30rpx;
	font-size: 28rpx;
	color: #999;
}

.empty {
	text-align: center;
	padding: 100rpx 0;
}

.empty-icon {
	margin-bottom: 30rpx;
}

.empty-text {
	font-size: 28rpx;
	color: #999;
}


</style>
