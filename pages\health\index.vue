<template>
	<view class="container">
		<!-- 头部横幅 -->
		<view class="header-banner">
			<view class="banner-content">
				<view class="banner-text">
					<text class="banner-title">健康管理</text>
					<text class="banner-subtitle">专业健康监测，守护您的健康</text>
				</view>
				<view class="banner-icon">
					<Icon name="health-book-line" size="80rpx" color="white"></Icon>
				</view>
			</view>
		</view>

		<!-- 健康概览 -->
		<view class="section">
			<view class="section-header">
				<text class="section-title">健康概览</text>
				<text class="section-subtitle">今日健康数据</text>
			</view>
			<view class="health-overview">
				<view class="health-card" v-for="(item, index) in healthData" :key="index" @click="viewHealthDetail(item)">
					<view class="health-icon" :style="{backgroundColor: item.color}">
						<Icon :name="item.icon" size="40rpx" color="white"></Icon>
					</view>
					<view class="health-content">
						<text class="health-label">{{item.label}}</text>
						<text class="health-value">{{item.value}}</text>
						<text class="health-unit">{{item.unit}}</text>
					</view>
					<view class="health-status" :class="item.status">
						<text class="status-text">{{item.statusText}}</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 健康服务 -->
		<view class="section">
			<view class="section-header">
				<text class="section-title">健康服务</text>
				<text class="section-subtitle">专业的健康管理服务</text>
			</view>
			<view class="service-grid">
				<view class="service-item" @click="navigateToService('checkup')">
					<view class="service-icon checkup">
						<Icon name="stethoscope-line" size="48rpx" color="white"></Icon>
					</view>
					<text class="service-title">体检预约</text>
					<text class="service-desc">专业体检服务预约</text>
				</view>
				<view class="service-item" @click="navigateToService('consultation')">
					<view class="service-icon consultation">
						<Icon name="user-heart-line" size="48rpx" color="white"></Icon>
					</view>
					<text class="service-title">在线咨询</text>
					<text class="service-desc">专家在线健康咨询</text>
				</view>
				<view class="service-item" @click="navigateToService('medication')">
					<view class="service-icon medication">
						<Icon name="capsule-line" size="48rpx" color="white"></Icon>
					</view>
					<text class="service-title">用药提醒</text>
					<text class="service-desc">智能用药提醒服务</text>
				</view>
				<view class="service-item" @click="navigateToService('emergency')">
					<view class="service-icon emergency">
						<Icon name="alarm-warning-line" size="48rpx" color="white"></Icon>
					</view>
					<text class="service-title">紧急呼叫</text>
					<text class="service-desc">24小时紧急救援</text>
				</view>
			</view>
		</view>

		<!-- 健康记录 -->
		<view class="section">
			<view class="section-header">
				<text class="section-title">健康记录</text>
				<text class="section-subtitle">查看历史健康数据</text>
			</view>
			<view class="record-list">
				<view class="record-item" v-for="(item, index) in healthRecords" :key="index" @click="viewRecord(item)">
					<view class="record-date">
						<text class="date-day">{{item.day}}</text>
						<text class="date-month">{{item.month}}</text>
					</view>
					<view class="record-content">
						<text class="record-title">{{item.title}}</text>
						<text class="record-desc">{{item.description}}</text>
						<view class="record-tags">
							<text class="record-tag" v-for="tag in item.tags" :key="tag">{{tag}}</text>
						</view>
					</view>
					<view class="record-action">
						<Icon name="arrow-right-s-line" size="24rpx" color="#999"></Icon>
					</view>
				</view>
			</view>
		</view>

		<!-- 健康数据录入 -->
		<view class="section">
			<view class="section-header">
				<text class="section-title">数据录入</text>
				<text class="section-subtitle">记录您的健康数据</text>
			</view>
			<view class="input-list">
				<view class="input-item">
					<view class="input-icon">
						<Icon name="heart-pulse-line" size="32rpx" color="#e91e63"></Icon>
					</view>
					<view class="input-content">
						<text class="input-label">血压 (mmHg)</text>
						<view class="input-row">
							<input class="input-field" v-model="inputData.systolic" placeholder="收缩压" type="number" />
							<text class="input-separator">/</text>
							<input class="input-field" v-model="inputData.diastolic" placeholder="舒张压" type="number" />
						</view>
					</view>
				</view>

				<view class="input-item">
					<view class="input-icon">
						<Icon name="drop-line" size="32rpx" color="#2196f3"></Icon>
					</view>
					<view class="input-content">
						<text class="input-label">血糖 (mmol/L)</text>
						<input class="input-field" v-model="inputData.bloodSugar" placeholder="请输入血糖值" type="number" />
					</view>
				</view>

				<view class="input-item">
					<view class="input-icon">
						<Icon name="heart-line" size="32rpx" color="#f44336"></Icon>
					</view>
					<view class="input-content">
						<text class="input-label">心率 (bpm)</text>
						<input class="input-field" v-model="inputData.heartRate" placeholder="请输入心率" type="number" />
					</view>
				</view>

				<view class="input-item">
					<view class="input-icon">
						<Icon name="scales-3-line" size="32rpx" color="#4caf50"></Icon>
					</view>
					<view class="input-content">
						<text class="input-label">体重 (kg)</text>
						<input class="input-field" v-model="inputData.weight" placeholder="请输入体重" type="number" />
					</view>
				</view>
			</view>
		</view>

		<!-- 底部操作 -->
		<view class="bottom-actions">
			<button class="action-btn secondary" @click="saveHealthData">
				<Icon name="save-line" size="32rpx" color="#ff8a00"></Icon>
				<text>保存数据</text>
			</button>
			<button class="action-btn primary" @click="emergencyCall">
				<Icon name="phone-line" size="32rpx" color="white"></Icon>
				<text>紧急呼叫</text>
			</button>
		</view>
	</view>
</template>

<script>
import Icon from '@/components/Icon/Icon.vue'

export default {
	components: {
		Icon
	},
	data() {
		return {
			healthData: [
				{
					label: '血压',
					value: '120/80',
					unit: 'mmHg',
					icon: 'heart-pulse-line',
					color: '#e91e63',
					status: 'normal',
					statusText: '正常'
				},
				{
					label: '血糖',
					value: '5.6',
					unit: 'mmol/L',
					icon: 'drop-line',
					color: '#2196f3',
					status: 'normal',
					statusText: '正常'
				},
				{
					label: '心率',
					value: '72',
					unit: 'bpm',
					icon: 'heart-line',
					color: '#f44336',
					status: 'normal',
					statusText: '正常'
				},
				{
					label: '体重',
					value: '65.5',
					unit: 'kg',
					icon: 'scales-3-line',
					color: '#4caf50',
					status: 'normal',
					statusText: '正常'
				}
			],
			healthRecords: [
				{
					day: '15',
					month: '01月',
					title: '体检报告',
					description: '年度健康体检，各项指标正常',
					tags: ['体检', '正常']
				},
				{
					day: '10',
					month: '01月',
					title: '血压监测',
					description: '血压130/85，略高于正常值',
					tags: ['血压', '监测']
				},
				{
					day: '05',
					month: '01月',
					title: '用药记录',
					description: '按时服用降压药，无不良反应',
					tags: ['用药', '记录']
				}
			],
			inputData: {
				systolic: '',
				diastolic: '',
				bloodSugar: '',
				heartRate: '',
				weight: ''
			}
		}
	},
	methods: {
		viewHealthDetail(item) {
			uni.navigateTo({
				url: `/pages/health/detail?type=${item.label}`
			});
		},
		navigateToService(service) {
			uni.navigateTo({
				url: `/pages/health/${service}`
			});
		},
		viewRecord(record) {
			uni.navigateTo({
				url: `/pages/health/record-detail?id=${record.id}`
			});
		},
		saveHealthData() {
			// 验证输入数据
			if (!this.inputData.systolic || !this.inputData.diastolic ||
				!this.inputData.bloodSugar || !this.inputData.heartRate ||
				!this.inputData.weight) {
				uni.showToast({
					title: '请填写完整的健康数据',
					icon: 'none'
				});
				return;
			}

			uni.showLoading({
				title: '保存中...'
			});

			// 模拟保存数据
			setTimeout(() => {
				uni.hideLoading();
				uni.showToast({
					title: '健康数据保存成功',
					icon: 'success'
				});

				// 清空输入数据
				this.inputData = {
					systolic: '',
					diastolic: '',
					bloodSugar: '',
					heartRate: '',
					weight: ''
				};
			}, 1500);
		},
		emergencyCall() {
			uni.makePhoneCall({
				phoneNumber: '120'
			});
		}
	}
}
</script>

<style scoped>
.container {
	background: linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%);
	min-height: 100vh;
	padding-bottom: 200rpx;
}

.header-banner {
	padding: 40rpx;
	margin-bottom: 40rpx;
}

.banner-content {
	background: rgba(255, 255, 255, 0.15);
	border-radius: 30rpx;
	padding: 40rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.banner-text {
	flex: 1;
}

.banner-title {
	font-size: 48rpx;
	font-weight: bold;
	color: white;
	display: block;
	margin-bottom: 15rpx;
}

.banner-subtitle {
	font-size: 28rpx;
	color: rgba(255, 255, 255, 0.9);
	display: block;
}

.banner-icon {
	margin-left: 30rpx;
}

.section {
	margin-bottom: 40rpx;
}

.section-header {
	padding: 0 40rpx 30rpx;
}

.section-title {
	font-size: 36rpx;
	font-weight: bold;
	color: white;
	display: block;
	margin-bottom: 10rpx;
}

.section-subtitle {
	font-size: 26rpx;
	color: rgba(255, 255, 255, 0.8);
	display: block;
}

.health-overview {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	gap: 20rpx;
	padding: 0 40rpx;
}

.health-card {
	background: white;
	border-radius: 30rpx;
	padding: 30rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	text-align: center;
	box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.health-icon {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-bottom: 20rpx;
}

.health-content {
	margin-bottom: 15rpx;
}

.health-label {
	font-size: 24rpx;
	color: #666;
	display: block;
	margin-bottom: 8rpx;
}

.health-value {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
	display: block;
}

.health-unit {
	font-size: 20rpx;
	color: #999;
	display: block;
}

.health-status {
	padding: 8rpx 20rpx;
	border-radius: 20rpx;
	font-size: 22rpx;
}

.health-status.normal {
	background: rgba(76, 175, 80, 0.1);
	color: #4caf50;
}

.health-status.warning {
	background: rgba(255, 152, 0, 0.1);
	color: #ff9800;
}

.health-status.danger {
	background: rgba(244, 67, 54, 0.1);
	color: #f44336;
}

.service-grid {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	gap: 30rpx;
	padding: 0 40rpx;
}

.service-item {
	background: white;
	border-radius: 30rpx;
	padding: 40rpx;
	text-align: center;
	box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.service-icon {
	width: 100rpx;
	height: 100rpx;
	border-radius: 20rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin: 0 auto 20rpx;
}

.service-icon.checkup { background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%); }
.service-icon.consultation { background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%); }
.service-icon.medication { background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%); }
.service-icon.emergency { background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%); }

.service-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 10rpx;
}

.service-desc {
	font-size: 24rpx;
	color: #666;
	display: block;
}

.record-list, .input-list {
	padding: 0 40rpx;
}

.record-item, .input-item {
	background: white;
	border-radius: 30rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	display: flex;
	align-items: center;
	gap: 20rpx;
	box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.input-icon {
	width: 60rpx;
	height: 60rpx;
	background: rgba(255, 138, 0, 0.1);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
}

.input-content {
	flex: 1;
}

.input-label {
	font-size: 28rpx;
	color: #333;
	font-weight: bold;
	display: block;
	margin-bottom: 15rpx;
}

.input-row {
	display: flex;
	align-items: center;
	gap: 15rpx;
}

.input-field {
	flex: 1;
	background: #f8f9fa;
	border: 1rpx solid #e0e0e0;
	border-radius: 15rpx;
	padding: 20rpx;
	font-size: 26rpx;
	color: #333;
}

.input-separator {
	font-size: 28rpx;
	color: #666;
	font-weight: bold;
}

.record-date {
	text-align: center;
	min-width: 80rpx;
}

.date-day {
	font-size: 36rpx;
	font-weight: bold;
	color: #ff8a00;
	display: block;
}

.date-month {
	font-size: 22rpx;
	color: #999;
	display: block;
}

.record-content {
	flex: 1;
}

.record-title {
	font-size: 30rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 8rpx;
}

.record-desc {
	font-size: 26rpx;
	color: #666;
	display: block;
	margin-bottom: 15rpx;
}

.record-tags {
	display: flex;
	gap: 10rpx;
}

.record-tag {
	background: rgba(255, 138, 0, 0.1);
	color: #ff8a00;
	font-size: 22rpx;
	padding: 5rpx 15rpx;
	border-radius: 15rpx;
}



.bottom-actions {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background: white;
	padding: 30rpx 40rpx;
	display: flex;
	gap: 20rpx;
	box-shadow: 0 -4rpx 20rpx rgba(0,0,0,0.1);
}

.action-btn {
	flex: 1;
	height: 100rpx;
	border-radius: 20rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 15rpx;
	font-size: 32rpx;
	font-weight: 500;
}

.action-btn.primary {
	background: linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%);
	color: white;
	border: none;
}

.action-btn.secondary {
	background: white;
	color: #ff8a00;
	border: 2rpx solid #ff8a00;
}
</style>
