<template>
	<view class="container">
		<!-- 自定义导航栏 -->
		<view class="custom-navbar">
			<view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
			<view class="navbar-content">
				<view class="navbar-left" @click="goBack">
					<Icon name="arrow-left-line" size="36rpx" color="#333"></Icon>
					<text class="back-text">返回</text>
				</view>
				<view class="navbar-center">
					<text class="navbar-title">紧急联系人</text>
				</view>
				<view class="navbar-right"></view>
			</view>
		</view>

		<!-- 紧急联系人列表 -->
		<view class="contacts-section">
			<view class="section-header">
				<Icon name="alarm-warning-line" size="32rpx" color="#f44336"></Icon>
				<text class="section-title">紧急联系人</text>
				<button class="add-btn" @click="addContact">
					<Icon name="add-line" size="24rpx" color="#ff8a00"></Icon>
				</button>
			</view>
			
			<view class="contacts-list">
				<view class="contact-item" v-for="(contact, index) in emergencyContacts" :key="index">
					<view class="contact-avatar">
						<Icon name="user-line" size="40rpx" color="#999"></Icon>
					</view>
					<view class="contact-info">
						<text class="contact-name">{{contact.name}}</text>
						<text class="contact-relation">{{contact.relation}}</text>
						<text class="contact-phone">{{contact.phone}}</text>
						<view class="contact-tags">
							<text class="contact-tag primary" v-if="contact.isPrimary">主要联系人</text>
							<text class="contact-tag emergency" v-if="contact.isEmergency">紧急联系人</text>
						</view>
					</view>
					<view class="contact-actions">
						<button class="action-btn call" @click="callContact(contact)">
							<Icon name="phone-line" size="24rpx" color="white"></Icon>
						</button>
						<button class="action-btn message" @click="messageContact(contact)">
							<Icon name="message-line" size="24rpx" color="white"></Icon>
						</button>
						<button class="action-btn edit" @click="editContact(index)">
							<Icon name="edit-line" size="24rpx" color="white"></Icon>
						</button>
						<button class="action-btn delete" @click="deleteContact(index)">
							<Icon name="delete-bin-line" size="24rpx" color="white"></Icon>
						</button>
					</view>
				</view>
			</view>
		</view>

		<!-- 快速拨号 -->
		<view class="quick-dial-section">
			<view class="section-header">
				<Icon name="phone-line" size="32rpx" color="#ff8a00"></Icon>
				<text class="section-title">快速拨号</text>
			</view>
			
			<view class="quick-dial-grid">
				<view class="dial-item" v-for="(contact, index) in quickDialContacts" :key="index" @click="callContact(contact)">
					<view class="dial-avatar">
						<text class="dial-initial">{{contact.name.charAt(0)}}</text>
					</view>
					<text class="dial-name">{{contact.name}}</text>
					<text class="dial-relation">{{contact.relation}}</text>
				</view>
			</view>
		</view>

		<!-- 紧急服务 -->
		<view class="emergency-services-section">
			<view class="section-header">
				<Icon name="service-line" size="32rpx" color="#f44336"></Icon>
				<text class="section-title">紧急服务</text>
			</view>
			
			<view class="services-grid">
				<view class="service-item" v-for="(service, index) in emergencyServices" :key="index" @click="callService(service)">
					<view class="service-icon" :style="{backgroundColor: service.color}">
						<Icon :name="service.icon" size="40rpx" color="white"></Icon>
					</view>
					<text class="service-name">{{service.name}}</text>
					<text class="service-number">{{service.number}}</text>
				</view>
			</view>
		</view>

		<!-- 联系人设置 -->
		<view class="settings-section">
			<view class="section-header">
				<Icon name="settings-3-line" size="32rpx" color="#ff8a00"></Icon>
				<text class="section-title">联系人设置</text>
			</view>
			
			<view class="setting-item">
				<text class="setting-label">自动发送位置信息</text>
				<switch :checked="settings.autoLocation" @change="onAutoLocationChange" color="#ff8a00" />
			</view>
			
			<view class="setting-item">
				<text class="setting-label">紧急情况自动拨号</text>
				<switch :checked="settings.autoCall" @change="onAutoCallChange" color="#ff8a00" />
			</view>
			
			<view class="setting-item">
				<text class="setting-label">发送健康状态</text>
				<switch :checked="settings.healthStatus" @change="onHealthStatusChange" color="#ff8a00" />
			</view>
		</view>

		<!-- 使用说明 -->
		<view class="help-section">
			<view class="section-header">
				<Icon name="question-line" size="32rpx" color="#ff8a00"></Icon>
				<text class="section-title">使用说明</text>
			</view>
			
			<view class="help-content">
				<view class="help-item">
					<Icon name="information-line" size="24rpx" color="#ff8a00"></Icon>
					<text class="help-text">建议设置2-3个紧急联系人，包括家人和朋友</text>
				</view>
				<view class="help-item">
					<Icon name="information-line" size="24rpx" color="#ff8a00"></Icon>
					<text class="help-text">主要联系人将优先接收紧急通知</text>
				</view>
				<view class="help-item">
					<Icon name="information-line" size="24rpx" color="#ff8a00"></Icon>
					<text class="help-text">紧急情况下可长按快速拨号按钮直接呼叫</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import Icon from '@/components/Icon/Icon.vue'

export default {
	components: {
		Icon
	},
	data() {
		return {
			statusBarHeight: 0, // 状态栏高度
			emergencyContacts: [
				{
					name: '张小明',
					relation: '儿子',
					phone: '139****1234',
					isPrimary: true,
					isEmergency: true
				},
				{
					name: '李小红',
					relation: '女儿',
					phone: '137****5678',
					isPrimary: false,
					isEmergency: true
				},
				{
					name: '王大夫',
					relation: '家庭医生',
					phone: '138****9012',
					isPrimary: false,
					isEmergency: false
				}
			],
			emergencyServices: [
				{
					name: '急救中心',
					number: '120',
					icon: 'hospital-line',
					color: '#f44336'
				},
				{
					name: '报警电话',
					number: '110',
					icon: 'police-car-line',
					color: '#2196f3'
				},
				{
					name: '消防电话',
					number: '119',
					icon: 'fire-line',
					color: '#ff9800'
				},
				{
					name: '社区服务',
					number: '12345',
					icon: 'community-line',
					color: '#4caf50'
				}
			],
			settings: {
				autoLocation: true,
				autoCall: false,
				healthStatus: true
			}
		}
	},
	computed: {
		quickDialContacts() {
			return this.emergencyContacts.filter(contact => contact.isPrimary || contact.isEmergency).slice(0, 4);
		}
	},
	onLoad() {
		// 获取状态栏高度
		const systemInfo = uni.getSystemInfoSync();
		this.statusBarHeight = systemInfo.statusBarHeight || 44;

		this.loadEmergencyContacts();
	},
	methods: {
		loadEmergencyContacts() {
			const savedContacts = uni.getStorageSync('emergencyContacts');
			if (savedContacts && savedContacts.length > 0) {
				this.emergencyContacts = savedContacts;
			}
		},
		addContact() {
			uni.navigateTo({
				url: '/pages/profile/emergency-edit'
			});
		},
		editContact(index) {
			uni.navigateTo({
				url: `/pages/profile/emergency-edit?index=${index}`
			});
		},
		deleteContact(index) {
			uni.showModal({
				title: '确认删除',
				content: '确定要删除这个联系人吗？',
				success: (res) => {
					if (res.confirm) {
						this.emergencyContacts.splice(index, 1);
						this.saveContacts();
						uni.showToast({
							title: '删除成功',
							icon: 'success'
						});
					}
				}
			});
		},
		callContact(contact) {
			uni.makePhoneCall({
				phoneNumber: contact.phone.replace(/\*/g, '')
			});
		},
		messageContact(contact) {
			uni.showToast({
				title: '发送消息功能开发中',
				icon: 'none'
			});
		},
		callService(service) {
			uni.showModal({
				title: '确认拨号',
				content: `确定要拨打${service.name} ${service.number}吗？`,
				success: (res) => {
					if (res.confirm) {
						uni.makePhoneCall({
							phoneNumber: service.number
						});
					}
				}
			});
		},
		onAutoLocationChange(e) {
			this.settings.autoLocation = e.detail.value;
			this.saveSettings();
		},
		onAutoCallChange(e) {
			this.settings.autoCall = e.detail.value;
			this.saveSettings();
		},
		onHealthStatusChange(e) {
			this.settings.healthStatus = e.detail.value;
			this.saveSettings();
		},
		saveContacts() {
			uni.setStorageSync('emergencyContacts', this.emergencyContacts);
		},
		saveSettings() {
			uni.setStorageSync('emergencySettings', this.settings);
		},

		// 返回上一页
		goBack() {
			uni.navigateBack({
				fail: () => {
					uni.switchTab({
						url: '/pages/profile/profile'
					});
				}
			});
		}
	}
}
</script>

<style scoped>
.container {
	background: #f8f9fa;
	min-height: 100vh;
}

/* 导航栏样式 */
.custom-navbar {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 1000;
	background: rgba(255, 255, 255, 0.95);
	backdrop-filter: blur(20rpx);
	-webkit-backdrop-filter: blur(20rpx);
	border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);
}

.navbar-content {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 20rpx 32rpx;
	min-height: 88rpx;
}

.navbar-left {
	display: flex;
	align-items: center;
	gap: 12rpx;
	padding: 8rpx 16rpx 8rpx 0;
	border-radius: 20rpx;
	transition: all 0.2s ease;
}

.navbar-left:active {
	background-color: rgba(0, 0, 0, 0.04);
	transform: scale(0.96);
}

.back-text {
	font-size: 32rpx;
	color: #333;
	font-weight: 500;
}

.navbar-center {
	position: absolute;
	left: 50%;
	transform: translateX(-50%);
}

.navbar-title {
	font-size: 34rpx;
	font-weight: 600;
	color: #333;
}

.navbar-right {
	display: flex;
	align-items: center;
}

.contacts-section, .quick-dial-section, .emergency-services-section, .settings-section, .help-section {
	background: white;
	margin: 20rpx;
	border-radius: 30rpx;
	padding: 40rpx;
}

.contacts-section {
	margin-top: 220rpx; /* 为导航栏留出空间 */
}

.section-header {
	display: flex;
	align-items: center;
	gap: 15rpx;
	margin-bottom: 30rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	flex: 1;
}

.add-btn {
	width: 60rpx;
	height: 60rpx;
	background: rgba(255, 138, 0, 0.1);
	border: 2rpx solid #ff8a00;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
}

.contacts-list {
	display: flex;
	flex-direction: column;
	gap: 25rpx;
}

.contact-item {
	display: flex;
	align-items: center;
	gap: 20rpx;
	padding: 25rpx;
	background: #f8f9fa;
	border-radius: 25rpx;
}

.contact-avatar {
	width: 80rpx;
	height: 80rpx;
	background: #e0e0e0;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
}

.contact-info {
	flex: 1;
}

.contact-name {
	font-size: 28rpx;
	color: #333;
	font-weight: bold;
	display: block;
	margin-bottom: 5rpx;
}

.contact-relation {
	font-size: 24rpx;
	color: #666;
	display: block;
	margin-bottom: 5rpx;
}

.contact-phone {
	font-size: 26rpx;
	color: #ff8a00;
	font-weight: bold;
	display: block;
	margin-bottom: 10rpx;
}

.contact-tags {
	display: flex;
	gap: 8rpx;
}

.contact-tag {
	font-size: 20rpx;
	padding: 4rpx 12rpx;
	border-radius: 12rpx;
}

.contact-tag.primary {
	background: rgba(255, 138, 0, 0.1);
	color: #ff8a00;
}

.contact-tag.emergency {
	background: rgba(244, 67, 54, 0.1);
	color: #f44336;
}

.contact-actions {
	display: flex;
	gap: 10rpx;
}

.action-btn {
	width: 60rpx;
	height: 60rpx;
	border-radius: 50%;
	border: none;
	display: flex;
	align-items: center;
	justify-content: center;
}

.action-btn.call {
	background: #4caf50;
}

.action-btn.message {
	background: #2196f3;
}

.action-btn.edit {
	background: #ff9800;
}

.action-btn.delete {
	background: #f44336;
}

.quick-dial-grid {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	gap: 25rpx;
}

.dial-item {
	text-align: center;
	padding: 30rpx;
	background: #f8f9fa;
	border-radius: 25rpx;
}

.dial-avatar {
	width: 100rpx;
	height: 100rpx;
	background: linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	margin: 0 auto 15rpx;
}

.dial-initial {
	font-size: 36rpx;
	color: white;
	font-weight: bold;
}

.dial-name {
	font-size: 26rpx;
	color: #333;
	font-weight: bold;
	display: block;
	margin-bottom: 5rpx;
}

.dial-relation {
	font-size: 22rpx;
	color: #666;
	display: block;
}

.services-grid {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	gap: 25rpx;
}

.service-item {
	text-align: center;
	padding: 30rpx;
	background: #f8f9fa;
	border-radius: 25rpx;
}

.service-icon {
	width: 100rpx;
	height: 100rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	margin: 0 auto 15rpx;
}

.service-name {
	font-size: 26rpx;
	color: #333;
	font-weight: bold;
	display: block;
	margin-bottom: 5rpx;
}

.service-number {
	font-size: 32rpx;
	color: #f44336;
	font-weight: bold;
	display: block;
}

.setting-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 25rpx 0;
	border-bottom: 1rpx solid #f0f0f0;
}

.setting-item:last-child {
	border-bottom: none;
}

.setting-label {
	font-size: 28rpx;
	color: #333;
}

.help-content {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

.help-item {
	display: flex;
	align-items: flex-start;
	gap: 15rpx;
}

.help-text {
	font-size: 26rpx;
	color: #666;
	line-height: 1.5;
}
</style>
