<template>
	<ErrorBoundary
		:error-type="errorType"
		:error-message="error"
		:elderly-mode="isElderlyMode"
		@retry="retryLoad"
	>
		<view class="container" :class="{ 'elderly-mode': isElderlyMode }">
			<!-- 自定义导航栏 -->
			<view class="custom-navbar">
				<view class="navbar-content">
					<view class="location-info">
						<Icon name="map-pin-line" size="32rpx" color="white" class="location-icon"></Icon>
						<text class="location-text">北京市朝阳区</text>
					</view>
					<view class="navbar-actions">
						<Icon name="notification-3-line" size="36rpx" color="white" class="notification-icon" @click="showNotifications"></Icon>
					</view>
				</view>
				<view class="status-bar"></view>
			</view>

		<!-- 顶部横幅区域 -->
		<view class="hero-section">
			<view class="hero-content">
				<view class="hero-text">
					<text class="hero-title">慧养老</text>
					<text class="hero-subtitle">为老年人群体</text>
					<text class="hero-subtitle">打造养老新生态</text>
				</view>
				<view class="hero-illustration">
					<image src="/static/illustrations/elderly-care.png" class="hero-image"></image>
				</view>
			</view>
		</view>

		<!-- 主要功能菜单 -->
		<view class="main-functions">
			<view class="function-row">
				<view class="function-item" @click="navigateTo('/pages/institution/select')">
					<view class="function-icon institution">
						<Icon name="building-line" size="48rpx" color="white"></Icon>
					</view>
					<text class="function-text">选机构</text>
				</view>
				<view class="function-item" @click="navigateTo('/pages/service/find')">
					<view class="function-icon service">
						<Icon name="search-line" size="48rpx" color="white"></Icon>
					</view>
					<text class="function-text">找服务</text>
				</view>
				<view class="function-item" @click="navigateTo('/pages/subsidy/receive')">
					<view class="function-icon subsidy">
						<Icon name="money-cny-circle-line" size="48rpx" color="white"></Icon>
					</view>
					<text class="function-text">领补贴</text>
				</view>
				<view class="function-item" @click="navigateTo('/pages/elderly/settings')">
					<view class="function-icon elderly">
						<Icon name="user-settings-line" size="48rpx" color="white"></Icon>
					</view>
					<text class="function-text">适老版</text>
				</view>
			</view>
		</view>

		<!-- 紧急服务卡片 -->
		<view class="emergency-cards">
			<view class="emergency-card call-center" @click="callCenter">
				<view class="card-icon">
					<Icon name="customer-service-2-line" size="48rpx" color="white"></Icon>
				</view>
				<view class="card-content">
					<text class="card-title">呼叫</text>
					<text class="card-title">中心</text>
				</view>
			</view>
			<view class="emergency-card guardian" @click="contactGuardian">
				<view class="card-icon">
					<Icon name="parent-line" size="48rpx" color="white"></Icon>
				</view>
				<view class="card-content">
					<text class="card-title">联系</text>
					<text class="card-title">监护人</text>
				</view>
			</view>
			<view class="emergency-card service" @click="contactService">
				<view class="card-icon">
					<Icon name="user-heart-line" size="48rpx" color="white"></Icon>
				</view>
				<view class="card-content">
					<text class="card-title">联系</text>
					<text class="card-title">服务人</text>
				</view>
			</view>
		</view>
		
		<!-- 服务中心 -->
		<view class="service-center">
			<view class="section-title">服务中心</view>
			<view class="service-grid">
				<view class="service-item" @click="navigateTo('/pages/renovation/index')">
					<view class="service-icon renovation">
						<Icon name="home-gear-line" size="36rpx" color="#4caf50"></Icon>
					</view>
					<text class="service-text">适老改造</text>
				</view>
				<view class="service-item" @click="navigateTo('/pages/policy/index')">
					<view class="service-icon policy">
						<Icon name="government-line" size="36rpx" color="#ff9800"></Icon>
					</view>
					<text class="service-text">政策引导</text>
				</view>
				<view class="service-item" @click="navigateTo('/pages/equipment/index')">
					<view class="service-icon equipment">
						<Icon name="wheelchair-line" size="36rpx" color="#2196f3"></Icon>
					</view>
					<text class="service-text">辅具租赁</text>
				</view>
				<view class="service-item" @click="navigateTo('/pages/bed/index')">
					<view class="service-icon bed">
						<Icon name="hotel-bed-line" size="36rpx" color="#e91e63"></Icon>
					</view>
					<text class="service-text">家庭床位</text>
				</view>
				<view class="service-item" @click="navigateTo('/pages/elderly-station/index')">
					<view class="service-icon elderly-station">
						<Icon name="gift-2-line" size="36rpx" color="#9c27b0"></Icon>
					</view>
					<text class="service-text">高龄津贴</text>
				</view>
				<view class="service-item" @click="navigateTo('/pages/community-care/index')">
					<view class="service-icon community-care">
						<Icon name="community-line" size="36rpx" color="#009688"></Icon>
					</view>
					<text class="service-text">社区养老</text>
				</view>
				<view class="service-item" @click="navigateTo('/pages/elderly-dining/index')">
					<view class="service-icon elderly-dining">
						<Icon name="restaurant-line" size="36rpx" color="#ffc107"></Icon>
					</view>
					<text class="service-text">长者食堂</text>
				</view>
				<view class="service-item" @click="navigateTo('/pages/care-service/index')">
					<view class="service-icon care-service">
						<Icon name="heart-3-line" size="36rpx" color="#f44336"></Icon>
					</view>
					<text class="service-text">养老服务</text>
				</view>
				<view class="service-item" @click="navigateTo('/pages/health/index')">
					<view class="service-icon health">
						<Icon name="health-book-line" size="36rpx" color="#4caf50"></Icon>
					</view>
					<text class="service-text">健康管理</text>
				</view>
				<view class="service-item" @click="navigateTo('/pages/monitoring/index')">
					<view class="service-icon monitoring">
						<Icon name="shield-check-line" size="36rpx" color="#2196f3"></Icon>
					</view>
					<text class="service-text">智能监护</text>
				</view>
				<view class="service-item" @click="navigateTo('/pages/entertainment/index')">
					<view class="service-icon entertainment">
						<Icon name="music-2-line" size="36rpx" color="#ff9800"></Icon>
					</view>
					<text class="service-text">文娱活动</text>
				</view>
				<view class="service-item" @click="navigateTo('/pages/data/manage')">
					<view class="service-icon data-manage">
						<Icon name="database-line" size="36rpx" color="#ff8a00"></Icon>
					</view>
					<text class="service-text">数据管理</text>
				</view>
			</view>
		</view>

		<!-- 资讯信息 -->
		<view class="news-section">
			<view class="section-header">
				<text class="section-title">资讯信息</text>
				<text class="more-link" @click="navigateTo('/pages/news/list')">更多</text>
			</view>

			<!-- 加载状态 -->
			<LoadingSkeleton v-if="loading" type="list" :count="3" :elderly-mode="isElderlyMode" />

			<!-- 资讯列表 -->
			<view v-else class="news-list">
				<view class="news-item" v-for="(item, index) in newsList" :key="index" @click="viewNews(item)">
					<!-- 使用懒加载图片组件 -->
					<view class="news-image-container">
						<LazyImage
							:src="item.image"
							:width="120"
							:height="80"
							:border-radius="12"
							placeholder-icon="article-line"
							:show-placeholder="true"
							class="news-image"
							@click="viewNews(item)"
						/>
					</view>
					<view class="news-content">
						<text class="news-title">{{item.title}}</text>
						<text class="news-summary">{{item.summary}}</text>
						<text class="news-time">{{item.time}}</text>
					</view>
				</view>
			</view>
		</view>
	</view>
	</ErrorBoundary>
</template>

<script>
import Icon from '@/components/Icon/Icon.vue'
import LoadingSkeleton from '@/components/LoadingSkeleton/LoadingSkeleton.vue'
import ErrorBoundary from '@/components/ErrorBoundary/ErrorBoundary.vue'
import LazyImage from '@/components/LazyImage/LazyImage.vue'

export default {
	components: {
		Icon,
		LoadingSkeleton,
		ErrorBoundary,
		LazyImage
	},
	data() {
		return {
			loading: false,
			error: null,
			errorType: 'unknown',
			bannerList: [],
			recommendList: [],
			newsList: []
		}
	},

	computed: {
		isElderlyMode() {
			return false // 暂时禁用适老版
		}
	},
	async onLoad() {
		console.log('首页加载')
		try {
			// 获取系统信息
			this.getSystemInfo()

			// 加载页面数据
			await this.loadPageData()
		} catch (error) {
			this.handlePageError(error)
		}
	},

	methods: {
		async loadPageData() {
			this.loading = true
			this.error = null

			try {
				// 并行加载数据
				const [bannerData, recommendData, newsData] = await Promise.all([
					this.loadBannerData(),
					this.loadRecommendData(),
					this.loadNewsData()
				])

				this.bannerList = bannerData
				this.recommendList = recommendData
				this.newsList = newsData
			} catch (error) {
				this.handlePageError(error)
			} finally {
				this.loading = false
			}
		},

		async loadBannerData() {
			// 模拟API调用
			return new Promise((resolve) => {
				setTimeout(() => {
					resolve([
						{ image: '/static/banner/banner1.jpg' },
						{ image: '/static/banner/banner2.jpg' },
						{ image: '/static/banner/banner3.jpg' }
					])
				}, 500)
			})
		},

		async loadRecommendData() {
			return new Promise((resolve) => {
				setTimeout(() => {
					resolve([
						{
							id: 1,
							name: '阳光养老院',
							description: '专业的养老服务机构',
							image: '/picture/20226131655111829696_10006313.jpg',
							rating: 4.8
						},
						{
							id: 2,
							name: '康乐老年公寓',
							description: '温馨舒适的居住环境',
							image: '/picture/v2-cf8da5bb3e3a5a87f1ce7f498397db9d_720w.jpg',
							rating: 4.6
						},
						{
							id: 3,
							name: '温馨护理中心',
							description: '贴心的护理服务',
							image: '/picture/R-C (1).jpg',
							rating: 4.9
						}
					])
				}, 300)
			})
		},

		async loadNewsData() {
			return new Promise((resolve) => {
				setTimeout(() => {
					resolve([
						{
							id: 1,
							title: '养老服务新政策发布',
							summary: '政府出台新的养老服务补贴政策，惠及更多老年人',
							image: '/picture/FAB64913B02FEDD318336D49F0A550A1_w798h530.png',
							time: '2024-01-15'
						},
						{
							id: 2,
							title: '智慧养老技术创新',
							summary: '最新的智能设备助力老年人生活更便利',
							image: '/picture/v2-cf8da5bb3e3a5a87f1ce7f498397db9d_720w.jpg',
							time: '2024-01-14'
						}
					])
				}, 400)
			})
		},

		handlePageError(error) {
			console.error('页面加载错误:', error)
			// 设置错误状态
			this.error = error.message || '页面加载失败'
			this.errorType = 'network'
		},

		retryLoad() {
			this.loadPageData()
		},

		getSystemInfo() {
			const systemInfo = uni.getSystemInfoSync()
			this.statusBarHeight = systemInfo.statusBarHeight
			this.navBarHeight = systemInfo.platform === 'ios' ? 44 : 48
		},
		navigateTo(url) {
			uni.navigateTo({
				url: url,
				success: () => {
					console.log('页面跳转成功:', url);
				},
				fail: (err) => {
					console.error('页面跳转失败:', err);
					uni.showToast({
						title: '页面跳转失败',
						icon: 'none'
					});
				}
			});
		},
		async showElderlyMode() {
			uni.showModal({
				title: '适老版',
				content: '是否切换到适老版界面？\n适老版将提供更大字体和简化操作',
				success: (res) => {
					if (res.confirm) {
						this.switchToElderlyMode();
					}
				}
			});
		},
		switchToElderlyMode() {
			// 实现适老版切换逻辑
			uni.showLoading({ title: '正在切换...' });

			setTimeout(() => {
				uni.hideLoading();
				uni.showToast({
					title: '已切换到适老版',
					icon: 'success'
				});

				// 这里可以添加实际的适老版切换逻辑
				// 比如修改全局样式、字体大小等
			}, 1500);
		},
		callCenter() {
			uni.showModal({
				title: '呼叫中心',
				content: '即将拨打客服热线：************',
				success: (res) => {
					if (res.confirm) {
						uni.makePhoneCall({
							phoneNumber: '************',
							success: () => {
								console.log('拨打电话成功');
							},
							fail: (err) => {
								console.error('拨打电话失败:', err);
								uni.showToast({
									title: '拨打电话失败',
									icon: 'none'
								});
							}
						});
					}
				}
			});
		},
		contactGuardian() {
			uni.showToast({
				title: '正在联系监护人...',
				icon: 'loading'
			});

			// 模拟联系过程
			setTimeout(() => {
				uni.showToast({
					title: '已通知监护人',
					icon: 'success'
				});
			}, 1000);
		},
		contactService() {
			uni.showToast({
				title: '正在联系服务人员...',
				icon: 'loading'
			});

			// 模拟联系过程
			setTimeout(() => {
				uni.showToast({
					title: '服务人员将尽快联系您',
					icon: 'success'
				});
			}, 1000);
		},
		viewInstitution(item) {
			uni.navigateTo({
				url: `/pages/institution/detail?id=${item.id}`
			});
		},
		viewNews(item) {
			uni.navigateTo({
				url: `/pages/news/detail?id=${item.id}`
			});
		},

		// 处理图片加载错误
		handleImageError(item) {
			item.imageError = true;
		},

		// 显示通知
		showNotifications() {
			uni.navigateTo({
				url: '/pages/notification/list'
			})
		},

		// 切换适老版模式
		toggleElderlyMode() {
			const message = this.isElderlyMode ? '已开启适老版' : '已关闭适老版'
			uni.showToast({
				title: message,
				icon: 'success'
			})
		}
	}
}
</script>

<style scoped>
.container {
	background: linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%);
	min-height: 100vh;
}

/* 自定义导航栏 */
.custom-navbar {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 1000;
	background: transparent;
}

.status-bar {
	height: var(--status-bar-height, 44rpx);
}

.navbar-content {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20rpx 40rpx;
	color: white;
}

.location-info {
	display: flex;
	align-items: center;
}

.location-icon {
	font-size: 32rpx;
	margin-right: 10rpx;
}

.location-text {
	font-size: 28rpx;
}

.navbar-actions {
	display: flex;
	align-items: center;
}

.notification-icon {
	font-size: 36rpx;
}

/* 顶部横幅区域 */
.hero-section {
	padding: 140rpx 40rpx 40rpx;
	color: white;
}

.hero-content {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.hero-text {
	flex: 1;
}

.hero-title {
	font-size: 64rpx;
	font-weight: bold;
	display: block;
	margin-bottom: 20rpx;
}

.hero-subtitle {
	font-size: 32rpx;
	display: block;
	line-height: 1.5;
	opacity: 0.9;
}

.hero-illustration {
	width: 200rpx;
	height: 200rpx;
}

.hero-image {
	width: 100%;
	height: 100%;
}

/* 主要功能菜单 */
.main-functions {
	padding: 0 40rpx;
	margin-bottom: 40rpx;
}

.function-row {
	display: flex;
	justify-content: space-between;
	gap: 20rpx;
}

.function-item {
	flex: 1;
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 20rpx 10rpx;
}

.function-icon {
	width: 120rpx;
	height: 120rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-bottom: 15rpx;
}

.function-icon.institution {
	background-color: #ff6b6b;
}

.function-icon.service {
	background-color: #4ecdc4;
}

.function-icon.subsidy {
	background-color: #45b7d1;
}

.function-icon.elderly {
	background-color: #96ceb4;
}

.icon-text {
	font-size: 48rpx;
	color: white;
}

.function-text {
	font-size: 28rpx;
	color: white;
	font-weight: 500;
}

/* 紧急服务卡片 */
.emergency-cards {
	padding: 0 40rpx;
	margin-bottom: 40rpx;
	display: flex;
	gap: 20rpx;
}

.emergency-card {
	flex: 1;
	height: 160rpx;
	border-radius: 20rpx;
	padding: 30rpx;
	display: flex;
	align-items: center;
	color: white;
	position: relative;
	overflow: hidden;
}

.emergency-card.call-center {
	background: linear-gradient(135deg, #ff6b6b 0%, #ff5252 100%);
}

.emergency-card.guardian {
	background: linear-gradient(135deg, #ffa726 0%, #ff9800 100%);
}

.emergency-card.service {
	background: linear-gradient(135deg, #66bb6a 0%, #4caf50 100%);
}

.card-icon {
	margin-right: 20rpx;
}

.card-icon-text {
	font-size: 48rpx;
	color: white;
}

.card-content {
	flex: 1;
}

.card-title {
	font-size: 32rpx;
	font-weight: bold;
	display: block;
	line-height: 1.2;
}

/* 服务中心 */
.service-center {
	background-color: white;
	margin: 0 40rpx 40rpx;
	border-radius: 30rpx;
	padding: 40rpx;
}

.section-title {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 30rpx;
}

.service-grid {
	display: grid;
	grid-template-columns: repeat(4, 1fr);
	gap: 30rpx;
}

.service-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 20rpx 10rpx;
}

.service-icon {
	width: 100rpx;
	height: 100rpx;
	border-radius: 20rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-bottom: 15rpx;
}

.service-icon.renovation {
	background-color: #e8f5e8;
}

.service-icon.policy {
	background-color: #fff3e0;
}

.service-icon.equipment {
	background-color: #e3f2fd;
}

.service-icon.bed {
	background-color: #fce4ec;
}

.service-icon.elderly-station {
	background-color: #f3e5f5;
}

.service-icon.community-care {
	background-color: #e0f2f1;
}

.service-icon.elderly-dining {
	background-color: #fff8e1;
}

.service-icon.care-service {
	background-color: #ffebee;
}

.service-icon.health {
	background-color: #e8f5e8;
}

.service-icon.monitoring {
	background-color: #e3f2fd;
}

.service-icon.entertainment {
	background-color: #fff3e0;
}

.service-icon.data-manage {
	background-color: rgba(255, 138, 0, 0.1);
}

.service-icon-text {
	font-size: 36rpx;
	color: #666;
}

.service-text {
	font-size: 24rpx;
	color: #666;
	text-align: center;
	line-height: 1.2;
}

/* 资讯信息 */
.news-section {
	background-color: white;
	margin: 0 40rpx 40rpx;
	border-radius: 30rpx;
	padding: 40rpx;
}

.section-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 30rpx;
}

.more-link {
	font-size: 32rpx;
	color: #ff8a00;
	font-weight: bold;
}

.news-list {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

.news-item {
	display: flex;
	background-color: #f8f9fa;
	border-radius: 20rpx;
	overflow: hidden;
	padding: 20rpx;
}

.news-image-container {
	flex-shrink: 0;
	width: 160rpx;
	height: 120rpx;
	margin-right: 20rpx;
}

.news-image {
	width: 100%;
	height: 100%;
	border-radius: 15rpx;
}

.news-icon-placeholder {
	width: 100%;
	height: 100%;
	border-radius: 15rpx;
	background: linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%);
	display: flex;
	align-items: center;
	justify-content: center;
}

.news-content {
	flex: 1;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
}

.news-title {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 10rpx;
	line-height: 1.4;
}

.news-summary {
	font-size: 24rpx;
	color: #666;
	margin-bottom: 10rpx;
	line-height: 1.3;
}

.news-time {
	font-size: 20rpx;
	color: #999;
}
</style>
