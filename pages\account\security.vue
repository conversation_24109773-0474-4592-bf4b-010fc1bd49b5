<template>
	<view class="container">
		<!-- 自定义导航栏 -->
		<view class="custom-navbar">
			<view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
			<view class="navbar-content">
				<view class="navbar-left" @click="goBack">
					<Icon name="arrow-left-line" size="36rpx" color="#333"></Icon>
					<text class="back-text">返回</text>
				</view>
				<view class="navbar-center">
					<text class="navbar-title">安全设置</text>
				</view>
				<view class="navbar-right"></view>
			</view>
		</view>

		<!-- 安全状态 -->
		<view class="security-status">
			<view class="status-header">
				<Icon name="shield-check-line" size="60rpx" color="#4caf50"></Icon>
				<text class="status-title">账户安全</text>
				<text class="status-desc">您的账户安全等级：高</text>
			</view>
			<view class="security-score">
				<view class="score-circle">
					<text class="score-number">85</text>
					<text class="score-label">分</text>
				</view>
			</view>
		</view>

		<!-- 安全设置项 -->
		<view class="security-items">
			<view class="security-item" @click="changePassword">
				<view class="item-icon">
					<Icon name="lock-password-line" size="40rpx" color="#ff8a00"></Icon>
				</view>
				<view class="item-info">
					<text class="item-title">登录密码</text>
					<text class="item-desc">定期更换密码，保护账户安全</text>
				</view>
				<view class="item-status">
					<text class="status-text safe">已设置</text>
					<Icon name="arrow-right-s-line" size="20rpx" color="#999"></Icon>
				</view>
			</view>

			<view class="security-item" @click="bindPhone">
				<view class="item-icon">
					<Icon name="smartphone-line" size="40rpx" color="#4caf50"></Icon>
				</view>
				<view class="item-info">
					<text class="item-title">手机绑定</text>
					<text class="item-desc">138****5678</text>
				</view>
				<view class="item-status">
					<text class="status-text safe">已绑定</text>
					<Icon name="arrow-right-s-line" size="20rpx" color="#999"></Icon>
				</view>
			</view>

			<view class="security-item" @click="bindEmail">
				<view class="item-icon">
					<Icon name="mail-line" size="40rpx" color="#2196f3"></Icon>
				</view>
				<view class="item-info">
					<text class="item-title">邮箱绑定</text>
					<text class="item-desc">用于找回密码和接收通知</text>
				</view>
				<view class="item-status">
					<text class="status-text warning">未绑定</text>
					<Icon name="arrow-right-s-line" size="20rpx" color="#999"></Icon>
				</view>
			</view>

			<view class="security-item" @click="setupBiometric">
				<view class="item-icon">
					<Icon name="fingerprint-line" size="40rpx" color="#9c27b0"></Icon>
				</view>
				<view class="item-info">
					<text class="item-title">生物识别</text>
					<text class="item-desc">指纹/面容识别登录</text>
				</view>
				<view class="item-status">
					<switch :checked="biometricEnabled" @change="toggleBiometric" color="#ff8a00" />
				</view>
			</view>

			<view class="security-item" @click="viewLoginHistory">
				<view class="item-icon">
					<Icon name="history-line" size="40rpx" color="#ff9800"></Icon>
				</view>
				<view class="item-info">
					<text class="item-title">登录记录</text>
					<text class="item-desc">查看最近登录设备和时间</text>
				</view>
				<view class="item-status">
					<Icon name="arrow-right-s-line" size="20rpx" color="#999"></Icon>
				</view>
			</view>

			<view class="security-item" @click="manageDevices">
				<view class="item-icon">
					<Icon name="computer-line" size="40rpx" color="#607d8b"></Icon>
				</view>
				<view class="item-info">
					<text class="item-title">设备管理</text>
					<text class="item-desc">管理已登录的设备</text>
				</view>
				<view class="item-status">
					<Icon name="arrow-right-s-line" size="20rpx" color="#999"></Icon>
				</view>
			</view>
		</view>

		<!-- 安全建议 -->
		<view class="security-tips">
			<view class="tips-header">
				<Icon name="lightbulb-line" size="32rpx" color="#ff8a00"></Icon>
				<text class="tips-title">安全建议</text>
			</view>
			<view class="tips-list">
				<view class="tip-item">
					<Icon name="check-line" size="20rpx" color="#4caf50"></Icon>
					<text class="tip-text">定期更换密码，建议3个月更换一次</text>
				</view>
				<view class="tip-item">
					<Icon name="close-line" size="20rpx" color="#f44336"></Icon>
					<text class="tip-text">绑定邮箱以便找回密码</text>
				</view>
				<view class="tip-item">
					<Icon name="check-line" size="20rpx" color="#4caf50"></Icon>
					<text class="tip-text">开启生物识别登录更安全便捷</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import Icon from '@/components/Icon/Icon.vue'

export default {
	components: {
		Icon
	},
	data() {
		return {
			statusBarHeight: 0, // 状态栏高度
			biometricEnabled: false
		}
	},
	onLoad() {
		// 获取状态栏高度
		const systemInfo = uni.getSystemInfoSync();
		this.statusBarHeight = systemInfo.statusBarHeight || 44;
	},
	methods: {
		changePassword() {
			uni.navigateTo({
				url: '/pages/account/password'
			});
		},
		bindPhone() {
			uni.navigateTo({
				url: '/pages/account/bind-phone'
			});
		},
		bindEmail() {
			uni.navigateTo({
				url: '/pages/account/bind-email'
			});
		},
		setupBiometric() {
			uni.showToast({
				title: '生物识别设置功能开发中',
				icon: 'none'
			});
		},
		toggleBiometric(e) {
			this.biometricEnabled = e.detail.value;
			uni.showToast({
				title: this.biometricEnabled ? '已开启生物识别' : '已关闭生物识别',
				icon: 'success'
			});
		},
		viewLoginHistory() {
			uni.navigateTo({
				url: '/pages/account/login-history'
			});
		},
		manageDevices() {
			uni.navigateTo({
				url: '/pages/account/devices'
			});
		},

		// 返回上一页
		goBack() {
			uni.navigateBack({
				fail: () => {
					uni.switchTab({
						url: '/pages/profile/profile'
					});
				}
			});
		}
	}
}
</script>

<style scoped>
.container {
	background: #f8f9fa;
	min-height: 100vh;
}

/* 导航栏样式 */
.custom-navbar {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 1000;
	background: rgba(255, 255, 255, 0.95);
	backdrop-filter: blur(20rpx);
	-webkit-backdrop-filter: blur(20rpx);
	border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);
}

.navbar-content {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 20rpx 32rpx;
	min-height: 88rpx;
}

.navbar-left {
	display: flex;
	align-items: center;
	gap: 12rpx;
	padding: 8rpx 16rpx 8rpx 0;
	border-radius: 20rpx;
	transition: all 0.2s ease;
}

.navbar-left:active {
	background-color: rgba(0, 0, 0, 0.04);
	transform: scale(0.96);
}

.back-text {
	font-size: 32rpx;
	color: #333;
	font-weight: 500;
}

.navbar-center {
	position: absolute;
	left: 50%;
	transform: translateX(-50%);
}

.navbar-title {
	font-size: 34rpx;
	font-weight: 600;
	color: #333;
}

.navbar-right {
	display: flex;
	align-items: center;
}

.security-status {
	background: linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%);
	padding: 40rpx;
	margin-top: 220rpx; /* 为导航栏留出空间 */
	display: flex;
	justify-content: space-between;
	align-items: center;
	color: white;
}

.status-header {
	flex: 1;
}

.status-title {
	font-size: 32rpx;
	font-weight: bold;
	display: block;
	margin: 15rpx 0 10rpx;
}

.status-desc {
	font-size: 24rpx;
	opacity: 0.9;
	display: block;
}

.security-score {
	text-align: center;
}

.score-circle {
	width: 120rpx;
	height: 120rpx;
	border: 4rpx solid rgba(255, 255, 255, 0.3);
	border-radius: 50%;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
}

.score-number {
	font-size: 36rpx;
	font-weight: bold;
}

.score-label {
	font-size: 20rpx;
	opacity: 0.8;
}

.security-items {
	background: white;
	margin: 20rpx;
	border-radius: 30rpx;
	padding: 20rpx;
}

.security-item {
	display: flex;
	align-items: center;
	padding: 30rpx 20rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.security-item:last-child {
	border-bottom: none;
}

.item-icon {
	width: 60rpx;
	height: 60rpx;
	background: rgba(255, 138, 0, 0.1);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 20rpx;
}

.item-info {
	flex: 1;
}

.item-title {
	font-size: 28rpx;
	color: #333;
	font-weight: bold;
	display: block;
	margin-bottom: 8rpx;
}

.item-desc {
	font-size: 24rpx;
	color: #666;
	display: block;
}

.item-status {
	display: flex;
	align-items: center;
	gap: 10rpx;
}

.status-text {
	font-size: 24rpx;
	padding: 8rpx 16rpx;
	border-radius: 12rpx;
}

.status-text.safe {
	background: rgba(76, 175, 80, 0.1);
	color: #4caf50;
}

.status-text.warning {
	background: rgba(255, 152, 0, 0.1);
	color: #ff9800;
}

.security-tips {
	background: white;
	margin: 20rpx;
	border-radius: 30rpx;
	padding: 40rpx;
}

.tips-header {
	display: flex;
	align-items: center;
	gap: 15rpx;
	margin-bottom: 30rpx;
}

.tips-title {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
}

.tips-list {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

.tip-item {
	display: flex;
	align-items: center;
	gap: 15rpx;
}

.tip-text {
	font-size: 26rpx;
	color: #666;
	line-height: 1.5;
}
</style>
