<template>
	<view class="container">
		<!-- 页面头部 -->
		<PageHeader title="我的预约">
			<template #actions>
				<InteractiveButton
					type="primary"
					size="small"
					text="新建预约"
					icon="add-line"
					@click="createAppointment"
				></InteractiveButton>
			</template>
		</PageHeader>

		<!-- 状态筛选 -->
		<view class="status-filter">
			<scroll-view scroll-x="true" class="filter-scroll">
				<view class="filter-tabs">
					<view 
						v-for="(status, index) in statusOptions" 
						:key="index"
						class="filter-tab"
						:class="{ active: selectedStatus === status.value }"
						@click="selectStatus(status.value)"
					>
						<text class="tab-text">{{ status.label }}</text>
						<text class="tab-count" v-if="status.count > 0">({{ status.count }})</text>
					</view>
				</view>
			</scroll-view>
		</view>

		<!-- 预约列表 -->
		<scroll-view 
			scroll-y="true" 
			class="appointment-list"
			@scrolltolower="loadMore"
			refresher-enabled="true"
			@refresherrefresh="refreshData"
			:refresher-triggered="refreshing"
		>
			<InteractiveCard 
				v-for="(item, index) in filteredAppointments" 
				:key="item.id"
				class="appointment-item"
				@click="viewDetail(item)"
			>
				<view class="appointment-content">
					<!-- 服务图标 -->
					<view class="service-icon-container">
						<view class="service-icon" :style="{ background: getStatusGradient(item.status) }">
							<Icon :name="getServiceIcon(item.serviceType)" size="40rpx" color="#fff"></Icon>
						</view>
						<!-- 状态标识 -->
						<view class="status-badge" :class="item.status">
							<text class="status-text">{{ getStatusText(item.status) }}</text>
						</view>
					</view>

					<!-- 预约信息 -->
					<view class="appointment-info">
						<view class="appointment-header">
							<text class="service-name">{{ item.serviceName }}</text>
							<text class="appointment-time">{{ formatDateTime(item.appointmentTime) }}</text>
						</view>
						
						<view class="appointment-meta">
							<view class="meta-item">
								<Icon name="store-line" size="20rpx" color="#999"></Icon>
								<text class="meta-text">{{ item.provider }}</text>
							</view>
							<view class="meta-item">
								<Icon name="map-pin-line" size="20rpx" color="#999"></Icon>
								<text class="meta-text">{{ item.address }}</text>
							</view>
							<view class="meta-item">
								<Icon name="phone-line" size="20rpx" color="#999"></Icon>
								<text class="meta-text">{{ item.contactPhone }}</text>
							</view>
						</view>

						<view class="appointment-price">
							<text class="price-label">费用：</text>
							<text class="price-value">￥{{ item.totalCost }}</text>
						</view>
					</view>

					<!-- 操作按钮 -->
					<view class="appointment-actions">
						<InteractiveButton 
							v-if="item.status === 'pending'"
							type="secondary" 
							size="small" 
							text="取消" 
							icon="close-line"
							@click.stop="cancelAppointment(item)"
						></InteractiveButton>
						<InteractiveButton 
							v-if="item.status === 'confirmed'"
							type="primary" 
							size="small" 
							text="联系服务商" 
							icon="phone-line"
							@click.stop="contactProvider(item)"
						></InteractiveButton>
						<InteractiveButton 
							v-if="item.status === 'completed'"
							type="success" 
							size="small" 
							text="评价" 
							icon="star-line"
							@click.stop="rateService(item)"
						></InteractiveButton>
					</view>
				</view>
			</InteractiveCard>

			<!-- 加载状态 -->
			<view class="load-more" v-if="hasMore && loading">
				<view class="loading-spinner"></view>
				<text class="loading-text">加载中...</text>
			</view>
			<view class="no-more" v-else-if="!hasMore && filteredAppointments.length > 0">
				<text>没有更多预约了</text>
			</view>
			<view class="empty" v-else-if="!loading && filteredAppointments.length === 0">
				<Icon name="calendar-line" size="120rpx" color="#ccc"></Icon>
				<text class="empty-text">暂无预约记录</text>
				<text class="empty-tip">点击右上角新建预约</text>
			</view>
		</scroll-view>
	</view>
</template>

<script>
import InteractiveCard from '@/components/InteractiveCard/InteractiveCard.vue'
import InteractiveButton from '@/components/InteractiveButton/InteractiveButton.vue'
import Icon from '@/components/Icon/Icon.vue'
import PageHeader from '@/components/PageHeader/PageHeader.vue'
import FeedbackUtils from '@/utils/feedback.js'

export default {
	components: {
		InteractiveCard,
		InteractiveButton,
		Icon,
		PageHeader
	},
	data() {
		return {
			appointmentList: [],
			loading: false,
			refreshing: false,
			hasMore: true,
			page: 1,
			pageSize: 10,
			selectedStatus: '',
			
			// 状态选项
			statusOptions: [
				{ label: '全部', value: '', count: 0 },
				{ label: '待确认', value: 'pending', count: 0 },
				{ label: '已确认', value: 'confirmed', count: 0 },
				{ label: '进行中', value: 'ongoing', count: 0 },
				{ label: '已完成', value: 'completed', count: 0 },
				{ label: '已取消', value: 'cancelled', count: 0 }
			],
			
			// 模拟预约数据
			mockAppointments: [
				{
					id: 1,
					serviceName: '居家护理服务',
					serviceType: 'nursing',
					provider: '康护医疗',
					appointmentTime: '2024-01-20 14:00',
					address: '北京市朝阳区建国路88号',
					contactPhone: '138****1234',
					totalCost: 170,
					status: 'confirmed'
				},
				{
					id: 2,
					serviceName: '家政清洁服务',
					serviceType: 'cleaning',
					provider: '爱心家政',
					appointmentTime: '2024-01-18 09:00',
					address: '北京市朝阳区建国路88号',
					contactPhone: '139****5678',
					totalCost: 100,
					status: 'completed'
				}
			]
		}
	},
	computed: {
		// 筛选后的预约列表
		filteredAppointments() {
			if (!this.selectedStatus) {
				return this.appointmentList;
			}
			return this.appointmentList.filter(item => item.status === this.selectedStatus);
		}
	},
	onLoad() {
		this.loadAppointments();
		this.updateStatusCounts();
	},
	methods: {
		// 加载预约列表
		async loadAppointments() {
			this.loading = true;
			
			try {
				// 模拟加载数据
				await new Promise(resolve => setTimeout(resolve, 1000));
				
				if (this.page === 1) {
					this.appointmentList = [...this.mockAppointments];
				} else {
					this.appointmentList.push(...this.mockAppointments);
				}
				
				this.hasMore = false; // 模拟没有更多数据
				FeedbackUtils.showSuccess('预约列表加载完成');
			} catch (error) {
				FeedbackUtils.showError('加载失败，请重试');
			} finally {
				this.loading = false;
				this.refreshing = false;
			}
		},

		// 更新状态计数
		updateStatusCounts() {
			this.statusOptions.forEach(option => {
				if (option.value === '') {
					option.count = this.appointmentList.length;
				} else {
					option.count = this.appointmentList.filter(item => item.status === option.value).length;
				}
			});
		},

		// 选择状态
		selectStatus(status) {
			FeedbackUtils.lightFeedback();
			this.selectedStatus = status;
		},

		// 新建预约
		createAppointment() {
			FeedbackUtils.lightFeedback();
			uni.navigateTo({
				url: '/pages/service/find'
			});
		},

		// 查看详情
		viewDetail(item) {
			FeedbackUtils.lightFeedback();
			uni.navigateTo({
				url: `/pages/appointment/detail?id=${item.id}`
			});
		},

		// 取消预约
		cancelAppointment(item) {
			FeedbackUtils.lightFeedback();
			uni.showModal({
				title: '取消预约',
				content: '确定要取消这个预约吗？',
				success: (res) => {
					if (res.confirm) {
						item.status = 'cancelled';
						this.updateStatusCounts();
						FeedbackUtils.showSuccess('预约已取消');
					}
				}
			});
		},

		// 联系服务商
		contactProvider(item) {
			FeedbackUtils.lightFeedback();
			uni.makePhoneCall({
				phoneNumber: item.contactPhone.replace(/\*/g, '')
			});
		},

		// 评价服务
		rateService(item) {
			FeedbackUtils.lightFeedback();
			uni.navigateTo({
				url: `/pages/rating/create?appointmentId=${item.id}`
			});
		},

		// 下拉刷新
		refreshData() {
			FeedbackUtils.lightFeedback();
			this.refreshing = true;
			this.page = 1;
			this.hasMore = true;
			this.loadAppointments();
		},

		// 上拉加载更多
		loadMore() {
			if (this.hasMore && !this.loading) {
				this.page++;
				this.loadAppointments();
			}
		},

		// 获取服务图标
		getServiceIcon(serviceType) {
			const iconMap = {
				'nursing': 'heart-3-line',
				'cleaning': 'home-heart-line',
				'medical': 'health-book-line',
				'rehabilitation': 'run-line'
			};
			return iconMap[serviceType] || 'service-line';
		},

		// 获取状态渐变色
		getStatusGradient(status) {
			const gradientMap = {
				'pending': 'linear-gradient(135deg, #ff9800 0%, #f57c00 100%)',
				'confirmed': 'linear-gradient(135deg, #2196f3 0%, #1976d2 100%)',
				'ongoing': 'linear-gradient(135deg, #4caf50 0%, #388e3c 100%)',
				'completed': 'linear-gradient(135deg, #4caf50 0%, #388e3c 100%)',
				'cancelled': 'linear-gradient(135deg, #f44336 0%, #d32f2f 100%)'
			};
			return gradientMap[status] || 'linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%)';
		},

		// 获取状态文本
		getStatusText(status) {
			const statusMap = {
				'pending': '待确认',
				'confirmed': '已确认',
				'ongoing': '进行中',
				'completed': '已完成',
				'cancelled': '已取消'
			};
			return statusMap[status] || '未知';
		},

		// 格式化日期时间
		formatDateTime(dateTimeString) {
			const date = new Date(dateTimeString);
			const month = date.getMonth() + 1;
			const day = date.getDate();
			const hour = date.getHours();
			const minute = date.getMinutes();
			return `${month}月${day}日 ${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
		}
	}
}
</script>

<style scoped>
.container {
	background-color: #f5f5f5;
	min-height: 100vh;
}

.status-filter {
	background: white;
	padding: 20rpx 0;
	margin-bottom: 20rpx;
}

.filter-scroll {
	white-space: nowrap;
}

.filter-tabs {
	display: flex;
	padding: 0 20rpx;
	gap: 30rpx;
}

.filter-tab {
	display: flex;
	align-items: center;
	gap: 5rpx;
	padding: 15rpx 25rpx;
	border-radius: 25rpx;
	background: #f8f9fa;
	white-space: nowrap;
}

.filter-tab.active {
	background: linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%);
}

.tab-text {
	font-size: 26rpx;
	color: #666;
}

.filter-tab.active .tab-text {
	color: white;
}

.tab-count {
	font-size: 22rpx;
	color: #999;
}

.filter-tab.active .tab-count {
	color: rgba(255, 255, 255, 0.8);
}

.appointment-list {
	padding: 0 20rpx;
	height: calc(100vh - 200rpx);
}

.appointment-item {
	margin-bottom: 20rpx;
}

.appointment-content {
	display: flex;
	gap: 20rpx;
	padding: 25rpx;
}

.service-icon-container {
	position: relative;
}

.service-icon {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
}

.status-badge {
	position: absolute;
	top: -5rpx;
	right: -10rpx;
	padding: 5rpx 10rpx;
	border-radius: 15rpx;
	font-size: 20rpx;
}

.status-badge.pending { background: #ff9800; }
.status-badge.confirmed { background: #2196f3; }
.status-badge.ongoing { background: #4caf50; }
.status-badge.completed { background: #4caf50; }
.status-badge.cancelled { background: #f44336; }

.status-text {
	color: white;
	font-size: 20rpx;
}

.appointment-info {
	flex: 1;
}

.appointment-header {
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
	margin-bottom: 15rpx;
}

.service-name {
	font-size: 30rpx;
	font-weight: bold;
	color: #333;
	flex: 1;
}

.appointment-time {
	font-size: 24rpx;
	color: #ff8a00;
	font-weight: bold;
}

.appointment-meta {
	margin-bottom: 15rpx;
}

.meta-item {
	display: flex;
	align-items: center;
	gap: 8rpx;
	margin-bottom: 8rpx;
}

.meta-text {
	font-size: 24rpx;
	color: #666;
}

.appointment-price {
	display: flex;
	align-items: center;
	gap: 10rpx;
}

.price-label {
	font-size: 26rpx;
	color: #666;
}

.price-value {
	font-size: 28rpx;
	color: #ff8a00;
	font-weight: bold;
}

.appointment-actions {
	display: flex;
	flex-direction: column;
	gap: 10rpx;
	align-items: flex-end;
}

.load-more,
.no-more,
.empty {
	text-align: center;
	padding: 40rpx;
	color: #999;
}

.loading-spinner {
	width: 40rpx;
	height: 40rpx;
	border: 4rpx solid #f3f3f3;
	border-top: 4rpx solid #ff8a00;
	border-radius: 50%;
	animation: spin 1s linear infinite;
	margin: 0 auto 20rpx;
}

@keyframes spin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

.empty-text {
	font-size: 28rpx;
	color: #999;
	display: block;
	margin: 20rpx 0 10rpx;
}

.empty-tip {
	font-size: 24rpx;
	color: #ccc;
}
</style>
