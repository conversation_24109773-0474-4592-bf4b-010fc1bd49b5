<template>
	<view class="container">
		<!-- 页面头部 -->
		<PageHeader title="选择机构">
			<template #actions>
				<InteractiveButton
					type="secondary"
					size="small"
					text="对比"
					icon="scales-line"
					:disabled="compareList.length < 2"
					@click="showCompare"
				></InteractiveButton>
			</template>
		</PageHeader>

		<!-- 搜索和筛选 -->
		<view class="search-filter-section">
			<view class="search-container">
				<Icon name="search-line" size="32rpx" secondary class="search-icon"></Icon>
				<input
					class="search-input"
					placeholder="搜索机构名称或地址"
					v-model="searchKeyword"
					@input="onSearchInput"
				/>
				<view v-if="searchKeyword" class="clear-btn" @click="clearSearch">
					<Icon name="close-line" size="32rpx" secondary></Icon>
				</view>
			</view>
			<InteractiveButton 
				type="primary" 
				size="medium" 
				text="筛选" 
				icon="filter-line"
				@click="showFilter"
			></InteractiveButton>
		</view>

		<!-- 快速筛选标签 -->
		<view class="quick-filter">
			<scroll-view scroll-x="true" class="filter-scroll">
				<view class="filter-tags">
					<view 
						v-for="(tag, index) in quickFilters" 
						:key="index"
						class="filter-tag"
						:class="{ active: selectedQuickFilter === tag.value }"
						@click="selectQuickFilter(tag.value)"
					>
						<Icon :name="tag.icon" size="24rpx" :primary="selectedQuickFilter !== tag.value" :color="selectedQuickFilter === tag.value ? '#fff' : ''"></Icon>
						<text class="tag-text">{{ tag.label }}</text>
					</view>
				</view>
			</scroll-view>
		</view>

		<!-- 排序选项 -->
		<view class="sort-section">
			<view class="sort-options">
				<view 
					v-for="(sort, index) in sortOptions" 
					:key="index"
					class="sort-option"
					:class="{ active: selectedSort === sort.value }"
					@click="selectSort(sort.value)"
				>
					<text class="sort-text">{{ sort.label }}</text>
					<Icon v-if="selectedSort === sort.value" name="check-line" size="24rpx" primary></Icon>
				</view>
			</view>
		</view>

		<!-- 机构列表 -->
		<scroll-view 
			scroll-y="true" 
			class="institution-list"
			@scrolltolower="loadMore"
			refresher-enabled="true"
			@refresherrefresh="refreshData"
			:refresher-triggered="refreshing"
		>
			<InteractiveCard 
				v-for="(item, index) in filteredInstitutions" 
				:key="item.id"
				class="institution-item"
				:loading="false"
				@click="viewDetail(item)"
			>
				<view class="institution-content">
					<!-- 机构图片或图标占位符 -->
					<view class="institution-image-container">
						<image 
							v-if="item.image && !item.imageError" 
							:src="item.image" 
							class="institution-image" 
							mode="aspectFill"
							@error="handleImageError(item)"
						></image>
						<view v-else class="institution-icon-placeholder">
							<Icon name="building-line" size="48rpx" color="#fff" institution></Icon>
						</view>
						<!-- 对比选择框 -->
						<view class="compare-checkbox" @click.stop="toggleCompare(item)">
							<Icon 
								:name="isInCompare(item) ? 'checkbox-circle-fill' : 'checkbox-circle-line'" 
								size="32rpx" 
								:color="isInCompare(item) ? '#ff8a00' : '#ccc'"
							></Icon>
						</view>
					</view>

					<!-- 机构信息 -->
					<view class="institution-info">
						<view class="institution-header">
							<text class="institution-name">{{ item.name }}</text>
							<view class="institution-type" :style="{ backgroundColor: getTypeColor(item.type) }">
								<text class="type-text">{{ item.type }}</text>
							</view>
						</view>
						
						<view class="institution-address">
							<Icon name="location-line" size="24rpx" secondary></Icon>
							<text class="address-text">{{ item.address }}</text>
						</view>

						<view class="institution-features">
							<view class="feature-item">
								<Icon name="star-fill" size="24rpx" color="#ffc107"></Icon>
								<text class="feature-text">{{ item.rating }}分</text>
							</view>
							<view class="feature-item">
								<Icon name="hotel-bed-line" size="24rpx" secondary></Icon>
								<text class="feature-text">{{ item.beds }}床位</text>
							</view>
							<view class="feature-item">
								<Icon name="money-cny-circle-line" size="24rpx" secondary></Icon>
								<text class="feature-text">￥{{ item.price }}/月起</text>
							</view>
						</view>

						<view class="institution-tags" v-if="item.tags && item.tags.length > 0">
							<view 
								v-for="(tag, tagIndex) in item.tags.slice(0, 3)" 
								:key="tagIndex"
								class="tag-item"
							>
								<text class="tag-label">{{ tag }}</text>
							</view>
							<text v-if="item.tags.length > 3" class="more-tags">+{{ item.tags.length - 3 }}</text>
						</view>
					</view>

					<!-- 操作按钮 -->
					<view class="institution-actions">
						<InteractiveButton 
							type="secondary" 
							size="small" 
							text="电话" 
							icon="phone-line"
							@click.stop="callPhone(item)"
						></InteractiveButton>
						<InteractiveButton 
							type="primary" 
							size="small" 
							text="预约" 
							icon="calendar-check-line"
							@click.stop="makeAppointment(item)"
						></InteractiveButton>
					</view>
				</view>
			</InteractiveCard>

			<!-- 加载状态 -->
			<view class="load-more" v-if="hasMore && loading">
				<view class="loading-spinner"></view>
				<text class="loading-text">加载中...</text>
			</view>
			<view class="no-more" v-else-if="!hasMore && filteredInstitutions.length > 0">
				<text>没有更多机构了</text>
			</view>
			<view class="empty" v-else-if="!loading && filteredInstitutions.length === 0">
				<Icon name="building-line" size="120rpx" color="#ccc"></Icon>
				<text class="empty-text">暂无符合条件的机构</text>
				<text class="empty-tip">请尝试调整筛选条件</text>
			</view>
		</scroll-view>

		<!-- 对比浮动按钮 -->
		<view class="compare-float" v-if="compareList.length > 0" @click="showCompare">
			<Icon name="scales-line" size="32rpx" color="#fff"></Icon>
			<text class="compare-text">对比({{ compareList.length }})</text>
		</view>

		<!-- 筛选弹窗 -->
		<view v-if="showFilterPopup" class="popup-mask" @click="hideFilter">
			<view class="filter-panel" @click.stop>
				<view class="filter-header">
					<text class="filter-title">筛选条件</text>
					<view class="filter-close" @click="hideFilter">
						<Icon name="close-line" size="32rpx" color="#666"></Icon>
					</view>
				</view>
				
				<view class="filter-content">
					<!-- 机构类型 -->
					<view class="filter-section">
						<text class="section-title">机构类型</text>
						<view class="filter-options">
							<view 
								v-for="(type, index) in institutionTypes" 
								:key="index"
								class="filter-option"
								:class="{ active: selectedTypes.includes(type) }"
								@click="toggleType(type)"
							>
								<text class="option-text">{{ type }}</text>
							</view>
						</view>
					</view>

					<!-- 价格范围 -->
					<view class="filter-section">
						<text class="section-title">价格范围</text>
						<view class="filter-options">
							<view 
								v-for="(price, index) in priceRanges" 
								:key="index"
								class="filter-option"
								:class="{ active: selectedPriceRange === price.value }"
								@click="selectPriceRange(price.value)"
							>
								<text class="option-text">{{ price.label }}</text>
							</view>
						</view>
					</view>

					<!-- 评分要求 -->
					<view class="filter-section">
						<text class="section-title">评分要求</text>
						<view class="filter-options">
							<view 
								v-for="(rating, index) in ratingOptions" 
								:key="index"
								class="filter-option"
								:class="{ active: selectedRating === rating.value }"
								@click="selectRating(rating.value)"
							>
								<text class="option-text">{{ rating.label }}</text>
							</view>
						</view>
					</view>
				</view>

				<view class="filter-actions">
					<InteractiveButton 
						type="secondary" 
						size="large" 
						text="重置" 
						@click="resetFilter"
					></InteractiveButton>
					<InteractiveButton 
						type="primary" 
						size="large" 
						text="确定" 
						@click="applyFilter"
					></InteractiveButton>
				</view>
			</view>
		</view>

		<!-- 对比弹窗 -->
		<view v-if="showComparePopup" class="popup-mask" @click="hideCompare">
			<view class="compare-panel" @click.stop>
				<view class="compare-header">
					<text class="compare-title">机构对比</text>
					<view class="compare-close" @click="hideCompare">
						<Icon name="close-line" size="32rpx" color="#666"></Icon>
					</view>
				</view>
				
				<scroll-view scroll-y="true" class="compare-content">
					<view class="compare-table">
						<!-- 对比项目 -->
						<view class="compare-row header-row">
							<text class="compare-label">对比项目</text>
							<view 
								v-for="(item, index) in compareList" 
								:key="item.id"
								class="compare-cell"
							>
								<text class="institution-name">{{ item.name }}</text>
							</view>
						</view>

						<!-- 具体对比内容 -->
						<view class="compare-row">
							<text class="compare-label">机构类型</text>
							<view 
								v-for="(item, index) in compareList" 
								:key="item.id"
								class="compare-cell"
							>
								<text class="compare-value">{{ item.type }}</text>
							</view>
						</view>

						<view class="compare-row">
							<text class="compare-label">评分</text>
							<view 
								v-for="(item, index) in compareList" 
								:key="item.id"
								class="compare-cell"
							>
								<text class="compare-value">{{ item.rating }}分</text>
							</view>
						</view>

						<view class="compare-row">
							<text class="compare-label">床位数</text>
							<view 
								v-for="(item, index) in compareList" 
								:key="item.id"
								class="compare-cell"
							>
								<text class="compare-value">{{ item.beds }}床</text>
							</view>
						</view>

						<view class="compare-row">
							<text class="compare-label">起始价格</text>
							<view 
								v-for="(item, index) in compareList" 
								:key="item.id"
								class="compare-cell"
							>
								<text class="compare-value">￥{{ item.price }}/月</text>
							</view>
						</view>
					</view>
				</scroll-view>

				<view class="compare-actions">
					<InteractiveButton 
						type="secondary" 
						size="large" 
						text="清空对比" 
						@click="clearCompare"
					></InteractiveButton>
					<InteractiveButton 
						type="primary" 
						size="large" 
						text="查看详情" 
						@click="viewCompareDetails"
					></InteractiveButton>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import InteractiveCard from '@/components/InteractiveCard/InteractiveCard.vue'
import InteractiveButton from '@/components/InteractiveButton/InteractiveButton.vue'
import Icon from '@/components/Icon/Icon.vue'
import PageHeader from '@/components/PageHeader/PageHeader.vue'
import FeedbackUtils from '@/utils/feedback.js'
import OfflineDataManager from '@/utils/offlineData.js'

export default {
	components: {
		InteractiveCard,
		InteractiveButton,
		Icon,
		PageHeader
	},
	data() {
		return {
			institutionList: [],
			compareList: [],
			loading: false,
			refreshing: false,
			hasMore: true,
			page: 1,
			pageSize: 10,

			// 弹窗控制
			showFilterPopup: false,
			showComparePopup: false,

			// 搜索和筛选
			searchKeyword: '',
			selectedQuickFilter: '',
			selectedSort: 'default',
			selectedTypes: [],
			selectedPriceRange: '',
			selectedRating: '',
			
			// 快速筛选选项
			quickFilters: [
				{ label: '全部', value: '', icon: 'apps-line' },
				{ label: '养老院', value: '养老院', icon: 'building-line' },
				{ label: '护理院', value: '护理院', icon: 'health-book-line' },
				{ label: '老年公寓', value: '老年公寓', icon: 'home-line' },
				{ label: '日间照料', value: '日间照料', icon: 'sun-line' }
			],
			
			// 排序选项
			sortOptions: [
				{ label: '默认排序', value: 'default' },
				{ label: '评分最高', value: 'rating' },
				{ label: '价格最低', value: 'price' },
				{ label: '距离最近', value: 'distance' }
			],
			
			// 筛选选项
			institutionTypes: ['养老院', '护理院', '老年公寓', '日间照料中心'],
			priceRanges: [
				{ label: '不限', value: '' },
				{ label: '2000以下', value: '0-2000' },
				{ label: '2000-5000', value: '2000-5000' },
				{ label: '5000-8000', value: '5000-8000' },
				{ label: '8000以上', value: '8000-' }
			],
			ratingOptions: [
				{ label: '不限', value: '' },
				{ label: '3分以上', value: '3' },
				{ label: '4分以上', value: '4' },
				{ label: '4.5分以上', value: '4.5' }
			]
		}
	},
	computed: {
		// 筛选后的机构列表
		filteredInstitutions() {
			let list = [...this.institutionList];
			
			// 搜索筛选
			if (this.searchKeyword) {
				const keyword = this.searchKeyword.toLowerCase();
				list = list.filter(item => 
					item.name.toLowerCase().includes(keyword) ||
					item.address.toLowerCase().includes(keyword)
				);
			}
			
			// 快速筛选
			if (this.selectedQuickFilter) {
				list = list.filter(item => item.type === this.selectedQuickFilter);
			}
			
			// 类型筛选
			if (this.selectedTypes.length > 0) {
				list = list.filter(item => this.selectedTypes.includes(item.type));
			}
			
			// 价格筛选
			if (this.selectedPriceRange) {
				if (this.selectedPriceRange.includes('-')) {
					const [min, max] = this.selectedPriceRange.split('-');
					if (max) {
						list = list.filter(item => item.price >= parseInt(min) && item.price <= parseInt(max));
					} else {
						list = list.filter(item => item.price >= parseInt(min));
					}
				}
			}
			
			// 评分筛选
			if (this.selectedRating) {
				const minRating = parseFloat(this.selectedRating);
				list = list.filter(item => item.rating >= minRating);
			}
			
			// 排序
			if (this.selectedSort === 'rating') {
				list.sort((a, b) => b.rating - a.rating);
			} else if (this.selectedSort === 'price') {
				list.sort((a, b) => a.price - b.price);
			} else if (this.selectedSort === 'distance') {
				list.sort((a, b) => (a.distance || 0) - (b.distance || 0));
			}
			
			return list;
		}
	},
	onLoad() {
		// 初始化离线数据
		OfflineDataManager.initOfflineData();

		// 加载机构列表（带网络检测）
		this.loadInstitutions();
	},
	methods: {
		// 加载机构列表 - 100%离线模式
		async loadInstitutions() {
			this.loading = true;

			try {
				// 强制初始化离线数据
				OfflineDataManager.initOfflineData();

				// 100%使用离线数据，确保用户能看到内容
				const params = {
					page: this.page,
					pageSize: this.pageSize,
					keyword: this.searchKeyword
				};

				const result = OfflineDataManager.getOfflineInstitutions(params);

				if (this.page === 1) {
					this.institutionList = result.data;
				} else {
					this.institutionList.push(...result.data);
				}

				this.hasMore = result.data.length === this.pageSize;

				// 显示成功提示
				if (this.page === 1 && result.data.length > 0) {
					FeedbackUtils.showSuccess(`已加载 ${result.data.length} 个机构`);
				}
			} catch (error) {
				console.error('加载机构数据失败:', error);
				// 使用备用数据确保页面有内容
				this.institutionList = [{
					id: 1,
					name: '阳光养老院',
					type: '综合型',
					address: '北京市朝阳区阳光街123号',
					phone: '010-12345678',
					rating: 4.8,
					price: 3500,
					beds: 200,
					availableBeds: 15,
					image: '/picture/b3bc07f949264b36811e26cf01c7f50c.jpeg',
					imageError: false
				}];
				FeedbackUtils.showInfo('已加载备用数据');
			} finally {
				this.loading = false;
				this.refreshing = false;
			}
		},

		// 搜索输入处理
		onSearchInput() {
			// 实时搜索功能
		},

		// 清空搜索
		clearSearch() {
			FeedbackUtils.lightFeedback();
			this.searchKeyword = '';
		},

		// 选择快速筛选
		selectQuickFilter(filter) {
			FeedbackUtils.lightFeedback();
			this.selectedQuickFilter = filter;
		},

		// 选择排序
		selectSort(sort) {
			FeedbackUtils.lightFeedback();
			this.selectedSort = sort;
		},

		// 显示筛选
		showFilter() {
			FeedbackUtils.lightFeedback();
			this.showFilterPopup = true;
		},

		// 隐藏筛选
		hideFilter() {
			FeedbackUtils.lightFeedback();
			this.showFilterPopup = false;
		},

		// 切换类型选择
		toggleType(type) {
			FeedbackUtils.lightFeedback();
			const index = this.selectedTypes.indexOf(type);
			if (index > -1) {
				this.selectedTypes.splice(index, 1);
			} else {
				this.selectedTypes.push(type);
			}
		},

		// 选择价格范围
		selectPriceRange(range) {
			FeedbackUtils.lightFeedback();
			this.selectedPriceRange = range;
		},

		// 选择评分
		selectRating(rating) {
			FeedbackUtils.lightFeedback();
			this.selectedRating = rating;
		},

		// 重置筛选
		resetFilter() {
			FeedbackUtils.lightFeedback();
			this.selectedTypes = [];
			this.selectedPriceRange = '';
			this.selectedRating = '';
		},

		// 应用筛选
		applyFilter() {
			FeedbackUtils.lightFeedback();
			this.hideFilter();
			FeedbackUtils.showSuccess('筛选条件已应用');
		},

		// 下拉刷新
		refreshData() {
			FeedbackUtils.lightFeedback();
			this.refreshing = true;
			this.page = 1;
			this.hasMore = true;
			this.loadInstitutions();
		},

		// 上拉加载更多
		loadMore() {
			if (this.hasMore && !this.loading) {
				this.page++;
				this.loadInstitutions();
			}
		},

		// 查看详情
		viewDetail(item) {
			FeedbackUtils.lightFeedback();
			uni.navigateTo({
				url: `/pages/institution/detail?id=${item.id}`
			});
		},

		// 切换对比
		toggleCompare(item) {
			FeedbackUtils.lightFeedback();
			const index = this.compareList.findIndex(compare => compare.id === item.id);
			if (index > -1) {
				this.compareList.splice(index, 1);
				FeedbackUtils.showInfo('已移出对比');
			} else {
				if (this.compareList.length >= 3) {
					FeedbackUtils.showError('最多只能对比3个机构');
					return;
				}
				this.compareList.push(item);
				FeedbackUtils.showSuccess('已加入对比');
			}
		},

		// 检查是否在对比列表中
		isInCompare(item) {
			return this.compareList.some(compare => compare.id === item.id);
		},

		// 显示对比
		showCompare() {
			if (this.compareList.length < 2) {
				FeedbackUtils.showError('至少选择2个机构进行对比');
				return;
			}
			FeedbackUtils.lightFeedback();
			this.showComparePopup = true;
		},

		// 隐藏对比
		hideCompare() {
			FeedbackUtils.lightFeedback();
			this.showComparePopup = false;
		},

		// 清空对比
		clearCompare() {
			FeedbackUtils.lightFeedback();
			this.compareList = [];
			this.hideCompare();
			FeedbackUtils.showSuccess('已清空对比列表');
		},

		// 查看对比详情
		viewCompareDetails() {
			FeedbackUtils.lightFeedback();
			// 可以跳转到详细对比页面
			FeedbackUtils.showInfo('详细对比功能开发中');
		},

		// 拨打电话
		callPhone(item) {
			FeedbackUtils.lightFeedback();
			uni.makePhoneCall({
				phoneNumber: item.phone,
				fail: () => {
					FeedbackUtils.showError('拨打电话失败');
				}
			});
		},

		// 预约参观
		makeAppointment(item) {
			FeedbackUtils.lightFeedback();
			uni.navigateTo({
				url: `/pages/appointment/create?institutionId=${item.id}`
			});
		},

		// 获取类型颜色
		getTypeColor(type) {
			const colorMap = {
				'养老院': '#ff8a00',
				'护理院': '#4caf50',
				'老年公寓': '#2196f3',
				'日间照料中心': '#9c27b0'
			};
			return colorMap[type] || '#666';
		},

		// 处理图片加载错误
		handleImageError(item) {
			item.imageError = true;
		}
	}
}
</script>

<style scoped>
.container {
	background-color: #f5f5f5;
	min-height: 100vh;
	display: flex;
	flex-direction: column;
}

.page-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 30rpx 40rpx;
	background: white;
	border-bottom: 1rpx solid #f0f0f0;
}

.page-title {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
}

.search-filter-section {
	display: flex;
	align-items: center;
	padding: 20rpx 40rpx;
	background: white;
	border-bottom: 1rpx solid #f0f0f0;
	gap: 20rpx;
}

.search-container {
	flex: 1;
	position: relative;
	display: flex;
	align-items: center;
	background: #f8f8f8;
	border-radius: 25rpx;
	height: 80rpx;
}

.search-icon {
	position: absolute;
	left: 20rpx;
	z-index: 1;
}

.search-input {
	flex: 1;
	height: 100%;
	padding: 0 60rpx 0 60rpx;
	border: none;
	border-radius: 25rpx;
	font-size: 28rpx;
	background: transparent;
}

.clear-btn {
	position: absolute;
	right: 20rpx;
	padding: 10rpx;
}

.quick-filter {
	background: white;
	border-bottom: 1rpx solid #f0f0f0;
}

.filter-scroll {
	white-space: nowrap;
}

.filter-tags {
	display: flex;
	padding: 20rpx 40rpx;
	gap: 20rpx;
}

.filter-tag {
	display: flex;
	align-items: center;
	gap: 8rpx;
	padding: 15rpx 20rpx;
	border-radius: 20rpx;
	background: rgba(255, 138, 0, 0.1);
	white-space: nowrap;
	transition: all 0.3s ease;
}

.filter-tag.active {
	background: linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%);
}

.tag-text {
	font-size: 24rpx;
	color: #ff8a00;
}

.filter-tag.active .tag-text {
	color: white;
	font-weight: 500;
}

.sort-section {
	background: white;
	border-bottom: 1rpx solid #f0f0f0;
}

.sort-options {
	display: flex;
	padding: 0 40rpx;
}

.sort-option {
	flex: 1;
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 8rpx;
	padding: 25rpx 15rpx;
	position: relative;
}

.sort-option.active {
	border-bottom: 4rpx solid #ff8a00;
}

.sort-text {
	font-size: 26rpx;
	color: #666;
}

.sort-option.active .sort-text {
	color: #ff8a00;
	font-weight: bold;
}

.institution-list {
	flex: 1;
	padding: 20rpx;
}

.institution-item {
	margin-bottom: 20rpx;
}

.institution-content {
	display: flex;
	padding: 30rpx;
	gap: 20rpx;
}

.institution-image-container {
	position: relative;
	flex-shrink: 0;
	width: 180rpx;
	height: 140rpx;
}

.institution-image {
	width: 100%;
	height: 100%;
	border-radius: 15rpx;
}

.institution-icon-placeholder {
	width: 100%;
	height: 100%;
	border-radius: 15rpx;
	background: linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%);
	display: flex;
	align-items: center;
	justify-content: center;
}

.compare-checkbox {
	position: absolute;
	top: 10rpx;
	right: 10rpx;
	background: rgba(255, 255, 255, 0.9);
	border-radius: 50%;
	padding: 5rpx;
}

.institution-info {
	flex: 1;
	display: flex;
	flex-direction: column;
	gap: 12rpx;
}

.institution-header {
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
	gap: 15rpx;
}

.institution-name {
	flex: 1;
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	line-height: 1.3;
}

.institution-type {
	flex-shrink: 0;
	padding: 6rpx 12rpx;
	border-radius: 12rpx;
}

.type-text {
	font-size: 20rpx;
	color: white;
	font-weight: 500;
}

.institution-address {
	display: flex;
	align-items: center;
	gap: 8rpx;
}

.address-text {
	font-size: 24rpx;
	color: #666;
	line-height: 1.4;
}

.institution-features {
	display: flex;
	gap: 20rpx;
}

.feature-item {
	display: flex;
	align-items: center;
	gap: 6rpx;
}

.feature-text {
	font-size: 24rpx;
	color: #666;
}

.institution-tags {
	display: flex;
	align-items: center;
	gap: 10rpx;
	flex-wrap: wrap;
}

.tag-item {
	padding: 4rpx 10rpx;
	background: rgba(255, 138, 0, 0.1);
	border-radius: 10rpx;
}

.tag-label {
	font-size: 20rpx;
	color: #ff8a00;
}

.more-tags {
	font-size: 20rpx;
	color: #999;
}

.institution-actions {
	display: flex;
	flex-direction: column;
	gap: 15rpx;
	justify-content: center;
}

.compare-float {
	position: fixed;
	bottom: 40rpx;
	right: 40rpx;
	background: linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%);
	border-radius: 30rpx;
	padding: 20rpx 30rpx;
	display: flex;
	align-items: center;
	gap: 10rpx;
	box-shadow: 0 4rpx 20rpx rgba(255, 138, 0, 0.3);
	z-index: 100;
}

.compare-text {
	font-size: 26rpx;
	color: white;
	font-weight: 500;
}

.load-more {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 40rpx;
	gap: 20rpx;
}

.loading-spinner {
	width: 40rpx;
	height: 40rpx;
	border: 3rpx solid rgba(255, 138, 0, 0.2);
	border-top: 3rpx solid #ff8a00;
	border-radius: 50%;
	animation: loading 1s linear infinite;
}

.loading-text {
	font-size: 26rpx;
	color: #999;
}

.no-more {
	text-align: center;
	padding: 40rpx;
	color: #999;
	font-size: 26rpx;
}

.empty {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 100rpx 40rpx;
	gap: 20rpx;
}

.empty-text {
	font-size: 28rpx;
	color: #999;
}

.empty-tip {
	font-size: 24rpx;
	color: #ccc;
}

/* 筛选面板 */
.filter-panel {
	background: white;
	border-radius: 30rpx 30rpx 0 0;
	max-height: 80vh;
	overflow-y: auto;
}

.filter-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 30rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.filter-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.filter-close {
	padding: 10rpx;
}

.filter-content {
	padding: 30rpx;
}

.filter-section {
	margin-bottom: 40rpx;
}

.section-title {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 20rpx;
}

.filter-options {
	display: flex;
	flex-wrap: wrap;
	gap: 15rpx;
}

.filter-option {
	padding: 15rpx 25rpx;
	border: 2rpx solid #e0e0e0;
	border-radius: 25rpx;
	background: white;
	transition: all 0.3s ease;
}

.filter-option.active {
	border-color: #ff8a00;
	background: rgba(255, 138, 0, 0.1);
}

.option-text {
	font-size: 26rpx;
	color: #666;
}

.filter-option.active .option-text {
	color: #ff8a00;
	font-weight: 500;
}

.filter-actions {
	display: flex;
	gap: 20rpx;
	padding: 30rpx;
	border-top: 1rpx solid #f0f0f0;
}

/* 对比面板 */
.compare-panel {
	background: white;
	border-radius: 30rpx;
	width: 700rpx;
	max-height: 80vh;
	overflow: hidden;
}

.compare-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 30rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.compare-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.compare-close {
	padding: 10rpx;
}

.compare-content {
	max-height: 50vh;
	padding: 20rpx;
}

.compare-table {
	display: flex;
	flex-direction: column;
}

.compare-row {
	display: flex;
	border-bottom: 1rpx solid #f0f0f0;
	min-height: 80rpx;
	align-items: center;
}

.compare-row.header-row {
	background: #f8f8f8;
	font-weight: bold;
}

.compare-label {
	width: 150rpx;
	padding: 20rpx 15rpx;
	font-size: 26rpx;
	color: #333;
	border-right: 1rpx solid #f0f0f0;
	flex-shrink: 0;
}

.compare-cell {
	flex: 1;
	padding: 20rpx 15rpx;
	border-right: 1rpx solid #f0f0f0;
	text-align: center;
}

.compare-cell:last-child {
	border-right: none;
}

.compare-value {
	font-size: 24rpx;
	color: #666;
}

.compare-actions {
	display: flex;
	gap: 20rpx;
	padding: 30rpx;
	border-top: 1rpx solid #f0f0f0;
}

@keyframes loading {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

/* 弹窗样式 */
.popup-mask {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: flex-end;
	justify-content: center;
	z-index: 9999;
}

.filter-panel, .compare-panel {
	background: white;
	border-radius: 20rpx 20rpx 0 0;
	width: 100%;
	max-height: 80vh;
	display: flex;
	flex-direction: column;
	animation: slideUp 0.3s ease-out;
}

.compare-panel {
	align-items: center;
	max-width: 90vw;
	max-height: 70vh;
	border-radius: 20rpx;
}

@keyframes slideUp {
	from {
		transform: translateY(100%);
	}
	to {
		transform: translateY(0);
	}
}
</style>
