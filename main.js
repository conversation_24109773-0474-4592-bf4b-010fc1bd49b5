import App from './App'

// 导入全局组件
import Icon from '@/components/Icon/Icon.vue'
import LoadingSkeleton from '@/components/LoadingSkeleton/LoadingSkeleton.vue'
import ErrorBoundary from '@/components/ErrorBoundary/ErrorBoundary.vue'
import LazyImage from '@/components/LazyImage/LazyImage.vue'
import InteractiveCard from '@/components/InteractiveCard/InteractiveCard.vue'
import InteractiveButton from '@/components/InteractiveButton/InteractiveButton.vue'
import FormBuilder from '@/components/FormBuilder/FormBuilder.vue'
import PageHeader from '@/components/PageHeader/PageHeader.vue'

// 导入工具类
import FeedbackUtils from '@/utils/feedback.js'
import OfflineDataManager from '@/utils/offlineData.js'
import MockAPI from '@/utils/mockData.js'

// #ifndef VUE3
import Vue from 'vue'
// import './uni.promisify.adaptor' // 注释掉不存在的文件

Vue.config.productionTip = false

// 注册全局组件
Vue.component('Icon', Icon)
Vue.component('LoadingSkeleton', LoadingSkeleton)
Vue.component('ErrorBoundary', ErrorBoundary)
Vue.component('LazyImage', LazyImage)
Vue.component('InteractiveCard', InteractiveCard)
Vue.component('InteractiveButton', InteractiveButton)
Vue.component('FormBuilder', FormBuilder)
Vue.component('PageHeader', PageHeader)

// 挂载全局工具类
Vue.prototype.$feedback = FeedbackUtils
Vue.prototype.$offlineData = OfflineDataManager
Vue.prototype.$mockAPI = MockAPI

App.mpType = 'app'
const app = new Vue({
  ...App
})
app.$mount()
// #endif

// #ifdef VUE3
import { createSSRApp } from 'vue'
export function createApp() {
  const app = createSSRApp(App)

  // 注册全局组件
  app.component('Icon', Icon)
  app.component('LoadingSkeleton', LoadingSkeleton)
  app.component('ErrorBoundary', ErrorBoundary)
  app.component('LazyImage', LazyImage)
  app.component('InteractiveCard', InteractiveCard)
  app.component('InteractiveButton', InteractiveButton)
  app.component('FormBuilder', FormBuilder)
  app.component('PageHeader', PageHeader)

  // 挂载全局工具类
  app.config.globalProperties.$feedback = FeedbackUtils
  app.config.globalProperties.$offlineData = OfflineDataManager
  app.config.globalProperties.$mockAPI = MockAPI

  return {
    app
  }
}
// #endif