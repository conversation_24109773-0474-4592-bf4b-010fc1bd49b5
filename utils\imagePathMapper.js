/**
 * 图片路径映射工具
 * 将项目中的图片路径统一映射到picture文件夹
 */

// 图片路径映射配置
export const ImagePathMapping = {
  // 机构图片映射
  '/static/institutions/institution1.jpg': '/picture/nursing_home_1.jpg',
  '/static/institutions/institution2.jpg': '/picture/nursing_home_2.jpg',
  '/static/institutions/institution3.jpg': '/picture/nursing_home_3.jpg',
  
  // 服务图片映射
  '/static/services/service1.jpg': '/picture/nursing_home_1.jpg', // 复用养老院图片作为服务图片
  '/static/services/service2.jpg': '/picture/nursing_home_2.jpg',
  '/static/services/service3.jpg': '/picture/nursing_home_3.jpg',
  
  // 资讯图片映射
  '/static/news/news1.jpg': '/picture/nursing_home_1.jpg',
  '/static/news/news2.jpg': '/picture/nursing_home_2.jpg',
  '/static/news/news3.jpg': '/picture/nursing_home_3.jpg',
  
  // 首页插图映射
  '/static/illustrations/elderly-care.png': '/picture/nursing_home_1.jpg',
  
  // 机构详情图片映射
  '/static/institution/detail1.jpg': '/picture/nursing_home_1.jpg',
  '/static/institution/detail2.jpg': '/picture/nursing_home_2.jpg',
  '/static/institution/detail3.jpg': '/picture/nursing_home_3.jpg',
  '/static/institution/detail4.jpg': '/picture/nursing_home_1.jpg',
  
  // 地图标记图片映射
  '/static/markers/institution.png': '/picture/nursing_home_1.jpg',
  '/static/markers/hospital.png': '/picture/nursing_home_2.jpg',
  '/static/markers/service.png': '/picture/nursing_home_3.jpg',
  
  // 分享图片映射
  '/static/share/subsidy.png': '/picture/nursing_home_1.jpg',
  '/static/share/institution.png': '/picture/nursing_home_2.jpg',
  '/static/share/service.png': '/picture/nursing_home_3.jpg',
  
  // 测试图片映射
  '/static/test/image1.jpg': '/picture/nursing_home_1.jpg',
  '/static/test/error.jpg': '/picture/nursing_home_2.jpg',
  
  // 默认占位图片
  '/static/placeholder/default.jpg': '/picture/nursing_home_1.jpg',
  '/static/placeholder/institution.jpg': '/picture/nursing_home_2.jpg',
  '/static/placeholder/service.jpg': '/picture/nursing_home_3.jpg'
}

/**
 * 获取映射后的图片路径
 * @param {string} originalPath 原始图片路径
 * @returns {string} 映射后的图片路径
 */
export function getMappedImagePath(originalPath) {
  // 如果已经是picture路径，直接返回
  if (originalPath && originalPath.startsWith('/picture/')) {
    return originalPath
  }
  
  // 查找映射路径
  const mappedPath = ImagePathMapping[originalPath]
  if (mappedPath) {
    return mappedPath
  }
  
  // 如果没有找到映射，返回默认图片
  return '/picture/nursing_home_1.jpg'
}

/**
 * 批量替换对象中的图片路径
 * @param {object} obj 包含图片路径的对象
 * @returns {object} 替换后的对象
 */
export function replaceImagePaths(obj) {
  if (!obj || typeof obj !== 'object') {
    return obj
  }
  
  // 如果是数组
  if (Array.isArray(obj)) {
    return obj.map(item => replaceImagePaths(item))
  }
  
  // 如果是对象
  const result = {}
  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      const value = obj[key]
      
      // 如果是图片相关的字段
      if (isImageField(key) && typeof value === 'string') {
        result[key] = getMappedImagePath(value)
      }
      // 如果是图片数组
      else if (key === 'images' && Array.isArray(value)) {
        result[key] = value.map(path => getMappedImagePath(path))
      }
      // 递归处理嵌套对象
      else if (typeof value === 'object') {
        result[key] = replaceImagePaths(value)
      }
      // 其他字段直接复制
      else {
        result[key] = value
      }
    }
  }
  
  return result
}

/**
 * 判断字段是否为图片字段
 * @param {string} fieldName 字段名
 * @returns {boolean} 是否为图片字段
 */
function isImageField(fieldName) {
  const imageFields = [
    'image', 'img', 'src', 'icon', 'avatar', 'cover', 'banner', 
    'thumbnail', 'photo', 'picture', 'iconPath', 'backgroundImage'
  ]
  
  return imageFields.some(field => 
    fieldName.toLowerCase().includes(field.toLowerCase())
  )
}

/**
 * 获取可用的图片列表
 * @returns {array} 可用图片路径列表
 */
export function getAvailableImages() {
  return [
    '/picture/nursing_home_1.jpg',
    '/picture/nursing_home_2.jpg',
    '/picture/nursing_home_3.jpg'
  ]
}

/**
 * 随机获取一张图片
 * @returns {string} 随机图片路径
 */
export function getRandomImage() {
  const images = getAvailableImages()
  const randomIndex = Math.floor(Math.random() * images.length)
  return images[randomIndex]
}

/**
 * 根据类型获取对应的图片
 * @param {string} type 类型（institution, service, news等）
 * @returns {string} 对应的图片路径
 */
export function getImageByType(type) {
  const typeMapping = {
    'institution': '/picture/nursing_home_1.jpg',
    'service': '/picture/nursing_home_2.jpg',
    'news': '/picture/nursing_home_3.jpg',
    'subsidy': '/picture/nursing_home_1.jpg',
    'default': '/picture/nursing_home_1.jpg'
  }
  
  return typeMapping[type] || typeMapping['default']
}

/**
 * 检查图片是否存在（模拟检查）
 * @param {string} imagePath 图片路径
 * @returns {boolean} 图片是否存在
 */
export function checkImageExists(imagePath) {
  const availableImages = getAvailableImages()
  return availableImages.includes(imagePath)
}

export default {
  ImagePathMapping,
  getMappedImagePath,
  replaceImagePaths,
  getAvailableImages,
  getRandomImage,
  getImageByType,
  checkImageExists
}
