<template>
	<view class="container">
		<!-- 自定义导航栏 -->
		<view class="custom-navbar">
			<view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
			<view class="navbar-content">
				<view class="navbar-left" @click="goBack">
					<Icon name="arrow-left-line" size="36rpx" color="#333"></Icon>
					<text class="back-text">返回</text>
				</view>
				<view class="navbar-center">
					<text class="navbar-title">交易详情</text>
				</view>
				<view class="navbar-right"></view>
			</view>
		</view>

		<!-- 交易状态 -->
		<view class="status-section">
			<view class="status-icon" :class="transactionDetail.status">
				<Icon :name="getStatusIcon(transactionDetail.status)" size="60rpx" color="white"></Icon>
			</view>
			<text class="status-text">{{getStatusText(transactionDetail.status)}}</text>
			<text class="amount-text" :class="transactionDetail.type">
				{{transactionDetail.type === 'income' ? '+' : '-'}}¥{{transactionDetail.amount}}
			</text>
		</view>

		<!-- 交易信息 -->
		<view class="detail-section">
			<view class="detail-item">
				<text class="detail-label">交易类型</text>
				<text class="detail-value">{{transactionDetail.title}}</text>
			</view>
			<view class="detail-item">
				<text class="detail-label">交易说明</text>
				<text class="detail-value">{{transactionDetail.description}}</text>
			</view>
			<view class="detail-item">
				<text class="detail-label">交易时间</text>
				<text class="detail-value">{{transactionDetail.fullTime}}</text>
			</view>
			<view class="detail-item">
				<text class="detail-label">交易单号</text>
				<view class="detail-value-row">
					<text class="detail-value">{{transactionDetail.orderNumber}}</text>
					<button class="copy-btn" @click="copyOrderNumber">复制</button>
				</view>
			</view>
			<view class="detail-item" v-if="transactionDetail.paymentMethod">
				<text class="detail-label">支付方式</text>
				<text class="detail-value">{{transactionDetail.paymentMethod}}</text>
			</view>
			<view class="detail-item" v-if="transactionDetail.fee">
				<text class="detail-label">手续费</text>
				<text class="detail-value">¥{{transactionDetail.fee}}</text>
			</view>
		</view>

		<!-- 操作按钮 -->
		<view class="action-section" v-if="showActions">
			<button class="action-btn secondary" @click="contactService">联系客服</button>
			<button class="action-btn primary" @click="reorder" v-if="canReorder">再次购买</button>
		</view>

		<!-- 功能开发中提示 -->
		<view class="dev-notice">
			<Icon name="tools-line" size="32rpx" color="#ff8a00"></Icon>
			<text class="dev-text">此功能正在开发中，敬请期待</text>
		</view>
	</view>
</template>

<script>
import Icon from '@/components/Icon/Icon.vue'

export default {
	components: {
		Icon
	},
	data() {
		return {
			statusBarHeight: 0,
			transactionId: '',
			transactionDetail: {
				id: 1,
				type: 'expense',
				title: '居家护理服务',
				description: '订单支付 - 护理服务费用',
				amount: '120.00',
				status: 'completed',
				fullTime: '2024年01月15日 14:30:25',
				orderNumber: 'TXN202401151430001',
				paymentMethod: '钱包余额',
				fee: '0.00'
			}
		}
	},
	computed: {
		showActions() {
			return this.transactionDetail.status === 'completed' || this.transactionDetail.status === 'failed';
		},
		canReorder() {
			return this.transactionDetail.type === 'expense' && this.transactionDetail.status === 'completed';
		}
	},
	onLoad(options) {
		const systemInfo = uni.getSystemInfoSync();
		this.statusBarHeight = systemInfo.statusBarHeight || 44;
		
		if (options.id) {
			this.transactionId = options.id;
			this.loadTransactionDetail();
		}
	},
	methods: {
		loadTransactionDetail() {
			// 模拟加载交易详情
			console.log('加载交易详情:', this.transactionId);
		},
		getStatusIcon(status) {
			const iconMap = {
				'completed': 'check-line',
				'processing': 'time-line',
				'failed': 'close-line'
			};
			return iconMap[status] || 'question-line';
		},
		getStatusText(status) {
			const statusMap = {
				'completed': '交易成功',
				'processing': '处理中',
				'failed': '交易失败'
			};
			return statusMap[status] || '未知状态';
		},
		copyOrderNumber() {
			uni.setClipboardData({
				data: this.transactionDetail.orderNumber,
				success: () => {
					uni.showToast({
						title: '已复制到剪贴板',
						icon: 'success'
					});
				}
			});
		},
		contactService() {
			uni.showToast({
				title: '客服功能开发中',
				icon: 'none',
				duration: 2000
			});
		},
		reorder() {
			uni.showToast({
				title: '再次购买功能开发中',
				icon: 'none',
				duration: 2000
			});
		},
		goBack() {
			uni.navigateBack({
				fail: () => {
					uni.navigateTo({
						url: '/pages/wallet/transactions'
					});
				}
			});
		}
	}
}
</script>

<style scoped>
.container {
	background: #f8f9fa;
	min-height: 100vh;
}

/* 导航栏样式 */
.custom-navbar {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 1000;
	background: rgba(255, 255, 255, 0.95);
	backdrop-filter: blur(20rpx);
	border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);
}

.navbar-content {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 20rpx 32rpx;
	min-height: 88rpx;
}

.navbar-left {
	display: flex;
	align-items: center;
	gap: 12rpx;
	padding: 8rpx 16rpx 8rpx 0;
	border-radius: 20rpx;
	transition: all 0.2s ease;
}

.navbar-left:active {
	background-color: rgba(0, 0, 0, 0.04);
	transform: scale(0.96);
}

.back-text {
	font-size: 32rpx;
	color: #333;
	font-weight: 500;
}

.navbar-center {
	position: absolute;
	left: 50%;
	transform: translateX(-50%);
}

.navbar-title {
	font-size: 34rpx;
	font-weight: 600;
	color: #333;
}

.navbar-right {
	display: flex;
	align-items: center;
}

/* 状态区域 */
.status-section {
	background: white;
	margin: 20rpx;
	margin-top: 220rpx;
	border-radius: 30rpx;
	padding: 60rpx 40rpx;
	text-align: center;
}

.status-icon {
	width: 120rpx;
	height: 120rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	margin: 0 auto 30rpx;
}

.status-icon.completed {
	background: #4caf50;
}

.status-icon.processing {
	background: #ff9800;
}

.status-icon.failed {
	background: #f44336;
}

.status-text {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 20rpx;
}

.amount-text {
	font-size: 48rpx;
	font-weight: bold;
	display: block;
}

.amount-text.income {
	color: #4caf50;
}

.amount-text.expense {
	color: #f44336;
}

/* 详情区域 */
.detail-section {
	background: white;
	margin: 20rpx;
	border-radius: 30rpx;
	padding: 40rpx;
}

.detail-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 25rpx 0;
	border-bottom: 1rpx solid #f0f0f0;
}

.detail-item:last-child {
	border-bottom: none;
}

.detail-label {
	font-size: 28rpx;
	color: #666;
	font-weight: 500;
}

.detail-value {
	font-size: 28rpx;
	color: #333;
	text-align: right;
	flex: 1;
	margin-left: 40rpx;
}

.detail-value-row {
	display: flex;
	align-items: center;
	gap: 20rpx;
	flex: 1;
	justify-content: flex-end;
}

.copy-btn {
	padding: 8rpx 20rpx;
	background: rgba(255, 138, 0, 0.1);
	color: #ff8a00;
	border: 1rpx solid #ff8a00;
	border-radius: 15rpx;
	font-size: 24rpx;
}

.copy-btn:active {
	background: #ff8a00;
	color: white;
}

/* 操作区域 */
.action-section {
	padding: 40rpx;
	display: flex;
	gap: 20rpx;
}

.action-btn {
	flex: 1;
	height: 88rpx;
	border-radius: 25rpx;
	font-size: 30rpx;
	font-weight: bold;
	border: none;
}

.action-btn.primary {
	background: #ff8a00;
	color: white;
}

.action-btn.secondary {
	background: white;
	color: #ff8a00;
	border: 2rpx solid #ff8a00;
}

.action-btn:active {
	opacity: 0.8;
}

.dev-notice {
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 15rpx;
	padding: 30rpx;
	margin: 20rpx;
	background: rgba(255, 138, 0, 0.1);
	border-radius: 20rpx;
}

.dev-text {
	font-size: 26rpx;
	color: #ff8a00;
}
</style>
