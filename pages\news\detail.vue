<template>
	<view class="container" :class="{ 'elderly-mode': isElderlyMode }">
		<!-- 自定义导航栏 -->
		<view class="custom-navbar" :class="{ 'elderly-mode': isElderlyMode }">
			<view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
			<view class="navbar-content">
				<view class="navbar-left" @click="goBack">
					<Icon
						name="arrow-left-line"
						:size="isElderlyMode ? '40rpx' : '36rpx'"
						color="#333"
					></Icon>
					<text class="back-text">返回</text>
				</view>
				<view class="navbar-center">
					<text class="navbar-title">资讯详情</text>
				</view>
				<view class="navbar-right">
					<view class="nav-action" @click="shareArticle">
						<Icon
							name="share-line"
							:size="isElderlyMode ? '36rpx' : '32rpx'"
							color="#666"
						></Icon>
					</view>
				</view>
			</view>
		</view>

		<!-- 加载状态 -->
		<view v-if="loading" class="loading-container">
			<view class="loading-spinner"></view>
			<text class="loading-text">正在加载资讯详情...</text>
		</view>

		<!-- 错误状态 -->
		<view v-else-if="error" class="error-container">
			<Icon name="alert-line" size="64rpx" color="#ff6b6b"></Icon>
			<text class="error-text">{{ error }}</text>
			<button class="retry-btn" @click="loadArticleDetail(newsId)">重试</button>
		</view>

		<!-- 正常内容 -->
		<view v-else>
			<!-- 文章头部 -->
			<view class="article-header">
				<text class="article-title">{{articleDetail.title}}</text>
				<view class="article-meta">
					<view class="meta-item">
						<Icon name="time-line" size="20rpx" color="#999"></Icon>
						<text class="meta-text">{{articleDetail.publishTime}}</text>
					</view>
					<view class="meta-item">
						<Icon name="eye-line" size="20rpx" color="#999"></Icon>
						<text class="meta-text">{{articleDetail.views}}次浏览</text>
					</view>
					<view class="meta-item">
						<Icon name="user-line" size="20rpx" color="#999"></Icon>
						<text class="meta-text">{{articleDetail.author}}</text>
					</view>
				</view>
				<view class="article-tags">
					<text class="article-tag" v-for="tag in articleDetail.tags" :key="tag">{{tag}}</text>
				</view>
			</view>

			<!-- 文章内容 -->
			<view class="article-content">
				<!-- 文章摘要 -->
				<view class="article-summary" v-if="articleDetail.summary">
					<text class="summary-text">{{articleDetail.summary}}</text>
				</view>

				<!-- 文章图片 -->
				<view class="article-image" v-if="articleDetail.image">
					<image :src="articleDetail.image" class="content-image" mode="widthFix" @click="previewImage"></image>
				</view>

				<!-- 文章正文 -->
				<view class="article-body">
					<rich-text :nodes="articleDetail.content"></rich-text>
				</view>

				<!-- 相关图片 -->
				<view class="article-images" v-if="articleDetail.images && articleDetail.images.length > 0">
					<view class="images-title">相关图片</view>
					<view class="images-grid">
						<image
							v-for="(img, index) in articleDetail.images"
							:key="index"
							:src="img"
							class="grid-image"
							mode="aspectFill"
							@click="previewImages(index)"
						></image>
					</view>
				</view>
			</view>

			<!-- 文章操作 -->
			<view class="article-actions">
				<view class="action-item" @click="toggleLike">
					<Icon :name="isLiked ? 'thumb-up-fill' : 'thumb-up-line'" size="32rpx" :color="isLiked ? '#ff8a00' : '#666'"></Icon>
					<text class="action-text" :class="{ active: isLiked }">{{articleDetail.likes}}</text>
				</view>
				<view class="action-item" @click="toggleFavorite">
					<Icon :name="isFavorited ? 'star-fill' : 'star-line'" size="32rpx" :color="isFavorited ? '#ff8a00' : '#666'"></Icon>
					<text class="action-text" :class="{ active: isFavorited }">收藏</text>
				</view>
				<view class="action-item" @click="shareArticle">
					<Icon name="share-line" size="32rpx" color="#666"></Icon>
					<text class="action-text">分享</text>
				</view>
				<view class="action-item" @click="showComments">
					<Icon name="chat-3-line" size="32rpx" color="#666"></Icon>
					<text class="action-text">{{articleDetail.comments}}条评论</text>
				</view>
			</view>

			<!-- 相关文章 -->
			<view class="related-articles">
				<view class="section-header">
					<Icon name="article-line" size="32rpx" color="#ff8a00"></Icon>
					<text class="section-title">相关文章</text>
				</view>
				<view class="related-list">
					<view class="related-item" v-for="(item, index) in relatedArticles" :key="index" @click="viewRelated(item)">
						<image :src="item.image" class="related-image" mode="aspectFill"></image>
						<view class="related-content">
							<text class="related-title">{{item.title}}</text>
							<view class="related-meta">
								<text class="related-time">{{item.publishTime}}</text>
								<text class="related-views">{{item.views}}次浏览</text>
							</view>
						</view>
					</view>
				</view>
			</view>

			<!-- 评论区 -->
			<view class="comments-section" v-if="showCommentsSection">
				<view class="section-header">
					<Icon name="chat-3-line" size="32rpx" color="#ff8a00"></Icon>
					<text class="section-title">评论 ({{articleDetail.comments}})</text>
				</view>

				<!-- 评论输入 -->
				<view class="comment-input">
					<input class="input-field" placeholder="写下你的评论..." v-model="commentText" />
					<button class="submit-btn" @click="submitComment" :disabled="!commentText.trim()">发布</button>
				</view>

				<!-- 评论列表 -->
				<view class="comment-list">
					<view class="comment-item" v-for="(comment, index) in commentList" :key="index">
						<view class="comment-avatar">
							<Icon name="user-line" size="32rpx" color="#999"></Icon>
						</view>
						<view class="comment-content">
							<view class="comment-header">
								<text class="comment-user">{{comment.userName}}</text>
								<text class="comment-time">{{comment.time}}</text>
							</view>
							<text class="comment-text">{{comment.content}}</text>
							<view class="comment-actions">
								<view class="comment-action" @click="likeComment(comment, index)">
									<Icon :name="comment.isLiked ? 'thumb-up-fill' : 'thumb-up-line'" size="20rpx" :color="comment.isLiked ? '#ff8a00' : '#999'"></Icon>
									<text class="action-count">{{comment.likes}}</text>
								</view>
								<view class="comment-action" @click="replyComment(comment)">
									<Icon name="chat-3-line" size="20rpx" color="#999"></Icon>
									<text class="action-text">回复</text>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import Icon from '@/components/Icon/Icon.vue'
import OfflineDataManager from '@/utils/offlineData.js'
import FeedbackUtils from '@/utils/feedback.js'
import { elderlyModeManager } from '@/utils/elderlyModeUtils.js'

export default {
	components: {
		Icon
	},
	data() {
		return {
			newsId: null,
			loading: false,
			error: null,
			isLiked: false,
			isFavorited: false,
			showCommentsSection: false,
			commentText: '',
			statusBarHeight: 0, // 状态栏高度
			isElderlyMode: false, // 适老化模式
			articleDetail: {
				id: 1,
				title: '养老服务新政策发布，惠及更多老年人',
				summary: '政府出台新的养老服务补贴政策，进一步完善养老服务体系，提高老年人生活质量。',
				content: `
					<p>近日，民政部联合财政部发布了《关于进一步完善养老服务补贴制度的通知》，这一新政策将为广大老年人带来更多实惠。</p>
					<p><strong>政策要点：</strong></p>
					<p>1. 扩大补贴覆盖范围，将更多中等收入老年人纳入补贴范围</p>
					<p>2. 提高补贴标准，基础养老服务补贴提高20%</p>
					<p>3. 简化申请流程，实现"一站式"办理</p>
					<p>4. 加强服务监管，确保补贴资金使用效率</p>
					<p>此次政策调整体现了国家对养老事业的高度重视，将有效缓解老年人养老服务费用负担，提升养老服务质量。</p>
				`,
				author: '政策解读员',
				publishTime: '2024-01-15 10:30',
				views: 1256,
				likes: 89,
				comments: 23,
				tags: ['政策解读', '养老补贴', '民生福利'],
				image: '/static/news/policy-news.jpg',
				images: [
					'/static/news/policy1.jpg',
					'/static/news/policy2.jpg',
					'/static/news/policy3.jpg'
				]
			},
			relatedArticles: [
				{
					id: 2,
					title: '智慧养老技术创新助力老年人生活',
					publishTime: '2024-01-14',
					views: 892,
					image: '/static/picture/zixun/71E00B4C613705188E11E072AC3B97F9A9493F32_size101_w1280_h853.jpg'
				},
				{
					id: 3,
					title: '社区养老服务中心建设加速推进',
					publishTime: '2024-01-13',
					views: 743,
					image: '/static/picture/zixun/8663976bbb664a0e9f6fd0ee564e5a8c.jpeg'
				},
				{
					id: 4,
					title: '老年人健康管理新模式探索',
					publishTime: '2024-01-12',
					views: 654,
					image: '/static/picture/zixun/OIP-C.jpg'
				}
			],
			commentList: [
				{
					userName: '关心老人的小李',
					time: '2小时前',
					content: '这个政策真的很好，希望能尽快落实到位，让更多老年人受益。',
					likes: 12,
					isLiked: false
				},
				{
					userName: '养老工作者',
					time: '3小时前',
					content: '作为养老服务从业者，我觉得这个政策对行业发展很有帮助，期待更多细则出台。',
					likes: 8,
					isLiked: false
				},
				{
					userName: '孝顺的女儿',
					time: '5小时前',
					content: '太好了！我妈妈正好符合条件，这下养老费用压力能减轻不少。',
					likes: 15,
					isLiked: false
				}
			]
		}
	},
	onLoad(options) {
		// 获取状态栏高度
		const systemInfo = uni.getSystemInfoSync();
		this.statusBarHeight = systemInfo.statusBarHeight || 44;

		// 初始化适老化模式
		this.isElderlyMode = elderlyModeManager.getElderlyMode();
		elderlyModeManager.onElderlyModeChange((isEnabled) => {
			this.isElderlyMode = isEnabled;
		});

		this.newsId = options.id;
		if (this.newsId) {
			this.loadArticleDetail(this.newsId);
		} else {
			// 如果没有ID，显示默认内容
			FeedbackUtils.showInfo('正在加载资讯详情...');
		}
	},
	methods: {
		async loadArticleDetail(id) {
			try {
				this.loading = true;
				this.error = null;

				// 确保离线数据已初始化
				OfflineDataManager.initOfflineData();

				// 从离线数据中查找对应的资讯
				const allNews = uni.getStorageSync('offline_news') || [];
				const newsDetail = allNews.find(item => item.id == id);

				if (newsDetail) {
					// 转换数据格式以适配模板
					this.articleDetail = {
						id: newsDetail.id,
						title: newsDetail.title,
						summary: newsDetail.summary,
						content: this.formatContent(newsDetail.content || newsDetail.summary),
						author: newsDetail.author,
						publishTime: newsDetail.publishTime,
						views: newsDetail.readCount || 0,
						likes: Math.floor(Math.random() * 100) + 50, // 模拟点赞数
						comments: Math.floor(Math.random() * 50) + 10, // 模拟评论数
						tags: newsDetail.tags || [newsDetail.category],
						image: newsDetail.image,
						images: [] // 暂时为空
					};

					FeedbackUtils.showSuccess('资讯详情加载成功');
				} else {
					// 如果找不到对应资讯，使用默认数据
					this.articleDetail = this.getDefaultArticleDetail();
					FeedbackUtils.showInfo('使用默认资讯内容');
				}
			} catch (error) {
				console.error('加载资讯详情失败:', error);
				this.error = '加载失败，请重试';
				this.articleDetail = this.getDefaultArticleDetail();
				FeedbackUtils.showError('加载失败，已显示默认内容');
			} finally {
				this.loading = false;
			}
		},

		// 格式化内容为富文本
		formatContent(content) {
			if (!content) return '<p>暂无详细内容</p>';

			// 简单的文本转HTML格式
			const paragraphs = content.split('\n').filter(p => p.trim());
			return paragraphs.map(p => `<p>${p}</p>`).join('');
		},

		// 获取默认资讯详情
		getDefaultArticleDetail() {
			return {
				id: this.newsId || 1,
				title: '养老服务新政策发布，惠及更多老年人',
				summary: '政府出台新的养老服务补贴政策，进一步完善养老服务体系，提高老年人生活质量。',
				content: `
					<p>近日，民政部联合财政部发布了《关于进一步完善养老服务补贴制度的通知》，这一新政策将为广大老年人带来更多实惠。</p>
					<p><strong>政策要点：</strong></p>
					<p>1. 扩大补贴覆盖范围，将更多中等收入老年人纳入补贴范围</p>
					<p>2. 提高补贴标准，基础养老服务补贴提高20%</p>
					<p>3. 简化申请流程，实现"一站式"办理</p>
					<p>4. 加强服务监管，确保补贴资金使用效率</p>
					<p>此次政策调整体现了国家对养老事业的高度重视，将有效缓解老年人养老服务费用负担，提升养老服务质量。</p>
				`,
				author: '政策解读员',
				publishTime: '2024-01-15 10:30',
				views: 1256,
				likes: 89,
				comments: 23,
				tags: ['政策解读', '养老补贴', '民生福利'],
				image: '/static/picture/zixun/W020211011780554733191.jpg',
				images: [
					'/static/picture/zixun/71E00B4C613705188E11E072AC3B97F9A9493F32_size101_w1280_h853.jpg',
					'/static/picture/zixun/8663976bbb664a0e9f6fd0ee564e5a8c.jpeg',
					'/static/picture/zixun/OIP-C.jpg'
				]
			};
		},
		previewImage() {
			uni.previewImage({
				urls: [this.articleDetail.image],
				current: this.articleDetail.image
			});
		},
		previewImages(index) {
			uni.previewImage({
				urls: this.articleDetail.images,
				current: index
			});
		},
		toggleLike() {
			this.isLiked = !this.isLiked;
			if (this.isLiked) {
				this.articleDetail.likes++;
			} else {
				this.articleDetail.likes--;
			}
		},
		toggleFavorite() {
			this.isFavorited = !this.isFavorited;
			uni.showToast({
				title: this.isFavorited ? '已收藏' : '已取消收藏',
				icon: 'success'
			});
		},
		shareArticle() {
			uni.share({
				provider: 'weixin',
				scene: 'WXSceneSession',
				type: 0,
				href: `https://example.com/news/${this.articleDetail.id}`,
				title: this.articleDetail.title,
				summary: this.articleDetail.summary,
				imageUrl: this.articleDetail.image
			});
		},
		showComments() {
			this.showCommentsSection = !this.showCommentsSection;
		},
		submitComment() {
			if (!this.commentText.trim()) return;
			
			const newComment = {
				userName: '我',
				time: '刚刚',
				content: this.commentText,
				likes: 0,
				isLiked: false
			};
			
			this.commentList.unshift(newComment);
			this.articleDetail.comments++;
			this.commentText = '';
			
			uni.showToast({
				title: '评论发布成功',
				icon: 'success'
			});
		},
		likeComment(comment, index) {
			comment.isLiked = !comment.isLiked;
			if (comment.isLiked) {
				comment.likes++;
			} else {
				comment.likes--;
			}
		},
		replyComment(comment) {
			this.commentText = `回复 @${comment.userName}: `;
		},
		viewRelated(article) {
			uni.navigateTo({
				url: `/pages/news/detail?id=${article.id}`
			});
		},

		// 返回上一页
		goBack() {
			FeedbackUtils.lightFeedback();
			uni.navigateBack({
				fail: () => {
					// 如果返回失败，跳转到首页
					uni.switchTab({
						url: '/pages/home/<USER>'
					});
				}
			});
		}
	}
}
</script>

<style scoped>
.container {
	background: #f8f9fa;
	min-height: 100vh;
}

/* iOS风格自定义导航栏 */
.custom-navbar {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 1000;
	background: rgba(255, 255, 255, 0.95);
	backdrop-filter: blur(20rpx);
	-webkit-backdrop-filter: blur(20rpx);
	border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);
	box-shadow: 0 2rpx 16rpx rgba(0, 0, 0, 0.08);
}

.status-bar {
	background: transparent;
}

.navbar-content {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 20rpx 32rpx;
	min-height: 88rpx;
	position: relative;
}

.navbar-left {
	display: flex;
	align-items: center;
	gap: 12rpx;
	padding: 8rpx 16rpx 8rpx 0;
	border-radius: 20rpx;
	transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.navbar-left:active {
	background-color: rgba(0, 0, 0, 0.04);
	transform: scale(0.96);
}

.back-text {
	font-size: 32rpx;
	color: #333;
	font-weight: 500;
	font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', sans-serif;
}

.navbar-center {
	position: absolute;
	left: 50%;
	transform: translateX(-50%);
	display: flex;
	align-items: center;
}

.navbar-title {
	font-size: 34rpx;
	font-weight: 600;
	color: #333;
	font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', sans-serif;
}

.navbar-right {
	display: flex;
	align-items: center;
	gap: 16rpx;
}

.nav-action {
	width: 64rpx;
	height: 64rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.nav-action:active {
	background-color: rgba(0, 0, 0, 0.04);
	transform: scale(0.9);
}

/* 加载状态 */
.loading-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	min-height: 60vh;
	gap: 20rpx;
	padding-top: 200rpx; /* 为导航栏留出空间 */
}

.loading-spinner {
	width: 60rpx;
	height: 60rpx;
	border: 4rpx solid rgba(255, 138, 0, 0.2);
	border-top: 4rpx solid #ff8a00;
	border-radius: 50%;
	animation: loading 1s linear infinite;
}

.loading-text {
	font-size: 28rpx;
	color: #666;
}

/* 错误状态 */
.error-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	min-height: 60vh;
	gap: 20rpx;
	padding: 40rpx;
	padding-top: 200rpx; /* 为导航栏留出空间 */
}

.error-text {
	font-size: 28rpx;
	color: #666;
	text-align: center;
}

.retry-btn {
	background: linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%);
	color: white;
	border: none;
	border-radius: 20rpx;
	padding: 20rpx 40rpx;
	font-size: 28rpx;
	font-weight: 600;
}

@keyframes loading {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

.article-header {
	background: white;
	padding: 40rpx;
	margin-bottom: 20rpx;
	margin-top: 200rpx; /* 为导航栏留出空间 */
}

.article-title {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
	line-height: 1.4;
	display: block;
	margin-bottom: 20rpx;
}

.article-meta {
	display: flex;
	gap: 30rpx;
	margin-bottom: 20rpx;
	flex-wrap: wrap;
}

.meta-item {
	display: flex;
	align-items: center;
	gap: 8rpx;
}

.meta-text {
	font-size: 24rpx;
	color: #999;
}

.article-tags {
	display: flex;
	gap: 10rpx;
	flex-wrap: wrap;
}

.article-tag {
	background: rgba(255, 138, 0, 0.1);
	color: #ff8a00;
	font-size: 22rpx;
	padding: 6rpx 15rpx;
	border-radius: 15rpx;
}

.article-content {
	background: white;
	padding: 40rpx;
	margin-bottom: 20rpx;
}

.article-summary {
	background: rgba(255, 138, 0, 0.05);
	border-left: 6rpx solid #ff8a00;
	padding: 25rpx;
	margin-bottom: 30rpx;
	border-radius: 0 10rpx 10rpx 0;
}

.summary-text {
	font-size: 28rpx;
	color: #666;
	line-height: 1.6;
	display: block;
}

.article-image {
	margin-bottom: 30rpx;
	border-radius: 24rpx;
	overflow: hidden;
	box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
	position: relative;
}

.article-image::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: linear-gradient(135deg, rgba(255, 138, 0, 0.1) 0%, transparent 50%);
	pointer-events: none;
	z-index: 1;
}

.content-image {
	width: 100%;
	border-radius: 24rpx;
	transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
	display: block;
}

.content-image:active {
	transform: scale(0.98);
}

.article-image:hover .content-image {
	transform: scale(1.02);
}

.article-body {
	font-size: 28rpx;
	line-height: 1.8;
	color: #333;
}

.images-title {
	font-size: 30rpx;
	font-weight: bold;
	color: #333;
	margin: 40rpx 0 20rpx;
}

.images-grid {
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	gap: 15rpx;
}

.grid-image {
	width: 100%;
	height: 150rpx;
	border-radius: 10rpx;
}

.article-actions {
	background: white;
	padding: 30rpx 40rpx;
	display: flex;
	justify-content: space-around;
	margin-bottom: 20rpx;
}

.action-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 8rpx;
}

.action-text {
	font-size: 24rpx;
	color: #666;
}

.action-text.active {
	color: #ff8a00;
}

.related-articles, .comments-section {
	background: white;
	padding: 40rpx;
	margin-bottom: 20rpx;
}

.section-header {
	display: flex;
	align-items: center;
	gap: 15rpx;
	margin-bottom: 30rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.related-list {
	display: flex;
	flex-direction: column;
	gap: 25rpx;
}

.related-item {
	display: flex;
	gap: 20rpx;
}

.related-image {
	width: 140rpx;
	height: 100rpx;
	border-radius: 16rpx;
	object-fit: cover;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
	transition: all 0.3s ease;
}

.related-image:hover {
	transform: scale(1.05);
	box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.15);
}

.related-content {
	flex: 1;
}

.related-title {
	font-size: 26rpx;
	color: #333;
	line-height: 1.4;
	display: block;
	margin-bottom: 10rpx;
}

.related-meta {
	display: flex;
	gap: 20rpx;
}

.related-time, .related-views {
	font-size: 22rpx;
	color: #999;
}

.comment-input {
	display: flex;
	gap: 20rpx;
	margin-bottom: 30rpx;
	padding: 20rpx;
	background: #f8f9fa;
	border-radius: 20rpx;
}

.input-field {
	flex: 1;
	font-size: 26rpx;
	color: #333;
}

.submit-btn {
	background: linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%);
	color: white;
	border: none;
	border-radius: 15rpx;
	padding: 10rpx 25rpx;
	font-size: 24rpx;
}

.submit-btn[disabled] {
	background: #ccc;
}

.comment-list {
	display: flex;
	flex-direction: column;
	gap: 25rpx;
}

.comment-item {
	display: flex;
	gap: 20rpx;
}

.comment-avatar {
	width: 60rpx;
	height: 60rpx;
	border-radius: 50%;
	background: #f0f0f0;
	display: flex;
	align-items: center;
	justify-content: center;
}

.comment-content {
	flex: 1;
}

.comment-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 10rpx;
}

.comment-user {
	font-size: 26rpx;
	color: #333;
	font-weight: bold;
}

.comment-time {
	font-size: 22rpx;
	color: #999;
}

.comment-text {
	font-size: 26rpx;
	color: #666;
	line-height: 1.5;
	display: block;
	margin-bottom: 15rpx;
}

.comment-actions {
	display: flex;
	gap: 30rpx;
}

.comment-action {
	display: flex;
	align-items: center;
	gap: 8rpx;
}

.action-count, .action-text {
	font-size: 22rpx;
	color: #999;
}

/* ===== 适老化模式样式 ===== */
.elderly-mode .navbar-content {
	padding: 24rpx 36rpx;
	min-height: 96rpx;
}

.elderly-mode .navbar-left {
	gap: 16rpx;
	padding: 12rpx 20rpx 12rpx 0;
}

.elderly-mode .back-text {
	font-size: 36rpx;
	font-weight: 600;
}

.elderly-mode .navbar-title {
	font-size: 38rpx;
	font-weight: 700;
}

.elderly-mode .navbar-right {
	gap: 16rpx;
}

.elderly-mode .nav-action {
	padding: 12rpx;
	border-radius: 20rpx;
}

.elderly-mode .article-content {
	margin-top: 240rpx; /* 适老化模式下导航栏更高 */
	padding: 50rpx 30rpx;
}

.elderly-mode .article-title {
	font-size: 48rpx;
	line-height: 1.4;
}

.elderly-mode .article-meta {
	font-size: 30rpx;
}

.elderly-mode .article-text {
	font-size: 36rpx;
	line-height: 1.8;
}

.elderly-mode .action-bar {
	padding: 30rpx;
}

.elderly-mode .action-item {
	padding: 20rpx;
}

.elderly-mode .action-text {
	font-size: 28rpx;
}
</style>
