<template>
	<view class="container">
		<PageHeader title="新建日程" showBack></PageHeader>
		
		<view class="content">
			<view class="form-section">
				<text class="section-title">基本信息</text>
				
				<view class="form-item">
					<text class="form-label">日程标题</text>
					<input 
						class="form-input" 
						v-model="formData.title" 
						placeholder="请输入日程标题"
						maxlength="50"
					/>
				</view>
				
				<view class="form-item">
					<text class="form-label">日程类型</text>
					<picker 
						:value="typeIndex" 
						:range="scheduleTypes" 
						range-key="name"
						@change="onTypeChange"
					>
						<view class="picker-display">
							<text class="picker-text">{{ formData.type || '请选择日程类型' }}</text>
							<Icon name="arrow-down-s-line" size="24rpx" color="#999"></Icon>
						</view>
					</picker>
				</view>
				
				<view class="form-item">
					<text class="form-label">日期</text>
					<picker 
						mode="date" 
						:value="formData.date" 
						@change="onDateChange"
					>
						<view class="picker-display">
							<text class="picker-text">{{ formData.date || '请选择日期' }}</text>
							<Icon name="calendar-line" size="24rpx" color="#999"></Icon>
						</view>
					</picker>
				</view>
				
				<view class="form-item">
					<text class="form-label">时间</text>
					<picker 
						mode="time" 
						:value="formData.time" 
						@change="onTimeChange"
					>
						<view class="picker-display">
							<text class="picker-text">{{ formData.time || '请选择时间' }}</text>
							<Icon name="time-line" size="24rpx" color="#999"></Icon>
						</view>
					</picker>
				</view>
				
				<view class="form-item">
					<text class="form-label">地点</text>
					<input 
						class="form-input" 
						v-model="formData.location" 
						placeholder="请输入地点（可选）"
						maxlength="100"
					/>
				</view>
				
				<view class="form-item">
					<text class="form-label">备注</text>
					<textarea 
						class="form-textarea" 
						v-model="formData.description" 
						placeholder="请输入备注信息（可选）"
						maxlength="200"
					/>
				</view>
			</view>
			
			<view class="form-section">
				<text class="section-title">提醒设置</text>
				
				<view class="form-item">
					<text class="form-label">是否提醒</text>
					<switch 
						:checked="formData.reminder" 
						@change="onReminderChange"
						color="#ff8a00"
					/>
				</view>
				
				<view class="form-item" v-if="formData.reminder">
					<text class="form-label">提醒时间</text>
					<picker 
						:value="reminderIndex" 
						:range="reminderOptions" 
						range-key="name"
						@change="onReminderTimeChange"
					>
						<view class="picker-display">
							<text class="picker-text">{{ formData.reminderTime || '请选择提醒时间' }}</text>
							<Icon name="arrow-down-s-line" size="24rpx" color="#999"></Icon>
						</view>
					</picker>
				</view>
			</view>
			
			<view class="action-buttons">
				<InteractiveButton 
					type="secondary" 
					text="取消" 
					@click="cancel"
				/>
				<InteractiveButton 
					type="primary" 
					text="保存" 
					:loading="saving"
					@click="save"
				/>
			</view>
		</view>
	</view>
</template>

<script>
import Icon from '@/components/Icon/Icon.vue'
import PageHeader from '@/components/PageHeader/PageHeader.vue'
import InteractiveButton from '@/components/InteractiveButton/InteractiveButton.vue'

export default {
	name: 'ScheduleCreate',
	components: {
		Icon,
		PageHeader,
		InteractiveButton
	},
	data() {
		return {
			saving: false,
			typeIndex: 0,
			reminderIndex: 0,
			formData: {
				title: '',
				type: '',
				date: '',
				time: '',
				location: '',
				description: '',
				reminder: false,
				reminderTime: ''
			},
			scheduleTypes: [
				{ name: '医疗', color: '#f44336' },
				{ name: '运动', color: '#4caf50' },
				{ name: '学习', color: '#9c27b0' },
				{ name: '社交', color: '#ff9800' },
				{ name: '娱乐', color: '#2196f3' },
				{ name: '工作', color: '#607d8b' },
				{ name: '其他', color: '#795548' }
			],
			reminderOptions: [
				{ name: '准时提醒', value: 0 },
				{ name: '提前5分钟', value: 5 },
				{ name: '提前15分钟', value: 15 },
				{ name: '提前30分钟', value: 30 },
				{ name: '提前1小时', value: 60 },
				{ name: '提前1天', value: 1440 }
			]
		}
	},
	onLoad(options) {
		if (options.date) {
			this.formData.date = options.date
		}
		if (options.id && options.mode === 'edit') {
			this.loadScheduleData(options.id)
		}
	},
	methods: {
		loadScheduleData(id) {
			// 模拟加载日程数据
			console.log('加载日程数据:', id)
		},
		onTypeChange(e) {
			this.typeIndex = e.detail.value
			const selectedType = this.scheduleTypes[this.typeIndex]
			this.formData.type = selectedType.name
		},
		onDateChange(e) {
			this.formData.date = e.detail.value
		},
		onTimeChange(e) {
			this.formData.time = e.detail.value
		},
		onReminderChange(e) {
			this.formData.reminder = e.detail.value
		},
		onReminderTimeChange(e) {
			this.reminderIndex = e.detail.value
			const selectedReminder = this.reminderOptions[this.reminderIndex]
			this.formData.reminderTime = selectedReminder.name
		},
		validate() {
			if (!this.formData.title.trim()) {
				uni.showToast({
					title: '请输入日程标题',
					icon: 'none'
				})
				return false
			}
			if (!this.formData.type) {
				uni.showToast({
					title: '请选择日程类型',
					icon: 'none'
				})
				return false
			}
			if (!this.formData.date) {
				uni.showToast({
					title: '请选择日期',
					icon: 'none'
				})
				return false
			}
			if (!this.formData.time) {
				uni.showToast({
					title: '请选择时间',
					icon: 'none'
				})
				return false
			}
			return true
		},
		async save() {
			if (!this.validate()) return
			
			this.saving = true
			try {
				// 模拟保存数据
				await new Promise(resolve => setTimeout(resolve, 1000))
				
				uni.showToast({
					title: '保存成功',
					icon: 'success'
				})
				
				setTimeout(() => {
					uni.navigateBack()
				}, 1500)
			} catch (error) {
				uni.showToast({
					title: '保存失败',
					icon: 'none'
				})
			} finally {
				this.saving = false
			}
		},
		cancel() {
			uni.showModal({
				title: '确认取消',
				content: '确定要取消编辑吗？未保存的内容将丢失。',
				success: (res) => {
					if (res.confirm) {
						uni.navigateBack()
					}
				}
			})
		}
	}
}
</script>

<style scoped>
.container {
	min-height: 100vh;
	background: #f5f5f5;
}

.content {
	padding: 140rpx 32rpx 40rpx;
}

.form-section {
	background: white;
	border-radius: 20rpx;
	padding: 32rpx;
	margin-bottom: 24rpx;
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

.section-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 24rpx;
	display: block;
}

.form-item {
	margin-bottom: 32rpx;
}

.form-item:last-child {
	margin-bottom: 0;
}

.form-label {
	font-size: 28rpx;
	color: #333;
	margin-bottom: 12rpx;
	display: block;
}

.form-input {
	width: 100%;
	padding: 20rpx;
	border: 1rpx solid #e0e0e0;
	border-radius: 12rpx;
	font-size: 28rpx;
	background: #fafafa;
}

.form-textarea {
	width: 100%;
	min-height: 120rpx;
	padding: 20rpx;
	border: 1rpx solid #e0e0e0;
	border-radius: 12rpx;
	font-size: 28rpx;
	background: #fafafa;
	resize: none;
}

.picker-display {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20rpx;
	border: 1rpx solid #e0e0e0;
	border-radius: 12rpx;
	background: #fafafa;
}

.picker-text {
	font-size: 28rpx;
	color: #333;
}

.action-buttons {
	display: flex;
	gap: 16rpx;
	margin-top: 40rpx;
}
</style>
