/**
 * 图标尺寸标准化配置
 * 统一项目中图标尺寸的使用规范
 */

// 标准图标尺寸定义
export const IconSizes = {
  // 小图标 - 用于列表项、标签等
  SMALL: '24rpx',
  
  // 常规图标 - 默认尺寸，用于导航、按钮等
  NORMAL: '32rpx',
  
  // 中等图标 - 用于功能卡片、重要按钮
  MEDIUM: '48rpx',
  
  // 大图标 - 用于主要功能、占位符
  LARGE: '64rpx',
  
  // 特大图标 - 用于适老化模式、重要展示
  EXTRA_LARGE: '96rpx'
}

// 场景化尺寸配置
export const SceneSizes = {
  // 导航栏图标
  NAVBAR: IconSizes.NORMAL,
  
  // TabBar图标
  TABBAR: IconSizes.MEDIUM,
  
  // 列表项图标
  LIST_ITEM: IconSizes.SMALL,
  
  // 功能卡片图标
  FUNCTION_CARD: IconSizes.MEDIUM,
  
  // 服务项图标
  SERVICE_ITEM: IconSizes.NORMAL,
  
  // 占位符图标
  PLACEHOLDER: IconSizes.MEDIUM,
  
  // 状态图标
  STATUS: IconSizes.SMALL,
  
  // 操作按钮图标
  ACTION_BUTTON: IconSizes.NORMAL,
  
  // 紧急服务图标
  EMERGENCY: IconSizes.LARGE,
  
  // 适老化模式图标
  ELDERLY_MODE: IconSizes.EXTRA_LARGE
}

// 页面特定尺寸配置
export const PageSizes = {
  // 首页
  HOME: {
    navbar: SceneSizes.NAVBAR,
    notification: '36rpx', // 特殊：通知图标稍大
    function: SceneSizes.FUNCTION_CARD,
    service: '36rpx' // 特殊：服务中心图标
  },
  
  // 机构页面
  INSTITUTION: {
    search: SceneSizes.NAVBAR,
    filter: SceneSizes.SMALL,
    placeholder: SceneSizes.MEDIUM,
    info: SceneSizes.SMALL,
    action: SceneSizes.NORMAL
  },
  
  // 服务页面
  SERVICE: {
    category: SceneSizes.NORMAL,
    item: SceneSizes.MEDIUM,
    status: SceneSizes.SMALL
  },
  
  // 个人中心
  PROFILE: {
    avatar: SceneSizes.LARGE,
    menu: SceneSizes.NORMAL,
    setting: SceneSizes.SMALL
  }
}

// 获取场景对应的图标尺寸
export function getSceneSize(scene) {
  return SceneSizes[scene] || IconSizes.NORMAL
}

// 获取页面特定尺寸
export function getPageSize(page, element) {
  const pageConfig = PageSizes[page]
  if (!pageConfig) return IconSizes.NORMAL
  return pageConfig[element] || IconSizes.NORMAL
}

// 适老化模式尺寸调整
export function getElderlySize(normalSize) {
  const sizeMap = {
    [IconSizes.SMALL]: IconSizes.NORMAL,
    [IconSizes.NORMAL]: IconSizes.MEDIUM,
    [IconSizes.MEDIUM]: IconSizes.LARGE,
    [IconSizes.LARGE]: IconSizes.EXTRA_LARGE,
    [IconSizes.EXTRA_LARGE]: '128rpx'
  }
  return sizeMap[normalSize] || normalSize
}

// 响应式尺寸调整
export function getResponsiveSize(baseSize, screenWidth) {
  // 小屏幕适配
  if (screenWidth < 375) {
    const smallScreenMap = {
      [IconSizes.EXTRA_LARGE]: IconSizes.LARGE,
      [IconSizes.LARGE]: IconSizes.MEDIUM,
      [IconSizes.MEDIUM]: IconSizes.NORMAL,
      [IconSizes.NORMAL]: IconSizes.SMALL,
      [IconSizes.SMALL]: '20rpx'
    }
    return smallScreenMap[baseSize] || baseSize
  }
  
  // 大屏幕适配
  if (screenWidth > 414) {
    const largeScreenMap = {
      [IconSizes.SMALL]: IconSizes.NORMAL,
      [IconSizes.NORMAL]: '36rpx',
      [IconSizes.MEDIUM]: '52rpx',
      [IconSizes.LARGE]: '72rpx',
      [IconSizes.EXTRA_LARGE]: '108rpx'
    }
    return largeScreenMap[baseSize] || baseSize
  }
  
  return baseSize
}

// 图标尺寸验证
export function validateIconSize(size) {
  const validSizes = Object.values(IconSizes)
  const customSizePattern = /^\d+rpx$/
  
  return validSizes.includes(size) || customSizePattern.test(size)
}

// 获取推荐尺寸建议
export function getSizeRecommendation(context) {
  const recommendations = {
    'navbar': '建议使用 32rpx，保持导航栏图标一致性',
    'list': '建议使用 24rpx，避免列表过于拥挤',
    'card': '建议使用 48rpx，突出功能重要性',
    'button': '建议使用 32rpx，与文字协调',
    'placeholder': '建议使用 48rpx 或 64rpx，根据容器大小调整',
    'elderly': '建议使用 64rpx 或更大，提升可读性'
  }
  
  return recommendations[context] || '建议使用标准尺寸：24rpx、32rpx、48rpx、64rpx、96rpx'
}

export default {
  IconSizes,
  SceneSizes,
  PageSizes,
  getSceneSize,
  getPageSize,
  getElderlySize,
  getResponsiveSize,
  validateIconSize,
  getSizeRecommendation
}
