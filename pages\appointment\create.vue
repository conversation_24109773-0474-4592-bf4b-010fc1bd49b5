<template>
	<view class="container">
		<!-- 页面头部 -->
		<PageHeader title="预约服务">
			<template #actions>
				<InteractiveButton
					type="secondary"
					size="small"
					text="我的预约"
					icon="calendar-line"
					@click="viewMyAppointments"
				></InteractiveButton>
			</template>
		</PageHeader>

		<!-- 服务信息 -->
		<view class="service-info" v-if="serviceInfo">
			<view class="service-header">
				<view class="service-icon">
					<Icon :name="serviceInfo.icon" size="48rpx" color="#fff"></Icon>
				</view>
				<view class="service-details">
					<text class="service-name">{{ serviceInfo.name }}</text>
					<text class="service-provider">{{ serviceInfo.provider }}</text>
					<text class="service-price">￥{{ serviceInfo.price }}/{{ serviceInfo.unit }}</text>
				</view>
			</view>
		</view>

		<!-- 预约表单 -->
		<view class="appointment-form">
			<text class="form-title">预约信息</text>
			
			<!-- 预约日期 -->
			<view class="form-item">
				<text class="form-label">预约日期</text>
				<picker mode="date" :value="formData.date" @change="onDateChange">
					<view class="picker-input">
						<text class="picker-text">{{ formData.date || '请选择日期' }}</text>
						<Icon name="calendar-line" size="24rpx" color="#999"></Icon>
					</view>
				</picker>
			</view>

			<!-- 预约时间 -->
			<view class="form-item">
				<text class="form-label">预约时间</text>
				<picker mode="time" :value="formData.time" @change="onTimeChange">
					<view class="picker-input">
						<text class="picker-text">{{ formData.time || '请选择时间' }}</text>
						<Icon name="time-line" size="24rpx" color="#999"></Icon>
					</view>
				</picker>
			</view>

			<!-- 联系人信息 -->
			<view class="form-item">
				<text class="form-label">联系人姓名</text>
				<input 
					class="form-input" 
					v-model="formData.contactName" 
					placeholder="请输入联系人姓名"
				/>
			</view>

			<view class="form-item">
				<text class="form-label">联系电话</text>
				<input 
					class="form-input" 
					v-model="formData.contactPhone" 
					placeholder="请输入联系电话"
					type="number"
				/>
			</view>

			<!-- 服务地址 -->
			<view class="form-item">
				<text class="form-label">服务地址</text>
				<textarea 
					class="form-textarea" 
					v-model="formData.address" 
					placeholder="请输入详细的服务地址"
				></textarea>
			</view>

			<!-- 特殊需求 -->
			<view class="form-item">
				<text class="form-label">特殊需求</text>
				<textarea 
					class="form-textarea" 
					v-model="formData.requirements" 
					placeholder="请描述特殊需求或注意事项（选填）"
				></textarea>
			</view>
		</view>

		<!-- 费用明细 -->
		<view class="cost-details">
			<text class="cost-title">费用明细</text>
			<view class="cost-item">
				<text class="cost-label">服务费用</text>
				<text class="cost-value">￥{{ serviceInfo?.price || 0 }}</text>
			</view>
			<view class="cost-item">
				<text class="cost-label">上门费</text>
				<text class="cost-value">￥{{ doorFee }}</text>
			</view>
			<view class="cost-item total">
				<text class="cost-label">总计</text>
				<text class="cost-value">￥{{ totalCost }}</text>
			</view>
		</view>

		<!-- 提交按钮 -->
		<view class="submit-section">
			<InteractiveButton
				type="primary"
				size="large"
				text="确认预约"
				icon="calendar-check-line"
				:loading="submitting"
				@click="submitAppointment"
			></InteractiveButton>
		</view>
	</view>
</template>

<script>
import InteractiveButton from '@/components/InteractiveButton/InteractiveButton.vue'
import Icon from '@/components/Icon/Icon.vue'
import PageHeader from '@/components/PageHeader/PageHeader.vue'
import FeedbackUtils from '@/utils/feedback.js'

export default {
	components: {
		InteractiveButton,
		Icon,
		PageHeader
	},
	data() {
		return {
			serviceInfo: null,
			submitting: false,
			doorFee: 20,
			formData: {
				date: '',
				time: '',
				contactName: '',
				contactPhone: '',
				address: '',
				requirements: ''
			}
		}
	},
	computed: {
		totalCost() {
			const servicePrice = this.serviceInfo?.price || 0;
			return servicePrice + this.doorFee;
		}
	},
	onLoad(options) {
		if (options.serviceId) {
			this.loadServiceInfo(options.serviceId);
		}
	},
	methods: {
		// 加载服务信息
		loadServiceInfo(serviceId) {
			// 模拟加载服务信息
			this.serviceInfo = {
				id: serviceId,
				name: '居家护理服务',
				provider: '康护医疗',
				price: 150,
				unit: '小时',
				icon: 'heart-3-line'
			};
		},

		// 日期选择
		onDateChange(e) {
			this.formData.date = e.detail.value;
		},

		// 时间选择
		onTimeChange(e) {
			this.formData.time = e.detail.value;
		},

		// 查看我的预约
		viewMyAppointments() {
			uni.navigateTo({
				url: '/pages/appointment/list'
			});
		},

		// 提交预约
		async submitAppointment() {
			// 表单验证
			if (!this.validateForm()) {
				return;
			}

			this.submitting = true;
			FeedbackUtils.showLoading('提交预约中...');

			try {
				// 模拟提交预约
				await new Promise(resolve => setTimeout(resolve, 2000));
				
				FeedbackUtils.hideLoading();
				FeedbackUtils.showSuccess('预约提交成功');
				
				// 跳转到预约列表
				setTimeout(() => {
					uni.redirectTo({
						url: '/pages/appointment/list'
					});
				}, 1500);
			} catch (error) {
				FeedbackUtils.hideLoading();
				FeedbackUtils.showError('预约提交失败，请重试');
			} finally {
				this.submitting = false;
			}
		},

		// 表单验证
		validateForm() {
			if (!this.formData.date) {
				FeedbackUtils.showError('请选择预约日期');
				return false;
			}
			if (!this.formData.time) {
				FeedbackUtils.showError('请选择预约时间');
				return false;
			}
			if (!this.formData.contactName) {
				FeedbackUtils.showError('请输入联系人姓名');
				return false;
			}
			if (!this.formData.contactPhone) {
				FeedbackUtils.showError('请输入联系电话');
				return false;
			}
			if (!this.formData.address) {
				FeedbackUtils.showError('请输入服务地址');
				return false;
			}
			return true;
		}
	}
}
</script>

<style scoped>
.container {
	background-color: #f5f5f5;
	min-height: 100vh;
	padding-bottom: 200rpx;
}

.service-info {
	background: white;
	margin: 20rpx;
	border-radius: 20rpx;
	padding: 30rpx;
	box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.service-header {
	display: flex;
	align-items: center;
	gap: 20rpx;
}

.service-icon {
	width: 80rpx;
	height: 80rpx;
	background: linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
}

.service-details {
	flex: 1;
}

.service-name {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 8rpx;
}

.service-provider {
	font-size: 26rpx;
	color: #666;
	display: block;
	margin-bottom: 8rpx;
}

.service-price {
	font-size: 28rpx;
	color: #ff8a00;
	font-weight: bold;
}

.appointment-form,
.cost-details {
	background: white;
	margin: 20rpx;
	border-radius: 20rpx;
	padding: 30rpx;
	box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.form-title,
.cost-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 25rpx;
}

.form-item {
	margin-bottom: 25rpx;
}

.form-label {
	font-size: 28rpx;
	color: #333;
	display: block;
	margin-bottom: 10rpx;
}

.form-input,
.form-textarea {
	width: 100%;
	background: #f8f9fa;
	border: 1rpx solid #e0e0e0;
	border-radius: 10rpx;
	padding: 20rpx;
	font-size: 28rpx;
	color: #333;
}

.form-textarea {
	min-height: 120rpx;
}

.picker-input {
	display: flex;
	align-items: center;
	justify-content: space-between;
	background: #f8f9fa;
	border: 1rpx solid #e0e0e0;
	border-radius: 10rpx;
	padding: 20rpx;
}

.picker-text {
	font-size: 28rpx;
	color: #333;
}

.cost-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 15rpx 0;
	border-bottom: 1rpx solid #f0f0f0;
}

.cost-item:last-child {
	border-bottom: none;
}

.cost-item.total {
	font-weight: bold;
	font-size: 32rpx;
	color: #ff8a00;
}

.cost-label {
	font-size: 28rpx;
	color: #666;
}

.cost-value {
	font-size: 28rpx;
	color: #333;
}

.submit-section {
	padding: 0 20rpx;
}
</style>
