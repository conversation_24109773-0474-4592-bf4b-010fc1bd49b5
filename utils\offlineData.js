import { getMappedImagePath } from './imagePathMapper.js'

/**
 * 离线数据管理器
 * 提供离线数据存储和获取功能，确保应用在无网络环境下也能正常使用
 */

class OfflineDataManager {
	/**
	 * 初始化离线数据
	 */
	static initOfflineData() {
		// 检查是否已经初始化过
		const isInitialized = uni.getStorageSync('offline_data_initialized')
		if (isInitialized) {
			return
		}

		// 初始化各类数据
		this.initInstitutionData()
		this.initServiceData()
		this.initSubsidyData()
		this.initNewsData()
		this.initUserData()

		// 标记已初始化
		uni.setStorageSync('offline_data_initialized', true)
		console.log('离线数据初始化完成')
	}

	/**
	 * 初始化机构数据
	 */
	static initInstitutionData() {
		const institutions = [
			{
				id: 1,
				name: '阳光养老院',
				type: '养老院',
				address: '北京市朝阳区阳光街123号',
				phone: '010-12345678',
				rating: 4.8,
				price: '3000-5000元/月',
				beds: 200,
				availableBeds: 15,
				distance: '1.2km',
				services: ['日常护理', '医疗服务', '康复训练', '文娱活动'],
				tags: ['医护齐全', '环境优美', '交通便利'],
				images: ['/static/picture/nursing_home_1.jpg'],
				image: '/static/picture/nursing_home_1.jpg', // 主图片，用于列表显示
				description: '专业的养老服务机构，提供全方位的老年人护理服务。',
				facilities: ['医务室', '康复室', '活动室', '餐厅', '花园'],
				createTime: '2024-01-01'
			},
			{
				id: 2,
				name: '康乐老年公寓',
				type: '老年公寓',
				address: '北京市海淀区康乐路456号',
				phone: '010-87654321',
				rating: 4.6,
				price: '2500-4000元/月',
				beds: 150,
				availableBeds: 8,
				distance: '2.1km',
				services: ['生活照料', '健康管理', '社交活动', '营养配餐'],
				tags: ['温馨舒适', '活动丰富', '价格实惠'],
				images: ['/static/picture/nursing_home_2.jpg'],
				image: '/static/picture/nursing_home_2.jpg', // 主图片，用于列表显示
				description: '温馨舒适的居住环境，让老年人享受家的温暖。',
				facilities: ['健身房', '图书室', '棋牌室', '医疗站'],
				createTime: '2024-01-02'
			},
			{
				id: 3,
				name: '温馨护理中心',
				type: '护理中心',
				address: '北京市西城区温馨路789号',
				phone: '010-11223344',
				rating: 4.9,
				price: '4000-6000元/月',
				beds: 100,
				availableBeds: 5,
				distance: '3.5km',
				services: ['专业护理', '康复治疗', '心理疏导', '家属陪护'],
				tags: ['专业护理', '设备先进', '服务贴心'],
				images: ['/static/picture/nursing_home_3.jpg'],
				image: '/static/picture/nursing_home_3.jpg', // 主图片，用于列表显示
				description: '专业的护理服务，为失能老人提供贴心照护。',
				facilities: ['护理站', '康复中心', '心理咨询室', '家属休息室'],
				createTime: '2024-01-03'
			},
			{
				id: 4,
				name: '幸福老年之家',
				type: '养老院',
				address: '北京市东城区幸福街456号',
				phone: '010-55667788',
				rating: 4.7,
				price: '2800-4500元/月',
				beds: 120,
				availableBeds: 12,
				distance: '1.8km',
				services: ['家庭式护理', '健康监测', '文化娱乐', '心理关怀'],
				tags: ['家庭式', '温馨环境', '个性化服务'],
				images: ['/static/picture/nursing_home_4.jpg'],
				image: '/static/picture/nursing_home_4.jpg', // 主图片，用于列表显示
				description: '家庭式温馨养老环境，让老人感受到家的温暖。',
				facilities: ['家庭式客厅', '小花园', '阅读角', '茶室'],
				createTime: '2024-01-04'
			},
			{
				id: 5,
				name: '爱心护理院',
				type: '护理中心',
				address: '北京市丰台区爱心路123号',
				phone: '010-99887766',
				rating: 4.5,
				price: '3500-5500元/月',
				beds: 80,
				availableBeds: 6,
				distance: '2.8km',
				services: ['专业医护', '康复训练', '营养配餐', '24小时护理'],
				tags: ['专业医护', '康复训练', '24小时护理'],
				images: ['/static/picture/nursing_home_5.jpg'],
				image: '/static/picture/nursing_home_5.jpg', // 主图片，用于列表显示
				description: '专业医护团队服务，为老人提供全天候的专业护理。',
				facilities: ['医疗室', '康复训练室', '营养餐厅', '护理站'],
				createTime: '2024-01-05'
			}
		]

		uni.setStorageSync('offline_institutions', institutions)
	}

	/**
	 * 初始化服务数据
	 */
	static initServiceData() {
		const services = [
			{
				id: 1,
				name: '居家护理服务',
				category: '护理服务',
				price: '80-120元/小时',
				provider: '专业护理团队',
				rating: 4.7,
				description: '提供专业的居家护理服务，包括生活照料、医疗护理等。',
				features: ['24小时服务', '专业护士', '医疗设备', '紧急救援'],
				image: '/static/picture/659c362e1e774324870c7a9200adc1e7.jpeg',
				createTime: '2024-01-01'
			},
			{
				id: 2,
				name: '康复理疗服务',
				category: '康复服务',
				price: '100-200元/次',
				provider: '康复医疗中心',
				rating: 4.8,
				description: '专业的康复理疗服务，帮助老年人恢复身体功能。',
				features: ['专业理疗师', '先进设备', '个性化方案', '跟踪服务'],
				image: '/static/picture/b3bc07f949264b36811e26cf01c7f50c.jpeg',
				createTime: '2024-01-02'
			},
			{
				id: 3,
				name: '营养配餐服务',
				category: '生活服务',
				price: '30-50元/餐',
				provider: '营养配餐中心',
				rating: 4.5,
				description: '根据老年人身体状况，提供营养均衡的配餐服务。',
				features: ['营养师配餐', '新鲜食材', '送餐上门', '特殊饮食'],
				image: '/static/picture/FAB64913B02FEDD318336D49F0A550A1_w798h530.png',
				createTime: '2024-01-03'
			}
		]

		uni.setStorageSync('offline_services', services)
	}

	/**
	 * 初始化补贴数据
	 */
	static initSubsidyData() {
		const subsidies = [
			{
				id: 1,
				title: '高龄津贴申请',
				category: '津贴补助',
				amount: '100-500元/月',
				condition: '80岁以上老人',
				status: 'available',
				deadline: '2024-12-31',
				description: '为80岁以上老年人提供生活津贴，减轻生活负担。',
				requirements: ['年满80周岁', '本市户籍', '无其他津贴', '生活困难'],
				materials: ['身份证', '户口本', '收入证明', '申请表'],
				process: ['填写申请表', '提交材料', '社区审核', '发放津贴'],
				createTime: '2024-01-01'
			},
			{
				id: 2,
				title: '养老服务补贴',
				category: '服务补贴',
				amount: '200-800元/月',
				condition: '失能老人',
				status: 'available',
				deadline: '2024-12-31',
				description: '为失能老年人提供养老服务补贴，支持居家养老。',
				requirements: ['失能评估', '本市户籍', '家庭困难', '无机构养老'],
				materials: ['失能证明', '医疗诊断', '家庭收入证明', '申请书'],
				process: ['失能评估', '提交申请', '部门审批', '补贴发放'],
				createTime: '2024-01-02'
			},
			{
				id: 3,
				title: '适老化改造补贴',
				category: '改造补贴',
				amount: '1000-5000元',
				condition: '困难老人家庭',
				status: 'available',
				deadline: '2024-12-31',
				description: '为困难老年人家庭提供适老化改造补贴。',
				requirements: ['年满60周岁', '家庭困难', '改造需求', '产权清晰'],
				materials: ['房产证明', '改造方案', '费用预算', '申请表'],
				process: ['需求评估', '方案设计', '审批通过', '改造实施'],
				createTime: '2024-01-03'
			}
		]

		uni.setStorageSync('offline_subsidies', subsidies)
	}

	/**
	 * 初始化新闻数据
	 */
	static initNewsData() {
		const news = [
			{
				id: 1,
				title: '养老服务新政策发布，惠及更多老年人',
				category: '政策资讯',
				summary: '政府出台新的养老服务补贴政策，扩大受益范围，提高补贴标准。',
				content: '详细的新闻内容...',
				image: '/static/picture/zixun/W020211011780554733191.jpg',
				author: '政策解读',
				publishTime: '2024-01-15',
				readCount: 1250,
				tags: ['政策', '补贴', '养老服务']
			},
			{
				id: 2,
				title: '智慧养老技术创新，助力老年人生活',
				category: '科技资讯',
				summary: '最新的智能设备和技术应用，让老年人生活更加便利和安全。',
				content: '详细的新闻内容...',
				image: '/static/picture/zixun/71E00B4C613705188E11E072AC3B97F9A9493F32_size101_w1280_h853.jpg',
				author: '科技前沿',
				publishTime: '2024-01-14',
				readCount: 980,
				tags: ['科技', '智能设备', '生活便利']
			},
			{
				id: 3,
				title: '社区养老服务中心建设加速推进',
				category: '建设资讯',
				summary: '全市社区养老服务中心建设进展顺利，预计年底覆盖所有社区。',
				content: '详细的新闻内容...',
				image: '/static/picture/zixun/8663976bbb664a0e9f6fd0ee564e5a8c.jpeg',
				author: '建设动态',
				publishTime: '2024-01-13',
				readCount: 756,
				tags: ['社区', '服务中心', '建设']
			}
		]

		uni.setStorageSync('offline_news', news)
	}

	/**
	 * 初始化用户数据
	 */
	static initUserData() {
		const userData = {
			profile: {
				name: '张大爷',
				age: 75,
				phone: '138****5678',
				address: '北京市朝阳区某某小区',
				emergencyContact: '李女士',
				emergencyPhone: '139****1234',
				healthStatus: '良好',
				interests: ['太极拳', '书法', '园艺']
			},
			preferences: {
				elderlyMode: false,
				fontSize: 'medium',
				notifications: true,
				location: '北京市朝阳区'
			}
		}

		uni.setStorageSync('offline_user_data', userData)
	}

	/**
	 * 获取机构列表
	 */
	static getOfflineInstitutions(params = {}) {
		const institutions = uni.getStorageSync('offline_institutions') || []
		const { page = 1, pageSize = 10, keyword = '', type = '' } = params

		let filteredData = institutions

		// 关键词搜索
		if (keyword) {
			filteredData = filteredData.filter(item => 
				item.name.includes(keyword) || 
				item.address.includes(keyword) ||
				item.description.includes(keyword)
			)
		}

		// 类型筛选
		if (type) {
			filteredData = filteredData.filter(item => item.type === type)
		}

		// 分页
		const startIndex = (page - 1) * pageSize
		const endIndex = startIndex + pageSize
		const data = filteredData.slice(startIndex, endIndex)

		return {
			data,
			total: filteredData.length,
			page,
			pageSize,
			hasMore: endIndex < filteredData.length
		}
	}

	/**
	 * 获取服务列表
	 */
	static getOfflineServices(params = {}) {
		const services = uni.getStorageSync('offline_services') || []
		const { page = 1, pageSize = 10, category = '' } = params

		let filteredData = services

		// 分类筛选
		if (category) {
			filteredData = filteredData.filter(item => item.category === category)
		}

		// 分页
		const startIndex = (page - 1) * pageSize
		const endIndex = startIndex + pageSize
		const data = filteredData.slice(startIndex, endIndex)

		return {
			data,
			total: filteredData.length,
			page,
			pageSize,
			hasMore: endIndex < filteredData.length
		}
	}

	/**
	 * 获取补贴列表
	 */
	static getOfflineSubsidies(params = {}) {
		const subsidies = uni.getStorageSync('offline_subsidies') || []
		const { page = 1, pageSize = 10, category = '' } = params

		let filteredData = subsidies

		// 分类筛选
		if (category) {
			filteredData = filteredData.filter(item => item.category === category)
		}

		// 分页
		const startIndex = (page - 1) * pageSize
		const endIndex = startIndex + pageSize
		const data = filteredData.slice(startIndex, endIndex)

		return {
			data,
			total: filteredData.length,
			page,
			pageSize,
			hasMore: endIndex < filteredData.length
		}
	}

	/**
	 * 获取新闻列表
	 */
	static getOfflineNews(params = {}) {
		const news = uni.getStorageSync('offline_news') || []
		const { page = 1, pageSize = 10, category = '' } = params

		let filteredData = news

		// 分类筛选
		if (category) {
			filteredData = filteredData.filter(item => item.category === category)
		}

		// 按发布时间排序
		filteredData.sort((a, b) => new Date(b.publishTime) - new Date(a.publishTime))

		// 分页
		const startIndex = (page - 1) * pageSize
		const endIndex = startIndex + pageSize
		const data = filteredData.slice(startIndex, endIndex)

		return {
			data,
			total: filteredData.length,
			page,
			pageSize,
			hasMore: endIndex < filteredData.length
		}
	}

	/**
	 * 获取用户数据
	 */
	static getOfflineUserData() {
		return uni.getStorageSync('offline_user_data') || {}
	}

	/**
	 * 更新用户数据
	 */
	static updateOfflineUserData(userData) {
		const currentData = this.getOfflineUserData()
		const updatedData = { ...currentData, ...userData }
		uni.setStorageSync('offline_user_data', updatedData)
		return updatedData
	}

	/**
	 * 清除所有离线数据
	 */
	static clearOfflineData() {
		uni.removeStorageSync('offline_institutions')
		uni.removeStorageSync('offline_services')
		uni.removeStorageSync('offline_subsidies')
		uni.removeStorageSync('offline_news')
		uni.removeStorageSync('offline_user_data')
		uni.removeStorageSync('offline_data_initialized')
		console.log('离线数据已清除')
	}
}

export default OfflineDataManager
