/**
 * 智慧养老 - iOS风格设计系统
 * 基于iOS Human Interface Guidelines设计规范
 * 保持现有品牌色彩和适老化功能的完整性
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 */

/* ================================
   iOS风格颜色系统
   ================================ */

/* 智慧养老品牌色系 - 保持现有色彩 */
$primary-color: #ff8a00;           // 主色调 - 温暖橙色（保持不变）
$primary-light: #ffb74d;           // 主色浅色变体
$primary-dark: #f57c00;            // 主色深色变体
$primary-tint: rgba(255, 138, 0, 0.1); // 主色淡色背景

$institution-color: #ff6b6b;       // 机构色 - 活力红色（保持不变）
$service-color: #4ecdc4;           // 服务色 - 清新青色（保持不变）
$elderly-color: #96ceb4;           // 适老色 - 温和绿色（保持不变）
$medical-color: #45b7d1;           // 医疗色 - 信任蓝色（保持不变）
$emergency-color: #e74c3c;         // 紧急色 - 警示红色（保持不变）

/* iOS系统颜色 - 符合Apple设计规范 */
$ios-blue: #007AFF;               // iOS蓝色
$ios-green: #34C759;              // iOS绿色
$ios-red: #FF3B30;                // iOS红色
$ios-orange: #FF9500;             // iOS橙色
$ios-yellow: #FFCC00;             // iOS黄色
$ios-purple: #AF52DE;             // iOS紫色
$ios-pink: #FF2D92;               // iOS粉色
$ios-teal: #5AC8FA;               // iOS青色

/* iOS风格中性色系统 - 基于Apple设计规范 */
$gray-50: #F9FAFB;               // 最浅灰 - 背景色
$gray-100: #F3F4F6;              // 浅灰 - 卡片背景
$gray-200: #E5E7EB;              // 边框灰
$gray-300: #D1D5DB;              // 分割线
$gray-400: #9CA3AF;              // 占位符文字
$gray-500: #6B7280;              // 辅助文字
$gray-600: #4B5563;              // 次要文字
$gray-700: #374151;              // 主要文字
$gray-800: #1F2937;              // 标题文字
$gray-900: #111827;              // 强调文字

/* 行为相关颜色 - 更新为iOS风格 */
$uni-color-primary: $primary-color;
$uni-color-success: $ios-green;   // 使用iOS绿色
$uni-color-warning: $ios-orange;  // 使用iOS橙色
$uni-color-error: $ios-red;       // 使用iOS红色
$uni-color-info: $ios-blue;       // 使用iOS蓝色

/* 文字颜色系统 - iOS风格 */
$uni-text-color: $gray-800;              // 主要文字色
$uni-text-color-inverse: #ffffff;        // 反色文字
$uni-text-color-secondary: $gray-600;    // 次要文字色
$uni-text-color-tertiary: $gray-500;     // 三级文字色
$uni-text-color-placeholder: $gray-400;  // 占位符文字
$uni-text-color-disabled: $gray-300;     // 禁用文字色

/* 背景颜色系统 - iOS风格 */
$uni-bg-color: #ffffff;                  // 主背景色
$uni-bg-color-page: $gray-50;            // 页面背景色
$uni-bg-color-secondary: $gray-100;      // 次要背景色
$uni-bg-color-hover: rgba(0, 0, 0, 0.04); // 悬停背景色
$uni-bg-color-active: rgba(0, 0, 0, 0.08); // 激活背景色
$uni-bg-color-mask: rgba(0, 0, 0, 0.4);  // 遮罩颜色

/* 边框颜色系统 - iOS风格 */
$uni-border-color: $gray-200;            // 主边框色
$uni-border-color-light: $gray-100;      // 浅边框色
$uni-border-color-dark: $gray-300;       // 深边框色

/* ================================
   iOS风格尺寸系统
   ================================ */

/* iOS字体系统 - 基于Apple Typography规范 */
$font-family-ios: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'SF Pro Text', 'Helvetica Neue', Helvetica, Arial, sans-serif;

/* iOS字体大小 - 使用rpx单位适配小程序 */
$font-size-caption2: 22rpx;      // Caption 2 - 11pt
$font-size-caption1: 24rpx;      // Caption 1 - 12pt
$font-size-footnote: 26rpx;      // Footnote - 13pt
$font-size-subheadline: 30rpx;   // Subheadline - 15pt
$font-size-callout: 32rpx;       // Callout - 16pt
$font-size-body: 34rpx;          // Body - 17pt (iOS默认)
$font-size-headline: 36rpx;      // Headline - 18pt
$font-size-title3: 40rpx;        // Title 3 - 20pt
$font-size-title2: 44rpx;        // Title 2 - 22pt
$font-size-title1: 56rpx;        // Title 1 - 28pt
$font-size-large-title: 68rpx;   // Large Title - 34pt

/* iOS字体粗细 */
$font-weight-regular: 400;       // Regular
$font-weight-medium: 500;        // Medium
$font-weight-semibold: 600;      // Semibold
$font-weight-bold: 700;          // Bold

/* 兼容原有字体尺寸变量 */
$uni-font-size-sm: $font-size-caption1;
$uni-font-size-base: $font-size-body;
$uni-font-size-lg: $font-size-headline;

/* iOS间距系统 - 基于4pt网格系统 */
$spacing-2: 4rpx;    // 2pt
$spacing-4: 8rpx;    // 4pt
$spacing-6: 12rpx;   // 6pt
$spacing-8: 16rpx;   // 8pt
$spacing-12: 24rpx;  // 12pt
$spacing-16: 32rpx;  // 16pt
$spacing-20: 40rpx;  // 20pt
$spacing-24: 48rpx;  // 24pt
$spacing-32: 64rpx;  // 32pt
$spacing-40: 80rpx;  // 40pt
$spacing-48: 96rpx;  // 48pt
$spacing-64: 128rpx; // 64pt

/* 兼容原有间距变量 */
$uni-spacing-row-sm: $spacing-8;
$uni-spacing-row-base: $spacing-16;
$uni-spacing-row-lg: $spacing-24;
$uni-spacing-col-sm: $spacing-4;
$uni-spacing-col-base: $spacing-8;
$uni-spacing-col-lg: $spacing-12;

/* iOS圆角系统 */
$border-radius-xs: 8rpx;    // 4pt - 小圆角
$border-radius-sm: 12rpx;   // 6pt - 中小圆角
$border-radius-md: 16rpx;   // 8pt - 中等圆角
$border-radius-lg: 20rpx;   // 10pt - 大圆角
$border-radius-xl: 24rpx;   // 12pt - 超大圆角
$border-radius-2xl: 32rpx;  // 16pt - 特大圆角
$border-radius-full: 50%;   // 完全圆角

/* 兼容原有圆角变量 */
$uni-border-radius-sm: $border-radius-xs;
$uni-border-radius-base: $border-radius-sm;
$uni-border-radius-lg: $border-radius-md;
$uni-border-radius-circle: $border-radius-full;

/* 图片尺寸 - 更新为iOS风格 */
$uni-img-size-sm: 40rpx;    // 20pt
$uni-img-size-base: 52rpx;  // 26pt
$uni-img-size-lg: 80rpx;    // 40pt

/* ================================
   iOS风格阴影和效果系统
   ================================ */

/* iOS阴影系统 - 基于Apple设计规范 */
$shadow-xs: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);   // 极小阴影
$shadow-sm: 0 2rpx 16rpx rgba(0, 0, 0, 0.06);  // 小阴影
$shadow-md: 0 4rpx 24rpx rgba(0, 0, 0, 0.08);  // 中等阴影
$shadow-lg: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);  // 大阴影
$shadow-xl: 0 16rpx 48rpx rgba(0, 0, 0, 0.16); // 超大阴影

/* iOS分层阴影 - 用于卡片等组件 */
$shadow-card:
  0 2rpx 8rpx rgba(0, 0, 0, 0.04),
  0 4rpx 24rpx rgba(0, 0, 0, 0.06);

$shadow-card-hover:
  0 4rpx 16rpx rgba(0, 0, 0, 0.08),
  0 8rpx 32rpx rgba(0, 0, 0, 0.12);

/* 透明度系统 */
$opacity-disabled: 0.4;      // 禁用状态透明度
$opacity-secondary: 0.6;     // 次要元素透明度
$opacity-tertiary: 0.3;      // 三级元素透明度
$opacity-overlay: 0.8;       // 遮罩透明度

/* 兼容原有透明度变量 */
$uni-opacity-disabled: $opacity-disabled;

/* iOS风格缓动函数 */
$ease-out-quart: cubic-bezier(0.25, 1, 0.5, 1);
$ease-out-expo: cubic-bezier(0.19, 1, 0.22, 1);
$ease-in-out-quart: cubic-bezier(0.77, 0, 0.175, 1);
$ios-spring: cubic-bezier(0.25, 0.46, 0.45, 0.94);

/* 文章场景相关 - 更新为iOS风格 */
$uni-color-title: $gray-900;           // 文章标题颜色
$uni-font-size-title: $font-size-title2; // 使用iOS Title 2
$uni-color-subtitle: $gray-700;        // 二级标题颜色
$uni-font-size-subtitle: $font-size-title3; // 使用iOS Title 3
$uni-color-paragraph: $gray-600;       // 文章段落颜色
$uni-font-size-paragraph: $font-size-body; // 使用iOS Body

/* ================================
   智慧养老扩展色彩系统 - iOS风格优化
   ================================ */

/* 功能分类色彩 - 保持现有功能，优化色彩 */
$navigation-color: $primary-color;     // 导航色（保持）
$function-color: $ios-purple;          // 功能色 - 使用iOS紫色
$action-color: $ios-green;             // 操作色 - 使用iOS绿色
$status-color: $gray-500;              // 状态色 - 使用中性灰
$business-color: $institution-color;   // 业务色（保持）

/* iOS风格渐变色系 - 简化渐变，更接近iOS原生 */
$gradient-primary: linear-gradient(135deg, $primary-color 0%, $primary-dark 100%);
$gradient-institution: linear-gradient(135deg, $institution-color 0%, #ff5252 100%);
$gradient-service: linear-gradient(135deg, $service-color 0%, #26d0ce 100%);
$gradient-elderly: linear-gradient(135deg, $elderly-color 0%, #74b9ff 100%);

/* 适老化色彩系统 - iOS风格优化 */
$elderly-bg-color: $gray-50;           // 适老背景色 - 使用iOS浅灰
$elderly-text-color: $gray-900;        // 适老文字色 - 使用iOS深灰
$elderly-border-color: $gray-200;      // 适老边框色 - 使用iOS边框灰
$elderly-highlight-color: $elderly-color; // 适老高亮色（保持）

/* 状态色彩扩展 - iOS风格 */
$success-light: rgba(52, 199, 89, 0.1);   // 成功浅色背景
$warning-light: rgba(255, 149, 0, 0.1);   // 警告浅色背景
$error-light: rgba(255, 59, 48, 0.1);     // 错误浅色背景
$info-light: rgba(0, 122, 255, 0.1);      // 信息浅色背景

/* 图标专用色彩 - 保持现有功能 */
$icon-primary: $primary-color;
$icon-secondary: $gray-500;            // 使用iOS中性灰
$icon-institution: $institution-color;
$icon-service: $service-color;
$icon-elderly: $elderly-color;
$icon-medical: $medical-color;
$icon-emergency: $emergency-color;
$icon-disabled: $gray-300;             // 使用iOS禁用灰

/* ================================
   适老化增强系统 - iOS风格
   ================================ */

/* 适老化字体增强 */
$elderly-font-scale: 1.2;             // 适老版字体放大倍数
$elderly-font-weight: $font-weight-semibold; // 适老版字体粗细

/* 适老化间距增强 */
$elderly-spacing-scale: 1.5;          // 适老版间距放大倍数
$elderly-touch-target: 88rpx;         // 适老版最小触摸目标（44pt）

/* 适老化对比度增强 */
$elderly-contrast-bg: #ffffff;        // 适老版高对比背景
$elderly-contrast-text: #1a1a1a;      // 适老版高对比文字
$elderly-contrast-border: #b0b0b0;    // 适老版高对比边框
