<template>
	<view class="container" :class="{ 'elderly-mode': isElderlyMode }">
		<!-- 自定义导航栏 -->
		<view class="custom-navbar" :class="{ 'elderly-mode': isElderlyMode }">
			<view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
			<view class="navbar-content">
				<view class="navbar-left" @click="goBack">
					<Icon
						name="arrow-left-line"
						:size="isElderlyMode ? '40rpx' : '36rpx'"
						color="#333"
					></Icon>
					<text class="back-text">返回</text>
				</view>
				<view class="navbar-center">
					<text class="navbar-title">我的收藏</text>
				</view>
				<view class="navbar-right"></view>
			</view>
		</view>

		<!-- 收藏分类筛选 -->
		<view class="filter-tabs">
			<view class="tab-item" :class="{ active: activeTab === tab.key }" v-for="tab in favoriteTabs" :key="tab.key" @click="switchTab(tab.key)">
				<text class="tab-text">{{tab.name}}</text>
				<view class="tab-badge" v-if="tab.count > 0">{{tab.count}}</view>
			</view>
		</view>

		<!-- 操作栏 -->
		<view class="action-bar" v-if="favoriteList.length > 0">
			<view class="select-all">
				<checkbox :checked="isAllSelected" @change="toggleSelectAll" color="#ff8a00" />
				<text class="select-text">全选</text>
			</view>
			<button class="batch-delete-btn" @click="batchDelete" :disabled="selectedItems.length === 0">
				删除选中 ({{selectedItems.length}})
			</button>
		</view>

		<!-- 收藏列表 -->
		<view class="favorite-list">
			<view class="favorite-item" v-for="(item, index) in filteredFavorites" :key="index">
				<view class="item-checkbox">
					<checkbox :checked="selectedItems.includes(item.id)" @change="toggleSelect(item.id)" color="#ff8a00" />
				</view>
				
				<view class="item-content" @click="viewDetail(item)">
					<view class="item-image">
						<image :src="item.image" class="image" mode="aspectFill" v-if="item.image"></image>
						<view class="image-placeholder" v-else>
							<Icon :name="item.icon" size="40rpx" color="#999"></Icon>
						</view>
					</view>
					
					<view class="item-info">
						<text class="item-title">{{item.title}}</text>
						<text class="item-desc">{{item.description}}</text>
						<view class="item-meta">
							<text class="item-type">{{getTypeText(item.type)}}</text>
							<text class="item-time">{{item.favoriteTime}}</text>
						</view>
						<view class="item-tags" v-if="item.tags && item.tags.length > 0">
							<text class="tag" v-for="tag in item.tags" :key="tag">{{tag}}</text>
						</view>
					</view>
					
					<view class="item-price" v-if="item.price">
						<text class="price-label">价格</text>
						<text class="price-value">¥{{item.price}}</text>
					</view>
				</view>
				
				<view class="item-actions">
					<button class="action-btn share" @click="shareItem(item)">
						<Icon name="share-line" size="24rpx" color="#666"></Icon>
					</button>
					<button class="action-btn unfavorite" @click="removeFavorite(item)">
						<Icon name="heart-fill" size="24rpx" color="#ff4444"></Icon>
					</button>
				</view>
			</view>
		</view>

		<!-- 空状态 -->
		<view class="empty-state" v-if="filteredFavorites.length === 0">
			<Icon name="star-line" size="120rpx" color="#ccc"></Icon>
			<text class="empty-text">暂无{{getTabName(activeTab)}}收藏</text>
			<button class="empty-btn" @click="goToExplore">去发现更多</button>
		</view>
	</view>
</template>

<script>
import Icon from '@/components/Icon/Icon.vue'
import crudAPI from '@/utils/crudAPI.js'
import FeedbackUtils from '@/utils/feedback.js'
import { elderlyModeManager } from '@/utils/elderlyModeUtils.js'

export default {
	components: {
		Icon
	},
	data() {
		return {
			statusBarHeight: 0, // 状态栏高度
			isElderlyMode: false, // 适老化模式
			activeTab: 'all',
			selectedItems: [],
			favoriteTabs: [
				{ key: 'all', name: '全部', count: 0 },
				{ key: 'service', name: '服务', count: 0 },
				{ key: 'news', name: '资讯', count: 0 },
				{ key: 'institution', name: '机构', count: 0 },
				{ key: 'equipment', name: '辅具', count: 0 }
			],
			favoriteList: [],
			loading: false,
			refreshing: false
		}
	},
	computed: {
		filteredFavorites() {
			if (this.activeTab === 'all') {
				return this.favoriteList;
			}
			return this.favoriteList.filter(item => item.type === this.activeTab);
		},
		isAllSelected() {
			return this.filteredFavorites.length > 0 && this.selectedItems.length === this.filteredFavorites.length;
		}
	},
	onLoad() {
		// 获取状态栏高度
		const systemInfo = uni.getSystemInfoSync();
		this.statusBarHeight = systemInfo.statusBarHeight || 44;

		// 初始化适老化模式
		this.isElderlyMode = elderlyModeManager.getElderlyMode();
		elderlyModeManager.onElderlyModeChange((isEnabled) => {
			this.isElderlyMode = isEnabled;
		});

		// 加载收藏数据
		this.loadFavorites();
	},
	onShow() {
		// 页面显示时重新加载数据
		this.loadFavorites();
	},
	methods: {
		// 加载收藏数据
		async loadFavorites() {
			if (this.loading) return;

			try {
				this.loading = true;

				const params = {
					page: 1,
					pageSize: 100
				};

				const result = await crudAPI.getFavorites(params);

				if (result.success) {
					this.favoriteList = result.data.list;
					this.updateTabCounts();

					if (result.data.list.length > 0) {
						FeedbackUtils.showSuccess(`已加载 ${result.data.list.length} 个收藏`);
					}
				} else {
					FeedbackUtils.showError(result.message || '数据加载失败，请重试');
					this.favoriteList = [];
				}
			} catch (error) {
				console.error('加载收藏失败:', error);
				FeedbackUtils.showError('数据加载失败，请重试');
				this.favoriteList = [];
			} finally {
				this.loading = false;
				this.refreshing = false;
			}
		},

		switchTab(tabKey) {
			this.activeTab = tabKey;
			this.selectedItems = [];
		},
		updateTabCounts() {
			this.favoriteTabs.forEach(tab => {
				if (tab.key === 'all') {
					tab.count = this.favoriteList.length;
				} else {
					tab.count = this.favoriteList.filter(item => item.type === tab.key).length;
				}
			});
		},
		getTypeText(type) {
			const typeMap = {
				'service': '服务',
				'news': '资讯',
				'institution': '机构',
				'equipment': '辅具'
			};
			return typeMap[type] || '其他';
		},
		getTabName(tabKey) {
			const tab = this.favoriteTabs.find(t => t.key === tabKey);
			return tab ? tab.name : '';
		},
		toggleSelect(itemId) {
			const index = this.selectedItems.indexOf(itemId);
			if (index > -1) {
				this.selectedItems.splice(index, 1);
			} else {
				this.selectedItems.push(itemId);
			}
		},
		toggleSelectAll() {
			if (this.isAllSelected) {
				this.selectedItems = [];
			} else {
				this.selectedItems = this.filteredFavorites.map(item => item.id);
			}
		},
		async batchDelete() {
			if (this.selectedItems.length === 0) return;

			try {
				await FeedbackUtils.showConfirm({
					title: '确认删除',
					content: `确定要删除选中的${this.selectedItems.length}个收藏吗？`,
					confirmText: '删除',
					cancelText: '取消'
				});

				FeedbackUtils.showLoading('删除中...');

				// 批量删除收藏
				const deletePromises = this.selectedItems.map(itemId =>
					crudAPI.deleteFavorite(itemId)
				);

				await Promise.all(deletePromises);

				FeedbackUtils.hideLoading();

				this.selectedItems = [];
				this.loadFavorites(); // 重新加载数据
				FeedbackUtils.showSuccess('删除成功');
			} catch (error) {
				FeedbackUtils.hideLoading();
				console.log('用户取消删除');
			}
		},
		async removeFavorite(item) {
			try {
				await FeedbackUtils.showConfirm({
					title: '取消收藏',
					content: '确定要取消收藏这个项目吗？',
					confirmText: '取消收藏',
					cancelText: '保留'
				});

				FeedbackUtils.showLoading('处理中...');

				const result = await crudAPI.deleteFavorite(item.id);

				FeedbackUtils.hideLoading();

				if (result.success) {
					FeedbackUtils.showSuccess('已取消收藏');
					this.loadFavorites(); // 重新加载数据
				} else {
					FeedbackUtils.showError(result.message || '取消收藏失败');
				}
			} catch (error) {
				FeedbackUtils.hideLoading();
				console.log('用户取消操作');
			}
		},
		shareItem(item) {
			uni.showActionSheet({
				itemList: ['分享给朋友', '分享到朋友圈', '复制链接'],
				success: (res) => {
					const actions = ['分享给朋友', '分享到朋友圈', '复制链接'];
					uni.showToast({
						title: actions[res.tapIndex],
						icon: 'success'
					});
				}
			});
		},
		viewDetail(item) {
			let url = '';
			switch (item.type) {
				case 'service':
					url = `/pages/service/detail?id=${item.id}`;
					break;
				case 'news':
					url = `/pages/news/detail?id=${item.id}`;
					break;
				case 'institution':
					url = `/pages/institution/detail?id=${item.id}`;
					break;
				case 'equipment':
					url = `/pages/equipment/detail?id=${item.id}`;
					break;
				default:
					return;
			}
			uni.navigateTo({ url });
		},
		goToExplore() {
			uni.switchTab({
				url: '/pages/home/<USER>'
			});
		},

		// 返回上一页
		goBack() {
			uni.navigateBack({
				fail: () => {
					uni.switchTab({
						url: '/pages/profile/profile'
					});
				}
			});
		}
	}
}
</script>

<style scoped>
.container {
	background: #f8f9fa;
	min-height: 100vh;
}

/* 导航栏样式 */
.custom-navbar {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 1000;
	background: rgba(255, 255, 255, 0.95);
	backdrop-filter: blur(20rpx);
	-webkit-backdrop-filter: blur(20rpx);
	border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);
}

.navbar-content {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 20rpx 32rpx;
	min-height: 88rpx;
}

.navbar-left {
	display: flex;
	align-items: center;
	gap: 12rpx;
	padding: 8rpx 16rpx 8rpx 0;
	border-radius: 20rpx;
	transition: all 0.2s ease;
}

.navbar-left:active {
	background-color: rgba(0, 0, 0, 0.04);
	transform: scale(0.96);
}

.back-text {
	font-size: 32rpx;
	color: #333;
	font-weight: 500;
}

.navbar-center {
	position: absolute;
	left: 50%;
	transform: translateX(-50%);
}

.navbar-title {
	font-size: 34rpx;
	font-weight: 600;
	color: #333;
}

.navbar-right {
	display: flex;
	align-items: center;
}

.filter-tabs {
	background: white;
	display: flex;
	padding: 0 20rpx;
	box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
	margin-top: 220rpx; /* 为导航栏留出空间 */
}

.tab-item {
	flex: 1;
	text-align: center;
	padding: 30rpx 20rpx;
	position: relative;
}

.tab-item.active {
	border-bottom: 4rpx solid #ff8a00;
}

.tab-text {
	font-size: 28rpx;
	color: #666;
}

.tab-item.active .tab-text {
	color: #ff8a00;
	font-weight: bold;
}

.tab-badge {
	position: absolute;
	top: 15rpx;
	right: 15rpx;
	background: #ff4444;
	color: white;
	font-size: 20rpx;
	padding: 4rpx 8rpx;
	border-radius: 12rpx;
	min-width: 24rpx;
	text-align: center;
}

.action-bar {
	background: white;
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20rpx 30rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.select-all {
	display: flex;
	align-items: center;
	gap: 15rpx;
}

.select-text {
	font-size: 28rpx;
	color: #333;
}

.batch-delete-btn {
	background: #ff4444;
	color: white;
	border: none;
	border-radius: 20rpx;
	padding: 15rpx 30rpx;
	font-size: 26rpx;
}

.batch-delete-btn:disabled {
	background: #ccc;
}

.favorite-list {
	padding: 20rpx;
}

.favorite-item {
	background: white;
	border-radius: 20rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	display: flex;
	align-items: center;
	gap: 20rpx;
	box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.item-checkbox {
	flex-shrink: 0;
}

.item-content {
	flex: 1;
	display: flex;
	gap: 20rpx;
}

.item-image {
	width: 120rpx;
	height: 120rpx;
	border-radius: 15rpx;
	overflow: hidden;
	flex-shrink: 0;
}

.image {
	width: 100%;
	height: 100%;
}

.image-placeholder {
	width: 100%;
	height: 100%;
	background: #f0f0f0;
	display: flex;
	align-items: center;
	justify-content: center;
}

.item-info {
	flex: 1;
}

.item-title {
	font-size: 28rpx;
	color: #333;
	font-weight: bold;
	display: block;
	margin-bottom: 8rpx;
}

.item-desc {
	font-size: 24rpx;
	color: #666;
	display: block;
	margin-bottom: 15rpx;
	line-height: 1.4;
}

.item-meta {
	display: flex;
	justify-content: space-between;
	margin-bottom: 15rpx;
}

.item-type {
	font-size: 22rpx;
	color: #ff8a00;
	background: rgba(255, 138, 0, 0.1);
	padding: 4rpx 12rpx;
	border-radius: 12rpx;
}

.item-time {
	font-size: 22rpx;
	color: #999;
}

.item-tags {
	display: flex;
	gap: 8rpx;
	flex-wrap: wrap;
}

.tag {
	font-size: 20rpx;
	color: #666;
	background: #f0f0f0;
	padding: 4rpx 10rpx;
	border-radius: 10rpx;
}

.item-price {
	text-align: right;
	flex-shrink: 0;
}

.price-label {
	font-size: 22rpx;
	color: #666;
	display: block;
	margin-bottom: 5rpx;
}

.price-value {
	font-size: 28rpx;
	color: #ff8a00;
	font-weight: bold;
	display: block;
}

.item-actions {
	display: flex;
	flex-direction: column;
	gap: 15rpx;
	flex-shrink: 0;
}

.action-btn {
	width: 60rpx;
	height: 60rpx;
	border-radius: 50%;
	border: 1rpx solid #e0e0e0;
	background: white;
	display: flex;
	align-items: center;
	justify-content: center;
}

.action-btn.unfavorite {
	border-color: #ff4444;
}

.empty-state {
	text-align: center;
	padding: 120rpx 40rpx;
}

.empty-text {
	font-size: 28rpx;
	color: #999;
	display: block;
	margin: 30rpx 0 40rpx;
}

.empty-btn {
	background: #ff8a00;
	color: white;
	border: none;
	border-radius: 25rpx;
	padding: 20rpx 40rpx;
	font-size: 28rpx;
}

/* ===== 适老化模式样式 ===== */
.elderly-mode .navbar-content {
	padding: 24rpx 36rpx;
	min-height: 96rpx;
}

.elderly-mode .navbar-left {
	gap: 16rpx;
	padding: 12rpx 20rpx 12rpx 0;
}

.elderly-mode .back-text {
	font-size: 36rpx;
	font-weight: 600;
}

.elderly-mode .navbar-title {
	font-size: 38rpx;
	font-weight: 700;
}

.elderly-mode .filter-tabs {
	margin-top: 240rpx; /* 适老化模式下导航栏更高 */
}

.elderly-mode .tab-item {
	padding: 30rpx 20rpx;
}

.elderly-mode .tab-text {
	font-size: 32rpx;
}

.elderly-mode .favorite-item {
	padding: 40rpx 30rpx;
}

.elderly-mode .favorite-title {
	font-size: 36rpx;
}

.elderly-mode .favorite-desc {
	font-size: 30rpx;
}

.elderly-mode .favorite-time {
	font-size: 28rpx;
}

.elderly-mode .favorite-category {
	font-size: 28rpx;
	padding: 12rpx 24rpx;
}
</style>
