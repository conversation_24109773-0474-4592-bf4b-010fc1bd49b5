<template>
	<view class="container" :class="{ 'elderly-mode': isElderlyMode }">
		<!-- 自定义导航栏 -->
		<view class="custom-navbar" :class="{ 'elderly-mode': isElderlyMode }">
			<view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
			<view class="navbar-content">
				<view class="navbar-left" @click="goBack">
					<Icon
						name="arrow-left-line"
						:size="isElderlyMode ? '40rpx' : '36rpx'"
						color="#333"
					></Icon>
					<text class="back-text">返回</text>
				</view>
				<view class="navbar-center">
					<text class="navbar-title">我的板块测试</text>
				</view>
				<view class="navbar-right"></view>
			</view>
		</view>

		<!-- 内容区域 -->
		<view class="content">
			<!-- 测试说明 -->
			<view class="info-section">
				<view class="info-header">
					<Icon name="information-line" size="32rpx" color="#ff8a00"></Icon>
					<text class="info-title">导航栏一致性测试</text>
				</view>
				<text class="info-desc">测试"我的"板块中所有功能页面的导航栏样式和返回功能是否一致</text>
			</view>

			<!-- 个人信息页面测试 -->
			<view class="test-section">
				<view class="section-header">
					<Icon name="user-line" size="32rpx" color="#ff8a00"></Icon>
					<text class="section-title">个人信息页面</text>
				</view>
				
				<view class="test-item" @click="testPage('/pages/profile/info')">
					<view class="test-info">
						<text class="test-name">基本信息</text>
						<text class="test-desc">用户基本信息编辑页面</text>
					</view>
					<view class="test-status">
						<Icon name="check-line" size="24rpx" color="#34c759"></Icon>
						<text class="status-text">已修复</text>
					</view>
				</view>
				
				<view class="test-item" @click="testPage('/pages/profile/contact')">
					<view class="test-info">
						<text class="test-name">联系方式</text>
						<text class="test-desc">联系方式和地址管理</text>
					</view>
					<view class="test-status">
						<Icon name="check-line" size="24rpx" color="#34c759"></Icon>
						<text class="status-text">已修复</text>
					</view>
				</view>
				
				<view class="test-item" @click="testPage('/pages/profile/emergency')">
					<view class="test-info">
						<text class="test-name">紧急联系人</text>
						<text class="test-desc">紧急联系人管理</text>
					</view>
					<view class="test-status">
						<Icon name="time-line" size="24rpx" color="#ff9800"></Icon>
						<text class="status-text">待开发</text>
					</view>
				</view>
			</view>

			<!-- 账户管理页面测试 -->
			<view class="test-section">
				<view class="section-header">
					<Icon name="shield-check-line" size="32rpx" color="#34c759"></Icon>
					<text class="section-title">账户管理页面</text>
				</view>
				
				<view class="test-item" @click="testPage('/pages/account/security')">
					<view class="test-info">
						<text class="test-name">安全设置</text>
						<text class="test-desc">账户安全和登录设置</text>
					</view>
					<view class="test-status">
						<Icon name="check-line" size="24rpx" color="#34c759"></Icon>
						<text class="status-text">已修复</text>
					</view>
				</view>
				
				<view class="test-item" @click="testPage('/pages/account/password')">
					<view class="test-info">
						<text class="test-name">密码管理</text>
						<text class="test-desc">修改登录密码</text>
					</view>
					<view class="test-status">
						<Icon name="time-line" size="24rpx" color="#ff9800"></Icon>
						<text class="status-text">待开发</text>
					</view>
				</view>
				
				<view class="test-item" @click="testPage('/pages/account/privacy')">
					<view class="test-info">
						<text class="test-name">隐私设置</text>
						<text class="test-desc">隐私和权限设置</text>
					</view>
					<view class="test-status">
						<Icon name="time-line" size="24rpx" color="#ff9800"></Icon>
						<text class="status-text">待开发</text>
					</view>
				</view>
			</view>

			<!-- 快捷功能页面测试 -->
			<view class="test-section">
				<view class="section-header">
					<Icon name="apps-line" size="32rpx" color="#2196f3"></Icon>
					<text class="section-title">快捷功能页面</text>
				</view>
				
				<view class="test-item" @click="testPage('/pages/order/list')">
					<view class="test-info">
						<text class="test-name">我的订单</text>
						<text class="test-desc">订单列表和管理</text>
					</view>
					<view class="test-status">
						<Icon name="time-line" size="24rpx" color="#ff9800"></Icon>
						<text class="status-text">待开发</text>
					</view>
				</view>
				
				<view class="test-item" @click="testPage('/pages/favorite/list')">
					<view class="test-info">
						<text class="test-name">我的收藏</text>
						<text class="test-desc">收藏内容管理</text>
					</view>
					<view class="test-status">
						<Icon name="time-line" size="24rpx" color="#ff9800"></Icon>
						<text class="status-text">待开发</text>
					</view>
				</view>
			</view>

			<!-- 测试结果 -->
			<view class="result-section">
				<view class="result-header">
					<Icon name="bar-chart-line" size="32rpx" color="#9c27b0"></Icon>
					<text class="result-title">测试结果统计</text>
				</view>
				
				<view class="result-stats">
					<view class="stat-item">
						<text class="stat-number">{{ completedCount }}</text>
						<text class="stat-label">已完成</text>
					</view>
					<view class="stat-item">
						<text class="stat-number">{{ pendingCount }}</text>
						<text class="stat-label">待开发</text>
					</view>
					<view class="stat-item">
						<text class="stat-number">{{ totalCount }}</text>
						<text class="stat-label">总计</text>
					</view>
				</view>
				
				<view class="progress-bar">
					<view class="progress-fill" :style="{ width: progressPercent + '%' }"></view>
				</view>
				<text class="progress-text">完成度：{{ progressPercent }}%</text>
			</view>

			<!-- 操作按钮 -->
			<view class="actions">
				<button class="action-btn primary" @click="testAllPages">测试所有页面</button>
				<button class="action-btn" @click="goToProfile">返回我的页面</button>
			</view>
		</view>
	</view>
</template>

<script>
import Icon from '@/components/Icon/Icon.vue'
import FeedbackUtils from '@/utils/feedback.js'
import { elderlyModeManager } from '@/utils/elderlyModeUtils.js'

export default {
	components: {
		Icon
	},
	data() {
		return {
			statusBarHeight: 0,
			isElderlyMode: false,
			testPages: [
				{ url: '/pages/profile/info', name: '基本信息', completed: true },
				{ url: '/pages/profile/contact', name: '联系方式', completed: true },
				{ url: '/pages/profile/emergency', name: '紧急联系人', completed: false },
				{ url: '/pages/account/security', name: '安全设置', completed: true },
				{ url: '/pages/account/password', name: '密码管理', completed: false },
				{ url: '/pages/account/privacy', name: '隐私设置', completed: false },
				{ url: '/pages/order/list', name: '我的订单', completed: false },
				{ url: '/pages/favorite/list', name: '我的收藏', completed: false }
			]
		}
	},
	
	computed: {
		completedCount() {
			return this.testPages.filter(page => page.completed).length;
		},
		
		pendingCount() {
			return this.testPages.filter(page => !page.completed).length;
		},
		
		totalCount() {
			return this.testPages.length;
		},
		
		progressPercent() {
			return Math.round((this.completedCount / this.totalCount) * 100);
		}
	},
	
	onLoad() {
		// 获取状态栏高度
		const systemInfo = uni.getSystemInfoSync();
		this.statusBarHeight = systemInfo.statusBarHeight || 44;

		// 初始化适老化模式
		this.isElderlyMode = elderlyModeManager.getElderlyMode();
		elderlyModeManager.onElderlyModeChange((isEnabled) => {
			this.isElderlyMode = isEnabled;
		});
	},
	
	methods: {
		testPage(url) {
			try {
				uni.navigateTo({
					url: url,
					success: () => {
						FeedbackUtils.showSuccess(`正在测试：${url}`);
					},
					fail: (error) => {
						FeedbackUtils.showError(`页面不存在：${url}`);
						console.error('页面跳转失败:', error);
					}
				});
			} catch (error) {
				FeedbackUtils.showError('测试失败');
				console.error('测试页面失败:', error);
			}
		},
		
		testAllPages() {
			FeedbackUtils.showInfo('开始批量测试所有页面...');
			
			let index = 0;
			const testNext = () => {
				if (index >= this.testPages.length) {
					FeedbackUtils.showSuccess('所有页面测试完成');
					return;
				}
				
				const page = this.testPages[index];
				if (page.completed) {
					setTimeout(() => {
						this.testPage(page.url);
						index++;
						setTimeout(testNext, 2000); // 2秒后测试下一个
					}, 1000);
				} else {
					FeedbackUtils.showInfo(`跳过未开发页面：${page.name}`);
					index++;
					setTimeout(testNext, 500);
				}
			};
			
			testNext();
		},
		
		goToProfile() {
			uni.switchTab({
				url: '/pages/profile/profile'
			});
		},
		
		goBack() {
			FeedbackUtils.lightFeedback();
			uni.navigateBack({
				fail: () => {
					this.goToProfile();
				}
			});
		}
	}
}
</script>

<style scoped>
.container {
	background: #f5f5f5;
	min-height: 100vh;
}

/* 导航栏样式 */
.custom-navbar {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 1000;
	background: rgba(255, 255, 255, 0.95);
	backdrop-filter: blur(20rpx);
	-webkit-backdrop-filter: blur(20rpx);
	border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);
}

.navbar-content {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 20rpx 32rpx;
	min-height: 88rpx;
}

.navbar-left {
	display: flex;
	align-items: center;
	gap: 12rpx;
	padding: 8rpx 16rpx 8rpx 0;
	border-radius: 20rpx;
	transition: all 0.2s ease;
}

.navbar-left:active {
	background-color: rgba(0, 0, 0, 0.04);
	transform: scale(0.96);
}

.back-text {
	font-size: 32rpx;
	color: #333;
	font-weight: 500;
}

.navbar-center {
	position: absolute;
	left: 50%;
	transform: translateX(-50%);
}

.navbar-title {
	font-size: 34rpx;
	font-weight: 600;
	color: #333;
}

/* 内容区域 */
.content {
	padding-top: 200rpx;
	padding-bottom: 40rpx;
}

/* 信息区块 */
.info-section {
	background: linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%);
	margin-bottom: 20rpx;
	border-radius: 16rpx;
	padding: 32rpx;
	color: white;
}

.info-header {
	display: flex;
	align-items: center;
	gap: 12rpx;
	margin-bottom: 16rpx;
}

.info-title {
	font-size: 32rpx;
	font-weight: 600;
}

.info-desc {
	font-size: 26rpx;
	opacity: 0.9;
	line-height: 1.5;
}

/* 测试区块 */
.test-section {
	background: white;
	margin-bottom: 20rpx;
	border-radius: 16rpx;
	overflow: hidden;
}

.section-header {
	display: flex;
	align-items: center;
	gap: 12rpx;
	padding: 24rpx 32rpx;
	background: rgba(255, 138, 0, 0.05);
	border-bottom: 1rpx solid #f0f0f0;
}

.section-title {
	font-size: 30rpx;
	font-weight: 600;
	color: #333;
}

.test-item {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 32rpx;
	border-bottom: 1rpx solid #f0f0f0;
	transition: all 0.2s ease;
}

.test-item:last-child {
	border-bottom: none;
}

.test-item:active {
	background-color: rgba(0, 0, 0, 0.02);
}

.test-info {
	flex: 1;
}

.test-name {
	font-size: 28rpx;
	color: #333;
	font-weight: 500;
	display: block;
	margin-bottom: 8rpx;
}

.test-desc {
	font-size: 24rpx;
	color: #666;
	display: block;
}

.test-status {
	display: flex;
	align-items: center;
	gap: 8rpx;
}

.status-text {
	font-size: 24rpx;
	font-weight: 500;
}

/* 结果统计 */
.result-section {
	background: white;
	margin-bottom: 20rpx;
	border-radius: 16rpx;
	padding: 32rpx;
}

.result-header {
	display: flex;
	align-items: center;
	gap: 12rpx;
	margin-bottom: 24rpx;
}

.result-title {
	font-size: 30rpx;
	font-weight: 600;
	color: #333;
}

.result-stats {
	display: flex;
	justify-content: space-around;
	margin-bottom: 24rpx;
}

.stat-item {
	text-align: center;
}

.stat-number {
	font-size: 48rpx;
	font-weight: bold;
	color: #ff8a00;
	display: block;
	margin-bottom: 8rpx;
}

.stat-label {
	font-size: 24rpx;
	color: #666;
}

.progress-bar {
	height: 12rpx;
	background: #f0f0f0;
	border-radius: 6rpx;
	overflow: hidden;
	margin-bottom: 12rpx;
}

.progress-fill {
	height: 100%;
	background: linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%);
	transition: width 0.3s ease;
}

.progress-text {
	font-size: 24rpx;
	color: #666;
	text-align: center;
	display: block;
}

/* 操作按钮 */
.actions {
	display: flex;
	gap: 20rpx;
	justify-content: center;
}

.action-btn {
	background: #6b7280;
	color: white;
	border: none;
	border-radius: 12rpx;
	padding: 20rpx 40rpx;
	font-size: 28rpx;
}

.action-btn.primary {
	background: #ff8a00;
}

/* ===== 适老化模式样式 ===== */
.elderly-mode .navbar-content {
	padding: 24rpx 36rpx;
	min-height: 96rpx;
}

.elderly-mode .navbar-left {
	gap: 16rpx;
	padding: 12rpx 20rpx 12rpx 0;
}

.elderly-mode .back-text {
	font-size: 36rpx;
	font-weight: 600;
}

.elderly-mode .navbar-title {
	font-size: 38rpx;
	font-weight: 700;
}

.elderly-mode .test-content {
	margin-top: 240rpx;
	padding: 50rpx 30rpx;
}

.elderly-mode .test-item {
	padding: 40rpx 30rpx;
}

.elderly-mode .test-name {
	font-size: 36rpx;
}
</style>
