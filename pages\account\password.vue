<template>
	<view class="container">
		<!-- 自定义导航栏 -->
		<view class="custom-navbar">
			<view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
			<view class="navbar-content">
				<view class="navbar-left" @click="goBack">
					<Icon name="arrow-left-line" size="36rpx" color="#333"></Icon>
					<text class="back-text">返回</text>
				</view>
				<view class="navbar-center">
					<text class="navbar-title">修改密码</text>
				</view>
				<view class="navbar-right"></view>
			</view>
		</view>

		<!-- 修改密码表单 -->
		<view class="password-form">
			<view class="form-header">
				<Icon name="lock-password-line" size="60rpx" color="#ff8a00"></Icon>
				<text class="form-title">修改登录密码</text>
				<text class="form-desc">为了您的账户安全，请定期更换密码</text>
			</view>

			<view class="form-content">
				<view class="form-item">
					<text class="form-label">当前密码</text>
					<view class="input-wrapper">
						<input 
							class="form-input" 
							:type="showOldPassword ? 'text' : 'password'"
							v-model="formData.oldPassword" 
							placeholder="请输入当前密码" 
						/>
						<button class="toggle-btn" @click="toggleOldPassword">
							<Icon :name="showOldPassword ? 'eye-line' : 'eye-off-line'" size="24rpx" color="#999"></Icon>
						</button>
					</view>
				</view>

				<view class="form-item">
					<text class="form-label">新密码</text>
					<view class="input-wrapper">
						<input 
							class="form-input" 
							:type="showNewPassword ? 'text' : 'password'"
							v-model="formData.newPassword" 
							placeholder="请输入新密码" 
							@input="checkPasswordStrength"
						/>
						<button class="toggle-btn" @click="toggleNewPassword">
							<Icon :name="showNewPassword ? 'eye-line' : 'eye-off-line'" size="24rpx" color="#999"></Icon>
						</button>
					</view>
					<view class="password-strength" v-if="formData.newPassword">
						<text class="strength-label">密码强度：</text>
						<view class="strength-bar">
							<view class="strength-fill" :class="passwordStrength.level" :style="{width: passwordStrength.width}"></view>
						</view>
						<text class="strength-text" :class="passwordStrength.level">{{passwordStrength.text}}</text>
					</view>
				</view>

				<view class="form-item">
					<text class="form-label">确认新密码</text>
					<view class="input-wrapper">
						<input 
							class="form-input" 
							:type="showConfirmPassword ? 'text' : 'password'"
							v-model="formData.confirmPassword" 
							placeholder="请再次输入新密码" 
						/>
						<button class="toggle-btn" @click="toggleConfirmPassword">
							<Icon :name="showConfirmPassword ? 'eye-line' : 'eye-off-line'" size="24rpx" color="#999"></Icon>
						</button>
					</view>
					<view class="password-match" v-if="formData.confirmPassword">
						<Icon :name="passwordMatch ? 'check-line' : 'close-line'" size="20rpx" :color="passwordMatch ? '#4caf50' : '#f44336'"></Icon>
						<text class="match-text" :class="passwordMatch ? 'success' : 'error'">
							{{passwordMatch ? '密码匹配' : '密码不匹配'}}
						</text>
					</view>
				</view>
			</view>

			<button class="submit-btn" @click="changePassword" :disabled="!canSubmit">修改密码</button>
		</view>

		<!-- 密码要求 -->
		<view class="password-rules">
			<view class="rules-header">
				<Icon name="information-line" size="32rpx" color="#ff8a00"></Icon>
				<text class="rules-title">密码要求</text>
			</view>
			<view class="rules-list">
				<view class="rule-item" :class="{ valid: rules.length }">
					<Icon :name="rules.length ? 'check-line' : 'close-line'" size="20rpx" :color="rules.length ? '#4caf50' : '#999'"></Icon>
					<text class="rule-text">密码长度8-20位</text>
				</view>
				<view class="rule-item" :class="{ valid: rules.letter }">
					<Icon :name="rules.letter ? 'check-line' : 'close-line'" size="20rpx" :color="rules.letter ? '#4caf50' : '#999'"></Icon>
					<text class="rule-text">包含大小写字母</text>
				</view>
				<view class="rule-item" :class="{ valid: rules.number }">
					<Icon :name="rules.number ? 'check-line' : 'close-line'" size="20rpx" :color="rules.number ? '#4caf50' : '#999'"></Icon>
					<text class="rule-text">包含数字</text>
				</view>
				<view class="rule-item" :class="{ valid: rules.special }">
					<Icon :name="rules.special ? 'check-line' : 'close-line'" size="20rpx" :color="rules.special ? '#4caf50' : '#999'"></Icon>
					<text class="rule-text">包含特殊字符</text>
				</view>
			</view>
		</view>

		<!-- 安全提示 -->
		<view class="security-tips">
			<view class="tips-header">
				<Icon name="shield-check-line" size="32rpx" color="#4caf50"></Icon>
				<text class="tips-title">安全提示</text>
			</view>
			<view class="tips-content">
				<text class="tip-text">• 建议每3个月更换一次密码</text>
				<text class="tip-text">• 不要使用生日、姓名等容易猜测的密码</text>
				<text class="tip-text">• 不要在多个平台使用相同密码</text>
				<text class="tip-text">• 密码修改后请重新登录所有设备</text>
			</view>
		</view>
	</view>
</template>

<script>
import Icon from '@/components/Icon/Icon.vue'

export default {
	components: {
		Icon
	},
	data() {
		return {
			statusBarHeight: 0,
			showOldPassword: false,
			showNewPassword: false,
			showConfirmPassword: false,
			formData: {
				oldPassword: '',
				newPassword: '',
				confirmPassword: ''
			},
			passwordStrength: {
				level: 'weak',
				width: '0%',
				text: '弱'
			},
			rules: {
				length: false,
				letter: false,
				number: false,
				special: false
			}
		}
	},
	computed: {
		passwordMatch() {
			return this.formData.newPassword && this.formData.confirmPassword && 
				   this.formData.newPassword === this.formData.confirmPassword;
		},
		canSubmit() {
			return this.formData.oldPassword && 
				   this.formData.newPassword && 
				   this.passwordMatch &&
				   this.rules.length && 
				   this.rules.letter && 
				   this.rules.number;
		}
	},
	onLoad() {
		const systemInfo = uni.getSystemInfoSync();
		this.statusBarHeight = systemInfo.statusBarHeight || 44;
	},
	methods: {
		toggleOldPassword() {
			this.showOldPassword = !this.showOldPassword;
		},
		toggleNewPassword() {
			this.showNewPassword = !this.showNewPassword;
		},
		toggleConfirmPassword() {
			this.showConfirmPassword = !this.showConfirmPassword;
		},
		checkPasswordStrength() {
			const password = this.formData.newPassword;
			
			// 检查规则
			this.rules.length = password.length >= 8 && password.length <= 20;
			this.rules.letter = /[a-z]/.test(password) && /[A-Z]/.test(password);
			this.rules.number = /\d/.test(password);
			this.rules.special = /[!@#$%^&*(),.?":{}|<>]/.test(password);
			
			// 计算强度
			let score = 0;
			if (this.rules.length) score += 25;
			if (this.rules.letter) score += 25;
			if (this.rules.number) score += 25;
			if (this.rules.special) score += 25;
			
			if (score <= 25) {
				this.passwordStrength = { level: 'weak', width: '25%', text: '弱' };
			} else if (score <= 50) {
				this.passwordStrength = { level: 'medium', width: '50%', text: '中' };
			} else if (score <= 75) {
				this.passwordStrength = { level: 'strong', width: '75%', text: '强' };
			} else {
				this.passwordStrength = { level: 'very-strong', width: '100%', text: '很强' };
			}
		},
		changePassword() {
			if (!this.canSubmit) return;
			
			uni.showLoading({
				title: '修改中...'
			});
			
			// 模拟API调用
			setTimeout(() => {
				uni.hideLoading();
				uni.showToast({
					title: '密码修改成功',
					icon: 'success'
				});
				
				setTimeout(() => {
					uni.navigateBack();
				}, 1500);
			}, 2000);
		},
		goBack() {
			uni.navigateBack({
				fail: () => {
					uni.navigateTo({
						url: '/pages/profile/profile'
					});
				}
			});
		}
	}
}
</script>

<style scoped>
.container {
	background: #f8f9fa;
	min-height: 100vh;
}

/* 导航栏样式 */
.custom-navbar {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 1000;
	background: rgba(255, 255, 255, 0.95);
	backdrop-filter: blur(20rpx);
	border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);
}

.navbar-content {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 20rpx 32rpx;
	min-height: 88rpx;
}

.navbar-left {
	display: flex;
	align-items: center;
	gap: 12rpx;
	padding: 8rpx 16rpx 8rpx 0;
	border-radius: 20rpx;
	transition: all 0.2s ease;
}

.navbar-left:active {
	background-color: rgba(0, 0, 0, 0.04);
	transform: scale(0.96);
}

.back-text {
	font-size: 32rpx;
	color: #333;
	font-weight: 500;
}

.navbar-center {
	position: absolute;
	left: 50%;
	transform: translateX(-50%);
}

.navbar-title {
	font-size: 34rpx;
	font-weight: 600;
	color: #333;
}

.navbar-right {
	display: flex;
	align-items: center;
}

.password-form {
	background: white;
	margin: 20rpx;
	margin-top: 220rpx; /* 为导航栏留出空间 */
	border-radius: 30rpx;
	padding: 40rpx;
}

.form-header {
	text-align: center;
	margin-bottom: 40rpx;
}

.form-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin: 20rpx 0 10rpx;
}

.form-desc {
	font-size: 24rpx;
	color: #666;
	display: block;
}

.form-item {
	margin-bottom: 30rpx;
}

.form-label {
	font-size: 28rpx;
	color: #333;
	font-weight: bold;
	display: block;
	margin-bottom: 15rpx;
}

.input-wrapper {
	position: relative;
	display: flex;
	align-items: center;
}

.form-input {
	flex: 1;
	background: #f8f9fa;
	border: 2rpx solid #e0e0e0;
	border-radius: 15rpx;
	padding: 25rpx 60rpx 25rpx 20rpx;
	font-size: 28rpx;
	color: #333;
}

.form-input:focus {
	border-color: #ff8a00;
}

.toggle-btn {
	position: absolute;
	right: 20rpx;
	background: none;
	border: none;
	padding: 10rpx;
}

.password-strength {
	display: flex;
	align-items: center;
	gap: 15rpx;
	margin-top: 15rpx;
}

.strength-label {
	font-size: 24rpx;
	color: #666;
}

.strength-bar {
	flex: 1;
	height: 8rpx;
	background: #f0f0f0;
	border-radius: 4rpx;
	overflow: hidden;
}

.strength-fill {
	height: 100%;
	transition: width 0.3s ease;
}

.strength-fill.weak {
	background: #f44336;
}

.strength-fill.medium {
	background: #ff9800;
}

.strength-fill.strong {
	background: #2196f3;
}

.strength-fill.very-strong {
	background: #4caf50;
}

.strength-text {
	font-size: 24rpx;
	font-weight: bold;
}

.strength-text.weak {
	color: #f44336;
}

.strength-text.medium {
	color: #ff9800;
}

.strength-text.strong {
	color: #2196f3;
}

.strength-text.very-strong {
	color: #4caf50;
}

.password-match {
	display: flex;
	align-items: center;
	gap: 8rpx;
	margin-top: 15rpx;
}

.match-text {
	font-size: 24rpx;
}

.match-text.success {
	color: #4caf50;
}

.match-text.error {
	color: #f44336;
}

.submit-btn {
	width: 100%;
	height: 100rpx;
	background: linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%);
	color: white;
	border: none;
	border-radius: 20rpx;
	font-size: 32rpx;
	font-weight: bold;
	margin-top: 20rpx;
}

.submit-btn:disabled {
	background: #ccc;
}

.password-rules, .security-tips {
	background: white;
	margin: 20rpx;
	border-radius: 30rpx;
	padding: 40rpx;
}

.rules-header, .tips-header {
	display: flex;
	align-items: center;
	gap: 15rpx;
	margin-bottom: 30rpx;
}

.rules-title, .tips-title {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
}

.rules-list {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

.rule-item {
	display: flex;
	align-items: center;
	gap: 15rpx;
}

.rule-item.valid .rule-text {
	color: #4caf50;
}

.rule-text {
	font-size: 26rpx;
	color: #666;
}

.tips-content {
	display: flex;
	flex-direction: column;
	gap: 15rpx;
}

.tip-text {
	font-size: 26rpx;
	color: #666;
	line-height: 1.5;
}
</style>
