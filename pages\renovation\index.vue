<template>
	<view class="container">
		<!-- 头部横幅 -->
		<view class="header-banner">
			<view class="banner-content">
				<view class="banner-text">
					<text class="banner-title">适老改造</text>
					<text class="banner-subtitle">让家更安全，让生活更便利</text>
				</view>
				<view class="banner-icon">
					<Icon name="home-gear-line" size="80rpx" color="white"></Icon>
				</view>
			</view>
		</view>

		<!-- 改造类型 -->
		<view class="section">
			<view class="section-header">
				<text class="section-title">改造类型</text>
				<text class="section-subtitle">选择适合的改造方案</text>
			</view>
			<view class="type-grid">
				<view class="type-item" @click="navigateToDetail('bathroom')">
					<view class="type-icon bathroom">
						<Icon name="drop-line" size="48rpx" color="white"></Icon>
					</view>
					<text class="type-title">卫生间改造</text>
					<text class="type-desc">防滑地砖、扶手安装</text>
				</view>
				<view class="type-item" @click="navigateToDetail('kitchen')">
					<view class="type-icon kitchen">
						<Icon name="restaurant-line" size="48rpx" color="white"></Icon>
					</view>
					<text class="type-title">厨房改造</text>
					<text class="type-desc">台面降低、橱柜调整</text>
				</view>
				<view class="type-item" @click="navigateToDetail('bedroom')">
					<view class="type-icon bedroom">
						<Icon name="hotel-bed-line" size="48rpx" color="white"></Icon>
					</view>
					<text class="type-title">卧室改造</text>
					<text class="type-desc">照明改善、床具调整</text>
				</view>
				<view class="type-item" @click="navigateToDetail('living')">
					<view class="type-icon living">
						<Icon name="sofa-line" size="48rpx" color="white"></Icon>
					</view>
					<text class="type-title">客厅改造</text>
					<text class="type-desc">通道拓宽、家具调整</text>
				</view>
			</view>
		</view>

		<!-- 改造流程 -->
		<view class="section">
			<view class="section-header">
				<text class="section-title">改造流程</text>
				<text class="section-subtitle">简单四步，轻松改造</text>
			</view>
			<view class="process-list">
				<view class="process-item">
					<view class="process-number">1</view>
					<view class="process-content">
						<text class="process-title">在线申请</text>
						<text class="process-desc">填写改造需求，提交申请表</text>
					</view>
				</view>
				<view class="process-item">
					<view class="process-number">2</view>
					<view class="process-content">
						<text class="process-title">上门评估</text>
						<text class="process-desc">专业人员实地勘察评估</text>
					</view>
				</view>
				<view class="process-item">
					<view class="process-number">3</view>
					<view class="process-content">
						<text class="process-title">方案设计</text>
						<text class="process-desc">制定个性化改造方案</text>
					</view>
				</view>
				<view class="process-item">
					<view class="process-number">4</view>
					<view class="process-content">
						<text class="process-title">施工完成</text>
						<text class="process-desc">专业施工，验收交付</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 成功案例 -->
		<view class="section">
			<view class="section-header">
				<text class="section-title">成功案例</text>
				<text class="section-subtitle">真实改造效果展示</text>
			</view>
			<view class="case-list">
				<view class="case-item" v-for="(item, index) in caseList" :key="index" @click="viewCase(item)">
					<view class="case-image">
						<Icon name="image-line" size="60rpx" color="#ff8a00"></Icon>
					</view>
					<view class="case-content">
						<text class="case-title">{{item.title}}</text>
						<text class="case-desc">{{item.description}}</text>
						<view class="case-tags">
							<text class="case-tag" v-for="tag in item.tags" :key="tag">{{tag}}</text>
						</view>
					</view>
					<view class="case-arrow">
						<Icon name="arrow-right-s-line" size="24rpx" color="#999"></Icon>
					</view>
				</view>
			</view>
		</view>

		<!-- 底部操作 -->
		<view class="bottom-actions">
			<button class="action-btn secondary" @click="consultService">
				<Icon name="customer-service-2-line" size="32rpx" color="#ff8a00"></Icon>
				<text>咨询服务</text>
			</button>
			<button class="action-btn primary" @click="applyRenovation">
				<Icon name="add-line" size="32rpx" color="white"></Icon>
				<text>立即申请</text>
			</button>
		</view>
	</view>
</template>

<script>
import Icon from '@/components/Icon/Icon.vue'

export default {
	components: {
		Icon
	},
	data() {
		return {
			caseList: [
				{
					title: '王奶奶家卫生间改造',
					description: '安装扶手、防滑地砖，大大提升了安全性',
					tags: ['卫生间', '扶手', '防滑']
				},
				{
					title: '李爷爷家厨房改造',
					description: '降低台面高度，方便轮椅使用者操作',
					tags: ['厨房', '台面', '无障碍']
				},
				{
					title: '张阿姨家全屋改造',
					description: '门槛移除、通道拓宽，行动更加便利',
					tags: ['全屋', '通道', '门槛']
				}
			]
		}
	},
	methods: {
		navigateToDetail(type) {
			uni.navigateTo({
				url: `/pages/renovation/detail?type=${type}`
			});
		},
		viewCase(caseItem) {
			uni.navigateTo({
				url: `/pages/renovation/case?id=${caseItem.id}`
			});
		},
		consultService() {
			uni.makePhoneCall({
				phoneNumber: '************'
			});
		},
		applyRenovation() {
			uni.navigateTo({
				url: '/pages/renovation/apply'
			});
		}
	}
}
</script>

<style scoped>
.container {
	background: linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%);
	min-height: 100vh;
}

.header-banner {
	padding: 40rpx;
	margin-bottom: 40rpx;
}

.banner-content {
	background: rgba(255, 255, 255, 0.15);
	border-radius: 30rpx;
	padding: 40rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.banner-text {
	flex: 1;
}

.banner-title {
	font-size: 48rpx;
	font-weight: bold;
	color: white;
	display: block;
	margin-bottom: 15rpx;
}

.banner-subtitle {
	font-size: 28rpx;
	color: rgba(255, 255, 255, 0.9);
	display: block;
}

.banner-icon {
	margin-left: 30rpx;
}

.section {
	margin-bottom: 40rpx;
}

.section-header {
	padding: 0 40rpx 30rpx;
}

.section-title {
	font-size: 36rpx;
	font-weight: bold;
	color: white;
	display: block;
	margin-bottom: 10rpx;
}

.section-subtitle {
	font-size: 26rpx;
	color: rgba(255, 255, 255, 0.8);
	display: block;
}

.type-grid {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	gap: 30rpx;
	padding: 0 40rpx;
}

.type-item {
	background: white;
	border-radius: 30rpx;
	padding: 40rpx;
	text-align: center;
	box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.type-icon {
	width: 100rpx;
	height: 100rpx;
	border-radius: 20rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin: 0 auto 20rpx;
}

.type-icon.bathroom { background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%); }
.type-icon.kitchen { background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%); }
.type-icon.bedroom { background: linear-gradient(135deg, #9c27b0 0%, #7b1fa2 100%); }
.type-icon.living { background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%); }

.type-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 10rpx;
}

.type-desc {
	font-size: 24rpx;
	color: #666;
	display: block;
}

.process-list {
	padding: 0 40rpx;
}

.process-item {
	background: white;
	border-radius: 30rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	display: flex;
	align-items: center;
	box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.process-number {
	width: 60rpx;
	height: 60rpx;
	background: linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	color: white;
	font-size: 28rpx;
	font-weight: bold;
	margin-right: 30rpx;
}

.process-content {
	flex: 1;
}

.process-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 8rpx;
}

.process-desc {
	font-size: 26rpx;
	color: #666;
	display: block;
}

.case-list {
	padding: 0 40rpx;
}

.case-item {
	background: white;
	border-radius: 30rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	display: flex;
	align-items: center;
	box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.case-image {
	width: 100rpx;
	height: 100rpx;
	background: #f8f9fa;
	border-radius: 20rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 30rpx;
}

.case-content {
	flex: 1;
}

.case-title {
	font-size: 30rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 8rpx;
}

.case-desc {
	font-size: 26rpx;
	color: #666;
	display: block;
	margin-bottom: 15rpx;
}

.case-tags {
	display: flex;
	gap: 10rpx;
}

.case-tag {
	background: rgba(255, 138, 0, 0.1);
	color: #ff8a00;
	font-size: 22rpx;
	padding: 5rpx 15rpx;
	border-radius: 15rpx;
}

.case-arrow {
	margin-left: 20rpx;
}

.bottom-actions {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background: white;
	padding: 30rpx 40rpx;
	display: flex;
	gap: 20rpx;
	box-shadow: 0 -4rpx 20rpx rgba(0,0,0,0.1);
}

.action-btn {
	flex: 1;
	height: 100rpx;
	border-radius: 20rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 15rpx;
	font-size: 32rpx;
	font-weight: 500;
}

.action-btn.primary {
	background: linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%);
	color: white;
	border: none;
}

.action-btn.secondary {
	background: white;
	color: #ff8a00;
	border: 2rpx solid #ff8a00;
}
</style>
