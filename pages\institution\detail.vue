<template>
	<view class="container">
		<!-- 页面头部 -->
		<PageHeader title="机构详情" />

		<!-- 图片轮播 -->
		<swiper class="image-swiper" indicator-dots="true" autoplay="false">
			<swiper-item v-for="(image, index) in institutionDetail.images" :key="index">
				<image :src="image" mode="aspectFill" class="swiper-image" @click="previewImage(index)"></image>
			</swiper-item>
		</swiper>
		
		<!-- 基本信息 -->
		<view class="basic-info">
			<view class="info-header">
				<text class="institution-name">{{institutionDetail.name}}</text>
				<view class="rating-section">
					<text class="rating-score">{{institutionDetail.rating}}</text>
					<text class="rating-text">分</text>
					<text class="review-count">({{institutionDetail.reviewCount}}条评价)</text>
				</view>
			</view>
			<text class="institution-address">{{institutionDetail.address}}</text>
			<view class="tags-section">
				<text class="tag" v-for="(tag, index) in institutionDetail.tags" :key="index">{{tag}}</text>
			</view>
			<view class="price-section">
				<text class="price-label">价格：</text>
				<text class="price-value">¥{{institutionDetail.price}}</text>
				<text class="price-unit">/月起</text>
			</view>
		</view>
		
		<!-- 快捷操作 -->
		<view class="quick-actions">
			<view class="action-item" @click="callInstitution">
				<view class="action-icon-wrapper">
					<Icon name="phone-line" size="48rpx" primary />
				</view>
				<text class="action-text">电话咨询</text>
			</view>
			<view class="action-item" @click="showLocation">
				<view class="action-icon-wrapper">
					<Icon name="location-line" size="48rpx" primary />
				</view>
				<text class="action-text">查看位置</text>
			</view>
			<view class="action-item" @click="toggleFavorite">
				<view class="action-icon-wrapper">
					<Icon :name="isFavorite ? 'heart-fill' : 'heart-line'" size="48rpx" :color="isFavorite ? '#ff6b6b' : '#ff8a00'" />
				</view>
				<text class="action-text">{{isFavorite ? '已收藏' : '收藏'}}</text>
			</view>
			<view class="action-item" @click="shareInstitution">
				<view class="action-icon-wrapper">
					<Icon name="share-line" size="48rpx" primary />
				</view>
				<text class="action-text">分享</text>
			</view>
		</view>
		
		<!-- 详细信息 -->
		<view class="detail-sections">
			<!-- 机构介绍 -->
			<view class="detail-section">
				<view class="section-header">
					<text class="section-title">机构介绍</text>
				</view>
				<view class="section-content">
					<text class="description">{{institutionDetail.description}}</text>
				</view>
			</view>
			
			<!-- 服务设施 -->
			<view class="detail-section">
				<view class="section-header">
					<text class="section-title">服务设施</text>
				</view>
				<view class="section-content">
					<view class="facility-grid">
						<view class="facility-item" v-for="(facility, index) in institutionDetail.facilities" :key="index">
							<view class="facility-icon-wrapper">
								<Icon :name="facility.icon" size="48rpx" service />
							</view>
							<text class="facility-name">{{facility.name}}</text>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 房型介绍 -->
			<view class="detail-section">
				<view class="section-header">
					<text class="section-title">房型介绍</text>
				</view>
				<view class="section-content">
					<view class="room-list">
						<view class="room-item" v-for="(room, index) in institutionDetail.rooms" :key="index">
							<image :src="room.image" class="room-image"></image>
							<view class="room-info">
								<text class="room-name">{{room.name}}</text>
								<text class="room-desc">{{room.description}}</text>
								<view class="room-price">
									<text class="price">¥{{room.price}}/月</text>
									<text class="beds">剩余{{room.available}}张床位</text>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 用户评价 -->
			<view class="detail-section">
				<view class="section-header">
					<text class="section-title">用户评价</text>
					<text class="view-all" @click="viewAllReviews">查看全部</text>
				</view>
				<view class="section-content">
					<view class="review-list">
						<view class="review-item" v-for="(review, index) in institutionDetail.reviews" :key="index">
							<view class="review-header">
								<view class="reviewer-avatar">
									<Icon name="user-line" size="40rpx" color="#ccc" />
								</view>
								<view class="reviewer-info">
									<text class="reviewer-name">{{review.name}}</text>
									<view class="review-rating">
										<text class="rating">{{review.rating}}分</text>
										<text class="review-time">{{review.time}}</text>
									</view>
								</view>
							</view>
							<text class="review-content">{{review.content}}</text>
						</view>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 底部操作栏 -->
		<view class="bottom-actions">
			<button class="action-btn secondary" @click="callInstitution">电话咨询</button>
			<button class="action-btn primary" @click="bookVisit">预约参观</button>
		</view>
	</view>
</template>

<script>
import PageHeader from '@/components/PageHeader/PageHeader.vue'
import Icon from '@/components/Icon/Icon.vue'
import InteractionUtils from '@/utils/interactionUtils.js'

export default {
	components: {
		PageHeader,
		Icon
	},
	data() {
		return {
			institutionId: '',
			isFavorite: false,
			institutionDetail: {
				name: '阳光养老院',
				rating: 4.8,
				reviewCount: 128,
				address: '北京市朝阳区xxx街道xxx号',
				price: 3500,
				phone: '010-12345678',
				tags: ['医养结合', '环境优美', '专业护理', '24小时医护'],
				description: '阳光养老院成立于2010年，是一家集养老、护理、康复、娱乐为一体的综合性养老机构。院内环境优美，设施齐全，拥有专业的医护团队，为老年人提供全方位的照护服务。我们秉承"以人为本，关爱生命"的服务理念，致力于为每一位入住老人创造温馨、舒适的生活环境。',
				images: [
					'/picture/nursing_home_1.jpg',
					'/picture/nursing_home_2.jpg',
					'/picture/nursing_home_3.jpg',
					'/picture/nursing_home_4.jpg'
				],
				facilities: [
					{ name: '医务室', icon: 'health-book-line' },
					{ name: '康复室', icon: 'wheelchair-line' },
					{ name: '餐厅', icon: 'restaurant-line' },
					{ name: '活动室', icon: 'game-line' },
					{ name: '花园', icon: 'plant-line' },
					{ name: '图书室', icon: 'book-line' },
					{ name: '理发室', icon: 'scissors-line' },
					{ name: '洗衣房', icon: 'shirt-line' }
				],
				rooms: [
					{
						name: '单人间',
						description: '独立卫生间，24小时热水，空调，电视',
						price: 4500,
						available: 3,
						image: '/picture/nursing_home_2.jpg'
					},
					{
						name: '双人间',
						description: '独立卫生间，24小时热水，空调，电视',
						price: 3500,
						available: 8,
						image: '/picture/nursing_home_4.jpg'
					},
					{
						name: '三人间',
						description: '独立卫生间，24小时热水，空调，电视',
						price: 2800,
						available: 12,
						image: '/picture/nursing_home_5.jpg'
					}
				],
				reviews: [
					{
						name: '李女士',
						avatar: '', // 使用默认头像图标
						rating: 5.0,
						time: '2024-01-10',
						content: '环境很好，护理人员很专业，老人在这里很开心。'
					},
					{
						name: '王先生',
						avatar: '', // 使用默认头像图标
						rating: 4.5,
						time: '2024-01-08',
						content: '设施齐全，服务周到，价格合理，推荐！'
					}
				]
			}
		}
	},
	onLoad(options) {
		if (options.id) {
			this.institutionId = options.id;
			this.loadInstitutionDetail();
		}
	},
	methods: {
		loadInstitutionDetail() {
			// 模拟加载机构详情
			console.log('加载机构详情:', this.institutionId);
			// 检查是否已收藏
			this.checkFavoriteStatus();
		},
		checkFavoriteStatus() {
			// 检查收藏状态
			const favorites = uni.getStorageSync('favorites') || [];
			this.isFavorite = favorites.includes(this.institutionId);
		},
		previewImage(index) {
			uni.previewImage({
				urls: this.institutionDetail.images,
				current: index
			});
		},
		callInstitution() {
			return InteractionUtils.handlePhoneCall({
				phoneNumber: this.institutionDetail.phone,
				confirmText: `确定要拨打 ${this.institutionDetail.name} 的电话吗？\n电话：${this.institutionDetail.phone}`
			});
		},
		showLocation() {
			// 这里应该获取机构的经纬度
			uni.openLocation({
				latitude: 39.90923,
				longitude: 116.397428,
				name: this.institutionDetail.name,
				address: this.institutionDetail.address
			});
		},
		toggleFavorite() {
			return InteractionUtils.handleFavoriteToggle({
				itemId: this.institutionId,
				currentStatus: this.isFavorite,
				toggleCallback: async (itemId, newStatus) => {
					let favorites = uni.getStorageSync('favorites') || [];

					if (newStatus) {
						// 添加收藏
						favorites.push(itemId);
					} else {
						// 取消收藏
						favorites = favorites.filter(id => id !== itemId);
					}

					uni.setStorageSync('favorites', favorites);
					this.isFavorite = newStatus;
					return true;
				}
			});
		},
		shareInstitution() {
			uni.share({
				provider: 'weixin',
				scene: 'WXSceneSession',
				type: 0,
				href: `pages/institution/detail?id=${this.institutionId}`,
				title: this.institutionDetail.name,
				summary: this.institutionDetail.description,
				imageUrl: this.institutionDetail.images[0],
				success: () => {
					uni.showToast({
						title: '分享成功',
						icon: 'success'
					});
				}
			});
		},
		bookVisit() {
			return InteractionUtils.handleButtonClick({
				callback: () => {
					return Promise.resolve(false);
				},
				loadingText: '',
				successText: '',
				errorText: '预约功能开发中，请电话咨询',
				showLoading: false,
				showSuccess: false
			});
		},
		viewAllReviews() {
			return InteractionUtils.handleButtonClick({
				callback: () => {
					return Promise.resolve(false);
				},
				loadingText: '',
				successText: '',
				errorText: '评价详情功能开发中',
				showLoading: false,
				showSuccess: false
			});
		}
	}
}
</script>

<style scoped>
.container {
	background-color: #f5f5f5;
	padding-bottom: 120rpx;
}

.image-swiper {
	height: 400rpx;
}

.swiper-image {
	width: 100%;
	height: 100%;
}

.basic-info {
	background-color: #fff;
	padding: 30rpx;
	margin-bottom: 20rpx;
}

.info-header {
	display: flex;
	align-items: center;
	margin-bottom: 15rpx;
}

.institution-name {
	flex: 1;
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
}

.rating-section {
	display: flex;
	align-items: center;
}

.rating-score {
	font-size: 32rpx;
	color: #ff9500;
	font-weight: bold;
}

.rating-text {
	font-size: 24rpx;
	color: #999;
	margin-left: 5rpx;
	margin-right: 10rpx;
}

.review-count {
	font-size: 24rpx;
	color: #999;
}

.institution-address {
	font-size: 28rpx;
	color: #666;
	margin-bottom: 20rpx;
}

.tags-section {
	display: flex;
	flex-wrap: wrap;
	gap: 10rpx;
	margin-bottom: 20rpx;
}

.tag {
	padding: 8rpx 16rpx;
	background-color: #e3f2fd;
	color: #4A90E2;
	font-size: 22rpx;
	border-radius: 15rpx;
}

.price-section {
	display: flex;
	align-items: center;
}

.price-label {
	font-size: 28rpx;
	color: #666;
}

.price-value {
	font-size: 36rpx;
	color: #ff6b35;
	font-weight: bold;
	margin: 0 5rpx;
}

.price-unit {
	font-size: 24rpx;
	color: #999;
}

.quick-actions {
	background-color: #fff;
	padding: 30rpx;
	margin-bottom: 20rpx;
	display: grid;
	grid-template-columns: repeat(4, 1fr);
	gap: 30rpx;
}

.action-item {
	display: flex;
	flex-direction: column;
	align-items: center;
}

.action-icon-wrapper {
	width: 60rpx;
	height: 60rpx;
	margin-bottom: 10rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.action-text {
	font-size: 24rpx;
	color: #666;
}

.detail-sections {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

.detail-section {
	background-color: #fff;
	padding: 30rpx;
}

.section-header {
	display: flex;
	align-items: center;
	margin-bottom: 30rpx;
}

.section-title {
	flex: 1;
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.view-all {
	font-size: 28rpx;
	color: #4A90E2;
}

.description {
	font-size: 28rpx;
	color: #666;
	line-height: 1.6;
}

.facility-grid {
	display: grid;
	grid-template-columns: repeat(4, 1fr);
	gap: 30rpx;
}

.facility-item {
	display: flex;
	flex-direction: column;
	align-items: center;
}

.facility-icon-wrapper {
	width: 60rpx;
	height: 60rpx;
	margin-bottom: 10rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.facility-name {
	font-size: 24rpx;
	color: #666;
}

.room-list {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

.room-item {
	display: flex;
	background-color: #f9f9f9;
	border-radius: 15rpx;
	overflow: hidden;
}

.room-image {
	width: 200rpx;
	height: 150rpx;
}

.room-info {
	flex: 1;
	padding: 20rpx;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
}

.room-name {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 10rpx;
}

.room-desc {
	font-size: 24rpx;
	color: #666;
	margin-bottom: 15rpx;
}

.room-price {
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.price {
	font-size: 28rpx;
	color: #ff6b35;
	font-weight: bold;
}

.beds {
	font-size: 22rpx;
	color: #999;
}

.review-list {
	display: flex;
	flex-direction: column;
	gap: 30rpx;
}

.review-item {
	padding: 20rpx;
	background-color: #f9f9f9;
	border-radius: 15rpx;
}

.review-header {
	display: flex;
	align-items: center;
	margin-bottom: 15rpx;
}

.reviewer-avatar {
	width: 60rpx;
	height: 60rpx;
	border-radius: 30rpx;
	margin-right: 15rpx;
	background-color: #f5f5f5;
	display: flex;
	align-items: center;
	justify-content: center;
}

.reviewer-info {
	flex: 1;
}

.reviewer-name {
	font-size: 26rpx;
	color: #333;
	display: block;
	margin-bottom: 5rpx;
}

.review-rating {
	display: flex;
	align-items: center;
	gap: 15rpx;
}

.rating {
	font-size: 22rpx;
	color: #ff9500;
}

.review-time {
	font-size: 22rpx;
	color: #999;
}

.review-content {
	font-size: 26rpx;
	color: #666;
	line-height: 1.5;
}

.bottom-actions {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background-color: #fff;
	padding: 20rpx 30rpx;
	border-top: 1rpx solid #eee;
	display: flex;
	gap: 20rpx;
}

.action-btn {
	flex: 1;
	height: 80rpx;
	border-radius: 40rpx;
	font-size: 28rpx;
	border: none;
}

.action-btn.secondary {
	background-color: #f0f0f0;
	color: #666;
}

.action-btn.primary {
	background-color: #4A90E2;
	color: #fff;
}
</style>
