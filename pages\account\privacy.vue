<template>
	<view class="container">
		<!-- 自定义导航栏 -->
		<view class="custom-navbar">
			<view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
			<view class="navbar-content">
				<view class="navbar-left" @click="goBack">
					<Icon name="arrow-left-line" size="36rpx" color="#333"></Icon>
					<text class="back-text">返回</text>
				</view>
				<view class="navbar-center">
					<text class="navbar-title">隐私设置</text>
				</view>
				<view class="navbar-right"></view>
			</view>
		</view>

		<!-- 隐私概览 -->
		<view class="privacy-overview">
			<view class="overview-header">
				<Icon name="shield-user-line" size="60rpx" color="#4caf50"></Icon>
				<text class="overview-title">隐私保护</text>
				<text class="overview-desc">我们重视您的隐私，您可以控制个人信息的使用方式</text>
			</view>
		</view>

		<!-- 数据权限设置 -->
		<view class="privacy-section">
			<view class="section-header">
				<Icon name="database-2-line" size="32rpx" color="#ff8a00"></Icon>
				<text class="section-title">数据权限</text>
			</view>
			
			<view class="privacy-item">
				<view class="item-info">
					<text class="item-title">位置信息</text>
					<text class="item-desc">用于提供附近服务和紧急定位</text>
				</view>
				<switch :checked="permissions.location" @change="onLocationChange" color="#ff8a00" />
			</view>
			
			<view class="privacy-item">
				<view class="item-info">
					<text class="item-title">相机权限</text>
					<text class="item-desc">用于拍照上传和扫码功能</text>
				</view>
				<switch :checked="permissions.camera" @change="onCameraChange" color="#ff8a00" />
			</view>
			
			<view class="privacy-item">
				<view class="item-info">
					<text class="item-title">麦克风权限</text>
					<text class="item-desc">用于语音通话和语音输入</text>
				</view>
				<switch :checked="permissions.microphone" @change="onMicrophoneChange" color="#ff8a00" />
			</view>
			
			<view class="privacy-item">
				<view class="item-info">
					<text class="item-title">通讯录权限</text>
					<text class="item-desc">用于快速添加紧急联系人</text>
				</view>
				<switch :checked="permissions.contacts" @change="onContactsChange" color="#ff8a00" />
			</view>
		</view>

		<!-- 个人信息可见性 -->
		<view class="privacy-section">
			<view class="section-header">
				<Icon name="eye-line" size="32rpx" color="#ff8a00"></Icon>
				<text class="section-title">信息可见性</text>
			</view>
			
			<view class="privacy-item">
				<view class="item-info">
					<text class="item-title">个人资料</text>
					<text class="item-desc">其他用户是否可以查看您的基本信息</text>
				</view>
				<picker :value="visibilityIndex.profile" :range="visibilityOptions" @change="onProfileVisibilityChange">
					<view class="picker-input">
						<text>{{visibilityOptions[visibilityIndex.profile]}}</text>
						<Icon name="arrow-down-s-line" size="20rpx" color="#999"></Icon>
					</view>
				</picker>
			</view>
			
			<view class="privacy-item">
				<view class="item-info">
					<text class="item-title">在线状态</text>
					<text class="item-desc">是否显示您的在线状态</text>
				</view>
				<switch :checked="visibility.onlineStatus" @change="onOnlineStatusChange" color="#ff8a00" />
			</view>
			
			<view class="privacy-item">
				<view class="item-info">
					<text class="item-title">活动记录</text>
					<text class="item-desc">是否允许查看您的活动记录</text>
				</view>
				<picker :value="visibilityIndex.activity" :range="visibilityOptions" @change="onActivityVisibilityChange">
					<view class="picker-input">
						<text>{{visibilityOptions[visibilityIndex.activity]}}</text>
						<Icon name="arrow-down-s-line" size="20rpx" color="#999"></Icon>
					</view>
				</picker>
			</view>
		</view>

		<!-- 数据使用设置 -->
		<view class="privacy-section">
			<view class="section-header">
				<Icon name="pie-chart-line" size="32rpx" color="#ff8a00"></Icon>
				<text class="section-title">数据使用</text>
			</view>
			
			<view class="privacy-item">
				<view class="item-info">
					<text class="item-title">个性化推荐</text>
					<text class="item-desc">基于您的使用习惯推荐相关服务</text>
				</view>
				<switch :checked="dataUsage.personalization" @change="onPersonalizationChange" color="#ff8a00" />
			</view>
			
			<view class="privacy-item">
				<view class="item-info">
					<text class="item-title">数据分析</text>
					<text class="item-desc">用于改善产品和服务质量</text>
				</view>
				<switch :checked="dataUsage.analytics" @change="onAnalyticsChange" color="#ff8a00" />
			</view>
			
			<view class="privacy-item">
				<view class="item-info">
					<text class="item-title">第三方分享</text>
					<text class="item-desc">与合作伙伴分享匿名化数据</text>
				</view>
				<switch :checked="dataUsage.thirdParty" @change="onThirdPartyChange" color="#ff8a00" />
			</view>
		</view>

		<!-- 数据管理 -->
		<view class="privacy-section">
			<view class="section-header">
				<Icon name="file-shield-line" size="32rpx" color="#ff8a00"></Icon>
				<text class="section-title">数据管理</text>
			</view>
			
			<view class="privacy-item" @click="exportData">
				<view class="item-info">
					<text class="item-title">导出数据</text>
					<text class="item-desc">下载您的个人数据副本</text>
				</view>
				<Icon name="arrow-right-s-line" size="20rpx" color="#999"></Icon>
			</view>
			
			<view class="privacy-item" @click="deleteAccount">
				<view class="item-info">
					<text class="item-title">删除账户</text>
					<text class="item-desc">永久删除您的账户和所有数据</text>
				</view>
				<Icon name="arrow-right-s-line" size="20rpx" color="#999"></Icon>
			</view>
		</view>

		<!-- 隐私政策 -->
		<view class="privacy-section">
			<view class="section-header">
				<Icon name="file-text-line" size="32rpx" color="#ff8a00"></Icon>
				<text class="section-title">相关政策</text>
			</view>
			
			<view class="privacy-item" @click="viewPrivacyPolicy">
				<view class="item-info">
					<text class="item-title">隐私政策</text>
					<text class="item-desc">了解我们如何保护您的隐私</text>
				</view>
				<Icon name="arrow-right-s-line" size="20rpx" color="#999"></Icon>
			</view>
			
			<view class="privacy-item" @click="viewDataPolicy">
				<view class="item-info">
					<text class="item-title">数据使用政策</text>
					<text class="item-desc">了解数据收集和使用方式</text>
				</view>
				<Icon name="arrow-right-s-line" size="20rpx" color="#999"></Icon>
			</view>
		</view>
	</view>
</template>

<script>
import Icon from '@/components/Icon/Icon.vue'

export default {
	components: {
		Icon
	},
	data() {
		return {
			statusBarHeight: 0,
			permissions: {
				location: true,
				camera: true,
				microphone: false,
				contacts: false
			},
			visibility: {
				onlineStatus: true
			},
			visibilityOptions: ['公开', '仅朋友', '仅自己'],
			visibilityIndex: {
				profile: 1,
				activity: 2
			},
			dataUsage: {
				personalization: true,
				analytics: true,
				thirdParty: false
			}
		}
	},
	methods: {
		onLocationChange(e) {
			this.permissions.location = e.detail.value;
			this.saveSettings();
		},
		onCameraChange(e) {
			this.permissions.camera = e.detail.value;
			this.saveSettings();
		},
		onMicrophoneChange(e) {
			this.permissions.microphone = e.detail.value;
			this.saveSettings();
		},
		onContactsChange(e) {
			this.permissions.contacts = e.detail.value;
			this.saveSettings();
		},
		onProfileVisibilityChange(e) {
			this.visibilityIndex.profile = e.detail.value;
			this.saveSettings();
		},
		onOnlineStatusChange(e) {
			this.visibility.onlineStatus = e.detail.value;
			this.saveSettings();
		},
		onActivityVisibilityChange(e) {
			this.visibilityIndex.activity = e.detail.value;
			this.saveSettings();
		},
		onPersonalizationChange(e) {
			this.dataUsage.personalization = e.detail.value;
			this.saveSettings();
		},
		onAnalyticsChange(e) {
			this.dataUsage.analytics = e.detail.value;
			this.saveSettings();
		},
		onThirdPartyChange(e) {
			this.dataUsage.thirdParty = e.detail.value;
			this.saveSettings();
		},
		exportData() {
			uni.showModal({
				title: '导出数据',
				content: '我们将在24小时内将您的数据发送到注册邮箱',
				success: (res) => {
					if (res.confirm) {
						uni.showToast({
							title: '导出请求已提交',
							icon: 'success'
						});
					}
				}
			});
		},
		deleteAccount() {
			uni.showModal({
				title: '删除账户',
				content: '此操作不可恢复，确定要删除账户吗？',
				confirmColor: '#f44336',
				success: (res) => {
					if (res.confirm) {
						uni.navigateTo({
							url: '/pages/account/delete-confirm'
						});
					}
				}
			});
		},
		viewPrivacyPolicy() {
			uni.navigateTo({
				url: '/pages/legal/privacy-policy'
			});
		},
		viewDataPolicy() {
			uni.navigateTo({
				url: '/pages/legal/data-policy'
			});
		},
		saveSettings() {
			uni.setStorageSync('privacySettings', {
				permissions: this.permissions,
				visibility: this.visibility,
				visibilityIndex: this.visibilityIndex,
				dataUsage: this.dataUsage
			});

			uni.showToast({
				title: '设置已保存',
				icon: 'success'
			});
		},
		goBack() {
			uni.navigateBack({
				fail: () => {
					uni.navigateTo({
						url: '/pages/profile/profile'
					});
				}
			});
		}
	},
	onLoad() {
		const systemInfo = uni.getSystemInfoSync();
		this.statusBarHeight = systemInfo.statusBarHeight || 44;

		const savedSettings = uni.getStorageSync('privacySettings');
		if (savedSettings) {
			this.permissions = { ...this.permissions, ...savedSettings.permissions };
			this.visibility = { ...this.visibility, ...savedSettings.visibility };
			this.visibilityIndex = { ...this.visibilityIndex, ...savedSettings.visibilityIndex };
			this.dataUsage = { ...this.dataUsage, ...savedSettings.dataUsage };
		}
	}
}
</script>

<style scoped>
.container {
	background: #f8f9fa;
	min-height: 100vh;
}

/* 导航栏样式 */
.custom-navbar {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 1000;
	background: rgba(255, 255, 255, 0.95);
	backdrop-filter: blur(20rpx);
	border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);
}

.navbar-content {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 20rpx 32rpx;
	min-height: 88rpx;
}

.navbar-left {
	display: flex;
	align-items: center;
	gap: 12rpx;
	padding: 8rpx 16rpx 8rpx 0;
	border-radius: 20rpx;
	transition: all 0.2s ease;
}

.navbar-left:active {
	background-color: rgba(0, 0, 0, 0.04);
	transform: scale(0.96);
}

.back-text {
	font-size: 32rpx;
	color: #333;
	font-weight: 500;
}

.navbar-center {
	position: absolute;
	left: 50%;
	transform: translateX(-50%);
}

.navbar-title {
	font-size: 34rpx;
	font-weight: 600;
	color: #333;
}

.navbar-right {
	display: flex;
	align-items: center;
}

.privacy-overview {
	background: linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%);
	padding: 40rpx;
	margin-top: 220rpx; /* 为导航栏留出空间 */
	text-align: center;
	color: white;
}

.overview-title {
	font-size: 32rpx;
	font-weight: bold;
	display: block;
	margin: 20rpx 0 15rpx;
}

.overview-desc {
	font-size: 24rpx;
	opacity: 0.9;
	line-height: 1.5;
	display: block;
}

.privacy-section {
	background: white;
	margin: 20rpx;
	border-radius: 30rpx;
	padding: 40rpx;
}

.section-header {
	display: flex;
	align-items: center;
	gap: 15rpx;
	margin-bottom: 30rpx;
}

.section-title {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
}

.privacy-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 25rpx 0;
	border-bottom: 1rpx solid #f0f0f0;
}

.privacy-item:last-child {
	border-bottom: none;
}

.item-info {
	flex: 1;
	margin-right: 20rpx;
}

.item-title {
	font-size: 28rpx;
	color: #333;
	font-weight: bold;
	display: block;
	margin-bottom: 8rpx;
}

.item-desc {
	font-size: 24rpx;
	color: #666;
	line-height: 1.4;
	display: block;
}

.picker-input {
	display: flex;
	align-items: center;
	gap: 10rpx;
	padding: 15rpx 20rpx;
	background: #f8f9fa;
	border-radius: 15rpx;
	font-size: 26rpx;
	color: #333;
}
</style>
