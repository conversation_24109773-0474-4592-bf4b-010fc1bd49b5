<template>
	<view class="container">
		<!-- 消息分类 -->
		<view class="category-section">
			<scroll-view scroll-x="true" class="category-scroll">
				<view class="category-list">
					<view 
						class="category-item" 
						:class="{ active: activeCategory === item.key }"
						v-for="(item, index) in categoryList" 
						:key="index"
						@click="selectCategory(item.key)"
					>
						<Icon :name="item.icon" size="32rpx" :color="activeCategory === item.key ? 'white' : '#666'"></Icon>
						<text class="category-text">{{item.name}}</text>
						<view class="category-badge" v-if="item.count > 0">{{item.count}}</view>
					</view>
				</view>
			</scroll-view>
		</view>

		<!-- 消息列表 -->
		<view class="message-list">
			<view class="message-item" v-for="(message, index) in filteredMessages" :key="index" @click="viewMessage(message)">
				<view class="message-icon" :class="message.type">
					<Icon :name="getMessageIcon(message.type)" size="32rpx" color="white"></Icon>
				</view>
				<view class="message-content">
					<view class="message-header">
						<text class="message-title">{{message.title}}</text>
						<text class="message-time">{{formatTime(message.time)}}</text>
					</view>
					<text class="message-desc">{{message.content}}</text>
					<view class="message-meta" v-if="message.meta">
						<text class="meta-text">{{message.meta}}</text>
					</view>
				</view>
				<view class="message-status">
					<view class="unread-dot" v-if="!message.isRead"></view>
					<Icon name="arrow-right-s-line" size="20rpx" color="#999"></Icon>
				</view>
			</view>
		</view>

		<!-- 空状态 -->
		<view class="empty-state" v-if="filteredMessages.length === 0">
			<Icon name="mail-line" size="120rpx" color="#ccc"></Icon>
			<text class="empty-text">暂无消息</text>
			<text class="empty-desc">您的消息通知将在这里显示</text>
		</view>

		<!-- 底部操作 -->
		<view class="bottom-actions">
			<button class="action-btn create" @click="showCreateMessage">
				<Icon name="add-line" size="32rpx" color="white"></Icon>
				<text>发送消息</text>
			</button>
			<button class="action-btn secondary" @click="markAllRead">
				<Icon name="check-line" size="32rpx" color="#ff8a00"></Icon>
				<text>全部已读</text>
			</button>
			<button class="action-btn primary" @click="clearMessages">
				<Icon name="delete-line" size="32rpx" color="white"></Icon>
				<text>清空消息</text>
			</button>
		</view>

		<!-- 创建消息弹窗 -->
		<uni-popup ref="createPopup" type="bottom" :mask-click="false">
			<view class="create-popup">
				<view class="popup-header">
					<text class="popup-title">发送消息</text>
					<button class="close-btn" @click="hideCreateMessage">
						<Icon name="close-line" size="32rpx" color="#666"></Icon>
					</button>
				</view>
				<view class="popup-content">
					<view class="form-group">
						<text class="form-label">消息类型</text>
						<picker :value="messageForm.typeIndex" :range="messageTypes" range-key="name" @change="onTypeChange">
							<view class="picker-input">
								<text>{{ messageTypes[messageForm.typeIndex].name }}</text>
								<Icon name="arrow-down-s-line" size="24rpx" color="#999"></Icon>
							</view>
						</picker>
					</view>
					<view class="form-group">
						<text class="form-label">消息标题</text>
						<input class="form-input" v-model="messageForm.title" placeholder="请输入消息标题" />
					</view>
					<view class="form-group">
						<text class="form-label">消息内容</text>
						<textarea class="form-textarea" v-model="messageForm.content" placeholder="请输入消息内容" />
					</view>
				</view>
				<view class="popup-actions">
					<button class="action-btn cancel" @click="hideCreateMessage">取消</button>
					<button class="action-btn confirm" @click="createMessage" :disabled="!canSubmit">发送</button>
				</view>
			</view>
		</uni-popup>
	</view>
</template>

<script>
import Icon from '@/components/Icon/Icon.vue'
import crudAPI from '@/utils/crudAPI.js'
import FeedbackUtils from '@/utils/feedback.js'

export default {
	components: {
		Icon
	},
	data() {
		return {
			activeCategory: 'all',
			categoryList: [
				{ key: 'all', name: '全部', icon: 'mail-line', count: 0 },
				{ key: 'system', name: '系统', icon: 'settings-line', count: 0 },
				{ key: 'service', name: '服务', icon: 'heart-line', count: 0 },
				{ key: 'health', name: '健康', icon: 'health-book-line', count: 0 },
				{ key: 'activity', name: '活动', icon: 'calendar-line', count: 0 }
			],
			messageList: [],
			loading: false,
			refreshing: false,
			messageForm: {
				typeIndex: 0,
				title: '',
				content: ''
			},
			messageTypes: [
				{ key: 'system', name: '系统消息' },
				{ key: 'service', name: '服务消息' },
				{ key: 'health', name: '健康消息' },
				{ key: 'activity', name: '活动消息' }
			],
			submitting: false
		}
	},
	computed: {
		filteredMessages() {
			if (this.activeCategory === 'all') {
				return this.messageList;
			}
			return this.messageList.filter(message => message.type === this.activeCategory);
		},
		canSubmit() {
			return this.messageForm.title.trim() && this.messageForm.content.trim() && !this.submitting;
		}
	},
	onLoad() {
		this.loadMessages();
	},
	methods: {
		// 加载消息数据
		async loadMessages() {
			try {
				this.loading = true;

				const params = {
					page: 1,
					pageSize: 50
				};

				const result = await crudAPI.getMessages(params);

				if (result.success) {
					this.messageList = result.data.list;
					this.updateCategoryCounts();

					if (result.data.list.length > 0) {
						FeedbackUtils.showSuccess(`已加载 ${result.data.list.length} 条消息`);
					}
				} else {
					FeedbackUtils.showError(result.message || '数据加载失败，请重试');
					this.messageList = [];
				}
			} catch (error) {
				console.error('加载消息失败:', error);
				FeedbackUtils.showError('数据加载失败，请重试');
				this.messageList = [];
			} finally {
				this.loading = false;
				this.refreshing = false;
			}
		},

		selectCategory(category) {
			this.activeCategory = category;
		},
		updateCategoryCounts() {
			this.categoryList.forEach(category => {
				if (category.key === 'all') {
					category.count = this.messageList.filter(msg => !msg.isRead).length;
				} else {
					category.count = this.messageList.filter(msg => msg.type === category.key && !msg.isRead).length;
				}
			});
		},
		getMessageIcon(type) {
			const iconMap = {
				'system': 'settings-line',
				'service': 'heart-line',
				'health': 'health-book-line',
				'activity': 'calendar-line'
			};
			return iconMap[type] || 'mail-line';
		},
		formatTime(time) {
			const now = new Date();
			const msgTime = new Date(time);
			const diff = now - msgTime;
			const days = Math.floor(diff / (1000 * 60 * 60 * 24));
			
			if (days === 0) {
				return time.split(' ')[1];
			} else if (days === 1) {
				return '昨天';
			} else if (days < 7) {
				return `${days}天前`;
			} else {
				return time.split(' ')[0];
			}
		},
		async viewMessage(message) {
			// 标记为已读
			if (!message.isRead) {
				try {
					const result = await crudAPI.markMessageAsRead(message.id);
					if (result.success) {
						message.isRead = true;
						this.updateCategoryCounts();
					}
				} catch (error) {
					console.error('标记消息已读失败:', error);
				}
			}

			uni.navigateTo({
				url: `/pages/message/detail?id=${message.id}`
			});
		},
		async markAllRead() {
			try {
				await FeedbackUtils.showConfirm({
					title: '确认操作',
					content: '确定要将所有消息标记为已读吗？',
					confirmText: '确定',
					cancelText: '取消'
				});

				FeedbackUtils.showLoading('处理中...');

				const result = await crudAPI.markAllMessagesAsRead();

				FeedbackUtils.hideLoading();

				if (result.success) {
					this.messageList.forEach(message => {
						message.isRead = true;
					});
					this.updateCategoryCounts();
					FeedbackUtils.showSuccess('已全部标记为已读');
				} else {
					FeedbackUtils.showError(result.message || '操作失败');
				}
			} catch (error) {
				FeedbackUtils.hideLoading();
				console.log('用户取消操作');
			}
		},
		async clearMessages() {
			try {
				await FeedbackUtils.showConfirm({
					title: '确认清空',
					content: '确定要清空所有消息吗？此操作不可恢复。',
					confirmText: '清空',
					cancelText: '取消'
				});

				FeedbackUtils.showLoading('清空中...');

				// 删除所有消息
				const deletePromises = this.messageList.map(message =>
					crudAPI.deleteMessage(message.id)
				);

				await Promise.all(deletePromises);

				FeedbackUtils.hideLoading();

				this.messageList = [];
				this.updateCategoryCounts();
				FeedbackUtils.showSuccess('消息已清空');
			} catch (error) {
				FeedbackUtils.hideLoading();
				console.log('用户取消操作');
			}
		},

		// 显示创建消息弹窗
		showCreateMessage() {
			this.messageForm = {
				typeIndex: 0,
				title: '',
				content: ''
			};
			this.$refs.createPopup.open();
		},

		// 隐藏创建消息弹窗
		hideCreateMessage() {
			this.$refs.createPopup.close();
		},

		// 消息类型选择
		onTypeChange(e) {
			this.messageForm.typeIndex = e.detail.value;
		},

		// 创建消息
		async createMessage() {
			if (!this.canSubmit) return;

			try {
				this.submitting = true;
				FeedbackUtils.showLoading('发送中...');

				const messageData = {
					type: this.messageTypes[this.messageForm.typeIndex].key,
					title: this.messageForm.title.trim(),
					content: this.messageForm.content.trim(),
					meta: '用户发送'
				};

				const result = await crudAPI.createMessage(messageData);

				FeedbackUtils.hideLoading();

				if (result.success) {
					FeedbackUtils.showSuccess('消息发送成功');
					this.hideCreateMessage();
					this.loadMessages(); // 重新加载消息列表
				} else {
					FeedbackUtils.showError(result.message || '发送失败');
				}
			} catch (error) {
				FeedbackUtils.hideLoading();
				console.error('创建消息失败:', error);
				FeedbackUtils.showError('发送失败，请重试');
			} finally {
				this.submitting = false;
			}
		}
	}
}
</script>

<style scoped>
.container {
	background: #f8f9fa;
	min-height: 100vh;
	padding-bottom: 200rpx;
}

.category-section {
	background: white;
	padding: 30rpx 0;
	border-bottom: 1rpx solid #f0f0f0;
}

.category-scroll {
	white-space: nowrap;
}

.category-list {
	display: inline-flex;
	gap: 20rpx;
	padding: 0 40rpx;
}

.category-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 20rpx 30rpx;
	border-radius: 25rpx;
	background: #f8f9fa;
	white-space: nowrap;
	position: relative;
	min-width: 120rpx;
}

.category-item.active {
	background: linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%);
}

.category-text {
	font-size: 24rpx;
	color: #666;
	margin-top: 8rpx;
}

.category-item.active .category-text {
	color: white;
}

.category-badge {
	position: absolute;
	top: 10rpx;
	right: 10rpx;
	background: #f44336;
	color: white;
	font-size: 20rpx;
	padding: 4rpx 8rpx;
	border-radius: 10rpx;
	min-width: 20rpx;
	text-align: center;
}

.message-list {
	padding: 40rpx;
}

.message-item {
	background: white;
	border-radius: 30rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	display: flex;
	align-items: flex-start;
	gap: 20rpx;
	box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.message-icon {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-top: 5rpx;
}

.message-icon.system {
	background: linear-gradient(135deg, #9c27b0 0%, #7b1fa2 100%);
}

.message-icon.service {
	background: linear-gradient(135deg, #e91e63 0%, #c2185b 100%);
}

.message-icon.health {
	background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);
}

.message-icon.activity {
	background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
}

.message-content {
	flex: 1;
}

.message-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 10rpx;
}

.message-title {
	font-size: 30rpx;
	font-weight: bold;
	color: #333;
}

.message-time {
	font-size: 22rpx;
	color: #999;
}

.message-desc {
	font-size: 26rpx;
	color: #666;
	line-height: 1.5;
	display: block;
	margin-bottom: 15rpx;
}

.message-meta {
	margin-top: 10rpx;
}

.meta-text {
	font-size: 22rpx;
	color: #ff8a00;
	background: rgba(255, 138, 0, 0.1);
	padding: 5rpx 15rpx;
	border-radius: 15rpx;
}

.message-status {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 10rpx;
	margin-top: 5rpx;
}

.unread-dot {
	width: 12rpx;
	height: 12rpx;
	background: #f44336;
	border-radius: 50%;
}

.empty-state {
	text-align: center;
	padding: 100rpx 40rpx;
}

.empty-text {
	font-size: 32rpx;
	color: #999;
	display: block;
	margin: 30rpx 0 15rpx;
}

.empty-desc {
	font-size: 26rpx;
	color: #ccc;
	display: block;
}

.bottom-actions {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background: white;
	padding: 30rpx 40rpx;
	display: flex;
	gap: 20rpx;
	box-shadow: 0 -4rpx 20rpx rgba(0,0,0,0.1);
}

.action-btn {
	flex: 1;
	height: 100rpx;
	border-radius: 20rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 15rpx;
	font-size: 32rpx;
	font-weight: 500;
}

.action-btn.primary {
	background: linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%);
	color: white;
	border: none;
}

.action-btn.secondary {
	background: white;
	color: #ff8a00;
	border: 2rpx solid #ff8a00;
}

.action-btn.create {
	background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);
	color: white;
	border: none;
}

/* 创建消息弹窗样式 */
.create-popup {
	background: white;
	border-radius: 40rpx 40rpx 0 0;
	padding: 40rpx;
	max-height: 80vh;
}

.popup-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 40rpx;
}

.popup-title {
	font-size: 36rpx;
	font-weight: 600;
	color: #333;
}

.close-btn {
	width: 60rpx;
	height: 60rpx;
	border-radius: 50%;
	background: #f5f5f5;
	border: none;
	display: flex;
	align-items: center;
	justify-content: center;
}

.popup-content {
	margin-bottom: 40rpx;
}

.form-group {
	margin-bottom: 30rpx;
}

.form-label {
	display: block;
	font-size: 28rpx;
	color: #333;
	margin-bottom: 15rpx;
	font-weight: 500;
}

.picker-input {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20rpx 30rpx;
	background: #f8f9fa;
	border-radius: 20rpx;
	font-size: 28rpx;
	color: #333;
}

.form-input {
	width: 100%;
	padding: 20rpx 30rpx;
	background: #f8f9fa;
	border-radius: 20rpx;
	border: none;
	font-size: 28rpx;
	color: #333;
}

.form-textarea {
	width: 100%;
	min-height: 200rpx;
	padding: 20rpx 30rpx;
	background: #f8f9fa;
	border-radius: 20rpx;
	border: none;
	font-size: 28rpx;
	color: #333;
	resize: none;
}

.popup-actions {
	display: flex;
	gap: 20rpx;
}

.popup-actions .action-btn {
	flex: 1;
	height: 100rpx;
	border-radius: 20rpx;
	font-size: 32rpx;
	font-weight: 500;
	border: none;
}

.popup-actions .action-btn.cancel {
	background: #f5f5f5;
	color: #666;
}

.popup-actions .action-btn.confirm {
	background: linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%);
	color: white;
}

.popup-actions .action-btn:disabled {
	background: #ccc;
	color: #999;
}
</style>
