<template>
	<view class="container">
		<!-- 日历头部 -->
		<view class="calendar-header">
			<view class="header-controls">
				<button class="nav-btn" @click="previousMonth">
					<Icon name="arrow-left-s-line" size="32rpx" color="#ff8a00"></Icon>
				</button>
				<text class="current-month">{{currentMonthText}}</text>
				<button class="nav-btn" @click="nextMonth">
					<Icon name="arrow-right-s-line" size="32rpx" color="#ff8a00"></Icon>
				</button>
			</view>
			<button class="today-btn" @click="goToday">今天</button>
		</view>

		<!-- 星期标题 -->
		<view class="week-header">
			<view class="week-day" v-for="day in weekDays" :key="day">{{day}}</view>
		</view>

		<!-- 日历网格 -->
		<view class="calendar-grid">
			<view 
				class="calendar-day" 
				:class="{ 
					'other-month': !day.isCurrentMonth,
					'today': day.isToday,
					'selected': day.isSelected,
					'has-schedule': day.hasSchedule
				}"
				v-for="day in calendarDays" 
				:key="day.date"
				@click="selectDay(day)"
			>
				<text class="day-number">{{day.day}}</text>
				<view class="schedule-dots" v-if="day.schedules && day.schedules.length > 0">
					<view 
						class="schedule-dot" 
						:style="{backgroundColor: schedule.color}"
						v-for="(schedule, index) in day.schedules.slice(0, 3)" 
						:key="index"
					></view>
				</view>
			</view>
		</view>

		<!-- 选中日期的日程 -->
		<view class="selected-day-schedules" v-if="selectedDay">
			<view class="schedules-header">
				<text class="schedules-title">{{selectedDay.date}} 的日程</text>
				<button class="add-schedule-btn" @click="addSchedule">
					<Icon name="add-line" size="24rpx" color="#ff8a00"></Icon>
					<text>添加</text>
				</button>
			</view>
			<view class="schedules-list">
				<view class="schedule-item" v-for="(schedule, index) in selectedDay.schedules" :key="index" @click="viewSchedule(schedule)">
					<view class="schedule-time">{{schedule.time}}</view>
					<view class="schedule-content">
						<text class="schedule-title">{{schedule.title}}</text>
						<text class="schedule-location" v-if="schedule.location">{{schedule.location}}</text>
					</view>
					<view class="schedule-type" :style="{backgroundColor: schedule.color}">
						<text class="type-text">{{schedule.type}}</text>
					</view>
				</view>
			</view>
			<view class="no-schedules" v-if="!selectedDay.schedules || selectedDay.schedules.length === 0">
				<Icon name="calendar-line" size="80rpx" color="#ccc"></Icon>
				<text class="no-schedules-text">暂无日程安排</text>
			</view>
		</view>

		<!-- 快速操作 -->
		<view class="quick-actions">
			<button class="quick-btn" @click="viewTodaySchedules">
				<Icon name="time-line" size="32rpx" color="#4caf50"></Icon>
				<text>今日日程</text>
			</button>
			<button class="quick-btn" @click="viewWeekSchedules">
				<Icon name="calendar-2-line" size="32rpx" color="#2196f3"></Icon>
				<text>本周日程</text>
			</button>
			<button class="quick-btn" @click="createSchedule">
				<Icon name="add-line" size="32rpx" color="#ff8a00"></Icon>
				<text>新建日程</text>
			</button>
		</view>
	</view>
</template>

<script>
import Icon from '@/components/Icon/Icon.vue'

export default {
	components: {
		Icon
	},
	data() {
		return {
			currentDate: new Date(),
			selectedDay: null,
			weekDays: ['日', '一', '二', '三', '四', '五', '六'],
			scheduleData: {
				'2024-01-18': [
					{
						id: 1,
						time: '09:00',
						title: '晨练',
						location: '社区公园',
						type: '运动',
						color: '#4caf50'
					},
					{
						id: 2,
						time: '14:30',
						title: '医生问诊',
						location: '社区医院',
						type: '医疗',
						color: '#f44336'
					}
				],
				'2024-01-19': [
					{
						id: 3,
						time: '10:00',
						title: '书法课',
						location: '文化中心',
						type: '学习',
						color: '#9c27b0'
					}
				],
				'2024-01-20': [
					{
						id: 4,
						time: '15:00',
						title: '社交聚会',
						location: '活动中心',
						type: '社交',
						color: '#ff9800'
					}
				]
			}
		}
	},
	computed: {
		currentMonthText() {
			const year = this.currentDate.getFullYear();
			const month = this.currentDate.getMonth() + 1;
			return `${year}年${month}月`;
		},
		calendarDays() {
			const year = this.currentDate.getFullYear();
			const month = this.currentDate.getMonth();
			const today = new Date();
			
			// 获取当月第一天和最后一天
			const firstDay = new Date(year, month, 1);
			const lastDay = new Date(year, month + 1, 0);
			
			// 获取第一天是星期几
			const firstDayWeek = firstDay.getDay();
			
			// 生成日历数组
			const days = [];
			
			// 添加上个月的日期
			for (let i = firstDayWeek - 1; i >= 0; i--) {
				const date = new Date(year, month, -i);
				days.push(this.createDayObject(date, false));
			}
			
			// 添加当月的日期
			for (let i = 1; i <= lastDay.getDate(); i++) {
				const date = new Date(year, month, i);
				days.push(this.createDayObject(date, true));
			}
			
			// 添加下个月的日期，补齐6行
			const remainingDays = 42 - days.length;
			for (let i = 1; i <= remainingDays; i++) {
				const date = new Date(year, month + 1, i);
				days.push(this.createDayObject(date, false));
			}
			
			return days;
		}
	},
	onLoad() {
		this.selectToday();
	},
	methods: {
		createDayObject(date, isCurrentMonth) {
			const dateStr = this.formatDate(date);
			const today = new Date();
			const isToday = dateStr === this.formatDate(today);
			const schedules = this.scheduleData[dateStr] || [];
			
			return {
				date: dateStr,
				day: date.getDate(),
				isCurrentMonth,
				isToday,
				isSelected: this.selectedDay && this.selectedDay.date === dateStr,
				hasSchedule: schedules.length > 0,
				schedules
			};
		},
		formatDate(date) {
			const year = date.getFullYear();
			const month = String(date.getMonth() + 1).padStart(2, '0');
			const day = String(date.getDate()).padStart(2, '0');
			return `${year}-${month}-${day}`;
		},
		selectDay(day) {
			this.selectedDay = day;
		},
		selectToday() {
			const today = new Date();
			const todayStr = this.formatDate(today);
			const todaySchedules = this.scheduleData[todayStr] || [];
			this.selectedDay = {
				date: todayStr,
				day: today.getDate(),
				schedules: todaySchedules
			};
		},
		previousMonth() {
			this.currentDate = new Date(this.currentDate.getFullYear(), this.currentDate.getMonth() - 1, 1);
		},
		nextMonth() {
			this.currentDate = new Date(this.currentDate.getFullYear(), this.currentDate.getMonth() + 1, 1);
		},
		goToday() {
			this.currentDate = new Date();
			this.selectToday();
		},
		addSchedule() {
			if (this.selectedDay) {
				uni.navigateTo({
					url: `/pages/schedule/create?date=${this.selectedDay.date}`
				});
			}
		},
		viewSchedule(schedule) {
			uni.navigateTo({
				url: `/pages/schedule/detail?id=${schedule.id}`
			});
		},
		createSchedule() {
			uni.navigateTo({
				url: '/pages/schedule/create'
			});
		},
		viewTodaySchedules() {
			this.selectToday();
		},
		viewWeekSchedules() {
			uni.navigateTo({
				url: '/pages/schedule/week'
			});
		}
	}
}
</script>

<style scoped>
.container {
	background: #f8f9fa;
	min-height: 100vh;
}

.calendar-header {
	background: linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%);
	padding: 40rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.header-controls {
	display: flex;
	align-items: center;
	gap: 30rpx;
}

.nav-btn {
	width: 60rpx;
	height: 60rpx;
	background: rgba(255, 255, 255, 0.2);
	border: none;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
}

.current-month {
	font-size: 36rpx;
	font-weight: bold;
	color: white;
}

.today-btn {
	background: rgba(255, 255, 255, 0.2);
	color: white;
	border: none;
	padding: 15rpx 30rpx;
	border-radius: 25rpx;
	font-size: 26rpx;
}

.week-header {
	background: white;
	display: grid;
	grid-template-columns: repeat(7, 1fr);
	padding: 20rpx 0;
	border-bottom: 1rpx solid #f0f0f0;
}

.week-day {
	text-align: center;
	font-size: 26rpx;
	color: #666;
	font-weight: 500;
}

.calendar-grid {
	background: white;
	display: grid;
	grid-template-columns: repeat(7, 1fr);
	gap: 1rpx;
	background-color: #f0f0f0;
}

.calendar-day {
	aspect-ratio: 1;
	background: white;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	position: relative;
	padding: 10rpx;
}

.calendar-day.other-month {
	color: #ccc;
	background: #fafafa;
}

.calendar-day.today {
	background: rgba(255, 138, 0, 0.1);
}

.calendar-day.selected {
	background: linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%);
}

.calendar-day.selected .day-number {
	color: white;
}

.calendar-day.has-schedule {
	background: rgba(76, 175, 80, 0.05);
}

.day-number {
	font-size: 28rpx;
	color: #333;
	margin-bottom: 5rpx;
}

.schedule-dots {
	display: flex;
	gap: 4rpx;
}

.schedule-dot {
	width: 8rpx;
	height: 8rpx;
	border-radius: 50%;
}

.selected-day-schedules {
	background: white;
	margin: 40rpx;
	border-radius: 30rpx;
	padding: 30rpx;
	box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.schedules-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 30rpx;
}

.schedules-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.add-schedule-btn {
	background: white;
	color: #ff8a00;
	border: 2rpx solid #ff8a00;
	padding: 10rpx 20rpx;
	border-radius: 20rpx;
	font-size: 24rpx;
	display: flex;
	align-items: center;
	gap: 8rpx;
}

.schedules-list {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

.schedule-item {
	display: flex;
	align-items: center;
	gap: 20rpx;
	padding: 20rpx;
	background: #f8f9fa;
	border-radius: 20rpx;
}

.schedule-time {
	font-size: 24rpx;
	color: #ff8a00;
	font-weight: bold;
	min-width: 100rpx;
}

.schedule-content {
	flex: 1;
}

.schedule-title {
	font-size: 28rpx;
	color: #333;
	display: block;
	margin-bottom: 5rpx;
}

.schedule-location {
	font-size: 24rpx;
	color: #666;
	display: block;
}

.schedule-type {
	padding: 8rpx 15rpx;
	border-radius: 15rpx;
	font-size: 22rpx;
}

.type-text {
	color: white;
}

.no-schedules {
	text-align: center;
	padding: 60rpx 0;
}

.no-schedules-text {
	font-size: 28rpx;
	color: #999;
	display: block;
	margin-top: 20rpx;
}

.quick-actions {
	display: flex;
	gap: 20rpx;
	padding: 40rpx;
}

.quick-btn {
	flex: 1;
	background: white;
	border: none;
	padding: 30rpx 20rpx;
	border-radius: 20rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 15rpx;
	box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
	font-size: 24rpx;
	color: #333;
}
</style>
