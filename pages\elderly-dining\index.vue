<template>
	<view class="container">
		<!-- 头部横幅 -->
		<view class="header-banner">
			<view class="banner-content">
				<view class="banner-text">
					<text class="banner-title">长者食堂</text>
					<text class="banner-subtitle">营养美味，温暖如家</text>
				</view>
				<view class="banner-icon">
					<Icon name="restaurant-line" size="80rpx" color="white"></Icon>
				</view>
			</view>
		</view>

		<!-- 今日菜单 -->
		<view class="section">
			<view class="section-header">
				<text class="section-title">今日菜单</text>
				<text class="section-subtitle">{{currentDate}} 精心搭配的营养餐</text>
			</view>
			<view class="menu-card">
				<view class="meal-section" v-for="(meal, index) in todayMenu" :key="index">
					<view class="meal-header">
						<Icon :name="meal.icon" size="32rpx" :color="meal.color"></Icon>
						<text class="meal-title">{{meal.name}}</text>
						<text class="meal-price">¥{{meal.price}}</text>
					</view>
					<view class="dish-list">
						<text class="dish-item" v-for="dish in meal.dishes" :key="dish">{{dish}}</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 附近食堂 -->
		<view class="section">
			<view class="section-header">
				<text class="section-title">附近食堂</text>
				<text class="section-subtitle">选择就近的长者食堂</text>
			</view>
			<view class="dining-list">
				<view class="dining-item" v-for="(item, index) in diningList" :key="index" @click="viewDining(item)">
					<view class="dining-image">
						<Icon name="restaurant-line" size="60rpx" color="#ffc107"></Icon>
					</view>
					<view class="dining-content">
						<text class="dining-name">{{item.name}}</text>
						<text class="dining-address">{{item.address}}</text>
						<view class="dining-features">
							<text class="feature-tag" v-for="feature in item.features" :key="feature">{{feature}}</text>
						</view>
						<view class="dining-meta">
							<view class="distance">
								<Icon name="map-pin-line" size="24rpx" color="#999"></Icon>
								<text class="distance-text">{{item.distance}}</text>
							</view>
							<view class="rating">
								<Icon name="star-fill" size="24rpx" color="#ffc107"></Icon>
								<text class="rating-text">{{item.rating}}</text>
							</view>
							<view class="price-range">
								<text class="price-text">¥{{item.priceRange}}</text>
							</view>
						</view>
					</view>
					<view class="dining-action">
						<button class="order-btn" @click.stop="orderMeal(item)">订餐</button>
					</view>
				</view>
			</view>
		</view>

		<!-- 服务特色 -->
		<view class="section">
			<view class="section-header">
				<text class="section-title">服务特色</text>
				<text class="section-subtitle">专为老年人设计的贴心服务</text>
			</view>
			<view class="feature-grid">
				<view class="feature-item">
					<view class="feature-icon nutrition">
						<Icon name="heart-3-line" size="48rpx" color="white"></Icon>
					</view>
					<text class="feature-title">营养搭配</text>
					<text class="feature-desc">专业营养师配餐</text>
				</view>
				<view class="feature-item">
					<view class="feature-icon soft">
						<Icon name="emotion-happy-line" size="48rpx" color="white"></Icon>
					</view>
					<text class="feature-title">软烂易嚼</text>
					<text class="feature-desc">适合老年人咀嚼</text>
				</view>
				<view class="feature-item">
					<view class="feature-icon delivery">
						<Icon name="truck-line" size="48rpx" color="white"></Icon>
					</view>
					<text class="feature-title">送餐到家</text>
					<text class="feature-desc">行动不便可配送</text>
				</view>
				<view class="feature-item">
					<view class="feature-icon affordable">
						<Icon name="money-cny-circle-line" size="48rpx" color="white"></Icon>
					</view>
					<text class="feature-title">价格优惠</text>
					<text class="feature-desc">政府补贴惠民价</text>
				</view>
			</view>
		</view>

		<!-- 订餐记录 -->
		<view class="section" v-if="orderList.length > 0">
			<view class="section-header">
				<text class="section-title">我的订餐</text>
				<text class="section-subtitle">查看订餐记录</text>
			</view>
			<view class="order-list">
				<view class="order-item" v-for="(item, index) in orderList" :key="index">
					<view class="order-header">
						<text class="order-dining">{{item.diningName}}</text>
						<view class="order-status" :class="item.status">{{item.statusText}}</view>
					</view>
					<view class="order-details">
						<text class="order-meals">{{item.meals}}</text>
						<text class="order-time">用餐时间：{{item.mealTime}}</text>
						<view class="order-meta">
							<text class="order-price">¥{{item.totalPrice}}</text>
							<text class="order-date">{{item.orderDate}}</text>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 营养建议 -->
		<view class="section">
			<view class="section-header">
				<text class="section-title">营养建议</text>
				<text class="section-subtitle">老年人饮食健康小贴士</text>
			</view>
			<view class="tips-list">
				<view class="tip-item" v-for="(tip, index) in nutritionTips" :key="index">
					<view class="tip-icon">
						<Icon name="lightbulb-line" size="32rpx" color="#ffc107"></Icon>
					</view>
					<text class="tip-text">{{tip}}</text>
				</view>
			</view>
		</view>

		<!-- 底部操作 -->
		<view class="bottom-actions">
			<button class="action-btn secondary" @click="findDining">
				<Icon name="map-line" size="32rpx" color="#ff8a00"></Icon>
				<text>查找食堂</text>
			</button>
			<button class="action-btn primary" @click="quickOrder">
				<Icon name="add-line" size="32rpx" color="white"></Icon>
				<text>立即订餐</text>
			</button>
		</view>
	</view>
</template>

<script>
import Icon from '@/components/Icon/Icon.vue'

export default {
	components: {
		Icon
	},
	data() {
		return {
			currentDate: '',
			todayMenu: [
				{
					name: '早餐',
					price: 8,
					icon: 'sun-line',
					color: '#ff9800',
					dishes: ['小米粥', '鸡蛋羹', '咸菜', '花卷']
				},
				{
					name: '午餐',
					price: 15,
					icon: 'sun-fill',
					color: '#ffc107',
					dishes: ['红烧肉', '清炒小白菜', '冬瓜汤', '米饭']
				},
				{
					name: '晚餐',
					price: 12,
					icon: 'moon-line',
					color: '#9c27b0',
					dishes: ['蒸蛋羹', '青菜豆腐', '紫菜蛋花汤', '馒头']
				}
			],
			diningList: [
				{
					id: 1,
					name: '阳光长者食堂',
					address: '阳光路123号',
					distance: '300m',
					rating: 4.8,
					priceRange: '8-20',
					features: ['营养配餐', '送餐服务', '无障碍设施']
				},
				{
					id: 2,
					name: '和谐社区餐厅',
					address: '和谐街456号',
					distance: '500m',
					rating: 4.6,
					priceRange: '10-25',
					features: ['软食制作', '糖尿病餐', '高血压餐']
				},
				{
					id: 3,
					name: '幸福长者驿站',
					address: '幸福大道789号',
					distance: '800m',
					rating: 4.9,
					priceRange: '6-18',
					features: ['政府补贴', '营养师指导', '定制餐食']
				}
			],
			orderList: [
				// 示例数据，实际应从后端获取
			],
			nutritionTips: [
				'每日保证充足的蛋白质摄入，如鸡蛋、瘦肉、豆制品',
				'多吃新鲜蔬菜水果，补充维生素和纤维素',
				'适量饮水，每日1500-2000ml，少量多次',
				'食物要软烂易消化，避免过硬过冷的食物',
				'定时定量用餐，避免暴饮暴食'
			]
		}
	},
	onLoad() {
		this.getCurrentDate();
	},
	methods: {
		getCurrentDate() {
			const date = new Date();
			const year = date.getFullYear();
			const month = String(date.getMonth() + 1).padStart(2, '0');
			const day = String(date.getDate()).padStart(2, '0');
			this.currentDate = `${year}-${month}-${day}`;
		},
		viewDining(dining) {
			uni.navigateTo({
				url: `/pages/elderly-dining/detail?id=${dining.id}`
			});
		},
		orderMeal(dining) {
			uni.navigateTo({
				url: `/pages/elderly-dining/order?diningId=${dining.id}`
			});
		},
		findDining() {
			uni.navigateTo({
				url: '/pages/map/map?category=dining'
			});
		},
		quickOrder() {
			if (this.diningList.length > 0) {
				this.orderMeal(this.diningList[0]);
			}
		}
	}
}
</script>

<style scoped>
.container {
	background: linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%);
	min-height: 100vh;
	padding-bottom: 200rpx;
}

.header-banner {
	padding: 40rpx;
	margin-bottom: 40rpx;
}

.banner-content {
	background: rgba(255, 255, 255, 0.15);
	border-radius: 30rpx;
	padding: 40rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.banner-text {
	flex: 1;
}

.banner-title {
	font-size: 48rpx;
	font-weight: bold;
	color: white;
	display: block;
	margin-bottom: 15rpx;
}

.banner-subtitle {
	font-size: 28rpx;
	color: rgba(255, 255, 255, 0.9);
	display: block;
}

.banner-icon {
	margin-left: 30rpx;
}

.section {
	margin-bottom: 40rpx;
}

.section-header {
	padding: 0 40rpx 30rpx;
}

.section-title {
	font-size: 36rpx;
	font-weight: bold;
	color: white;
	display: block;
	margin-bottom: 10rpx;
}

.section-subtitle {
	font-size: 26rpx;
	color: rgba(255, 255, 255, 0.8);
	display: block;
}

.menu-card {
	background: white;
	border-radius: 30rpx;
	padding: 40rpx;
	margin: 0 40rpx;
	box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.meal-section {
	margin-bottom: 30rpx;
}

.meal-section:last-child {
	margin-bottom: 0;
}

.meal-header {
	display: flex;
	align-items: center;
	gap: 15rpx;
	margin-bottom: 20rpx;
	padding-bottom: 15rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.meal-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	flex: 1;
}

.meal-price {
	font-size: 28rpx;
	font-weight: bold;
	color: #ff8a00;
}

.dish-list {
	display: flex;
	flex-wrap: wrap;
	gap: 15rpx;
}

.dish-item {
	background: #f8f9fa;
	color: #666;
	font-size: 26rpx;
	padding: 8rpx 20rpx;
	border-radius: 20rpx;
}

.dining-list, .order-list, .tips-list {
	padding: 0 40rpx;
}

.dining-item, .order-item {
	background: white;
	border-radius: 30rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	display: flex;
	align-items: center;
	gap: 20rpx;
	box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.dining-image {
	width: 100rpx;
	height: 100rpx;
	background: #fff8e1;
	border-radius: 20rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.dining-content {
	flex: 1;
}

.dining-name {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 8rpx;
}

.dining-address {
	font-size: 26rpx;
	color: #666;
	display: block;
	margin-bottom: 15rpx;
}

.dining-features {
	display: flex;
	gap: 10rpx;
	margin-bottom: 15rpx;
}

.feature-tag {
	background: rgba(255, 193, 7, 0.1);
	color: #ffc107;
	font-size: 22rpx;
	padding: 5rpx 15rpx;
	border-radius: 15rpx;
}

.dining-meta {
	display: flex;
	gap: 20rpx;
	align-items: center;
}

.distance, .rating {
	display: flex;
	align-items: center;
	gap: 8rpx;
}

.distance-text, .rating-text, .price-text {
	font-size: 24rpx;
	color: #666;
}

.order-btn {
	background: linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%);
	color: white;
	border: none;
	padding: 15rpx 30rpx;
	border-radius: 20rpx;
	font-size: 26rpx;
	font-weight: 500;
}

.feature-grid {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	gap: 30rpx;
	padding: 0 40rpx;
}

.feature-item {
	background: white;
	border-radius: 30rpx;
	padding: 40rpx;
	text-align: center;
	box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.feature-icon {
	width: 100rpx;
	height: 100rpx;
	border-radius: 20rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin: 0 auto 20rpx;
}

.feature-icon.nutrition { background: linear-gradient(135deg, #e91e63 0%, #c2185b 100%); }
.feature-icon.soft { background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%); }
.feature-icon.delivery { background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%); }
.feature-icon.affordable { background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%); }

.feature-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 10rpx;
}

.feature-desc {
	font-size: 24rpx;
	color: #666;
	display: block;
}

.order-item {
	flex-direction: column;
	align-items: stretch;
}

.order-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 15rpx;
}

.order-dining {
	font-size: 30rpx;
	font-weight: bold;
	color: #333;
}

.order-status {
	padding: 8rpx 20rpx;
	border-radius: 20rpx;
	font-size: 24rpx;
}

.order-status.confirmed {
	background: rgba(76, 175, 80, 0.1);
	color: #4caf50;
}

.order-status.preparing {
	background: rgba(255, 152, 0, 0.1);
	color: #ff9800;
}

.order-details {
	display: flex;
	flex-direction: column;
	gap: 8rpx;
}

.order-meals, .order-time {
	font-size: 26rpx;
	color: #666;
}

.order-meta {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.order-price {
	font-size: 28rpx;
	font-weight: bold;
	color: #ff8a00;
}

.order-date {
	font-size: 24rpx;
	color: #999;
}

.tip-item {
	background: white;
	border-radius: 30rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	display: flex;
	align-items: flex-start;
	gap: 20rpx;
	box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.tip-icon {
	width: 60rpx;
	height: 60rpx;
	background: rgba(255, 193, 7, 0.1);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-top: 5rpx;
}

.tip-text {
	flex: 1;
	font-size: 28rpx;
	color: #333;
	line-height: 1.6;
}

.bottom-actions {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background: white;
	padding: 30rpx 40rpx;
	display: flex;
	gap: 20rpx;
	box-shadow: 0 -4rpx 20rpx rgba(0,0,0,0.1);
}

.action-btn {
	flex: 1;
	height: 100rpx;
	border-radius: 20rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 15rpx;
	font-size: 32rpx;
	font-weight: 500;
}

.action-btn.primary {
	background: linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%);
	color: white;
	border: none;
}

.action-btn.secondary {
	background: white;
	color: #ff8a00;
	border: 2rpx solid #ff8a00;
}
</style>
