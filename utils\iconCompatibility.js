/**
 * 图标兼容性映射配置
 * 确保旧的图标名称仍然可以正常使用，提供向后兼容性
 */

// 已废弃的图标名称映射到新的图标名称
export const DeprecatedIconMap = {
  // 位置相关图标统一
  'map-pin-line': 'location-line',
  
  // 重复图标整合后的映射
  'home-gear-line': 'settings-line', // 家居设置统一使用设置图标
  'user-settings-line': 'settings-line', // 用户设置统一使用设置图标
  
  // 功能相似图标的统一
  'clipboard-line': 'copy-line', // 剪贴板功能统一到复制
  'file-line': 'article-line', // 文件统一到文章
  
  // 医疗相关图标优化
  'hospital-line': 'medical-cross-line', // 医院统一使用医疗十字
  
  // 状态图标优化
  'success-line': 'check-line', // 成功统一使用勾选
  'error-line': 'close-line', // 错误统一使用关闭
  
  // 业务图标优化
  'money-dollar-circle-line': 'money-cny-circle-line', // 统一使用人民币图标
}

// 图标名称变更历史记录
export const IconChangeHistory = {
  'v1.0.0': {
    // 初始版本的图标映射
    deprecated: [],
    added: ['home-line', 'user-line', 'search-line']
  },
  'v1.1.0': {
    // 第一次优化
    deprecated: ['map-pin-line'],
    added: ['location-line'],
    changed: {
      'map-pin-line': 'location-line'
    }
  },
  'v1.2.0': {
    // 重复性检查优化
    deprecated: ['home-gear-line', 'user-settings-line'],
    added: ['settings-line'],
    changed: {
      'home-gear-line': 'settings-line',
      'user-settings-line': 'settings-line'
    }
  }
}

// 获取兼容的图标名称
export function getCompatibleIconName(iconName) {
  // 如果是已废弃的图标名称，返回新的名称
  if (DeprecatedIconMap[iconName]) {
    console.warn(`图标 "${iconName}" 已废弃，建议使用 "${DeprecatedIconMap[iconName]}"`)
    return DeprecatedIconMap[iconName]
  }
  
  return iconName
}

// 检查图标是否已废弃
export function isDeprecatedIcon(iconName) {
  return DeprecatedIconMap.hasOwnProperty(iconName)
}

// 获取图标的替代建议
export function getIconReplacement(iconName) {
  if (isDeprecatedIcon(iconName)) {
    return {
      deprecated: true,
      replacement: DeprecatedIconMap[iconName],
      message: `建议将 "${iconName}" 替换为 "${DeprecatedIconMap[iconName]}"`
    }
  }
  
  return {
    deprecated: false,
    replacement: iconName,
    message: '图标名称正常'
  }
}

// 批量检查图标兼容性
export function checkIconsCompatibility(iconNames) {
  const results = {
    compatible: [],
    deprecated: [],
    suggestions: []
  }
  
  iconNames.forEach(iconName => {
    const check = getIconReplacement(iconName)
    if (check.deprecated) {
      results.deprecated.push({
        old: iconName,
        new: check.replacement,
        message: check.message
      })
    } else {
      results.compatible.push(iconName)
    }
  })
  
  return results
}

// 自动迁移图标名称
export function migrateIconNames(codeString) {
  let migratedCode = codeString
  let changeCount = 0
  
  Object.entries(DeprecatedIconMap).forEach(([oldName, newName]) => {
    const regex = new RegExp(`name=["']${oldName}["']`, 'g')
    const matches = migratedCode.match(regex)
    if (matches) {
      migratedCode = migratedCode.replace(regex, `name="${newName}"`)
      changeCount += matches.length
      console.log(`已将 ${matches.length} 个 "${oldName}" 替换为 "${newName}"`)
    }
  })
  
  return {
    code: migratedCode,
    changes: changeCount,
    success: changeCount > 0
  }
}

// 生成迁移报告
export function generateMigrationReport(iconUsage) {
  const report = {
    total: iconUsage.length,
    compatible: 0,
    deprecated: 0,
    details: []
  }
  
  iconUsage.forEach(usage => {
    const check = getIconReplacement(usage.iconName)
    if (check.deprecated) {
      report.deprecated++
      report.details.push({
        file: usage.file,
        line: usage.line,
        old: usage.iconName,
        new: check.replacement,
        context: usage.context
      })
    } else {
      report.compatible++
    }
  })
  
  return report
}

// 验证图标配置完整性
export function validateIconConfig(iconConfig) {
  const issues = []
  
  // 检查是否有重复的emoji
  const emojiMap = new Map()
  Object.entries(iconConfig).forEach(([name, config]) => {
    if (config.emoji) {
      if (emojiMap.has(config.emoji)) {
        issues.push({
          type: 'duplicate_emoji',
          emoji: config.emoji,
          icons: [emojiMap.get(config.emoji), name],
          message: `图标 "${emojiMap.get(config.emoji)}" 和 "${name}" 使用了相同的emoji "${config.emoji}"`
        })
      } else {
        emojiMap.set(config.emoji, name)
      }
    }
  })
  
  // 检查分类是否有效
  const validCategories = ['NAVIGATION', 'FUNCTION', 'ACTION', 'STATUS', 'BUSINESS', 'ELDERLY', 'MEDICAL', 'SOCIAL', 'EMERGENCY', 'ENTERTAINMENT']
  Object.entries(iconConfig).forEach(([name, config]) => {
    if (!validCategories.includes(config.category)) {
      issues.push({
        type: 'invalid_category',
        icon: name,
        category: config.category,
        message: `图标 "${name}" 的分类 "${config.category}" 无效`
      })
    }
  })
  
  return {
    valid: issues.length === 0,
    issues: issues,
    summary: {
      total: Object.keys(iconConfig).length,
      duplicateEmojis: issues.filter(i => i.type === 'duplicate_emoji').length,
      invalidCategories: issues.filter(i => i.type === 'invalid_category').length
    }
  }
}

export default {
  DeprecatedIconMap,
  IconChangeHistory,
  getCompatibleIconName,
  isDeprecatedIcon,
  getIconReplacement,
  checkIconsCompatibility,
  migrateIconNames,
  generateMigrationReport,
  validateIconConfig
}
