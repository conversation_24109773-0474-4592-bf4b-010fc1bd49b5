<template>
	<view class="container">
		<!-- 自定义导航栏 -->
		<view class="custom-navbar">
			<view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
			<view class="navbar-content">
				<view class="navbar-left" @click="goBack">
					<Icon name="arrow-left-line" size="36rpx" color="#333"></Icon>
					<text class="back-text">返回</text>
				</view>
				<view class="navbar-center">
					<text class="navbar-title">转账</text>
				</view>
				<view class="navbar-right"></view>
			</view>
		</view>

		<!-- 收款人信息 -->
		<view class="recipient-section">
			<view class="section-title">收款人信息</view>
			<view class="input-group">
				<view class="input-item">
					<text class="input-label">收款人姓名</text>
					<input class="input-field" 
						placeholder="请输入收款人姓名" 
						v-model="recipientName" />
				</view>
				<view class="input-item">
					<text class="input-label">收款人手机号</text>
					<input class="input-field" 
						type="number" 
						placeholder="请输入收款人手机号" 
						v-model="recipientPhone" />
				</view>
			</view>
			
			<view class="recent-contacts" v-if="recentContacts.length > 0">
				<text class="contacts-title">最近转账</text>
				<view class="contacts-list">
					<view class="contact-item" 
						v-for="contact in recentContacts" 
						:key="contact.id"
						@click="selectContact(contact)">
						<view class="contact-avatar">
							<Icon name="user-line" size="32rpx" color="#666"></Icon>
						</view>
						<view class="contact-info">
							<text class="contact-name">{{contact.name}}</text>
							<text class="contact-phone">{{contact.phone}}</text>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 转账金额 -->
		<view class="amount-section">
			<view class="section-title">转账金额</view>
			<view class="amount-input-container">
				<text class="currency-symbol">¥</text>
				<input class="amount-input" 
					type="digit" 
					placeholder="0.00" 
					v-model="transferAmount"
					@input="onAmountInput" />
			</view>
			<view class="balance-info">
				<text class="balance-text">余额：¥1,258.50</text>
			</view>
		</view>

		<!-- 转账说明 -->
		<view class="memo-section">
			<view class="section-title">转账说明（可选）</view>
			<textarea class="memo-input" 
				placeholder="请输入转账说明" 
				v-model="transferMemo"
				maxlength="50"></textarea>
			<view class="memo-count">{{transferMemo.length}}/50</view>
		</view>

		<!-- 转账须知 -->
		<view class="notice-section">
			<view class="notice-title">
				<Icon name="information-line" size="24rpx" color="#ff8a00"></Icon>
				<text>转账须知</text>
			</view>
			<view class="notice-content">
				<text class="notice-item">• 转账前请仔细核对收款人信息</text>
				<text class="notice-item">• 转账成功后资金将立即到达对方账户</text>
				<text class="notice-item">• 单次转账金额不得超过5000元</text>
				<text class="notice-item">• 如有疑问，请联系客服：400-123-4567</text>
			</view>
		</view>

		<!-- 转账按钮 -->
		<view class="action-section">
			<button class="transfer-btn" 
				:disabled="!canTransfer" 
				@click="confirmTransfer">
				确认转账
			</button>
		</view>

		<!-- 功能开发中提示 -->
		<view class="dev-notice">
			<Icon name="tools-line" size="32rpx" color="#ff8a00"></Icon>
			<text class="dev-text">此功能正在开发中，敬请期待</text>
		</view>
	</view>
</template>

<script>
import Icon from '@/components/Icon/Icon.vue'

export default {
	components: {
		Icon
	},
	data() {
		return {
			statusBarHeight: 0,
			recipientName: '',
			recipientPhone: '',
			transferAmount: '',
			transferMemo: '',
			availableBalance: 1258.50,
			recentContacts: [
				{
					id: 1,
					name: '李阿姨',
					phone: '138****5678'
				},
				{
					id: 2,
					name: '王大爷',
					phone: '139****1234'
				},
				{
					id: 3,
					name: '张护士',
					phone: '136****9876'
				}
			]
		}
	},
	computed: {
		canTransfer() {
			const amount = parseFloat(this.transferAmount) || 0;
			return this.recipientName.trim() && 
				   this.recipientPhone.trim() && 
				   amount > 0 && 
				   amount <= 5000 && 
				   amount <= this.availableBalance;
		}
	},
	onLoad() {
		const systemInfo = uni.getSystemInfoSync();
		this.statusBarHeight = systemInfo.statusBarHeight || 44;
	},
	methods: {
		onAmountInput() {
			// 限制输入格式
			this.transferAmount = this.transferAmount.replace(/[^\d.]/g, '');
		},
		selectContact(contact) {
			this.recipientName = contact.name;
			this.recipientPhone = contact.phone;
		},
		confirmTransfer() {
			if (!this.canTransfer) return;
			
			uni.showModal({
				title: '确认转账',
				content: `确定要向 ${this.recipientName}(${this.recipientPhone}) 转账 ¥${this.transferAmount} 吗？`,
				success: (res) => {
					if (res.confirm) {
						this.processTransfer();
					}
				}
			});
		},
		processTransfer() {
			uni.showLoading({
				title: '正在转账...',
				mask: true
			});
			
			// 模拟转账流程
			setTimeout(() => {
				uni.hideLoading();
				uni.showToast({
					title: '功能开发中，敬请期待',
					icon: 'none',
					duration: 2000
				});
			}, 2000);
		},
		goBack() {
			uni.navigateBack({
				fail: () => {
					uni.navigateTo({
						url: '/pages/wallet/wallet'
					});
				}
			});
		}
	}
}
</script>

<style scoped>
.container {
	background: #f8f9fa;
	min-height: 100vh;
}

/* 导航栏样式 */
.custom-navbar {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 1000;
	background: rgba(255, 255, 255, 0.95);
	backdrop-filter: blur(20rpx);
	border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);
}

.navbar-content {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 20rpx 32rpx;
	min-height: 88rpx;
}

.navbar-left {
	display: flex;
	align-items: center;
	gap: 12rpx;
	padding: 8rpx 16rpx 8rpx 0;
	border-radius: 20rpx;
	transition: all 0.2s ease;
}

.navbar-left:active {
	background-color: rgba(0, 0, 0, 0.04);
	transform: scale(0.96);
}

.back-text {
	font-size: 32rpx;
	color: #333;
	font-weight: 500;
}

.navbar-center {
	position: absolute;
	left: 50%;
	transform: translateX(-50%);
}

.navbar-title {
	font-size: 34rpx;
	font-weight: 600;
	color: #333;
}

.navbar-right {
	display: flex;
	align-items: center;
}

/* 内容区域 */
.recipient-section, .amount-section, .memo-section, .notice-section {
	background: white;
	margin: 20rpx;
	border-radius: 30rpx;
	padding: 40rpx;
}

.recipient-section {
	margin-top: 220rpx; /* 为导航栏留出空间 */
}

.section-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 30rpx;
}

.input-group {
	display: flex;
	flex-direction: column;
	gap: 25rpx;
	margin-bottom: 30rpx;
}

.input-item {
	display: flex;
	flex-direction: column;
	gap: 15rpx;
}

.input-label {
	font-size: 28rpx;
	color: #333;
	font-weight: 500;
}

.input-field {
	padding: 25rpx;
	background: #f8f9fa;
	border-radius: 15rpx;
	font-size: 28rpx;
	border: 2rpx solid transparent;
}

.input-field:focus {
	border-color: #ff8a00;
	background: white;
}

.recent-contacts {
	border-top: 1rpx solid #f0f0f0;
	padding-top: 30rpx;
}

.contacts-title {
	font-size: 26rpx;
	color: #666;
	margin-bottom: 20rpx;
	display: block;
}

.contacts-list {
	display: flex;
	flex-direction: column;
	gap: 15rpx;
}

.contact-item {
	display: flex;
	align-items: center;
	padding: 20rpx;
	background: #f8f9fa;
	border-radius: 15rpx;
	transition: all 0.2s ease;
}

.contact-item:active {
	background: rgba(255, 138, 0, 0.1);
}

.contact-avatar {
	width: 60rpx;
	height: 60rpx;
	border-radius: 50%;
	background: #e0e0e0;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 20rpx;
}

.contact-info {
	flex: 1;
}

.contact-name {
	font-size: 28rpx;
	color: #333;
	font-weight: 500;
	display: block;
	margin-bottom: 5rpx;
}

.contact-phone {
	font-size: 24rpx;
	color: #666;
	display: block;
}

.amount-input-container {
	display: flex;
	align-items: center;
	background: #f8f9fa;
	border-radius: 20rpx;
	padding: 30rpx;
	border: 2rpx solid transparent;
	margin-bottom: 20rpx;
}

.amount-input-container:focus-within {
	border-color: #ff8a00;
	background: white;
}

.currency-symbol {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
	margin-right: 15rpx;
}

.amount-input {
	flex: 1;
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
	border: none;
	background: transparent;
}

.balance-info {
	text-align: right;
}

.balance-text {
	font-size: 24rpx;
	color: #666;
}

.memo-input {
	width: 100%;
	min-height: 120rpx;
	padding: 25rpx;
	background: #f8f9fa;
	border-radius: 15rpx;
	font-size: 28rpx;
	border: 2rpx solid transparent;
	resize: none;
	margin-bottom: 15rpx;
}

.memo-input:focus {
	border-color: #ff8a00;
	background: white;
}

.memo-count {
	text-align: right;
	font-size: 22rpx;
	color: #999;
}

.notice-title {
	display: flex;
	align-items: center;
	gap: 10rpx;
	font-size: 28rpx;
	font-weight: bold;
	color: #ff8a00;
	margin-bottom: 20rpx;
}

.notice-content {
	display: flex;
	flex-direction: column;
	gap: 15rpx;
}

.notice-item {
	font-size: 26rpx;
	color: #666;
	line-height: 1.5;
}

.action-section {
	padding: 40rpx;
}

.transfer-btn {
	width: 100%;
	height: 100rpx;
	background: #ff8a00;
	color: white;
	border: none;
	border-radius: 25rpx;
	font-size: 32rpx;
	font-weight: bold;
}

.transfer-btn:disabled {
	background: #ccc;
	color: #999;
}

.dev-notice {
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 15rpx;
	padding: 30rpx;
	margin: 20rpx;
	background: rgba(255, 138, 0, 0.1);
	border-radius: 20rpx;
}

.dev-text {
	font-size: 26rpx;
	color: #ff8a00;
}
</style>
