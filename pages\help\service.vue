<template>
	<view class="container" :class="{ 'elderly-mode': isElderlyMode }">
		<!-- 自定义导航栏 -->
		<view class="custom-navbar" :class="{ 'elderly-mode': isElderlyMode }">
			<view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
			<view class="navbar-content">
				<view class="navbar-left" @click="goBack">
					<Icon
						name="arrow-left-line"
						:size="isElderlyMode ? '40rpx' : '36rpx'"
						color="#333"
					></Icon>
					<text class="back-text">返回</text>
				</view>
				<view class="navbar-center">
					<text class="navbar-title">在线客服</text>
				</view>
				<view class="navbar-right">
					<view class="service-status-indicator" :class="{ online: isOnline }">
						<view class="status-dot"></view>
						<text class="status-text">{{isOnline ? '在线' : '离线'}}</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 客服状态栏 -->
		<view class="service-status">
			<view class="status-info">
				<view class="avatar">
					<image src="/static/avatar/service.png" class="avatar-image" mode="aspectFill"></image>
					<view class="status-dot" :class="{ online: isOnline }"></view>
				</view>
				<view class="service-info">
					<text class="service-name">智慧养老客服</text>
					<text class="service-status-text">{{isOnline ? '在线' : '离线'}} · 平均响应时间 2分钟</text>
				</view>
			</view>
			<view class="service-actions">
				<button class="action-btn call" @click="makeCall">
					<Icon name="phone-line" size="24rpx" color="white"></Icon>
				</button>
				<button class="action-btn video" @click="startVideo">
					<Icon name="video-line" size="24rpx" color="white"></Icon>
				</button>
			</view>
		</view>

		<!-- 聊天区域 -->
		<scroll-view class="chat-area" scroll-y :scroll-top="scrollTop" scroll-with-animation>
			<view class="message-list">
				<view class="message-item" v-for="(message, index) in messages" :key="index" :class="message.type">
					<view class="message-avatar" v-if="message.type === 'received'">
						<image src="/static/avatar/service.png" class="avatar-image" mode="aspectFill"></image>
					</view>
					
					<view class="message-content">
						<view class="message-bubble" :class="message.type">
							<text class="message-text" v-if="message.contentType === 'text'">{{message.content}}</text>
							
							<view class="message-image" v-else-if="message.contentType === 'image'" @click="previewImage(message.content)">
								<image :src="message.content" class="image" mode="aspectFill"></image>
							</view>
							
							<view class="message-file" v-else-if="message.contentType === 'file'">
								<Icon name="file-line" size="32rpx" color="#ff8a00"></Icon>
								<text class="file-name">{{message.fileName}}</text>
							</view>
							
							<view class="quick-replies" v-else-if="message.contentType === 'quickReply'">
								<text class="quick-title">{{message.content}}</text>
								<view class="reply-buttons">
									<button class="reply-btn" v-for="(reply, rIndex) in message.replies" :key="rIndex" @click="sendQuickReply(reply)">
										{{reply}}
									</button>
								</view>
							</view>
						</view>
						<text class="message-time">{{message.time}}</text>
					</view>
					
					<view class="message-avatar" v-if="message.type === 'sent'">
						<image src="/static/avatar/user.png" class="avatar-image" mode="aspectFill"></image>
					</view>
				</view>
			</view>
		</scroll-view>

		<!-- 快捷回复 -->
		<view class="quick-actions" v-if="showQuickActions">
			<view class="quick-title">常见问题</view>
			<view class="quick-buttons">
				<button class="quick-btn" v-for="(action, index) in quickActions" :key="index" @click="sendQuickReply(action.text)">
					<Icon :name="action.icon" size="24rpx" color="#ff8a00"></Icon>
					<text>{{action.text}}</text>
				</button>
			</view>
		</view>

		<!-- 输入区域 -->
		<view class="input-area">
			<view class="input-tools">
				<button class="tool-btn" @click="selectImage">
					<Icon name="image-line" size="32rpx" color="#666"></Icon>
				</button>
				<button class="tool-btn" @click="selectFile">
					<Icon name="attachment-line" size="32rpx" color="#666"></Icon>
				</button>
				<button class="tool-btn" @click="toggleVoice">
					<Icon :name="isVoiceMode ? 'keyboard-line' : 'mic-line'" size="32rpx" color="#666"></Icon>
				</button>
			</view>
			
			<view class="input-wrapper">
				<textarea 
					v-if="!isVoiceMode"
					class="text-input" 
					v-model="inputText" 
					placeholder="请输入您的问题..." 
					:auto-height="true"
					:show-confirm-bar="false"
					@focus="onInputFocus"
					@blur="onInputBlur"
				></textarea>
				
				<button v-else class="voice-btn" :class="{ recording: isRecording }" @touchstart="startRecord" @touchend="stopRecord">
					<Icon name="mic-line" size="32rpx" color="white"></Icon>
					<text>{{isRecording ? '松开发送' : '按住说话'}}</text>
				</button>
			</view>
			
			<button class="send-btn" @click="sendMessage" :disabled="!inputText.trim()" v-if="!isVoiceMode">
				<Icon name="send-plane-line" size="32rpx" color="white"></Icon>
			</button>
		</view>

		<!-- 满意度评价 -->
		<view class="satisfaction-modal" v-if="showSatisfaction">
			<view class="modal-content">
				<view class="modal-header">
					<text class="modal-title">服务评价</text>
					<button class="close-btn" @click="closeSatisfaction">
						<Icon name="close-line" size="24rpx" color="#999"></Icon>
					</button>
				</view>
				<view class="rating-section">
					<text class="rating-title">请为本次服务打分</text>
					<view class="stars">
						<view class="star" v-for="n in 5" :key="n" @click="setRating(n)">
							<Icon :name="n <= rating ? 'star-fill' : 'star-line'" size="40rpx" :color="n <= rating ? '#ff8a00' : '#ccc'"></Icon>
						</view>
					</view>
				</view>
				<view class="comment-section">
					<text class="comment-title">意见建议（可选）</text>
					<textarea class="comment-input" v-model="comment" placeholder="请输入您的意见建议"></textarea>
				</view>
				<button class="submit-rating-btn" @click="submitRating">提交评价</button>
			</view>
		</view>
	</view>
</template>

<script>
import Icon from '@/components/Icon/Icon.vue'
import { elderlyModeManager } from '@/utils/elderlyModeUtils.js'

export default {
	components: {
		Icon
	},
	data() {
		return {
			statusBarHeight: 0, // 状态栏高度
			isElderlyMode: false, // 适老化模式
			isOnline: true,
			scrollTop: 0,
			inputText: '',
			isVoiceMode: false,
			isRecording: false,
			showQuickActions: true,
			showSatisfaction: false,
			rating: 0,
			comment: '',
			messages: [
				{
					type: 'received',
					contentType: 'text',
					content: '您好！欢迎使用智慧养老客服，我是您的专属客服小智。有什么可以帮助您的吗？',
					time: '14:30'
				},
				{
					type: 'received',
					contentType: 'quickReply',
					content: '您可以选择以下常见问题：',
					replies: ['账户问题', '服务预约', '支付问题', '技术支持'],
					time: '14:30'
				}
			],
			quickActions: [
				{ icon: 'user-line', text: '账户问题' },
				{ icon: 'calendar-check-line', text: '服务预约' },
				{ icon: 'wallet-line', text: '支付问题' },
				{ icon: 'settings-line', text: '技术支持' },
				{ icon: 'heart-pulse-line', text: '健康咨询' },
				{ icon: 'phone-line', text: '联系方式' }
			]
		}
	},
	methods: {
		makeCall() {
			uni.makePhoneCall({
				phoneNumber: '************'
			});
		},
		startVideo() {
			uni.showToast({
				title: '视频通话功能开发中',
				icon: 'none'
			});
		},
		sendMessage() {
			if (!this.inputText.trim()) return;
			
			const message = {
				type: 'sent',
				contentType: 'text',
				content: this.inputText,
				time: this.getCurrentTime()
			};
			
			this.messages.push(message);
			this.inputText = '';
			this.scrollToBottom();
			
			// 模拟客服回复
			setTimeout(() => {
				this.simulateReply();
			}, 1000);
		},
		sendQuickReply(text) {
			const message = {
				type: 'sent',
				contentType: 'text',
				content: text,
				time: this.getCurrentTime()
			};
			
			this.messages.push(message);
			this.showQuickActions = false;
			this.scrollToBottom();
			
			// 模拟客服回复
			setTimeout(() => {
				this.simulateReply();
			}, 1000);
		},
		simulateReply() {
			const replies = [
				'我已经收到您的问题，正在为您查询相关信息...',
				'根据您的问题，我为您提供以下解决方案：',
				'如果还有其他问题，请随时告诉我。',
				'感谢您的咨询，还有什么可以帮助您的吗？'
			];
			
			const reply = {
				type: 'received',
				contentType: 'text',
				content: replies[Math.floor(Math.random() * replies.length)],
				time: this.getCurrentTime()
			};
			
			this.messages.push(reply);
			this.scrollToBottom();
		},
		selectImage() {
			uni.chooseImage({
				count: 1,
				success: (res) => {
					const message = {
						type: 'sent',
						contentType: 'image',
						content: res.tempFilePaths[0],
						time: this.getCurrentTime()
					};
					this.messages.push(message);
					this.scrollToBottom();
				}
			});
		},
		selectFile() {
			uni.showToast({
				title: '文件上传功能开发中',
				icon: 'none'
			});
		},
		toggleVoice() {
			this.isVoiceMode = !this.isVoiceMode;
		},
		startRecord() {
			this.isRecording = true;
			uni.showToast({
				title: '开始录音',
				icon: 'none'
			});
		},
		stopRecord() {
			this.isRecording = false;
			uni.showToast({
				title: '录音结束',
				icon: 'none'
			});
		},
		previewImage(url) {
			uni.previewImage({
				urls: [url]
			});
		},
		onInputFocus() {
			this.showQuickActions = false;
			setTimeout(() => {
				this.scrollToBottom();
			}, 300);
		},
		onInputBlur() {
			// 可以在这里处理输入框失焦逻辑
		},
		scrollToBottom() {
			this.$nextTick(() => {
				this.scrollTop = 999999;
			});
		},
		getCurrentTime() {
			const now = new Date();
			return `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;
		},
		closeSatisfaction() {
			this.showSatisfaction = false;
		},
		setRating(rating) {
			this.rating = rating;
		},
		submitRating() {
			uni.showToast({
				title: '感谢您的评价',
				icon: 'success'
			});
			this.showSatisfaction = false;
		},

		// 返回上一页
		goBack() {
			uni.navigateBack({
				fail: () => {
					uni.switchTab({
						url: '/pages/profile/profile'
					});
				}
			});
		}
	},
	onLoad() {
		// 获取状态栏高度
		const systemInfo = uni.getSystemInfoSync();
		this.statusBarHeight = systemInfo.statusBarHeight || 44;

		// 初始化适老化模式
		this.isElderlyMode = elderlyModeManager.getElderlyMode();
		elderlyModeManager.onElderlyModeChange((isEnabled) => {
			this.isElderlyMode = isEnabled;
		});

		// 模拟5分钟后显示满意度评价
		setTimeout(() => {
			this.showSatisfaction = true;
		}, 300000);
	},
	onUnload() {
		// 页面卸载时显示满意度评价
		if (!this.showSatisfaction) {
			this.showSatisfaction = true;
		}
	}
}
</script>

<style scoped>
.container {
	display: flex;
	flex-direction: column;
	height: 100vh;
	background: #f8f9fa;
}

/* 导航栏样式 */
.custom-navbar {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 1000;
	background: rgba(255, 255, 255, 0.95);
	backdrop-filter: blur(20rpx);
	-webkit-backdrop-filter: blur(20rpx);
	border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);
}

.navbar-content {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 20rpx 32rpx;
	min-height: 88rpx;
}

.navbar-left {
	display: flex;
	align-items: center;
	gap: 12rpx;
	padding: 8rpx 16rpx 8rpx 0;
	border-radius: 20rpx;
	transition: all 0.2s ease;
}

.navbar-left:active {
	background-color: rgba(0, 0, 0, 0.04);
	transform: scale(0.96);
}

.back-text {
	font-size: 32rpx;
	color: #333;
	font-weight: 500;
}

.navbar-center {
	position: absolute;
	left: 50%;
	transform: translateX(-50%);
}

.navbar-title {
	font-size: 34rpx;
	font-weight: 600;
	color: #333;
}

.navbar-right {
	display: flex;
	align-items: center;
}

.service-status-indicator {
	display: flex;
	align-items: center;
	gap: 8rpx;
	padding: 8rpx 15rpx;
	background: #f8f8f8;
	border-radius: 15rpx;
}

.service-status-indicator.online {
	background: rgba(76, 175, 80, 0.1);
}

.service-status-indicator .status-dot {
	width: 12rpx;
	height: 12rpx;
	border-radius: 50%;
	background: #ccc;
}

.service-status-indicator.online .status-dot {
	background: #4caf50;
}

.service-status-indicator .status-text {
	font-size: 22rpx;
	color: #666;
}

.service-status-indicator.online .status-text {
	color: #4caf50;
}

.service-status {
	background: white;
	padding: 30rpx;
	margin-top: 220rpx; /* 为导航栏留出空间 */
	display: flex;
	justify-content: space-between;
	align-items: center;
	box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.status-info {
	display: flex;
	align-items: center;
	gap: 20rpx;
}

.avatar {
	position: relative;
	width: 80rpx;
	height: 80rpx;
}

.avatar-image {
	width: 100%;
	height: 100%;
	border-radius: 50%;
}

.status-dot {
	position: absolute;
	bottom: 5rpx;
	right: 5rpx;
	width: 20rpx;
	height: 20rpx;
	border-radius: 50%;
	background: #ccc;
	border: 3rpx solid white;
}

.status-dot.online {
	background: #4caf50;
}

.service-name {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 5rpx;
}

.service-status-text {
	font-size: 22rpx;
	color: #666;
	display: block;
}

.service-actions {
	display: flex;
	gap: 15rpx;
}

.action-btn {
	width: 60rpx;
	height: 60rpx;
	border-radius: 50%;
	border: none;
	display: flex;
	align-items: center;
	justify-content: center;
}

.action-btn.call {
	background: #4caf50;
}

.action-btn.video {
	background: #2196f3;
}

.chat-area {
	flex: 1;
	padding: 20rpx;
}

.message-list {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

.message-item {
	display: flex;
	gap: 15rpx;
}

.message-item.sent {
	flex-direction: row-reverse;
}

.message-avatar {
	width: 60rpx;
	height: 60rpx;
	flex-shrink: 0;
}

.message-content {
	flex: 1;
	max-width: 70%;
}

.message-item.sent .message-content {
	display: flex;
	flex-direction: column;
	align-items: flex-end;
}

.message-bubble {
	padding: 20rpx;
	border-radius: 20rpx;
	margin-bottom: 8rpx;
}

.message-bubble.received {
	background: white;
	border-bottom-left-radius: 8rpx;
}

.message-bubble.sent {
	background: #ff8a00;
	color: white;
	border-bottom-right-radius: 8rpx;
}

.message-text {
	font-size: 26rpx;
	line-height: 1.5;
}

.message-image {
	width: 200rpx;
	height: 200rpx;
	border-radius: 15rpx;
	overflow: hidden;
}

.image {
	width: 100%;
	height: 100%;
}

.message-file {
	display: flex;
	align-items: center;
	gap: 15rpx;
}

.file-name {
	font-size: 24rpx;
}

.quick-replies {
	display: flex;
	flex-direction: column;
	gap: 15rpx;
}

.quick-title {
	font-size: 26rpx;
	margin-bottom: 10rpx;
}

.reply-buttons {
	display: flex;
	flex-direction: column;
	gap: 10rpx;
}

.reply-btn {
	background: rgba(255, 138, 0, 0.1);
	border: 1rpx solid #ff8a00;
	border-radius: 15rpx;
	padding: 15rpx 20rpx;
	font-size: 24rpx;
	color: #ff8a00;
	text-align: left;
}

.message-time {
	font-size: 20rpx;
	color: #999;
}

.quick-actions {
	background: white;
	padding: 30rpx;
	border-top: 1rpx solid #f0f0f0;
}

.quick-title {
	font-size: 24rpx;
	color: #666;
	margin-bottom: 20rpx;
}

.quick-buttons {
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	gap: 15rpx;
}

.quick-btn {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 8rpx;
	padding: 20rpx;
	background: #f8f9fa;
	border: none;
	border-radius: 15rpx;
	font-size: 22rpx;
	color: #333;
}

.input-area {
	background: white;
	padding: 20rpx;
	display: flex;
	align-items: flex-end;
	gap: 15rpx;
	border-top: 1rpx solid #f0f0f0;
}

.input-tools {
	display: flex;
	gap: 10rpx;
}

.tool-btn {
	width: 60rpx;
	height: 60rpx;
	background: #f8f9fa;
	border: none;
	border-radius: 15rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.input-wrapper {
	flex: 1;
}

.text-input {
	width: 100%;
	min-height: 60rpx;
	max-height: 200rpx;
	background: #f8f9fa;
	border: none;
	border-radius: 15rpx;
	padding: 20rpx;
	font-size: 26rpx;
	color: #333;
}

.voice-btn {
	width: 100%;
	height: 60rpx;
	background: #ff8a00;
	border: none;
	border-radius: 15rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 10rpx;
	font-size: 24rpx;
	color: white;
}

.voice-btn.recording {
	background: #f44336;
}

.send-btn {
	width: 60rpx;
	height: 60rpx;
	background: #ff8a00;
	border: none;
	border-radius: 15rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.send-btn:disabled {
	background: #ccc;
}

.satisfaction-modal {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 1000;
}

.modal-content {
	background: white;
	margin: 40rpx;
	border-radius: 30rpx;
	padding: 40rpx;
	width: calc(100% - 80rpx);
}

.modal-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 30rpx;
}

.modal-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.close-btn {
	background: none;
	border: none;
	padding: 10rpx;
}

.rating-section {
	text-align: center;
	margin-bottom: 30rpx;
}

.rating-title {
	font-size: 26rpx;
	color: #333;
	display: block;
	margin-bottom: 20rpx;
}

.stars {
	display: flex;
	justify-content: center;
	gap: 10rpx;
}

.star {
	padding: 10rpx;
}

.comment-section {
	margin-bottom: 30rpx;
}

.comment-title {
	font-size: 26rpx;
	color: #333;
	display: block;
	margin-bottom: 15rpx;
}

.comment-input {
	width: 100%;
	height: 120rpx;
	background: #f8f9fa;
	border: 1rpx solid #e0e0e0;
	border-radius: 15rpx;
	padding: 20rpx;
	font-size: 24rpx;
	color: #333;
}

.submit-rating-btn {
	width: 100%;
	height: 80rpx;
	background: #ff8a00;
	color: white;
	border: none;
	border-radius: 20rpx;
	font-size: 28rpx;
	font-weight: bold;
}

/* ===== 适老化模式样式 ===== */
.elderly-mode .navbar-content {
	padding: 24rpx 36rpx;
	min-height: 96rpx;
}

.elderly-mode .navbar-left {
	gap: 16rpx;
	padding: 12rpx 20rpx 12rpx 0;
}

.elderly-mode .back-text {
	font-size: 36rpx;
	font-weight: 600;
}

.elderly-mode .navbar-title {
	font-size: 38rpx;
	font-weight: 700;
}

.elderly-mode .service-status-text {
	font-size: 30rpx;
}

.elderly-mode .chat-container {
	margin-top: 240rpx; /* 适老化模式下导航栏更高 */
}

.elderly-mode .message-text {
	font-size: 32rpx;
	line-height: 1.6;
}

.elderly-mode .input-container {
	padding: 30rpx;
}

.elderly-mode .input-field {
	font-size: 32rpx;
	padding: 24rpx;
}

.elderly-mode .send-button {
	padding: 24rpx 30rpx;
	font-size: 32rpx;
}
</style>
