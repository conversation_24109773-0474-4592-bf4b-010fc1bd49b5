<template>
	<view class="container">
		<!-- 搜索框 -->
		<view class="search-section">
			<view class="search-box">
				<Icon name="search-line" size="32rpx" color="#999"></Icon>
				<input class="search-input" v-model="searchKeyword" placeholder="搜索帮助内容" @input="onSearch" />
			</view>
		</view>

		<!-- 问题诊断 -->
		<view class="diagnosis-section">
			<view class="section-header">
				<Icon name="stethoscope-line" size="32rpx" color="#ff8a00"></Icon>
				<text class="section-title">问题诊断</text>
			</view>

			<view class="diagnosis-grid">
				<view class="diagnosis-item" @click="checkNetwork">
					<view class="diagnosis-icon network">
						<Icon name="wifi-line" size="40rpx" color="white"></Icon>
					</view>
					<text class="diagnosis-title">网络检测</text>
					<text class="diagnosis-desc">检查网络连接状态</text>
				</view>

				<view class="diagnosis-item" @click="checkStorage">
					<view class="diagnosis-icon storage">
						<Icon name="hard-drive-line" size="40rpx" color="white"></Icon>
					</view>
					<text class="diagnosis-title">存储检测</text>
					<text class="diagnosis-desc">检查存储空间使用</text>
				</view>

				<view class="diagnosis-item" @click="checkPermissions">
					<view class="diagnosis-icon permission">
						<Icon name="shield-check-line" size="40rpx" color="white"></Icon>
					</view>
					<text class="diagnosis-title">权限检测</text>
					<text class="diagnosis-desc">检查应用权限状态</text>
				</view>

				<view class="diagnosis-item" @click="systemInfo">
					<view class="diagnosis-icon system">
						<Icon name="information-line" size="40rpx" color="white"></Icon>
					</view>
					<text class="diagnosis-title">系统信息</text>
					<text class="diagnosis-desc">查看设备系统信息</text>
				</view>
			</view>
		</view>

		<!-- 常见问题 -->
		<view class="faq-section">
			<view class="section-header">
				<Icon name="question-answer-line" size="32rpx" color="#ff8a00"></Icon>
				<text class="section-title">常见问题</text>
			</view>
			
			<view class="faq-list">
				<view class="faq-item" v-for="(faq, index) in filteredFAQs" :key="index" @click="toggleFAQ(index)">
					<view class="faq-question">
						<text class="question-text">{{faq.question}}</text>
						<Icon :name="faq.expanded ? 'arrow-up-s-line' : 'arrow-down-s-line'" size="24rpx" color="#999"></Icon>
					</view>
					<view class="faq-answer" v-if="faq.expanded">
						<text class="answer-text">{{faq.answer}}</text>
						<view class="answer-actions" v-if="faq.helpful !== undefined">
							<text class="helpful-text">这个回答对您有帮助吗？</text>
							<view class="helpful-buttons">
								<button class="helpful-btn yes" :class="{ active: faq.helpful === true }" @click.stop="markHelpful(index, true)">
									<Icon name="thumb-up-line" size="20rpx"></Icon>
									<text>有帮助</text>
								</button>
								<button class="helpful-btn no" :class="{ active: faq.helpful === false }" @click.stop="markHelpful(index, false)">
									<Icon name="thumb-down-line" size="20rpx"></Icon>
									<text>没帮助</text>
								</button>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 实用工具 -->
		<view class="tools-section">
			<view class="section-header">
				<Icon name="tools-line" size="32rpx" color="#ff8a00"></Icon>
				<text class="section-title">实用工具</text>
			</view>

			<view class="tools-list">
				<view class="tool-item" @click="logExport">
					<view class="tool-icon">
						<Icon name="file-download-line" size="32rpx" color="#4caf50"></Icon>
					</view>
					<view class="tool-content">
						<text class="tool-title">导出日志</text>
						<text class="tool-desc">导出应用运行日志</text>
					</view>
					<Icon name="arrow-right-s-line" size="20rpx" color="#999"></Icon>
				</view>

				<view class="tool-item" @click="performanceTest">
					<view class="tool-icon">
						<Icon name="speed-line" size="32rpx" color="#2196f3"></Icon>
					</view>
					<view class="tool-content">
						<text class="tool-title">性能测试</text>
						<text class="tool-desc">测试应用运行性能</text>
					</view>
					<Icon name="arrow-right-s-line" size="20rpx" color="#999"></Icon>
				</view>

				<view class="tool-item" @click="connectivityTest">
					<view class="tool-icon">
						<Icon name="global-line" size="32rpx" color="#ff9800"></Icon>
					</view>
					<view class="tool-content">
						<text class="tool-title">连接测试</text>
						<text class="tool-desc">测试服务器连接状态</text>
					</view>
					<Icon name="arrow-right-s-line" size="20rpx" color="#999"></Icon>
				</view>

				<view class="tool-item" @click="dataBackup">
					<view class="tool-icon">
						<Icon name="save-line" size="32rpx" color="#9c27b0"></Icon>
					</view>
					<view class="tool-content">
						<text class="tool-title">数据备份</text>
						<text class="tool-desc">备份个人数据</text>
					</view>
					<Icon name="arrow-right-s-line" size="20rpx" color="#999"></Icon>
				</view>
			</view>
		</view>

		<!-- 联系客服 -->
		<view class="contact-section">
			<view class="section-header">
				<Icon name="customer-service-line" size="32rpx" color="#ff8a00"></Icon>
				<text class="section-title">联系客服</text>
			</view>
			
			<view class="contact-options">
				<view class="contact-item" @click="callService">
					<view class="contact-icon">
						<Icon name="phone-line" size="32rpx" color="#4caf50"></Icon>
					</view>
					<view class="contact-info">
						<text class="contact-title">电话客服</text>
						<text class="contact-desc">************</text>
						<text class="contact-time">服务时间：9:00-18:00</text>
					</view>
				</view>
				
				<view class="contact-item" @click="openChat">
					<view class="contact-icon">
						<Icon name="message-line" size="32rpx" color="#2196f3"></Icon>
					</view>
					<view class="contact-info">
						<text class="contact-title">在线客服</text>
						<text class="contact-desc">即时回复，快速解决问题</text>
						<text class="contact-time">24小时在线</text>
					</view>
				</view>
				
				<view class="contact-item" @click="sendFeedback">
					<view class="contact-icon">
						<Icon name="feedback-line" size="32rpx" color="#ff9800"></Icon>
					</view>
					<view class="contact-info">
						<text class="contact-title">意见反馈</text>
						<text class="contact-desc">提交问题和建议</text>
						<text class="contact-time">1个工作日内回复</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import Icon from '@/components/Icon/Icon.vue'

export default {
	components: {
		Icon
	},
	data() {
		return {
			searchKeyword: '',
			faqList: [
				{
					question: '如何注册账号？',
					answer: '点击登录页面的"注册"按钮，填写手机号码和验证码，设置密码即可完成注册。',
					category: 'account',
					expanded: false,
					helpful: undefined
				},
				{
					question: '忘记密码怎么办？',
					answer: '在登录页面点击"忘记密码"，输入注册时的手机号码，通过短信验证码重置密码。',
					category: 'account',
					expanded: false,
					helpful: undefined
				},
				{
					question: '如何预约服务？',
					answer: '进入相应的服务页面，选择服务类型、时间和地点，填写相关信息后提交预约申请。',
					category: 'service',
					expanded: false,
					helpful: undefined
				},
				{
					question: '如何取消预约？',
					answer: '在"我的预约"页面找到相应的预约记录，点击"取消预约"按钮。注意：部分服务需要提前24小时取消。',
					category: 'service',
					expanded: false,
					helpful: undefined
				},
				{
					question: '紧急情况下如何求助？',
					answer: '可以使用紧急呼叫功能，或者直接拨打紧急联系人电话。系统会自动发送位置信息给紧急联系人。',
					category: 'emergency',
					expanded: false,
					helpful: undefined
				}
			],

		}
	},
	computed: {
		filteredFAQs() {
			if (!this.searchKeyword) {
				return this.faqList;
			}
			return this.faqList.filter(faq => 
				faq.question.includes(this.searchKeyword) || 
				faq.answer.includes(this.searchKeyword)
			);
		}
	},
	methods: {
		onSearch() {
			// 搜索功能已通过computed实现
		},
		toggleFAQ(index) {
			this.filteredFAQs[index].expanded = !this.filteredFAQs[index].expanded;
		},
		markHelpful(index, helpful) {
			this.filteredFAQs[index].helpful = helpful;
			uni.showToast({
				title: helpful ? '感谢您的反馈' : '我们会改进内容',
				icon: 'success'
			});
		},
		// 诊断功能
		checkNetwork() {
			uni.showLoading({
				title: '检测网络中...'
			});

			// 模拟网络检测
			setTimeout(() => {
				uni.hideLoading();
				uni.showModal({
					title: '网络检测结果',
					content: '网络连接正常\n延迟: 45ms\n下载速度: 50Mbps',
					showCancel: false
				});
			}, 2000);
		},
		checkStorage() {
			uni.showLoading({
				title: '检测存储中...'
			});

			// 模拟存储检测
			setTimeout(() => {
				uni.hideLoading();
				uni.showModal({
					title: '存储检测结果',
					content: '总空间: 64GB\n已用空间: 32GB\n可用空间: 32GB\n应用占用: 256MB',
					showCancel: false
				});
			}, 1500);
		},
		checkPermissions() {
			uni.showLoading({
				title: '检测权限中...'
			});

			// 模拟权限检测
			setTimeout(() => {
				uni.hideLoading();
				uni.showModal({
					title: '权限检测结果',
					content: '相机权限: ✓ 已授权\n位置权限: ✓ 已授权\n存储权限: ✓ 已授权\n通知权限: ✓ 已授权',
					showCancel: false
				});
			}, 1000);
		},
		systemInfo() {
			uni.getSystemInfo({
				success: (res) => {
					uni.showModal({
						title: '系统信息',
						content: `设备型号: ${res.model}\n系统版本: ${res.system}\n平台: ${res.platform}\n屏幕尺寸: ${res.screenWidth}×${res.screenHeight}`,
						showCancel: false
					});
				}
			});
		},
		// 工具功能
		logExport() {
			uni.showLoading({
				title: '导出日志中...'
			});

			setTimeout(() => {
				uni.hideLoading();
				uni.showToast({
					title: '日志导出完成',
					icon: 'success'
				});
			}, 2000);
		},
		performanceTest() {
			uni.showLoading({
				title: '性能测试中...'
			});

			setTimeout(() => {
				uni.hideLoading();
				uni.showModal({
					title: '性能测试结果',
					content: 'CPU使用率: 15%\n内存使用率: 45%\n电池温度: 32°C\n运行流畅度: 优秀',
					showCancel: false
				});
			}, 3000);
		},
		connectivityTest() {
			uni.showLoading({
				title: '连接测试中...'
			});

			setTimeout(() => {
				uni.hideLoading();
				uni.showModal({
					title: '连接测试结果',
					content: '主服务器: ✓ 连接正常\n备用服务器: ✓ 连接正常\n数据同步: ✓ 正常\n响应时间: 120ms',
					showCancel: false
				});
			}, 2500);
		},
		dataBackup() {
			uni.showModal({
				title: '数据备份',
				content: '确定要备份个人数据吗？',
				success: (res) => {
					if (res.confirm) {
						uni.showLoading({
							title: '备份中...'
						});

						setTimeout(() => {
							uni.hideLoading();
							uni.showToast({
								title: '数据备份完成',
								icon: 'success'
							});
						}, 3000);
					}
				}
			});
		},
		callService() {
			uni.makePhoneCall({
				phoneNumber: '************'
			});
		},
		openChat() {
			uni.navigateTo({
				url: '/pages/help/chat'
			});
		},
		sendFeedback() {
			uni.navigateTo({
				url: '/pages/help/feedback'
			});
		}
	}
}
</script>

<style scoped>
.container {
	background: #f8f9fa;
	min-height: 100vh;
}

.search-section {
	background: white;
	padding: 40rpx;
	margin-bottom: 20rpx;
}

.search-box {
	display: flex;
	align-items: center;
	gap: 15rpx;
	background: #f8f9fa;
	border-radius: 25rpx;
	padding: 20rpx 30rpx;
}

.search-input {
	flex: 1;
	font-size: 28rpx;
	color: #333;
}

.diagnosis-section, .faq-section, .tools-section, .contact-section {
	background: white;
	margin: 20rpx;
	border-radius: 30rpx;
	padding: 40rpx;
}

.section-header {
	display: flex;
	align-items: center;
	gap: 15rpx;
	margin-bottom: 30rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.diagnosis-grid {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	gap: 25rpx;
}

.diagnosis-item {
	text-align: center;
	padding: 30rpx;
	background: #f8f9fa;
	border-radius: 25rpx;
}

.diagnosis-icon {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	margin: 0 auto 20rpx;
}

.diagnosis-icon.network { background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%); }
.diagnosis-icon.storage { background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%); }
.diagnosis-icon.permission { background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%); }
.diagnosis-icon.system { background: linear-gradient(135deg, #9c27b0 0%, #7b1fa2 100%); }

.diagnosis-title {
	font-size: 26rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 8rpx;
}

.diagnosis-desc {
	font-size: 22rpx;
	color: #666;
	display: block;
}

.tools-list {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

.tool-item {
	display: flex;
	align-items: center;
	gap: 20rpx;
	padding: 25rpx;
	background: #f8f9fa;
	border-radius: 20rpx;
}

.tool-icon {
	width: 60rpx;
	height: 60rpx;
	background: rgba(255, 138, 0, 0.1);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
}

.tool-content {
	flex: 1;
}

.tool-title {
	font-size: 28rpx;
	color: #333;
	font-weight: bold;
	display: block;
	margin-bottom: 5rpx;
}

.tool-desc {
	font-size: 24rpx;
	color: #666;
	display: block;
}

.faq-list {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

.faq-item {
	border: 1rpx solid #f0f0f0;
	border-radius: 20rpx;
	overflow: hidden;
}

.faq-question {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 25rpx 30rpx;
	background: #f8f9fa;
}

.question-text {
	font-size: 26rpx;
	color: #333;
	font-weight: 500;
}

.faq-answer {
	padding: 25rpx 30rpx;
	background: white;
}

.answer-text {
	font-size: 24rpx;
	color: #666;
	line-height: 1.6;
	display: block;
	margin-bottom: 20rpx;
}

.answer-actions {
	border-top: 1rpx solid #f0f0f0;
	padding-top: 20rpx;
}

.helpful-text {
	font-size: 22rpx;
	color: #666;
	display: block;
	margin-bottom: 15rpx;
}

.helpful-buttons {
	display: flex;
	gap: 15rpx;
}

.helpful-btn {
	display: flex;
	align-items: center;
	gap: 8rpx;
	padding: 10rpx 20rpx;
	background: #f8f9fa;
	border: 1rpx solid #e0e0e0;
	border-radius: 20rpx;
	font-size: 22rpx;
	color: #666;
}

.helpful-btn.active.yes {
	background: rgba(76, 175, 80, 0.1);
	border-color: #4caf50;
	color: #4caf50;
}

.helpful-btn.active.no {
	background: rgba(244, 67, 54, 0.1);
	border-color: #f44336;
	color: #f44336;
}

.guide-list {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

.guide-item {
	display: flex;
	align-items: center;
	gap: 20rpx;
	padding: 25rpx;
	background: #f8f9fa;
	border-radius: 20rpx;
}

.guide-icon {
	width: 60rpx;
	height: 60rpx;
	background: rgba(255, 138, 0, 0.1);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
}

.guide-content {
	flex: 1;
}

.guide-title {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 8rpx;
}

.guide-desc {
	font-size: 24rpx;
	color: #666;
	display: block;
	margin-bottom: 15rpx;
}

.guide-tags {
	display: flex;
	gap: 8rpx;
}

.guide-tag {
	background: rgba(255, 138, 0, 0.1);
	color: #ff8a00;
	font-size: 20rpx;
	padding: 4rpx 12rpx;
	border-radius: 12rpx;
}

.video-list {
	display: flex;
	flex-direction: column;
	gap: 25rpx;
}

.video-item {
	display: flex;
	gap: 20rpx;
}

.video-thumbnail {
	position: relative;
	width: 200rpx;
	height: 120rpx;
	border-radius: 15rpx;
	overflow: hidden;
}

.thumbnail-image {
	width: 100%;
	height: 100%;
}

.play-button {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	width: 60rpx;
	height: 60rpx;
	background: rgba(0, 0, 0, 0.6);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
}

.video-duration {
	position: absolute;
	bottom: 8rpx;
	right: 8rpx;
	background: rgba(0, 0, 0, 0.6);
	color: white;
	font-size: 20rpx;
	padding: 4rpx 8rpx;
	border-radius: 8rpx;
}

.video-info {
	flex: 1;
}

.video-title {
	font-size: 26rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 8rpx;
}

.video-desc {
	font-size: 22rpx;
	color: #666;
	display: block;
	margin-bottom: 15rpx;
}

.video-stats {
	display: flex;
	gap: 20rpx;
}

.video-views, .video-date {
	font-size: 20rpx;
	color: #999;
}

.contact-options {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

.contact-item {
	display: flex;
	align-items: center;
	gap: 20rpx;
	padding: 25rpx;
	background: #f8f9fa;
	border-radius: 20rpx;
}

.contact-icon {
	width: 60rpx;
	height: 60rpx;
	background: white;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.contact-info {
	flex: 1;
}

.contact-title {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 5rpx;
}

.contact-desc {
	font-size: 24rpx;
	color: #666;
	display: block;
	margin-bottom: 5rpx;
}

.contact-time {
	font-size: 22rpx;
	color: #999;
	display: block;
}
</style>
