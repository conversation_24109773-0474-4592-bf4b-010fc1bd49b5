<template>
	<view class="container">
		<!-- 自定义导航栏 -->
		<view class="custom-navbar">
			<view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
			<view class="navbar-content">
				<view class="navbar-left" @click="goBack">
					<Icon name="arrow-left-line" size="36rpx" color="#333"></Icon>
					<text class="back-text">返回</text>
				</view>
				<view class="navbar-center">
					<text class="navbar-title">常见问题</text>
				</view>
				<view class="navbar-right"></view>
			</view>
		</view>

		<!-- 搜索框 -->
		<view class="search-section">
			<view class="search-box">
				<Icon name="search-line" size="32rpx" color="#999"></Icon>
				<input class="search-input" v-model="searchKeyword" placeholder="搜索问题" @input="onSearch" />
				<button class="clear-btn" @click="clearSearch" v-if="searchKeyword">
					<Icon name="close-line" size="24rpx" color="#999"></Icon>
				</button>
			</view>
		</view>

		<!-- 热门问题 -->
		<view class="hot-questions" v-if="!searchKeyword">
			<view class="section-header">
				<Icon name="fire-line" size="32rpx" color="#ff8a00"></Icon>
				<text class="section-title">热门问题</text>
			</view>
			<view class="hot-list">
				<view class="hot-item" v-for="(item, index) in hotQuestions" :key="index" @click="viewQuestion(item)">
					<text class="hot-number">{{index + 1}}</text>
					<text class="hot-question">{{item.question}}</text>
					<Icon name="arrow-right-s-line" size="20rpx" color="#999"></Icon>
				</view>
			</view>
		</view>

		<!-- 问题分类 -->
		<view class="category-section" v-if="!searchKeyword">
			<view class="section-header">
				<Icon name="folder-line" size="32rpx" color="#ff8a00"></Icon>
				<text class="section-title">问题分类</text>
			</view>
			<view class="category-grid">
				<view class="category-item" v-for="(category, index) in categories" :key="index" @click="selectCategory(category)">
					<view class="category-icon" :style="{backgroundColor: category.color}">
						<Icon :name="category.icon" size="32rpx" color="white"></Icon>
					</view>
					<text class="category-name">{{category.name}}</text>
					<text class="category-count">{{category.count}}个问题</text>
				</view>
			</view>
		</view>

		<!-- 问题列表 -->
		<view class="questions-section">
			<view class="section-header" v-if="selectedCategory">
				<Icon :name="selectedCategory.icon" size="32rpx" color="#ff8a00"></Icon>
				<text class="section-title">{{selectedCategory.name}}</text>
				<button class="back-btn" @click="backToCategories">
					<Icon name="arrow-left-line" size="20rpx" color="#666"></Icon>
					<text>返回</text>
				</button>
			</view>
			<view class="section-header" v-else-if="searchKeyword">
				<Icon name="search-line" size="32rpx" color="#ff8a00"></Icon>
				<text class="section-title">搜索结果</text>
				<text class="result-count">{{filteredQuestions.length}}个结果</text>
			</view>
			
			<view class="questions-list">
				<view class="question-item" v-for="(question, index) in filteredQuestions" :key="index">
					<view class="question-header" @click="toggleQuestion(index)">
						<text class="question-text">{{question.question}}</text>
						<view class="question-actions">
							<view class="question-tags">
								<text class="question-tag" v-for="tag in question.tags" :key="tag">{{tag}}</text>
							</view>
							<Icon :name="question.expanded ? 'arrow-up-s-line' : 'arrow-down-s-line'" size="24rpx" color="#999"></Icon>
						</view>
					</view>
					
					<view class="question-answer" v-if="question.expanded">
						<text class="answer-text">{{question.answer}}</text>
						
						<view class="answer-attachments" v-if="question.attachments && question.attachments.length > 0">
							<text class="attachments-title">相关资料：</text>
							<view class="attachment-list">
								<view class="attachment-item" v-for="(attachment, aIndex) in question.attachments" :key="aIndex" @click="viewAttachment(attachment)">
									<Icon :name="getAttachmentIcon(attachment.type)" size="24rpx" color="#ff8a00"></Icon>
									<text class="attachment-name">{{attachment.name}}</text>
								</view>
							</view>
						</view>
						
						<view class="answer-footer">
							<view class="helpful-section">
								<text class="helpful-text">这个回答对您有帮助吗？</text>
								<view class="helpful-buttons">
									<button class="helpful-btn yes" :class="{ active: question.helpful === true }" @click="markHelpful(index, true)">
										<Icon name="thumb-up-line" size="20rpx"></Icon>
										<text>有帮助</text>
									</button>
									<button class="helpful-btn no" :class="{ active: question.helpful === false }" @click="markHelpful(index, false)">
										<Icon name="thumb-down-line" size="20rpx"></Icon>
										<text>没帮助</text>
									</button>
								</view>
							</view>
							<button class="contact-btn" @click="contactSupport">
								<Icon name="customer-service-line" size="20rpx" color="#ff8a00"></Icon>
								<text>联系客服</text>
							</button>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 空状态 -->
		<view class="empty-state" v-if="searchKeyword && filteredQuestions.length === 0">
			<Icon name="search-line" size="120rpx" color="#ccc"></Icon>
			<text class="empty-text">没有找到相关问题</text>
			<text class="empty-desc">试试其他关键词或联系客服</text>
			<button class="contact-service-btn" @click="contactSupport">联系客服</button>
		</view>

		<!-- 底部提示 -->
		<view class="bottom-tip">
			<text class="tip-text">没有找到您要的答案？</text>
			<button class="submit-question-btn" @click="submitQuestion">
				<Icon name="add-line" size="24rpx" color="#ff8a00"></Icon>
				<text>提交问题</text>
			</button>
		</view>
	</view>
</template>

<script>
import Icon from '@/components/Icon/Icon.vue'

export default {
	components: {
		Icon
	},
	data() {
		return {
			statusBarHeight: 0, // 状态栏高度
			searchKeyword: '',
			selectedCategory: null,
			hotQuestions: [
				{ id: 1, question: '如何注册账号？', category: 'account' },
				{ id: 2, question: '忘记密码怎么办？', category: 'account' },
				{ id: 3, question: '如何预约服务？', category: 'service' },
				{ id: 4, question: '如何取消预约？', category: 'service' },
				{ id: 5, question: '紧急情况下如何求助？', category: 'emergency' }
			],
			categories: [
				{
					id: 'account',
					name: '账户相关',
					icon: 'user-line',
					color: '#4caf50',
					count: 8
				},
				{
					id: 'service',
					name: '服务预约',
					icon: 'calendar-check-line',
					color: '#2196f3',
					count: 12
				},
				{
					id: 'payment',
					name: '支付问题',
					icon: 'wallet-line',
					color: '#ff9800',
					count: 6
				},
				{
					id: 'emergency',
					name: '紧急求助',
					icon: 'alarm-warning-line',
					color: '#f44336',
					count: 4
				},
				{
					id: 'health',
					name: '健康监测',
					icon: 'heart-pulse-line',
					color: '#9c27b0',
					count: 10
				},
				{
					id: 'technical',
					name: '技术问题',
					icon: 'settings-line',
					color: '#607d8b',
					count: 7
				}
			],
			questions: [
				{
					id: 1,
					category: 'account',
					question: '如何注册账号？',
					answer: '您可以通过以下步骤注册账号：\n1. 打开应用，点击"注册"按钮\n2. 输入手机号码\n3. 获取并输入验证码\n4. 设置登录密码\n5. 完善个人信息\n注册成功后即可使用所有功能。',
					tags: ['注册', '账号'],
					expanded: false,
					helpful: undefined,
					attachments: [
						{ type: 'image', name: '注册流程图.jpg' },
						{ type: 'video', name: '注册教程视频.mp4' }
					]
				},
				{
					id: 2,
					category: 'account',
					question: '忘记密码怎么办？',
					answer: '如果您忘记了密码，可以通过以下方式找回：\n1. 在登录页面点击"忘记密码"\n2. 输入注册时的手机号码\n3. 获取短信验证码\n4. 输入验证码后设置新密码\n如果手机号码已更换，请联系客服协助找回。',
					tags: ['密码', '找回'],
					expanded: false,
					helpful: undefined
				},
				{
					id: 3,
					category: 'service',
					question: '如何预约服务？',
					answer: '预约服务的步骤如下：\n1. 进入"工作台"页面\n2. 选择需要的服务类型\n3. 选择服务时间和地点\n4. 填写服务需求详情\n5. 确认订单并支付\n预约成功后会有专人联系您确认服务详情。',
					tags: ['预约', '服务'],
					expanded: false,
					helpful: undefined
				},
				{
					id: 4,
					category: 'service',
					question: '如何取消预约？',
					answer: '取消预约请按以下步骤操作：\n1. 进入"我的"页面\n2. 点击"我的订单"\n3. 找到要取消的预约\n4. 点击"取消预约"按钮\n注意：部分服务需要提前24小时取消，具体以服务条款为准。',
					tags: ['取消', '预约'],
					expanded: false,
					helpful: undefined
				},
				{
					id: 5,
					category: 'emergency',
					question: '紧急情况下如何求助？',
					answer: '遇到紧急情况时：\n1. 点击首页的"紧急呼叫"按钮\n2. 系统会自动拨打紧急联系人电话\n3. 同时发送位置信息给联系人\n4. 如需要可直接拨打120、110等急救电话\n建议提前设置好紧急联系人信息。',
					tags: ['紧急', '求助'],
					expanded: false,
					helpful: undefined
				}
			]
		}
	},
	computed: {
		filteredQuestions() {
			let questions = this.questions;
			
			// 按分类筛选
			if (this.selectedCategory) {
				questions = questions.filter(q => q.category === this.selectedCategory.id);
			}
			
			// 按搜索关键词筛选
			if (this.searchKeyword) {
				const keyword = this.searchKeyword.toLowerCase();
				questions = questions.filter(q => 
					q.question.toLowerCase().includes(keyword) || 
					q.answer.toLowerCase().includes(keyword) ||
					q.tags.some(tag => tag.toLowerCase().includes(keyword))
				);
			}
			
			return questions;
		}
	},
	methods: {
		onSearch() {
			this.selectedCategory = null;
		},
		clearSearch() {
			this.searchKeyword = '';
		},
		selectCategory(category) {
			this.selectedCategory = category;
			this.searchKeyword = '';
		},
		backToCategories() {
			this.selectedCategory = null;
		},
		viewQuestion(question) {
			const index = this.questions.findIndex(q => q.id === question.id);
			if (index > -1) {
				this.questions[index].expanded = true;
			}
		},
		toggleQuestion(index) {
			this.filteredQuestions[index].expanded = !this.filteredQuestions[index].expanded;
		},
		markHelpful(index, helpful) {
			this.filteredQuestions[index].helpful = helpful;
			uni.showToast({
				title: helpful ? '感谢您的反馈' : '我们会改进内容',
				icon: 'success'
			});
		},
		getAttachmentIcon(type) {
			const iconMap = {
				'image': 'image-line',
				'video': 'video-line',
				'pdf': 'file-pdf-line',
				'doc': 'file-word-line'
			};
			return iconMap[type] || 'file-line';
		},
		viewAttachment(attachment) {
			uni.showToast({
				title: `正在打开${attachment.name}`,
				icon: 'loading'
			});
		},
		contactSupport() {
			uni.navigateTo({
				url: '/pages/help/service'
			});
		},
		submitQuestion() {
			uni.navigateTo({
				url: '/pages/help/submit-question'
			});
		},

		// 返回上一页
		goBack() {
			uni.navigateBack({
				fail: () => {
					uni.switchTab({
						url: '/pages/profile/profile'
					});
				}
			});
		}
	},
	onLoad() {
		// 获取状态栏高度
		const systemInfo = uni.getSystemInfoSync();
		this.statusBarHeight = systemInfo.statusBarHeight || 44;
	}
}
</script>

<style scoped>
.container {
	background: #f8f9fa;
	min-height: 100vh;
}

/* 导航栏样式 */
.custom-navbar {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 1000;
	background: rgba(255, 255, 255, 0.95);
	backdrop-filter: blur(20rpx);
	-webkit-backdrop-filter: blur(20rpx);
	border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);
}

.navbar-content {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 20rpx 32rpx;
	min-height: 88rpx;
}

.navbar-left {
	display: flex;
	align-items: center;
	gap: 12rpx;
	padding: 8rpx 16rpx 8rpx 0;
	border-radius: 20rpx;
	transition: all 0.2s ease;
}

.navbar-left:active {
	background-color: rgba(0, 0, 0, 0.04);
	transform: scale(0.96);
}

.back-text {
	font-size: 32rpx;
	color: #333;
	font-weight: 500;
}

.navbar-center {
	position: absolute;
	left: 50%;
	transform: translateX(-50%);
}

.navbar-title {
	font-size: 34rpx;
	font-weight: 600;
	color: #333;
}

.navbar-right {
	display: flex;
	align-items: center;
}

.search-section {
	background: white;
	padding: 40rpx;
	margin-top: 220rpx; /* 为导航栏留出空间 */
	box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.search-box {
	display: flex;
	align-items: center;
	gap: 15rpx;
	background: #f8f9fa;
	border-radius: 25rpx;
	padding: 20rpx 30rpx;
}

.search-input {
	flex: 1;
	font-size: 28rpx;
	color: #333;
}

.clear-btn {
	background: none;
	border: none;
	padding: 5rpx;
}

.hot-questions, .category-section, .questions-section {
	background: white;
	margin: 20rpx;
	border-radius: 30rpx;
	padding: 40rpx;
}

.section-header {
	display: flex;
	align-items: center;
	gap: 15rpx;
	margin-bottom: 30rpx;
}

.section-title {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
	flex: 1;
}

.result-count {
	font-size: 24rpx;
	color: #666;
}

.back-btn {
	display: flex;
	align-items: center;
	gap: 8rpx;
	background: #f8f9fa;
	border: none;
	border-radius: 15rpx;
	padding: 10rpx 20rpx;
	font-size: 24rpx;
	color: #666;
}

.hot-list {
	display: flex;
	flex-direction: column;
	gap: 15rpx;
}

.hot-item {
	display: flex;
	align-items: center;
	gap: 20rpx;
	padding: 20rpx;
	background: #f8f9fa;
	border-radius: 15rpx;
}

.hot-number {
	width: 40rpx;
	height: 40rpx;
	background: #ff8a00;
	color: white;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 22rpx;
	font-weight: bold;
}

.hot-question {
	flex: 1;
	font-size: 26rpx;
	color: #333;
}

.category-grid {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	gap: 20rpx;
}

.category-item {
	text-align: center;
	padding: 30rpx;
	background: #f8f9fa;
	border-radius: 20rpx;
}

.category-icon {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	margin: 0 auto 20rpx;
}

.category-name {
	font-size: 26rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 8rpx;
}

.category-count {
	font-size: 22rpx;
	color: #666;
	display: block;
}

.questions-list {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

.question-item {
	border: 1rpx solid #f0f0f0;
	border-radius: 20rpx;
	overflow: hidden;
}

.question-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 25rpx 30rpx;
	background: #f8f9fa;
}

.question-text {
	font-size: 26rpx;
	color: #333;
	font-weight: 500;
	flex: 1;
	margin-right: 20rpx;
}

.question-actions {
	display: flex;
	align-items: center;
	gap: 15rpx;
}

.question-tags {
	display: flex;
	gap: 8rpx;
}

.question-tag {
	background: rgba(255, 138, 0, 0.1);
	color: #ff8a00;
	font-size: 20rpx;
	padding: 4rpx 10rpx;
	border-radius: 10rpx;
}

.question-answer {
	padding: 25rpx 30rpx;
	background: white;
}

.answer-text {
	font-size: 24rpx;
	color: #666;
	line-height: 1.6;
	display: block;
	margin-bottom: 20rpx;
	white-space: pre-line;
}

.answer-attachments {
	margin-bottom: 20rpx;
}

.attachments-title {
	font-size: 22rpx;
	color: #666;
	display: block;
	margin-bottom: 15rpx;
}

.attachment-list {
	display: flex;
	flex-direction: column;
	gap: 10rpx;
}

.attachment-item {
	display: flex;
	align-items: center;
	gap: 10rpx;
	padding: 15rpx;
	background: #f8f9fa;
	border-radius: 10rpx;
}

.attachment-name {
	font-size: 22rpx;
	color: #333;
}

.answer-footer {
	border-top: 1rpx solid #f0f0f0;
	padding-top: 20rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.helpful-section {
	flex: 1;
}

.helpful-text {
	font-size: 22rpx;
	color: #666;
	display: block;
	margin-bottom: 15rpx;
}

.helpful-buttons {
	display: flex;
	gap: 15rpx;
}

.helpful-btn {
	display: flex;
	align-items: center;
	gap: 8rpx;
	padding: 10rpx 20rpx;
	background: #f8f9fa;
	border: 1rpx solid #e0e0e0;
	border-radius: 20rpx;
	font-size: 22rpx;
	color: #666;
}

.helpful-btn.active.yes {
	background: rgba(76, 175, 80, 0.1);
	border-color: #4caf50;
	color: #4caf50;
}

.helpful-btn.active.no {
	background: rgba(244, 67, 54, 0.1);
	border-color: #f44336;
	color: #f44336;
}

.contact-btn {
	display: flex;
	align-items: center;
	gap: 8rpx;
	background: rgba(255, 138, 0, 0.1);
	border: 1rpx solid #ff8a00;
	border-radius: 20rpx;
	padding: 15rpx 25rpx;
	font-size: 22rpx;
	color: #ff8a00;
}

.empty-state {
	text-align: center;
	padding: 120rpx 40rpx;
}

.empty-text {
	font-size: 28rpx;
	color: #999;
	display: block;
	margin: 30rpx 0 15rpx;
}

.empty-desc {
	font-size: 24rpx;
	color: #ccc;
	display: block;
	margin-bottom: 40rpx;
}

.contact-service-btn {
	background: #ff8a00;
	color: white;
	border: none;
	border-radius: 25rpx;
	padding: 20rpx 40rpx;
	font-size: 28rpx;
}

.bottom-tip {
	background: white;
	margin: 20rpx;
	border-radius: 30rpx;
	padding: 40rpx;
	text-align: center;
}

.tip-text {
	font-size: 26rpx;
	color: #666;
	display: block;
	margin-bottom: 20rpx;
}

.submit-question-btn {
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 10rpx;
	background: rgba(255, 138, 0, 0.1);
	border: 2rpx solid #ff8a00;
	border-radius: 25rpx;
	padding: 20rpx 40rpx;
	font-size: 26rpx;
	color: #ff8a00;
}
</style>
