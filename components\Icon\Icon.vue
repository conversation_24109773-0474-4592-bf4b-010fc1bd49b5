<template>
	<!-- SVG图标 -->
	<image
		v-if="iconType === 'svg'"
		:src="iconSrc"
		class="icon icon-svg"
		:class="[
			{ 'icon-primary': primary },
			{ 'icon-secondary': secondary },
			{ 'icon-institution': institution },
			{ 'icon-service': service },
			{ 'icon-elderly': elderly },
			{ 'icon-success': success },
			{ 'icon-warning': warning },
			{ 'icon-error': error },
			{ 'icon-info': info },
			{ 'disabled': disabled },
			{ 'loading': loading },
			customClass
		]"
		:style="iconStyle"
		@click="handleClick"
		mode="aspectFit"
	/>

	<!-- 图片图标 -->
	<image
		v-else-if="iconType === 'image'"
		:src="iconSrc"
		class="icon icon-image"
		:class="[
			{ 'icon-primary': primary },
			{ 'icon-secondary': secondary },
			{ 'icon-institution': institution },
			{ 'icon-service': service },
			{ 'icon-elderly': elderly },
			{ 'icon-success': success },
			{ 'icon-warning': warning },
			{ 'icon-error': error },
			{ 'icon-info': info },
			{ 'disabled': disabled },
			{ 'loading': loading },
			customClass
		]"
		:style="iconStyle"
		@click="handleClick"
		mode="aspectFit"
	/>

	<!-- Emoji图标 -->
	<text
		v-else
		class="icon icon-emoji"
		:class="[
			{ 'icon-primary': primary },
			{ 'icon-secondary': secondary },
			{ 'icon-institution': institution },
			{ 'icon-service': service },
			{ 'icon-elderly': elderly },
			{ 'icon-success': success },
			{ 'icon-warning': warning },
			{ 'icon-error': error },
			{ 'icon-info': info },
			{ 'disabled': disabled },
			{ 'loading': loading },
			customClass
		]"
		:style="iconStyle"
		@click="handleClick"
	>
		{{ iconText }}
	</text>
</template>

<script>
import { getIconEmoji, hasIcon, getIconConfig } from '@/utils/iconConfig.js'
import { getCompatibleIconName } from '@/utils/iconCompatibility.js'

export default {
	name: 'Icon',
	props: {
		name: {
			type: String,
			required: true
		},
		size: {
			type: String,
			default: '32rpx'
		},
		color: {
			type: String,
			default: ''
		},
		// 主题色快捷方式
		primary: {
			type: Boolean,
			default: false
		},
		secondary: {
			type: Boolean,
			default: false
		},
		institution: {
			type: Boolean,
			default: false
		},
		service: {
			type: Boolean,
			default: false
		},
		elderly: {
			type: Boolean,
			default: false
		},
		// 新增语义化主题色
		success: {
			type: Boolean,
			default: false
		},
		warning: {
			type: Boolean,
			default: false
		},
		error: {
			type: Boolean,
			default: false
		},
		info: {
			type: Boolean,
			default: false
		},
		// 状态控制
		disabled: {
			type: Boolean,
			default: false
		},
		loading: {
			type: Boolean,
			default: false
		},
		customClass: {
			type: String,
			default: ''
		},
		// 图标类型：auto(自动检测)、emoji、svg、image
		type: {
			type: String,
			default: 'auto',
			validator: value => ['auto', 'emoji', 'svg', 'image'].includes(value)
		},
		// 自定义图标路径（当type为svg或image时使用）
		src: {
			type: String,
			default: ''
		}
	},
	computed: {
		// 兼容的图标名称
		compatibleIconName() {
			return getCompatibleIconName(this.name)
		},

		// 图标类型判断
		iconType() {
			// 如果指定了类型，直接使用
			if (this.type !== 'auto') {
				return this.type
			}

			// 如果提供了src，判断是SVG还是图片
			if (this.src) {
				return this.src.toLowerCase().endsWith('.svg') ? 'svg' : 'image'
			}

			// 检查是否有对应的SVG文件
			const svgPath = `/static/icons/${this.compatibleIconName}.svg`
			// 注意：在实际使用中，这里可能需要异步检查文件是否存在
			// 暂时默认为emoji类型
			return 'emoji'
		},

		// 图标源路径
		iconSrc() {
			if (this.src) {
				return this.src
			}

			// 检查配置文件中是否有SVG路径（使用兼容的图标名称）
			if (hasIcon(this.compatibleIconName)) {
				const config = getIconConfig(this.compatibleIconName)
				if (config.svg) {
					return config.svg
				}
			}

			if (this.iconType === 'svg') {
				return `/static/icons/${this.compatibleIconName}.svg`
			}

			if (this.iconType === 'image') {
				return `/static/icons/${this.compatibleIconName}.png`
			}

			return ''
		},

		// Emoji图标文本
		iconText() {
			// 优先使用配置文件中的图标（使用兼容的图标名称）
			if (hasIcon(this.compatibleIconName)) {
				return getIconEmoji(this.compatibleIconName)
			}

			// 兼容旧的图标映射（逐步迁移）
			const legacyIconMap = {
				// 导航图标 - 统一使用location-line替代map-pin-line
				'map-pin-line': '📍', // 建议使用location-line
				'notification-3-line': '🔔',
				'dashboard-line': '📊',

				// 紧急服务图标
				'parent-line': '👨‍👩‍👧‍👦',

				// 登录页面图标
				'lock-password-line': '🔒',
				'wechat-line': '💬',
				'user-smile-line': '😊',
				'fingerprint-line': '👆',

				// 扩展图标（未在配置文件中的）
				'filter-line': '🔽',
				'sort-line': '📶',
				'question-line': '❓',
				'success-line': '✅',
				'video-line': '🎥',
				'music-line': '🎵',
				'wifi-line': '📶',
				'bluetooth-line': '📶',
				'signal-line': '📶',
				'car-line': '🚗',
				'bus-line': '🚌',
				'train-line': '🚆',
				'plane-line': '✈️',
				'ship-line': '🚢',
				'bike-line': '🚲',
				'walk-line': '🚶',
				'bookmark-line': '🔖',
				'flag-line': '🚩',
				'shopping-cart-line': '🛒',
				'wallet-line': '👛',
				'bank-card-line': '💳',
				'coin-line': '🪙',
				'medicine-bottle-line': '💊',
				'stethoscope-line': '🩺',
				'thermometer-line': '🌡️',
				'syringe-line': '💉',
				'cake-line': '🍰',
				'coffee-line': '☕',
				'bowl-line': '🍜',
				'apple-line': '🍎',
				'sun-line': '☀️',
				'moon-line': '🌙',
				'cloud-line': '☁️',
				'rain-line': '🌧️',
				'snow-line': '❄️'
			}

			return legacyIconMap[this.name] || '⚙️' // 使用设置图标代替问号，表示需要配置
		},

		// 图标样式
		iconStyle() {
			const style = {}

			// 设置尺寸
			if (this.iconType === 'emoji') {
				style.fontSize = this.size
			} else {
				style.width = this.size
				style.height = this.size
			}

			// 设置颜色（仅对emoji有效）
			if (this.color && this.iconType === 'emoji') {
				style.color = this.color
			}

			return style
		}
	},
	methods: {
		handleClick(e) {
			// 禁用状态下不触发点击事件
			if (this.disabled || this.loading) {
				e.preventDefault()
				e.stopPropagation()
				return
			}
			this.$emit('click', e)
		}
	}
}
</script>

<style scoped>
/* 基础图标样式 - iOS风格优化 */
.icon {
	display: inline-block;
	text-align: center;
	line-height: 1;
	vertical-align: middle;
	user-select: none;
	cursor: pointer;
	transition: all 0.25s cubic-bezier(0.25, 0.46, 0.45, 0.94); /* iOS标准缓动 */
	border-radius: var(--radius-xs, 6rpx); /* 添加圆角 */
	position: relative;
	overflow: hidden;
}

/* Emoji图标样式 - 增强版 */
.icon-emoji {
	font-family: "Apple Color Emoji", "Segoe UI Emoji", "Noto Color Emoji", "Segoe UI Symbol", sans-serif;
	font-feature-settings: "liga" off; /* 禁用连字，确保emoji显示正确 */
	text-rendering: optimizeQuality;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

/* SVG和图片图标样式 - 增强版 */
.icon-svg,
.icon-image {
	object-fit: contain;
	border-radius: inherit;
	filter: drop-shadow(0 1rpx 2rpx rgba(0, 0, 0, 0.1)); /* 添加微妙阴影 */
}

/* 主题色样式 - 使用CSS变量 */
.icon-primary {
	color: var(--primary-color, #ff8a00);
}

.icon-secondary {
	color: var(--text-secondary, #666666);
}

.icon-institution {
	color: #ff6b6b;
}

.icon-service {
	color: #4ecdc4;
}

.icon-elderly {
	color: #96ceb4;
}

/* 新增语义化主题色 */
.icon-success {
	color: var(--success-color, #34c759);
}

.icon-warning {
	color: var(--warning-color, #ff9500);
}

.icon-error {
	color: var(--error-color, #ff3b30);
}

.icon-info {
	color: var(--info-color, #007aff);
}

/* iOS风格交互效果 - 增强版 */
.icon:hover {
	transform: scale(1.05); /* 减小悬停缩放，更符合iOS风格 */
	opacity: 0.85;
	filter: brightness(1.1); /* 添加亮度提升 */
}

/* iOS风格按压效果 */
.icon:active {
	transform: scale(0.95);
	opacity: 0.7;
	transition: all 0.1s cubic-bezier(0.25, 0.46, 0.45, 0.94); /* 快速按压反馈 */
}

/* 焦点状态 - 增强可访问性 */
.icon:focus {
	outline: none;
	box-shadow: var(--shadow-focus, 0 0 0 4rpx rgba(255, 138, 0, 0.25));
	border-radius: var(--radius-sm, 8rpx);
}

/* 禁用状态 - 增强版 */
.icon.disabled {
	opacity: 0.4;
	cursor: not-allowed;
	pointer-events: none;
	filter: grayscale(0.5); /* 添加灰度效果 */
}

/* 加载状态 - iOS风格 */
.icon.loading {
	animation: icon-spin 1s linear infinite;
	opacity: 0.7;
}

@keyframes icon-spin {
	from {
		transform: rotate(0deg);
	}
	to {
		transform: rotate(360deg);
	}
}

/* 尺寸变体 */
.icon-xs { font-size: 20rpx; width: 20rpx; height: 20rpx; }
.icon-sm { font-size: 24rpx; width: 24rpx; height: 24rpx; }
.icon-md { font-size: 32rpx; width: 32rpx; height: 32rpx; }
.icon-lg { font-size: 40rpx; width: 40rpx; height: 40rpx; }
.icon-xl { font-size: 48rpx; width: 48rpx; height: 48rpx; }
.icon-2xl { font-size: 56rpx; width: 56rpx; height: 56rpx; }

/* 适老化增强 */
.elderly-mode .icon,
.ios-elderly-mode .icon {
	transform: scale(1.2) !important; /* 图标放大 */
	filter: contrast(1.3) brightness(1.1) !important; /* 增强对比度和亮度 */
	border: 1rpx solid rgba(0, 0, 0, 0.1) !important; /* 添加边框增强可见性 */
	border-radius: var(--radius-md, 12rpx) !important;
	padding: 4rpx !important; /* 增加内边距 */
}

.elderly-mode .icon:hover,
.ios-elderly-mode .icon:hover {
	transform: scale(1.25) !important; /* 悬停时进一步放大 */
	box-shadow: var(--shadow-md, 0 4rpx 16rpx rgba(0, 0, 0, 0.15)) !important;
}

.elderly-mode .icon:active,
.ios-elderly-mode .icon:active {
	transform: scale(1.15) !important; /* 按压时仍保持放大 */
}

/* 响应式优化 */
@media (max-width: 750rpx) {
	.icon {
		transform-origin: center;
		min-width: 44rpx; /* 确保最小触摸目标 */
		min-height: 44rpx;
	}

	/* 小屏设备上的图标尺寸调整 */
	.icon-xs { font-size: 18rpx; width: 18rpx; height: 18rpx; }
	.icon-sm { font-size: 22rpx; width: 22rpx; height: 22rpx; }
	.icon-md { font-size: 28rpx; width: 28rpx; height: 28rpx; }
	.icon-lg { font-size: 36rpx; width: 36rpx; height: 36rpx; }
	.icon-xl { font-size: 44rpx; width: 44rpx; height: 44rpx; }
	.icon-2xl { font-size: 52rpx; width: 52rpx; height: 52rpx; }
}

@media (min-width: 768px) {
	/* iPad等大屏设备优化 */
	.icon:hover {
		transform: scale(1.08); /* 大屏设备上稍微增大悬停效果 */
	}
}
</style>
