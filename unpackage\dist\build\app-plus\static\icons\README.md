# 图标资源说明

这个目录存放项目中使用的图标文件。

## 图标规格
- 格式：PNG（推荐）或SVG
- 尺寸：建议32x32px、48x48px、64x64px等标准尺寸
- 背景：透明
- 颜色：单色图标，支持CSS着色

## 图标分类

### 导航图标
- home.png - 首页
- user.png - 用户
- map.png - 地图
- search.png - 搜索
- menu.png - 菜单

### 功能图标
- building.png - 建筑/机构
- heart.png - 关爱/健康
- money.png - 金钱/补贴
- settings.png - 设置
- notification.png - 通知

### 操作图标
- add.png - 添加
- edit.png - 编辑
- delete.png - 删除
- share.png - 分享
- download.png - 下载

### 状态图标
- success.png - 成功
- warning.png - 警告
- error.png - 错误
- info.png - 信息

## 使用方法

在Icon组件中使用：
```vue
<Icon name="home" size="32rpx" color="#ff8a00" />
```

## 注意事项

1. 图标文件名应与Icon组件中的name属性对应
2. 建议使用统一的设计风格
3. 确保图标在不同背景下都清晰可见
4. 考虑适老化需求，图标应简洁明了
