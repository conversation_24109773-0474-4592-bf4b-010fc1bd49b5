<template>
	<view class="container">
		<!-- 任务基本信息 -->
		<view class="task-info-card">
			<view class="task-header">
				<view class="task-priority" :class="taskDetail.priority">
					<text class="priority-text">{{getPriorityText(taskDetail.priority)}}</text>
				</view>
				<view class="task-status" :class="taskDetail.status">
					<text class="status-text">{{getStatusText(taskDetail.status)}}</text>
				</view>
			</view>
			
			<text class="task-title">{{taskDetail.title}}</text>
			<text class="task-description">{{taskDetail.description}}</text>
			
			<view class="task-meta">
				<view class="meta-item">
					<Icon name="time-line" size="24rpx" color="#666"></Icon>
					<text class="meta-label">截止时间</text>
					<text class="meta-value">{{taskDetail.deadline}}</text>
				</view>
				<view class="meta-item" v-if="taskDetail.location">
					<Icon name="location-line" size="24rpx" color="#666"></Icon>
					<text class="meta-label">地点</text>
					<text class="meta-value">{{taskDetail.location}}</text>
				</view>
				<view class="meta-item" v-if="taskDetail.reminder">
					<Icon name="notification-3-line" size="24rpx" color="#666"></Icon>
					<text class="meta-label">提醒</text>
					<text class="meta-value">{{taskDetail.reminder}}</text>
				</view>
			</view>
			
			<view class="task-tags" v-if="taskDetail.tags && taskDetail.tags.length > 0">
				<text class="task-tag" v-for="tag in taskDetail.tags" :key="tag">{{tag}}</text>
			</view>
		</view>

		<!-- 任务进度 -->
		<view class="progress-section" v-if="taskDetail.subtasks && taskDetail.subtasks.length > 0">
			<view class="section-header">
				<text class="section-title">任务进度</text>
				<text class="progress-text">{{completedSubtasks}}/{{taskDetail.subtasks.length}}</text>
			</view>
			<view class="progress-bar">
				<view class="progress-fill" :style="{width: progressPercentage + '%'}"></view>
			</view>
			<view class="subtask-list">
				<view class="subtask-item" v-for="(subtask, index) in taskDetail.subtasks" :key="index" @click="toggleSubtask(index)">
					<view class="subtask-checkbox" :class="{ checked: subtask.completed }">
						<Icon name="check-line" size="20rpx" color="white" v-if="subtask.completed"></Icon>
					</view>
					<text class="subtask-text" :class="{ completed: subtask.completed }">{{subtask.title}}</text>
				</view>
			</view>
		</view>

		<!-- 任务备注 -->
		<view class="notes-section">
			<view class="section-header">
				<text class="section-title">任务备注</text>
				<text class="add-note" @click="addNote">添加备注</text>
			</view>
			<view class="notes-list" v-if="taskDetail.notes && taskDetail.notes.length > 0">
				<view class="note-item" v-for="(note, index) in taskDetail.notes" :key="index">
					<view class="note-header">
						<text class="note-time">{{note.time}}</text>
						<Icon name="delete-line" size="20rpx" color="#999" @click="deleteNote(index)"></Icon>
					</view>
					<text class="note-content">{{note.content}}</text>
				</view>
			</view>
			<view class="no-notes" v-else>
				<Icon name="file-line" size="60rpx" color="#ccc"></Icon>
				<text class="no-notes-text">暂无备注</text>
			</view>
		</view>

		<!-- 相关文件 -->
		<view class="files-section" v-if="taskDetail.files && taskDetail.files.length > 0">
			<view class="section-header">
				<text class="section-title">相关文件</text>
				<text class="add-file" @click="addFile">添加文件</text>
			</view>
			<view class="files-list">
				<view class="file-item" v-for="(file, index) in taskDetail.files" :key="index" @click="viewFile(file)">
					<view class="file-icon">
						<Icon :name="getFileIcon(file.type)" size="32rpx" color="#ff8a00"></Icon>
					</view>
					<view class="file-info">
						<text class="file-name">{{file.name}}</text>
						<text class="file-size">{{file.size}}</text>
					</view>
					<Icon name="download-line" size="24rpx" color="#999"></Icon>
				</view>
			</view>
		</view>

		<!-- 任务历史 -->
		<view class="history-section">
			<view class="section-header">
				<text class="section-title">任务历史</text>
			</view>
			<view class="history-timeline">
				<view class="timeline-item" v-for="(history, index) in taskDetail.history" :key="index">
					<view class="timeline-dot" :class="history.type"></view>
					<view class="timeline-content">
						<text class="timeline-action">{{history.action}}</text>
						<text class="timeline-time">{{history.time}}</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 底部操作 -->
		<view class="bottom-actions">
			<button class="action-btn secondary" @click="editTask" v-if="taskDetail.status === 'pending'">
				<Icon name="edit-line" size="32rpx" color="#ff8a00"></Icon>
				<text>编辑</text>
			</button>
			<button class="action-btn primary" @click="completeTask" v-if="taskDetail.status === 'pending'">
				<Icon name="check-line" size="32rpx" color="white"></Icon>
				<text>完成任务</text>
			</button>
			<button class="action-btn primary" @click="restartTask" v-if="taskDetail.status === 'completed'">
				<Icon name="refresh-line" size="32rpx" color="white"></Icon>
				<text>重新开始</text>
			</button>
		</view>
	</view>
</template>

<script>
import Icon from '@/components/Icon/Icon.vue'

export default {
	components: {
		Icon
	},
	data() {
		return {
			taskDetail: {
				id: 1,
				title: '体检预约',
				description: '到社区医院进行年度体检，包括血常规、心电图、B超等常规检查项目。',
				deadline: '2024-01-20 09:00',
				location: '社区医院',
				reminder: '提前1小时',
				priority: 'high',
				status: 'pending',
				tags: ['健康', '预约', '重要'],
				subtasks: [
					{ title: '预约挂号', completed: true },
					{ title: '准备身份证', completed: true },
					{ title: '空腹准备', completed: false },
					{ title: '按时到达', completed: false }
				],
				notes: [
					{
						time: '2024-01-18 14:30',
						content: '已联系医院确认预约时间，需要空腹8小时以上。'
					},
					{
						time: '2024-01-17 16:20',
						content: '记得带上去年的体检报告作为对比。'
					}
				],
				files: [
					{
						name: '体检须知.pdf',
						size: '2.3MB',
						type: 'pdf'
					},
					{
						name: '去年体检报告.jpg',
						size: '1.8MB',
						type: 'image'
					}
				],
				history: [
					{
						action: '创建任务',
						time: '2024-01-15 10:30',
						type: 'create'
					},
					{
						action: '添加备注',
						time: '2024-01-17 16:20',
						type: 'note'
					},
					{
						action: '完成子任务：预约挂号',
						time: '2024-01-18 09:15',
						type: 'progress'
					}
				]
			}
		}
	},
	computed: {
		completedSubtasks() {
			return this.taskDetail.subtasks ? this.taskDetail.subtasks.filter(task => task.completed).length : 0;
		},
		progressPercentage() {
			if (!this.taskDetail.subtasks || this.taskDetail.subtasks.length === 0) return 0;
			return Math.round((this.completedSubtasks / this.taskDetail.subtasks.length) * 100);
		}
	},
	onLoad(options) {
		if (options.id) {
			this.loadTaskDetail(options.id);
		}
	},
	methods: {
		loadTaskDetail(id) {
			// 根据ID加载任务详情
			console.log('加载任务详情:', id);
		},
		getPriorityText(priority) {
			const priorityMap = {
				'high': '高',
				'medium': '中',
				'low': '低'
			};
			return priorityMap[priority] || '中';
		},
		getStatusText(status) {
			const statusMap = {
				'pending': '待完成',
				'completed': '已完成',
				'overdue': '已逾期'
			};
			return statusMap[status] || '未知';
		},
		toggleSubtask(index) {
			this.taskDetail.subtasks[index].completed = !this.taskDetail.subtasks[index].completed;
			// 更新任务历史
			const action = this.taskDetail.subtasks[index].completed ? '完成' : '取消完成';
			this.taskDetail.history.unshift({
				action: `${action}子任务：${this.taskDetail.subtasks[index].title}`,
				time: new Date().toLocaleString(),
				type: 'progress'
			});
		},
		addNote() {
			uni.showModal({
				title: '添加备注',
				editable: true,
				placeholderText: '请输入备注内容',
				success: (res) => {
					if (res.confirm && res.content) {
						this.taskDetail.notes.unshift({
							time: new Date().toLocaleString(),
							content: res.content
						});
					}
				}
			});
		},
		deleteNote(index) {
			uni.showModal({
				title: '确认删除',
				content: '确定要删除这条备注吗？',
				success: (res) => {
					if (res.confirm) {
						this.taskDetail.notes.splice(index, 1);
					}
				}
			});
		},
		addFile() {
			uni.chooseFile({
				count: 1,
				success: (res) => {
					console.log('选择文件:', res);
					// 处理文件上传
				}
			});
		},
		viewFile(file) {
			console.log('查看文件:', file);
		},
		getFileIcon(type) {
			const iconMap = {
				'pdf': 'file-pdf-line',
				'image': 'image-line',
				'doc': 'file-word-line',
				'excel': 'file-excel-line'
			};
			return iconMap[type] || 'file-line';
		},
		editTask() {
			uni.navigateTo({
				url: `/pages/task/manage?id=${this.taskDetail.id}&mode=edit`
			});
		},
		completeTask() {
			uni.showModal({
				title: '确认完成',
				content: '确定要完成这个任务吗？',
				success: (res) => {
					if (res.confirm) {
						this.taskDetail.status = 'completed';
						this.taskDetail.history.unshift({
							action: '完成任务',
							time: new Date().toLocaleString(),
							type: 'complete'
						});
						uni.showToast({
							title: '任务已完成',
							icon: 'success'
						});
					}
				}
			});
		},
		restartTask() {
			uni.showModal({
				title: '确认重新开始',
				content: '确定要重新开始这个任务吗？',
				success: (res) => {
					if (res.confirm) {
						this.taskDetail.status = 'pending';
						this.taskDetail.history.unshift({
							action: '重新开始任务',
							time: new Date().toLocaleString(),
							type: 'restart'
						});
						uni.showToast({
							title: '任务已重新开始',
							icon: 'success'
						});
					}
				}
			});
		}
	}
}
</script>

<style scoped>
.container {
	background: #f8f9fa;
	min-height: 100vh;
	padding-bottom: 200rpx;
}

.task-info-card, .progress-section, .notes-section, .files-section, .history-section {
	background: white;
	margin: 40rpx;
	border-radius: 30rpx;
	padding: 40rpx;
	box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.task-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 30rpx;
}

.task-priority, .task-status {
	padding: 8rpx 20rpx;
	border-radius: 20rpx;
	font-size: 22rpx;
}

.task-priority.high {
	background: rgba(244, 67, 54, 0.1);
	color: #f44336;
}

.task-priority.medium {
	background: rgba(255, 152, 0, 0.1);
	color: #ff9800;
}

.task-priority.low {
	background: rgba(76, 175, 80, 0.1);
	color: #4caf50;
}

.task-status.pending {
	background: rgba(255, 152, 0, 0.1);
	color: #ff9800;
}

.task-status.completed {
	background: rgba(76, 175, 80, 0.1);
	color: #4caf50;
}

.task-title {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 20rpx;
}

.task-description {
	font-size: 28rpx;
	color: #666;
	line-height: 1.6;
	display: block;
	margin-bottom: 30rpx;
}

.task-meta {
	display: flex;
	flex-direction: column;
	gap: 15rpx;
	margin-bottom: 30rpx;
}

.meta-item {
	display: flex;
	align-items: center;
	gap: 15rpx;
}

.meta-label {
	font-size: 26rpx;
	color: #666;
	min-width: 120rpx;
}

.meta-value {
	font-size: 26rpx;
	color: #333;
	flex: 1;
}

.task-tags {
	display: flex;
	gap: 10rpx;
	flex-wrap: wrap;
}

.task-tag {
	background: rgba(255, 138, 0, 0.1);
	color: #ff8a00;
	font-size: 22rpx;
	padding: 5rpx 15rpx;
	border-radius: 15rpx;
}

.section-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 30rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.progress-text {
	font-size: 26rpx;
	color: #ff8a00;
	font-weight: bold;
}

.add-note, .add-file {
	font-size: 26rpx;
	color: #ff8a00;
}

.progress-bar {
	height: 12rpx;
	background: #f0f0f0;
	border-radius: 6rpx;
	margin-bottom: 30rpx;
	overflow: hidden;
}

.progress-fill {
	height: 100%;
	background: linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%);
	border-radius: 6rpx;
	transition: width 0.3s ease;
}

.subtask-list {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

.subtask-item {
	display: flex;
	align-items: center;
	gap: 20rpx;
	padding: 15rpx 0;
}

.subtask-checkbox {
	width: 40rpx;
	height: 40rpx;
	border: 2rpx solid #ddd;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
}

.subtask-checkbox.checked {
	background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);
	border-color: #4caf50;
}

.subtask-text {
	font-size: 28rpx;
	color: #333;
	flex: 1;
}

.subtask-text.completed {
	text-decoration: line-through;
	color: #999;
}

.notes-list, .files-list {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

.note-item {
	padding: 20rpx;
	background: #f8f9fa;
	border-radius: 20rpx;
}

.note-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 10rpx;
}

.note-time {
	font-size: 22rpx;
	color: #999;
}

.note-content {
	font-size: 26rpx;
	color: #333;
	line-height: 1.5;
	display: block;
}

.file-item {
	display: flex;
	align-items: center;
	gap: 20rpx;
	padding: 20rpx;
	background: #f8f9fa;
	border-radius: 20rpx;
}

.file-icon {
	width: 60rpx;
	height: 60rpx;
	background: rgba(255, 138, 0, 0.1);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
}

.file-info {
	flex: 1;
}

.file-name {
	font-size: 28rpx;
	color: #333;
	display: block;
	margin-bottom: 5rpx;
}

.file-size {
	font-size: 22rpx;
	color: #999;
	display: block;
}

.no-notes {
	text-align: center;
	padding: 60rpx 0;
}

.no-notes-text {
	font-size: 26rpx;
	color: #999;
	display: block;
	margin-top: 20rpx;
}

.history-timeline {
	position: relative;
	padding-left: 40rpx;
}

.timeline-item {
	position: relative;
	padding-bottom: 30rpx;
}

.timeline-item:not(:last-child)::after {
	content: '';
	position: absolute;
	left: -32rpx;
	top: 40rpx;
	width: 2rpx;
	height: calc(100% - 20rpx);
	background: #f0f0f0;
}

.timeline-dot {
	position: absolute;
	left: -40rpx;
	top: 8rpx;
	width: 16rpx;
	height: 16rpx;
	border-radius: 50%;
	background: #ff8a00;
}

.timeline-dot.create {
	background: #4caf50;
}

.timeline-dot.progress {
	background: #2196f3;
}

.timeline-dot.complete {
	background: #ff8a00;
}

.timeline-content {
	padding-left: 20rpx;
}

.timeline-action {
	font-size: 28rpx;
	color: #333;
	display: block;
	margin-bottom: 5rpx;
}

.timeline-time {
	font-size: 22rpx;
	color: #999;
	display: block;
}

.bottom-actions {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background: white;
	padding: 30rpx 40rpx;
	display: flex;
	gap: 20rpx;
	box-shadow: 0 -4rpx 20rpx rgba(0,0,0,0.1);
}

.action-btn {
	flex: 1;
	height: 100rpx;
	border-radius: 20rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 15rpx;
	font-size: 32rpx;
	font-weight: 500;
}

.action-btn.primary {
	background: linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%);
	color: white;
	border: none;
}

.action-btn.secondary {
	background: white;
	color: #ff8a00;
	border: 2rpx solid #ff8a00;
}
</style>
