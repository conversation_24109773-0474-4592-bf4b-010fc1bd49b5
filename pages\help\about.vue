<template>
	<view class="container" :class="{ 'elderly-mode': isElderlyMode }">
		<!-- 自定义导航栏 -->
		<view class="custom-navbar" :class="{ 'elderly-mode': isElderlyMode }">
			<view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
			<view class="navbar-content">
				<view class="navbar-left" @click="goBack">
					<Icon
						name="arrow-left-line"
						:size="isElderlyMode ? '40rpx' : '36rpx'"
						color="#333"
					></Icon>
					<text class="back-text">返回</text>
				</view>
				<view class="navbar-center">
					<text class="navbar-title">关于我们</text>
				</view>
				<view class="navbar-right"></view>
			</view>
		</view>

		<!-- 内容区域 -->
		<view class="content">
			<!-- 应用信息 -->
			<view class="app-info">
				<view class="app-logo">
					<image src="/static/logo.png" class="logo-image" mode="aspectFit"></image>
				</view>
				<text class="app-name">智慧养老</text>
				<text class="app-slogan">让养老更智慧，让生活更美好</text>
				<text class="app-version">版本 v1.2.0</text>
			</view>

			<!-- 公司介绍 -->
			<view class="section">
				<view class="section-header">
					<Icon name="building-line" size="32rpx" color="#ff8a00"></Icon>
					<text class="section-title">公司介绍</text>
				</view>
				<view class="company-intro">
					<text class="intro-text">
						智慧养老科技有限公司成立于2020年，专注于为老年人提供智能化、人性化的养老服务解决方案。
						我们致力于通过科技创新，让每一位老年人都能享受到便捷、贴心的数字化生活服务。
					</text>
					<view class="company-stats">
						<view class="stat-item">
							<text class="stat-number">100万+</text>
							<text class="stat-label">用户信赖</text>
						</view>
						<view class="stat-item">
							<text class="stat-number">50+</text>
							<text class="stat-label">城市覆盖</text>
						</view>
						<view class="stat-item">
							<text class="stat-number">1000+</text>
							<text class="stat-label">服务网点</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 核心功能 -->
			<view class="section">
				<view class="section-header">
					<Icon name="star-line" size="32rpx" color="#34c759"></Icon>
					<text class="section-title">核心功能</text>
				</view>
				<view class="features-list">
					<view class="feature-item" v-for="(feature, index) in features" :key="index">
						<Icon :name="feature.icon" size="32rpx" :color="feature.color"></Icon>
						<view class="feature-content">
							<text class="feature-name">{{ feature.name }}</text>
							<text class="feature-desc">{{ feature.description }}</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 联系我们 -->
			<view class="section">
				<view class="section-header">
					<Icon name="phone-line" size="32rpx" color="#2196f3"></Icon>
					<text class="section-title">联系我们</text>
				</view>
				<view class="contact-list">
					<view class="contact-item" @click="callPhone">
						<Icon name="phone-line" size="32rpx" color="#34c759"></Icon>
						<view class="contact-content">
							<text class="contact-label">客服热线</text>
							<text class="contact-value">************</text>
						</view>
						<Icon name="arrow-right-s-line" size="24rpx" color="#999"></Icon>
					</view>
					<view class="contact-item" @click="copyEmail">
						<Icon name="mail-line" size="32rpx" color="#ff9800"></Icon>
						<view class="contact-content">
							<text class="contact-label">邮箱地址</text>
							<text class="contact-value"><EMAIL></text>
						</view>
						<Icon name="arrow-right-s-line" size="24rpx" color="#999"></Icon>
					</view>
					<view class="contact-item" @click="copyAddress">
						<Icon name="map-pin-line" size="32rpx" color="#f44336"></Icon>
						<view class="contact-content">
							<text class="contact-label">公司地址</text>
							<text class="contact-value">北京市朝阳区科技园区智慧大厦</text>
						</view>
						<Icon name="arrow-right-s-line" size="24rpx" color="#999"></Icon>
					</view>
				</view>
			</view>

			<!-- 法律信息 -->
			<view class="section">
				<view class="section-header">
					<Icon name="file-text-line" size="32rpx" color="#9c27b0"></Icon>
					<text class="section-title">法律信息</text>
				</view>
				<view class="legal-list">
					<view class="legal-item" @click="viewPrivacy">
						<text class="legal-text">隐私政策</text>
						<Icon name="arrow-right-s-line" size="24rpx" color="#999"></Icon>
					</view>
					<view class="legal-item" @click="viewTerms">
						<text class="legal-text">用户协议</text>
						<Icon name="arrow-right-s-line" size="24rpx" color="#999"></Icon>
					</view>
					<view class="legal-item" @click="viewLicense">
						<text class="legal-text">软件许可</text>
						<Icon name="arrow-right-s-line" size="24rpx" color="#999"></Icon>
					</view>
				</view>
			</view>

			<!-- 社交媒体 -->
			<view class="section">
				<view class="section-header">
					<Icon name="share-line" size="32rpx" color="#673ab7"></Icon>
					<text class="section-title">关注我们</text>
				</view>
				<view class="social-media">
					<view class="social-item" @click="followWechat">
						<Icon name="wechat-line" size="48rpx" color="#1aad19"></Icon>
						<text class="social-text">微信公众号</text>
					</view>
					<view class="social-item" @click="followWeibo">
						<Icon name="weibo-line" size="48rpx" color="#e6162d"></Icon>
						<text class="social-text">新浪微博</text>
					</view>
					<view class="social-item" @click="followApp">
						<Icon name="smartphone-line" size="48rpx" color="#ff8a00"></Icon>
						<text class="social-text">官方APP</text>
					</view>
				</view>
			</view>

			<!-- 版权信息 -->
			<view class="copyright">
				<text class="copyright-text">© 2024 智慧养老科技有限公司</text>
				<text class="copyright-text">版权所有 保留一切权利</text>
				<text class="copyright-text">京ICP备12345678号</text>
			</view>
		</view>
	</view>
</template>

<script>
import Icon from '@/components/Icon/Icon.vue'
import FeedbackUtils from '@/utils/feedback.js'
import { elderlyModeManager } from '@/utils/elderlyModeUtils.js'

export default {
	components: {
		Icon
	},
	data() {
		return {
			statusBarHeight: 0,
			isElderlyMode: false,
			features: [
				{
					name: '健康管理',
					description: '记录健康数据，智能分析健康趋势',
					icon: 'heart-pulse-line',
					color: '#f44336'
				},
				{
					name: '养老服务',
					description: '预约专业养老服务，享受贴心照护',
					icon: 'service-line',
					color: '#4caf50'
				},
				{
					name: '社区活动',
					description: '参与丰富多彩的社区活动和讲座',
					icon: 'community-line',
					color: '#2196f3'
				},
				{
					name: '资讯阅读',
					description: '获取最新养老政策和健康资讯',
					icon: 'news-line',
					color: '#ff9800'
				},
				{
					name: '紧急求助',
					description: '一键求助功能，保障安全无忧',
					icon: 'alarm-warning-line',
					color: '#e91e63'
				},
				{
					name: '智能提醒',
					description: '用药提醒、体检提醒等贴心服务',
					icon: 'notification-3-line',
					color: '#9c27b0'
				}
			]
		}
	},
	
	onLoad() {
		// 获取状态栏高度
		const systemInfo = uni.getSystemInfoSync();
		this.statusBarHeight = systemInfo.statusBarHeight || 44;

		// 初始化适老化模式
		this.isElderlyMode = elderlyModeManager.getElderlyMode();
		elderlyModeManager.onElderlyModeChange((isEnabled) => {
			this.isElderlyMode = isEnabled;
		});
	},
	
	methods: {
		callPhone() {
			uni.makePhoneCall({
				phoneNumber: '************',
				success: () => {
					console.log('拨打电话成功');
				},
				fail: () => {
					FeedbackUtils.showError('拨打电话失败');
				}
			});
		},
		
		copyEmail() {
			uni.setClipboardData({
				data: '<EMAIL>',
				success: () => {
					FeedbackUtils.showSuccess('邮箱地址已复制');
				},
				fail: () => {
					FeedbackUtils.showError('复制失败');
				}
			});
		},
		
		copyAddress() {
			uni.setClipboardData({
				data: '北京市朝阳区科技园区智慧大厦',
				success: () => {
					FeedbackUtils.showSuccess('地址已复制');
				},
				fail: () => {
					FeedbackUtils.showError('复制失败');
				}
			});
		},
		
		viewPrivacy() {
			FeedbackUtils.showInfo('隐私政策功能正在开发中');
		},

		viewTerms() {
			FeedbackUtils.showInfo('用户协议功能正在开发中');
		},

		viewLicense() {
			FeedbackUtils.showInfo('软件许可功能正在开发中');
		},
		
		followWechat() {
			FeedbackUtils.showInfo('请搜索"智慧养老"关注我们的微信公众号');
		},
		
		followWeibo() {
			FeedbackUtils.showInfo('请搜索"@智慧养老官方"关注我们的微博');
		},
		
		followApp() {
			FeedbackUtils.showInfo('感谢您使用智慧养老APP！');
		},
		
		goBack() {
			FeedbackUtils.lightFeedback();
			uni.navigateBack({
				fail: () => {
					uni.switchTab({
						url: '/pages/profile/profile'
					});
				}
			});
		}
	}
}
</script>

<style scoped>
.container {
	background: #f5f5f5;
	min-height: 100vh;
}

/* 导航栏样式 */
.custom-navbar {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 1000;
	background: rgba(255, 255, 255, 0.95);
	backdrop-filter: blur(20rpx);
	-webkit-backdrop-filter: blur(20rpx);
	border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);
}

.navbar-content {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 20rpx 32rpx;
	min-height: 88rpx;
}

.navbar-left {
	display: flex;
	align-items: center;
	gap: 12rpx;
	padding: 8rpx 16rpx 8rpx 0;
	border-radius: 20rpx;
	transition: all 0.2s ease;
}

.navbar-left:active {
	background-color: rgba(0, 0, 0, 0.04);
	transform: scale(0.96);
}

.back-text {
	font-size: 32rpx;
	color: #333;
	font-weight: 500;
}

.navbar-center {
	position: absolute;
	left: 50%;
	transform: translateX(-50%);
}

.navbar-title {
	font-size: 34rpx;
	font-weight: 600;
	color: #333;
}

/* 内容区域 */
.content {
	padding-top: 200rpx;
	padding-bottom: 40rpx;
}

/* 应用信息 */
.app-info {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 60rpx 40rpx;
	background: linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%);
	margin-bottom: 20rpx;
	border-radius: 16rpx;
	color: white;
}

.app-logo {
	width: 120rpx;
	height: 120rpx;
	border-radius: 24rpx;
	overflow: hidden;
	margin-bottom: 24rpx;
	background: white;
}

.logo-image {
	width: 100%;
	height: 100%;
}

.app-name {
	font-size: 48rpx;
	font-weight: bold;
	margin-bottom: 12rpx;
}

.app-slogan {
	font-size: 28rpx;
	opacity: 0.9;
	margin-bottom: 20rpx;
	text-align: center;
}

.app-version {
	font-size: 24rpx;
	opacity: 0.8;
	background: rgba(255, 255, 255, 0.2);
	padding: 8rpx 16rpx;
	border-radius: 12rpx;
}

/* 区块样式 */
.section {
	background: white;
	margin-bottom: 20rpx;
	border-radius: 16rpx;
	overflow: hidden;
}

.section-header {
	display: flex;
	align-items: center;
	gap: 12rpx;
	padding: 24rpx 32rpx;
	background: rgba(255, 138, 0, 0.05);
	border-bottom: 1rpx solid #f0f0f0;
}

.section-title {
	font-size: 30rpx;
	font-weight: 600;
	color: #333;
}

/* 公司介绍 */
.company-intro {
	padding: 32rpx;
}

.intro-text {
	font-size: 28rpx;
	color: #666;
	line-height: 1.6;
	margin-bottom: 32rpx;
	display: block;
}

.company-stats {
	display: flex;
	justify-content: space-around;
}

.stat-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 8rpx;
}

.stat-number {
	font-size: 36rpx;
	font-weight: bold;
	color: #ff8a00;
}

.stat-label {
	font-size: 24rpx;
	color: #666;
}

/* 功能列表 */
.features-list {
	padding: 32rpx;
}

.feature-item {
	display: flex;
	align-items: center;
	gap: 16rpx;
	margin-bottom: 24rpx;
}

.feature-item:last-child {
	margin-bottom: 0;
}

.feature-content {
	flex: 1;
}

.feature-name {
	font-size: 28rpx;
	color: #333;
	font-weight: 500;
	display: block;
	margin-bottom: 8rpx;
}

.feature-desc {
	font-size: 24rpx;
	color: #666;
	display: block;
}

/* 联系列表 */
.contact-list {
	padding: 0;
}

.contact-item {
	display: flex;
	align-items: center;
	gap: 16rpx;
	padding: 32rpx;
	border-bottom: 1rpx solid #f0f0f0;
	transition: all 0.2s ease;
}

.contact-item:last-child {
	border-bottom: none;
}

.contact-item:active {
	background-color: rgba(0, 0, 0, 0.02);
}

.contact-content {
	flex: 1;
}

.contact-label {
	font-size: 26rpx;
	color: #666;
	display: block;
	margin-bottom: 4rpx;
}

.contact-value {
	font-size: 28rpx;
	color: #333;
	font-weight: 500;
	display: block;
}

/* 法律信息 */
.legal-list {
	padding: 0;
}

.legal-item {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 32rpx;
	border-bottom: 1rpx solid #f0f0f0;
	transition: all 0.2s ease;
}

.legal-item:last-child {
	border-bottom: none;
}

.legal-item:active {
	background-color: rgba(0, 0, 0, 0.02);
}

.legal-text {
	font-size: 28rpx;
	color: #333;
	font-weight: 500;
}

/* 社交媒体 */
.social-media {
	display: flex;
	justify-content: space-around;
	padding: 32rpx;
}

.social-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 12rpx;
	padding: 20rpx;
	border-radius: 12rpx;
	transition: all 0.2s ease;
}

.social-item:active {
	background-color: rgba(0, 0, 0, 0.02);
	transform: scale(0.96);
}

.social-text {
	font-size: 24rpx;
	color: #666;
}

/* 版权信息 */
.copyright {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 8rpx;
	padding: 40rpx;
	background: white;
	border-radius: 16rpx;
}

.copyright-text {
	font-size: 22rpx;
	color: #999;
	text-align: center;
}

/* ===== 适老化模式样式 ===== */
.elderly-mode .navbar-content {
	padding: 24rpx 36rpx;
	min-height: 96rpx;
}

.elderly-mode .navbar-left {
	gap: 16rpx;
	padding: 12rpx 20rpx 12rpx 0;
}

.elderly-mode .back-text {
	font-size: 36rpx;
	font-weight: 600;
}

.elderly-mode .navbar-title {
	font-size: 38rpx;
	font-weight: 700;
}

.elderly-mode .content {
	padding-top: 240rpx;
}

.elderly-mode .app-name {
	font-size: 52rpx;
}

.elderly-mode .app-slogan {
	font-size: 32rpx;
}

.elderly-mode .section-title {
	font-size: 36rpx;
}

.elderly-mode .intro-text {
	font-size: 32rpx;
}

.elderly-mode .feature-name {
	font-size: 32rpx;
}

.elderly-mode .contact-value {
	font-size: 32rpx;
}

.elderly-mode .legal-text {
	font-size: 32rpx;
}
</style>
