<template>
	<view class="container">
		<PageHeader title="健康记录详情" showBack></PageHeader>
		
		<view class="content">
			<!-- 记录头部信息 -->
			<view class="record-header">
				<view class="record-icon" :style="{ background: recordDetail.color }">
					<Icon :name="recordDetail.icon" size="48rpx" color="white"></Icon>
				</view>
				<view class="record-info">
					<text class="record-title">{{ recordDetail.title }}</text>
					<text class="record-date">{{ recordDetail.date }}</text>
					<text class="record-time">{{ recordDetail.time }}</text>
				</view>
				<view class="record-status" :class="recordDetail.status">
					<text class="status-text">{{ getStatusText(recordDetail.status) }}</text>
				</view>
			</view>

			<!-- 测量数据 -->
			<view class="section">
				<text class="section-title">测量数据</text>
				<view class="data-card">
					<view class="data-item" v-for="(item, index) in recordDetail.measurements" :key="index">
						<text class="data-label">{{ item.label }}</text>
						<text class="data-value">{{ item.value }}</text>
						<text class="data-unit">{{ item.unit }}</text>
						<view class="data-status" :class="item.status">
							<text class="status-dot"></text>
							<text class="status-label">{{ getStatusText(item.status) }}</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 数据趋势 -->
			<view class="section">
				<text class="section-title">数据趋势</text>
				<view class="chart-card">
					<view class="chart-header">
						<text class="chart-title">{{ recordDetail.type }}变化趋势</text>
						<view class="time-filters">
							<text 
								class="filter-item" 
								:class="{ active: activeFilter === filter }"
								v-for="filter in timeFilters" 
								:key="filter"
								@click="selectTimeFilter(filter)"
							>
								{{ filter }}
							</text>
						</view>
					</view>
					<view class="chart-placeholder">
						<Icon name="bar-chart-line" size="80rpx" color="#ddd"></Icon>
						<text class="placeholder-text">图表数据加载中...</text>
					</view>
				</view>
			</view>

			<!-- 参考范围 -->
			<view class="section">
				<text class="section-title">参考范围</text>
				<view class="reference-card">
					<view class="reference-item" v-for="(range, index) in referenceRanges" :key="index">
						<view class="range-indicator" :class="range.type"></view>
						<text class="range-label">{{ range.label }}</text>
						<text class="range-value">{{ range.value }}</text>
					</view>
				</view>
			</view>

			<!-- 备注信息 -->
			<view class="section" v-if="recordDetail.notes">
				<text class="section-title">备注信息</text>
				<view class="notes-card">
					<text class="notes-text">{{ recordDetail.notes }}</text>
				</view>
			</view>

			<!-- 相关建议 -->
			<view class="section">
				<text class="section-title">健康建议</text>
				<view class="suggestions-card">
					<view class="suggestion-item" v-for="(suggestion, index) in healthSuggestions" :key="index">
						<Icon :name="suggestion.icon" size="24rpx" :color="suggestion.color"></Icon>
						<text class="suggestion-text">{{ suggestion.text }}</text>
					</view>
				</view>
			</view>

			<!-- 历史记录 -->
			<view class="section">
				<view class="section-header">
					<text class="section-title">相关记录</text>
					<text class="view-all" @click="viewAllHistory">查看全部</text>
				</view>
				<view class="history-list">
					<view class="history-item" v-for="item in relatedRecords" :key="item.id" @click="viewRecord(item)">
						<view class="history-date">
							<text class="date-day">{{ item.day }}</text>
							<text class="date-month">{{ item.month }}</text>
						</view>
						<view class="history-content">
							<text class="history-value">{{ item.value }}</text>
							<text class="history-time">{{ item.time }}</text>
						</view>
						<view class="history-status" :class="item.status">
							<text class="status-dot"></text>
						</view>
					</view>
				</view>
			</view>

			<!-- 底部操作 -->
			<view class="bottom-actions">
				<InteractiveButton 
					type="secondary" 
					text="编辑记录" 
					leftIcon="edit-line"
					@click="editRecord"
				/>
				<InteractiveButton 
					type="primary" 
					text="添加记录" 
					leftIcon="add-line"
					@click="addRecord"
				/>
			</view>
		</view>
	</view>
</template>

<script>
import Icon from '@/components/Icon/Icon.vue'
import PageHeader from '@/components/PageHeader/PageHeader.vue'
import InteractiveButton from '@/components/InteractiveButton/InteractiveButton.vue'

export default {
	name: 'HealthRecordDetail',
	components: {
		Icon,
		PageHeader,
		InteractiveButton
	},
	data() {
		return {
			recordId: '',
			activeFilter: '一周',
			timeFilters: ['一周', '一月', '三月', '一年'],
			recordDetail: {
				id: 1,
				title: '血压测量',
				type: '血压',
				date: '2024年1月18日',
				time: '08:30',
				icon: 'heart-pulse-line',
				color: '#e91e63',
				status: 'warning',
				measurements: [
					{
						label: '收缩压',
						value: '135',
						unit: 'mmHg',
						status: 'warning'
					},
					{
						label: '舒张压',
						value: '85',
						unit: 'mmHg',
						status: 'normal'
					},
					{
						label: '心率',
						value: '72',
						unit: 'bpm',
						status: 'normal'
					}
				],
				notes: '晨起测量，昨晚睡眠质量一般'
			},
			referenceRanges: [
				{
					type: 'normal',
					label: '正常范围',
					value: '90-120 / 60-80 mmHg'
				},
				{
					type: 'warning',
					label: '偏高',
					value: '120-140 / 80-90 mmHg'
				},
				{
					type: 'danger',
					label: '高血压',
					value: '>140 / >90 mmHg'
				}
			],
			healthSuggestions: [
				{
					icon: 'lightbulb-line',
					color: '#ff8a00',
					text: '建议减少盐分摄入，每日盐分不超过6克'
				},
				{
					icon: 'run-line',
					color: '#4caf50',
					text: '适量运动，每周至少150分钟中等强度运动'
				},
				{
					icon: 'time-line',
					color: '#2196f3',
					text: '保持规律作息，每日睡眠7-8小时'
				}
			],
			relatedRecords: [
				{
					id: 1,
					day: '17',
					month: '01月',
					value: '125/82',
					time: '19:00',
					status: 'normal'
				},
				{
					id: 2,
					day: '16',
					month: '01月',
					value: '130/85',
					time: '08:15',
					status: 'warning'
				},
				{
					id: 3,
					day: '15',
					month: '01月',
					value: '118/78',
					time: '20:30',
					status: 'normal'
				}
			]
		}
	},
	onLoad(options) {
		if (options.id) {
			this.recordId = options.id
			this.loadRecordDetail()
		}
	},
	methods: {
		loadRecordDetail() {
			// 模拟加载记录详情
			console.log('加载记录详情:', this.recordId)
		},
		getStatusText(status) {
			const statusMap = {
				'normal': '正常',
				'warning': '偏高',
				'danger': '异常'
			}
			return statusMap[status] || '未知'
		},
		selectTimeFilter(filter) {
			this.activeFilter = filter
			// 重新加载图表数据
		},
		viewAllHistory() {
			uni.navigateTo({
				url: `/pages/health/history?type=${this.recordDetail.type}`
			})
		},
		viewRecord(record) {
			uni.navigateTo({
				url: `/pages/health/record-detail?id=${record.id}`
			})
		},
		editRecord() {
			uni.navigateTo({
				url: `/pages/health/record?id=${this.recordId}&mode=edit`
			})
		},
		addRecord() {
			uni.navigateTo({
				url: `/pages/health/record?type=${this.recordDetail.type}`
			})
		}
	}
}
</script>

<style scoped>
.container {
	min-height: 100vh;
	background: #f5f5f5;
}

.content {
	padding: 140rpx 32rpx 200rpx;
}

.record-header {
	background: white;
	border-radius: 20rpx;
	padding: 32rpx;
	margin-bottom: 24rpx;
	display: flex;
	align-items: center;
	gap: 24rpx;
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

.record-icon {
	width: 96rpx;
	height: 96rpx;
	border-radius: 24rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.record-info {
	flex: 1;
}

.record-title {
	font-size: 36rpx;
	font-weight: 600;
	color: #333;
	display: block;
	margin-bottom: 8rpx;
}

.record-date {
	font-size: 26rpx;
	color: #666;
	display: block;
	margin-bottom: 4rpx;
}

.record-time {
	font-size: 24rpx;
	color: #999;
	display: block;
}

.record-status {
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
	font-size: 24rpx;
}

.record-status.normal {
	background: rgba(76, 175, 80, 0.1);
	color: #4caf50;
}

.record-status.warning {
	background: rgba(255, 152, 0, 0.1);
	color: #ff9800;
}

.record-status.danger {
	background: rgba(244, 67, 54, 0.1);
	color: #f44336;
}

.section {
	margin-bottom: 32rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 24rpx;
	display: block;
}

.section-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 24rpx;
}

.view-all {
	font-size: 26rpx;
	color: #ff8a00;
}

.data-card {
	background: white;
	border-radius: 16rpx;
	padding: 24rpx;
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

.data-item {
	display: flex;
	align-items: center;
	margin-bottom: 20rpx;
}

.data-item:last-child {
	margin-bottom: 0;
}

.data-label {
	font-size: 28rpx;
	color: #666;
	min-width: 120rpx;
}

.data-value {
	font-size: 36rpx;
	font-weight: 600;
	color: #333;
	margin-right: 8rpx;
}

.data-unit {
	font-size: 24rpx;
	color: #999;
	margin-right: 16rpx;
}

.data-status {
	display: flex;
	align-items: center;
	gap: 8rpx;
	margin-left: auto;
}

.status-dot {
	width: 12rpx;
	height: 12rpx;
	border-radius: 50%;
}

.data-status.normal .status-dot {
	background: #4caf50;
}

.data-status.warning .status-dot {
	background: #ff9800;
}

.data-status.danger .status-dot {
	background: #f44336;
}

.status-label {
	font-size: 24rpx;
	color: #666;
}

.chart-card {
	background: white;
	border-radius: 16rpx;
	padding: 24rpx;
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

.chart-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 24rpx;
}

.chart-title {
	font-size: 28rpx;
	font-weight: 600;
	color: #333;
}

.time-filters {
	display: flex;
	gap: 16rpx;
}

.filter-item {
	font-size: 24rpx;
	color: #666;
	padding: 8rpx 16rpx;
	border-radius: 16rpx;
	background: #f5f5f5;
}

.filter-item.active {
	background: #ff8a00;
	color: white;
}

.chart-placeholder {
	height: 300rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	gap: 16rpx;
}

.placeholder-text {
	font-size: 26rpx;
	color: #999;
}

.reference-card {
	background: white;
	border-radius: 16rpx;
	padding: 24rpx;
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

.reference-item {
	display: flex;
	align-items: center;
	gap: 16rpx;
	margin-bottom: 16rpx;
}

.reference-item:last-child {
	margin-bottom: 0;
}

.range-indicator {
	width: 20rpx;
	height: 20rpx;
	border-radius: 50%;
}

.range-indicator.normal {
	background: #4caf50;
}

.range-indicator.warning {
	background: #ff9800;
}

.range-indicator.danger {
	background: #f44336;
}

.range-label {
	font-size: 28rpx;
	color: #333;
	min-width: 120rpx;
}

.range-value {
	font-size: 26rpx;
	color: #666;
}

.notes-card {
	background: white;
	border-radius: 16rpx;
	padding: 24rpx;
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

.notes-text {
	font-size: 28rpx;
	color: #333;
	line-height: 1.6;
}

.suggestions-card {
	background: white;
	border-radius: 16rpx;
	padding: 24rpx;
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

.suggestion-item {
	display: flex;
	align-items: flex-start;
	gap: 12rpx;
	margin-bottom: 16rpx;
}

.suggestion-item:last-child {
	margin-bottom: 0;
}

.suggestion-text {
	font-size: 26rpx;
	color: #666;
	line-height: 1.5;
}

.history-list {
	background: white;
	border-radius: 16rpx;
	padding: 24rpx;
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

.history-item {
	display: flex;
	align-items: center;
	gap: 16rpx;
	margin-bottom: 20rpx;
}

.history-item:last-child {
	margin-bottom: 0;
}

.history-date {
	text-align: center;
	min-width: 80rpx;
}

.date-day {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
	display: block;
}

.date-month {
	font-size: 22rpx;
	color: #999;
	display: block;
}

.history-content {
	flex: 1;
}

.history-value {
	font-size: 28rpx;
	font-weight: 600;
	color: #333;
	display: block;
	margin-bottom: 4rpx;
}

.history-time {
	font-size: 24rpx;
	color: #666;
	display: block;
}

.history-status {
	width: 16rpx;
	height: 16rpx;
	border-radius: 50%;
}

.history-status.normal {
	background: #4caf50;
}

.history-status.warning {
	background: #ff9800;
}

.history-status.danger {
	background: #f44336;
}

.bottom-actions {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background: white;
	padding: 24rpx 32rpx;
	border-top: 1rpx solid #f0f0f0;
	display: flex;
	gap: 16rpx;
}
</style>
