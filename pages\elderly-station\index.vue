<template>
	<view class="container">
		<!-- 头部横幅 -->
		<view class="header-banner">
			<view class="banner-content">
				<view class="banner-text">
					<text class="banner-title">高龄津贴</text>
					<text class="banner-subtitle">关爱高龄老人，享受政府津贴</text>
				</view>
				<view class="banner-icon">
					<Icon name="gift-2-line" size="80rpx" color="white"></Icon>
				</view>
			</view>
		</view>

		<!-- 津贴标准 -->
		<view class="section">
			<view class="section-header">
				<text class="section-title">津贴标准</text>
				<text class="section-subtitle">根据年龄段享受不同标准津贴</text>
			</view>
			<view class="standard-cards">
				<view class="standard-card">
					<view class="age-range">80-89岁</view>
					<view class="amount">¥100</view>
					<view class="unit">元/月</view>
					<view class="desc">基础津贴</view>
				</view>
				<view class="standard-card">
					<view class="age-range">90-99岁</view>
					<view class="amount">¥200</view>
					<view class="unit">元/月</view>
					<view class="desc">提升津贴</view>
				</view>
				<view class="standard-card highlight">
					<view class="age-range">100岁以上</view>
					<view class="amount">¥500</view>
					<view class="unit">元/月</view>
					<view class="desc">长寿津贴</view>
				</view>
			</view>
		</view>

		<!-- 申请条件 -->
		<view class="section">
			<view class="section-header">
				<text class="section-title">申请条件</text>
				<text class="section-subtitle">符合以下条件即可申请</text>
			</view>
			<view class="condition-list">
				<view class="condition-item">
					<view class="condition-icon">
						<Icon name="user-line" size="32rpx" color="#ff8a00"></Icon>
					</view>
					<view class="condition-content">
						<text class="condition-title">年龄要求</text>
						<text class="condition-desc">年满80周岁的老年人</text>
					</view>
				</view>
				<view class="condition-item">
					<view class="condition-icon">
						<Icon name="home-line" size="32rpx" color="#ff8a00"></Icon>
					</view>
					<view class="condition-content">
						<text class="condition-title">户籍要求</text>
						<text class="condition-desc">具有本市户籍</text>
					</view>
				</view>
				<view class="condition-item">
					<view class="condition-icon">
						<Icon name="file-text-line" size="32rpx" color="#ff8a00"></Icon>
					</view>
					<view class="condition-content">
						<text class="condition-title">材料齐全</text>
						<text class="condition-desc">身份证、户口本等材料</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 申请流程 -->
		<view class="section">
			<view class="section-header">
				<text class="section-title">申请流程</text>
				<text class="section-subtitle">简单四步，轻松申请</text>
			</view>
			<view class="process-timeline">
				<view class="process-step">
					<view class="step-number">1</view>
					<view class="step-content">
						<text class="step-title">准备材料</text>
						<text class="step-desc">身份证、户口本、银行卡</text>
					</view>
				</view>
				<view class="process-step">
					<view class="step-number">2</view>
					<view class="step-content">
						<text class="step-title">提交申请</text>
						<text class="step-desc">到社区服务中心提交申请</text>
					</view>
				</view>
				<view class="process-step">
					<view class="step-number">3</view>
					<view class="step-content">
						<text class="step-title">审核确认</text>
						<text class="step-desc">相关部门审核申请材料</text>
					</view>
				</view>
				<view class="process-step">
					<view class="step-number">4</view>
					<view class="step-content">
						<text class="step-title">发放津贴</text>
						<text class="step-desc">审核通过后按月发放</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 常见问题 -->
		<view class="section">
			<view class="section-header">
				<text class="section-title">常见问题</text>
				<text class="section-subtitle">解答您关心的问题</text>
			</view>
			<view class="faq-list">
				<view class="faq-item" v-for="(item, index) in faqList" :key="index" @click="toggleFAQ(index)">
					<view class="faq-question">
						<text class="question-text">{{item.question}}</text>
						<Icon :name="item.expanded ? 'arrow-up-s-line' : 'arrow-down-s-line'" size="24rpx" color="#999"></Icon>
					</view>
					<view class="faq-answer" v-if="item.expanded">
						<text class="answer-text">{{item.answer}}</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 申请记录 -->
		<view class="section" v-if="applicationList.length > 0">
			<view class="section-header">
				<text class="section-title">我的申请</text>
				<text class="section-subtitle">查看申请状态</text>
			</view>
			<view class="application-list">
				<view class="application-item" v-for="(item, index) in applicationList" :key="index">
					<view class="app-header">
						<text class="app-title">高龄津贴申请</text>
						<view class="app-status" :class="item.status">{{item.statusText}}</view>
					</view>
					<view class="app-info">
						<text class="app-date">申请时间：{{item.applyDate}}</text>
						<text class="app-amount">津贴金额：¥{{item.amount}}/月</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 底部操作 -->
		<view class="bottom-actions">
			<button class="action-btn secondary" @click="consultService">
				<Icon name="customer-service-2-line" size="32rpx" color="#ff8a00"></Icon>
				<text>咨询客服</text>
			</button>
			<button class="action-btn primary" @click="applySubsidy">
				<Icon name="add-line" size="32rpx" color="white"></Icon>
				<text>立即申请</text>
			</button>
		</view>
	</view>
</template>

<script>
import Icon from '@/components/Icon/Icon.vue'

export default {
	components: {
		Icon
	},
	data() {
		return {
			faqList: [
				{
					question: '高龄津贴什么时候发放？',
					answer: '高龄津贴每月15日前发放到申请人指定的银行账户中。',
					expanded: false
				},
				{
					question: '申请需要多长时间？',
					answer: '从提交申请到审核完成，一般需要15-20个工作日。',
					expanded: false
				},
				{
					question: '津贴可以代领吗？',
					answer: '可以，需要提供代领人身份证和委托书等相关材料。',
					expanded: false
				}
			],
			applicationList: [
				// 示例数据，实际应从后端获取
				// {
				//   applyDate: '2024-01-15',
				//   amount: 200,
				//   status: 'approved',
				//   statusText: '已通过'
				// }
			]
		}
	},
	methods: {
		toggleFAQ(index) {
			this.faqList[index].expanded = !this.faqList[index].expanded;
		},
		consultService() {
			uni.makePhoneCall({
				phoneNumber: '12345'
			});
		},
		applySubsidy() {
			uni.navigateTo({
				url: '/pages/elderly-station/apply'
			});
		}
	}
}
</script>

<style scoped>
.container {
	background: linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%);
	min-height: 100vh;
	padding-bottom: 200rpx;
}

.header-banner {
	padding: 40rpx;
	margin-bottom: 40rpx;
}

.banner-content {
	background: rgba(255, 255, 255, 0.15);
	border-radius: 30rpx;
	padding: 40rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.banner-text {
	flex: 1;
}

.banner-title {
	font-size: 48rpx;
	font-weight: bold;
	color: white;
	display: block;
	margin-bottom: 15rpx;
}

.banner-subtitle {
	font-size: 28rpx;
	color: rgba(255, 255, 255, 0.9);
	display: block;
}

.banner-icon {
	margin-left: 30rpx;
}

.section {
	margin-bottom: 40rpx;
}

.section-header {
	padding: 0 40rpx 30rpx;
}

.section-title {
	font-size: 36rpx;
	font-weight: bold;
	color: white;
	display: block;
	margin-bottom: 10rpx;
}

.section-subtitle {
	font-size: 26rpx;
	color: rgba(255, 255, 255, 0.8);
	display: block;
}

.standard-cards {
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	gap: 20rpx;
	padding: 0 40rpx;
}

.standard-card {
	background: white;
	border-radius: 30rpx;
	padding: 30rpx 20rpx;
	text-align: center;
	box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.standard-card.highlight {
	border: 3rpx solid #ff8a00;
}

.age-range {
	font-size: 26rpx;
	color: #666;
	margin-bottom: 15rpx;
}

.amount {
	font-size: 48rpx;
	font-weight: bold;
	color: #ff8a00;
	line-height: 1;
}

.unit {
	font-size: 24rpx;
	color: #999;
	margin-bottom: 10rpx;
}

.desc {
	font-size: 24rpx;
	color: #666;
}

.condition-list, .faq-list, .application-list {
	padding: 0 40rpx;
}

.condition-item, .faq-item, .application-item {
	background: white;
	border-radius: 30rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.condition-item {
	display: flex;
	align-items: center;
	gap: 20rpx;
}

.condition-icon {
	width: 60rpx;
	height: 60rpx;
	background: rgba(255, 138, 0, 0.1);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
}

.condition-content {
	flex: 1;
}

.condition-title {
	font-size: 30rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 8rpx;
}

.condition-desc {
	font-size: 26rpx;
	color: #666;
	display: block;
}

.process-timeline {
	padding: 0 40rpx;
}

.process-step {
	display: flex;
	align-items: center;
	gap: 30rpx;
	background: white;
	border-radius: 30rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.step-number {
	width: 60rpx;
	height: 60rpx;
	background: linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	color: white;
	font-size: 28rpx;
	font-weight: bold;
}

.step-content {
	flex: 1;
}

.step-title {
	font-size: 30rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 8rpx;
}

.step-desc {
	font-size: 26rpx;
	color: #666;
	display: block;
}

.faq-question {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.question-text {
	font-size: 30rpx;
	font-weight: 500;
	color: #333;
}

.faq-answer {
	margin-top: 20rpx;
	padding-top: 20rpx;
	border-top: 1rpx solid #f0f0f0;
}

.answer-text {
	font-size: 26rpx;
	color: #666;
	line-height: 1.6;
	display: block;
}

.app-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 15rpx;
}

.app-title {
	font-size: 30rpx;
	font-weight: bold;
	color: #333;
}

.app-status {
	padding: 8rpx 20rpx;
	border-radius: 20rpx;
	font-size: 24rpx;
}

.app-status.approved {
	background: rgba(76, 175, 80, 0.1);
	color: #4caf50;
}

.app-status.pending {
	background: rgba(255, 152, 0, 0.1);
	color: #ff9800;
}

.app-info {
	display: flex;
	justify-content: space-between;
}

.app-date, .app-amount {
	font-size: 26rpx;
	color: #666;
}

.bottom-actions {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background: white;
	padding: 30rpx 40rpx;
	display: flex;
	gap: 20rpx;
	box-shadow: 0 -4rpx 20rpx rgba(0,0,0,0.1);
}

.action-btn {
	flex: 1;
	height: 100rpx;
	border-radius: 20rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 15rpx;
	font-size: 32rpx;
	font-weight: 500;
}

.action-btn.primary {
	background: linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%);
	color: white;
	border: none;
}

.action-btn.secondary {
	background: white;
	color: #ff8a00;
	border: 2rpx solid #ff8a00;
}
</style>
