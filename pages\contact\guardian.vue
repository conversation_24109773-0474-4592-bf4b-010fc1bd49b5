<template>
	<view class="container">
		<!-- 紧急联系提示 -->
		<view class="emergency-banner" v-if="isEmergency">
			<Icon name="alarm-warning-line" size="32rpx" color="white"></Icon>
			<text class="emergency-text">紧急情况，正在联系监护人...</text>
		</view>

		<!-- 监护人信息 -->
		<view class="guardian-info">
			<view class="guardian-avatar">
				<image :src="guardianInfo.avatar" class="avatar-image" mode="aspectFill" v-if="guardianInfo.avatar"></image>
				<Icon name="user-line" size="60rpx" color="#999" v-else></Icon>
			</view>
			<view class="guardian-details">
				<text class="guardian-name">{{guardianInfo.name}}</text>
				<text class="guardian-relation">{{guardianInfo.relation}}</text>
				<text class="guardian-phone">{{guardianInfo.phone}}</text>
			</view>
			<view class="guardian-status" :class="guardianInfo.status">
				<text class="status-text">{{getStatusText(guardianInfo.status)}}</text>
			</view>
		</view>

		<!-- 联系方式 -->
		<view class="contact-methods">
			<view class="method-item" @click="makeCall">
				<view class="method-icon call">
					<Icon name="phone-line" size="40rpx" color="white"></Icon>
				</view>
				<view class="method-content">
					<text class="method-title">语音通话</text>
					<text class="method-desc">立即拨打电话联系</text>
				</view>
				<Icon name="arrow-right-s-line" size="24rpx" color="#999"></Icon>
			</view>

			<view class="method-item" @click="sendMessage">
				<view class="method-icon message">
					<Icon name="message-line" size="40rpx" color="white"></Icon>
				</view>
				<view class="method-content">
					<text class="method-title">发送短信</text>
					<text class="method-desc">发送文字消息</text>
				</view>
				<Icon name="arrow-right-s-line" size="24rpx" color="#999"></Icon>
			</view>

			<view class="method-item" @click="videoCall">
				<view class="method-icon video">
					<Icon name="video-line" size="40rpx" color="white"></Icon>
				</view>
				<view class="method-content">
					<text class="method-title">视频通话</text>
					<text class="method-desc">面对面视频交流</text>
				</view>
				<Icon name="arrow-right-s-line" size="24rpx" color="#999"></Icon>
			</view>

			<view class="method-item" @click="sendLocation">
				<view class="method-icon location">
					<Icon name="map-pin-line" size="40rpx" color="white"></Icon>
				</view>
				<view class="method-content">
					<text class="method-title">发送位置</text>
					<text class="method-desc">分享当前位置信息</text>
				</view>
				<Icon name="arrow-right-s-line" size="24rpx" color="#999"></Icon>
			</view>
		</view>

		<!-- 快速消息 -->
		<view class="quick-messages">
			<view class="section-header">
				<Icon name="chat-3-line" size="32rpx" color="#ff8a00"></Icon>
				<text class="section-title">快速消息</text>
			</view>
			<view class="message-list">
				<view class="message-item" v-for="(msg, index) in quickMessages" :key="index" @click="sendQuickMessage(msg)">
					<text class="message-text">{{msg}}</text>
				</view>
			</view>
		</view>

		<!-- 联系历史 -->
		<view class="contact-history">
			<view class="section-header">
				<Icon name="history-line" size="32rpx" color="#ff8a00"></Icon>
				<text class="section-title">联系历史</text>
				<text class="section-more" @click="viewAllHistory">查看全部</text>
			</view>
			<view class="history-list">
				<view class="history-item" v-for="(record, index) in contactHistory" :key="index">
					<view class="history-icon" :class="record.type">
						<Icon :name="getHistoryIcon(record.type)" size="24rpx" color="white"></Icon>
					</view>
					<view class="history-content">
						<text class="history-title">{{record.title}}</text>
						<text class="history-time">{{record.time}}</text>
					</view>
					<view class="history-status" :class="record.status">
						<text class="status-text">{{getHistoryStatus(record.status)}}</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 紧急联系 -->
		<view class="emergency-section">
			<view class="section-header">
				<Icon name="alarm-warning-line" size="32rpx" color="#f44336"></Icon>
				<text class="section-title">紧急联系</text>
			</view>
			<view class="emergency-contacts">
				<view class="emergency-item" v-for="(contact, index) in emergencyContacts" :key="index" @click="emergencyCall(contact)">
					<view class="emergency-icon">
						<Icon :name="contact.icon" size="32rpx" color="#f44336"></Icon>
					</view>
					<view class="emergency-content">
						<text class="emergency-name">{{contact.name}}</text>
						<text class="emergency-number">{{contact.number}}</text>
					</view>
					<Icon name="phone-line" size="24rpx" color="#f44336"></Icon>
				</view>
			</view>
		</view>

		<!-- 底部操作 -->
		<view class="bottom-actions">
			<button class="action-btn secondary" @click="editGuardian">
				<Icon name="edit-line" size="32rpx" color="#ff8a00"></Icon>
				<text>编辑信息</text>
			</button>
			<button class="action-btn primary" @click="makeCall">
				<Icon name="phone-line" size="32rpx" color="white"></Icon>
				<text>立即联系</text>
			</button>
		</view>
	</view>
</template>

<script>
import Icon from '@/components/Icon/Icon.vue'

export default {
	components: {
		Icon
	},
	data() {
		return {
			isEmergency: false,
			guardianInfo: {
				name: '张小明',
				relation: '儿子',
				phone: '138****5678',
				avatar: '/static/avatar/guardian.jpg',
				status: 'online'
			},
			quickMessages: [
				'我很好，不用担心',
				'需要帮助',
				'身体不舒服',
				'想你了',
				'今天天气很好',
				'吃药了'
			],
			contactHistory: [
				{
					type: 'call',
					title: '语音通话',
					time: '今天 14:30',
					status: 'completed',
					duration: '5分钟'
				},
				{
					type: 'message',
					title: '发送消息',
					time: '今天 10:15',
					status: 'read',
					content: '我很好，不用担心'
				},
				{
					type: 'video',
					title: '视频通话',
					time: '昨天 19:20',
					status: 'completed',
					duration: '12分钟'
				},
				{
					type: 'location',
					title: '发送位置',
					time: '昨天 16:45',
					status: 'sent'
				}
			],
			emergencyContacts: [
				{
					name: '急救中心',
					number: '120',
					icon: 'hospital-line'
				},
				{
					name: '报警电话',
					number: '110',
					icon: 'police-car-line'
				},
				{
					name: '火警电话',
					number: '119',
					icon: 'fire-line'
				},
				{
					name: '社区服务',
					number: '************',
					icon: 'community-line'
				}
			]
		}
	},
	onLoad(options) {
		if (options.emergency === 'true') {
			this.isEmergency = true;
			this.handleEmergency();
		}
	},
	methods: {
		getStatusText(status) {
			const statusMap = {
				'online': '在线',
				'offline': '离线',
				'busy': '忙碌'
			};
			return statusMap[status] || '未知';
		},
		getHistoryIcon(type) {
			const iconMap = {
				'call': 'phone-line',
				'message': 'message-line',
				'video': 'video-line',
				'location': 'map-pin-line'
			};
			return iconMap[type] || 'chat-3-line';
		},
		getHistoryStatus(status) {
			const statusMap = {
				'completed': '已完成',
				'read': '已读',
				'sent': '已发送',
				'failed': '失败'
			};
			return statusMap[status] || '未知';
		},
		handleEmergency() {
			// 紧急情况处理
			uni.showModal({
				title: '紧急联系',
				content: '检测到紧急情况，是否立即联系监护人？',
				confirmText: '立即联系',
				success: (res) => {
					if (res.confirm) {
						this.makeCall();
					}
				}
			});
		},
		makeCall() {
			uni.makePhoneCall({
				phoneNumber: this.guardianInfo.phone.replace(/\*/g, ''),
				success: () => {
					this.addContactRecord('call', '语音通话');
				}
			});
		},
		sendMessage() {
			uni.navigateTo({
				url: '/pages/contact/message?type=guardian'
			});
		},
		videoCall() {
			uni.showToast({
				title: '正在发起视频通话...',
				icon: 'loading',
				duration: 2000
			});
			
			setTimeout(() => {
				this.addContactRecord('video', '视频通话');
			}, 2000);
		},
		sendLocation() {
			uni.getLocation({
				type: 'gcj02',
				success: (res) => {
					uni.showToast({
						title: '位置已发送',
						icon: 'success'
					});
					this.addContactRecord('location', '发送位置');
				},
				fail: () => {
					uni.showToast({
						title: '获取位置失败',
						icon: 'none'
					});
				}
			});
		},
		sendQuickMessage(message) {
			uni.showToast({
				title: '消息已发送',
				icon: 'success'
			});
			this.addContactRecord('message', '发送消息', message);
		},
		addContactRecord(type, title, content = '') {
			const record = {
				type: type,
				title: title,
				time: new Date().toLocaleString(),
				status: 'sent',
				content: content
			};
			this.contactHistory.unshift(record);
		},
		emergencyCall(contact) {
			uni.makePhoneCall({
				phoneNumber: contact.number
			});
		},
		editGuardian() {
			uni.navigateTo({
				url: '/pages/contact/edit-guardian'
			});
		},
		viewAllHistory() {
			uni.navigateTo({
				url: '/pages/contact/history'
			});
		}
	}
}
</script>

<style scoped>
.container {
	background: #f8f9fa;
	min-height: 100vh;
	padding-bottom: 200rpx;
}

.emergency-banner {
	background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
	color: white;
	padding: 30rpx 40rpx;
	display: flex;
	align-items: center;
	gap: 15rpx;
}

.emergency-text {
	font-size: 28rpx;
	font-weight: bold;
}

.guardian-info {
	background: white;
	margin: 40rpx;
	border-radius: 30rpx;
	padding: 40rpx;
	display: flex;
	align-items: center;
	gap: 30rpx;
	box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.guardian-avatar {
	width: 120rpx;
	height: 120rpx;
	border-radius: 50%;
	background: #f0f0f0;
	display: flex;
	align-items: center;
	justify-content: center;
	overflow: hidden;
}

.avatar-image {
	width: 100%;
	height: 100%;
}

.guardian-details {
	flex: 1;
}

.guardian-name {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 10rpx;
}

.guardian-relation {
	font-size: 26rpx;
	color: #666;
	display: block;
	margin-bottom: 8rpx;
}

.guardian-phone {
	font-size: 28rpx;
	color: #ff8a00;
	font-weight: bold;
	display: block;
}

.guardian-status {
	padding: 8rpx 20rpx;
	border-radius: 20rpx;
	font-size: 22rpx;
}

.guardian-status.online {
	background: rgba(76, 175, 80, 0.1);
	color: #4caf50;
}

.guardian-status.offline {
	background: rgba(158, 158, 158, 0.1);
	color: #9e9e9e;
}

.guardian-status.busy {
	background: rgba(255, 152, 0, 0.1);
	color: #ff9800;
}

.contact-methods, .quick-messages, .contact-history, .emergency-section {
	background: white;
	margin: 40rpx;
	border-radius: 30rpx;
	padding: 40rpx;
	box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.method-item {
	display: flex;
	align-items: center;
	gap: 25rpx;
	padding: 25rpx 0;
	border-bottom: 1rpx solid #f0f0f0;
}

.method-item:last-child {
	border-bottom: none;
}

.method-icon {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
}

.method-icon.call {
	background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);
}

.method-icon.message {
	background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);
}

.method-icon.video {
	background: linear-gradient(135deg, #9c27b0 0%, #7b1fa2 100%);
}

.method-icon.location {
	background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
}

.method-content {
	flex: 1;
}

.method-title {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 8rpx;
}

.method-desc {
	font-size: 24rpx;
	color: #666;
	display: block;
}

.section-header {
	display: flex;
	align-items: center;
	gap: 15rpx;
	margin-bottom: 30rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	flex: 1;
}

.section-more {
	font-size: 26rpx;
	color: #ff8a00;
}

.message-list {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	gap: 15rpx;
}

.message-item {
	background: #f8f9fa;
	padding: 20rpx;
	border-radius: 20rpx;
	text-align: center;
}

.message-text {
	font-size: 26rpx;
	color: #333;
}

.history-list {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

.history-item {
	display: flex;
	align-items: center;
	gap: 20rpx;
	padding: 20rpx;
	background: #f8f9fa;
	border-radius: 20rpx;
}

.history-icon {
	width: 60rpx;
	height: 60rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
}

.history-icon.call {
	background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);
}

.history-icon.message {
	background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);
}

.history-icon.video {
	background: linear-gradient(135deg, #9c27b0 0%, #7b1fa2 100%);
}

.history-icon.location {
	background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
}

.history-content {
	flex: 1;
}

.history-title {
	font-size: 26rpx;
	color: #333;
	font-weight: bold;
	display: block;
	margin-bottom: 5rpx;
}

.history-time {
	font-size: 22rpx;
	color: #999;
	display: block;
}

.history-status {
	padding: 6rpx 15rpx;
	border-radius: 15rpx;
	font-size: 20rpx;
}

.history-status.completed {
	background: rgba(76, 175, 80, 0.1);
	color: #4caf50;
}

.history-status.read {
	background: rgba(33, 150, 243, 0.1);
	color: #2196f3;
}

.history-status.sent {
	background: rgba(255, 152, 0, 0.1);
	color: #ff9800;
}

.emergency-contacts {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

.emergency-item {
	display: flex;
	align-items: center;
	gap: 20rpx;
	padding: 20rpx;
	background: rgba(244, 67, 54, 0.05);
	border: 2rpx solid rgba(244, 67, 54, 0.1);
	border-radius: 20rpx;
}

.emergency-icon {
	width: 60rpx;
	height: 60rpx;
	background: rgba(244, 67, 54, 0.1);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
}

.emergency-content {
	flex: 1;
}

.emergency-name {
	font-size: 26rpx;
	color: #333;
	font-weight: bold;
	display: block;
	margin-bottom: 5rpx;
}

.emergency-number {
	font-size: 24rpx;
	color: #f44336;
	font-weight: bold;
	display: block;
}

.bottom-actions {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background: white;
	padding: 30rpx 40rpx;
	display: flex;
	gap: 20rpx;
	box-shadow: 0 -4rpx 20rpx rgba(0,0,0,0.1);
}

.action-btn {
	flex: 1;
	height: 100rpx;
	border-radius: 20rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 15rpx;
	font-size: 32rpx;
	font-weight: 500;
	border: none;
}

.action-btn.primary {
	background: linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%);
	color: white;
}

.action-btn.secondary {
	background: white;
	color: #ff8a00;
	border: 2rpx solid #ff8a00;
}
</style>
