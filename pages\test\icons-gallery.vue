<template>
	<view class="container">
		<view class="header">
			<text class="title">图标库</text>
			<text class="subtitle">{{ filteredIcons.length }} 个图标</text>
		</view>
		
		<!-- 搜索和筛选 -->
		<view class="search-section">
			<view class="search-bar">
				<input 
					v-model="searchKeyword" 
					placeholder="搜索图标名称或描述..."
					class="search-input"
					@input="filterIcons"
				/>
				<text class="search-icon">🔍</text>
			</view>
			
			<view class="filter-tabs">
				<view 
					v-for="category in categories" 
					:key="category.key"
					class="filter-tab"
					:class="{ active: selectedCategory === category.key }"
					@click="selectCategory(category.key)"
				>
					<text>{{ category.name }}</text>
				</view>
			</view>
		</view>
		
		<!-- 图标网格 -->
		<view class="icons-grid">
			<view 
				v-for="(icon, index) in filteredIcons" 
				:key="index"
				class="icon-item"
				@click="selectIcon(icon)"
			>
				<view class="icon-display">
					<Icon 
						:name="icon.name" 
						size="64rpx" 
						:color="getIconColor(icon)"
					/>
				</view>
				<text class="icon-name">{{ icon.name }}</text>
				<text class="icon-emoji">{{ icon.emoji }}</text>
				<text class="icon-desc">{{ icon.description }}</text>
				<view class="icon-category">
					<text>{{ getCategoryName(icon.category) }}</text>
				</view>
			</view>
		</view>
		
		<!-- 图标详情弹窗 -->
		<view v-if="selectedIcon" class="modal-overlay" @click="closeModal">
			<view class="modal-content" @click.stop>
				<view class="modal-header">
					<text class="modal-title">{{ selectedIcon.name }}</text>
					<text class="modal-close" @click="closeModal">✕</text>
				</view>
				
				<view class="modal-body">
					<view class="icon-preview">
						<Icon 
							:name="selectedIcon.name" 
							size="120rpx" 
							:color="getIconColor(selectedIcon)"
						/>
					</view>
					
					<view class="icon-info">
						<view class="info-item">
							<text class="info-label">名称:</text>
							<text class="info-value">{{ selectedIcon.name }}</text>
						</view>
						<view class="info-item">
							<text class="info-label">描述:</text>
							<text class="info-value">{{ selectedIcon.description }}</text>
						</view>
						<view class="info-item">
							<text class="info-label">分类:</text>
							<text class="info-value">{{ getCategoryName(selectedIcon.category) }}</text>
						</view>
						<view class="info-item">
							<text class="info-label">Emoji:</text>
							<text class="info-value">{{ selectedIcon.emoji }}</text>
						</view>
						<view class="info-item">
							<text class="info-label">类型:</text>
							<text class="info-value">{{ selectedIcon.type }}</text>
						</view>
					</view>
					
					<view class="usage-examples">
						<text class="usage-title">使用示例:</text>
						<view class="code-block">
							<text class="code-text">&lt;Icon name="{{ selectedIcon.name }}" size="32rpx" color="#ff8a00" /&gt;</text>
						</view>
						<view class="code-block">
							<text class="code-text">&lt;Icon name="{{ selectedIcon.name }}" {{ getThemeExample(selectedIcon) }} /&gt;</text>
						</view>
					</view>
					
					<view class="modal-actions">
						<button class="action-btn copy-btn" @click="copyIconCode">
							📋 复制代码
						</button>
						<button class="action-btn test-btn" @click="testIcon">
							🧪 测试图标
						</button>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import { IconConfig, IconCategories, getAllIconNames } from '@/utils/iconConfig.js'

export default {
	name: 'IconsGallery',
	data() {
		return {
			searchKeyword: '',
			selectedCategory: 'all',
			selectedIcon: null,
			allIcons: [],
			filteredIcons: [],
			categories: [
				{ key: 'all', name: '全部' },
				{ key: 'navigation', name: '导航' },
				{ key: 'function', name: '功能' },
				{ key: 'action', name: '操作' },
				{ key: 'status', name: '状态' },
				{ key: 'business', name: '业务' },
				{ key: 'elderly', name: '适老化' },
				{ key: 'medical', name: '医疗' },
				{ key: 'social', name: '社交' },
				{ key: 'emergency', name: '紧急' },
				{ key: 'entertainment', name: '娱乐' }
			]
		}
	},
	onLoad() {
		this.initIcons()
	},
	methods: {
		initIcons() {
			// 从配置文件加载所有图标
			this.allIcons = Object.entries(IconConfig).map(([name, config]) => ({
				name,
				...config
			}))
			
			this.filteredIcons = [...this.allIcons]
		},
		
		filterIcons() {
			let filtered = [...this.allIcons]
			
			// 按分类筛选
			if (this.selectedCategory !== 'all') {
				filtered = filtered.filter(icon => 
					icon.category.toLowerCase() === this.selectedCategory
				)
			}
			
			// 按关键词搜索
			if (this.searchKeyword.trim()) {
				const keyword = this.searchKeyword.toLowerCase()
				filtered = filtered.filter(icon => 
					icon.name.toLowerCase().includes(keyword) ||
					icon.description.toLowerCase().includes(keyword) ||
					icon.emoji.includes(keyword)
				)
			}
			
			this.filteredIcons = filtered
		},
		
		selectCategory(category) {
			this.selectedCategory = category
			this.filterIcons()
		},
		
		selectIcon(icon) {
			this.selectedIcon = icon
		},
		
		closeModal() {
			this.selectedIcon = null
		},
		
		getIconColor(icon) {
			// 根据分类返回合适的颜色
			const colorMap = {
				'navigation': '#2196f3',
				'function': '#4caf50',
				'action': '#ff9800',
				'status': '#9c27b0',
				'business': '#f44336',
				'elderly': '#96ceb4',
				'medical': '#e91e63',
				'social': '#00bcd4',
				'emergency': '#ff5722',
				'entertainment': '#795548'
			}
			return colorMap[icon.category.toLowerCase()] || '#333'
		},
		
		getCategoryName(category) {
			const categoryMap = {
				'NAVIGATION': '导航',
				'FUNCTION': '功能',
				'ACTION': '操作',
				'STATUS': '状态',
				'BUSINESS': '业务',
				'ELDERLY': '适老化',
				'MEDICAL': '医疗',
				'SOCIAL': '社交',
				'EMERGENCY': '紧急',
				'ENTERTAINMENT': '娱乐'
			}
			return categoryMap[category] || category
		},
		
		getThemeExample(icon) {
			// 根据图标分类生成主题示例
			const themeMap = {
				'BUSINESS': 'primary',
				'ELDERLY': 'elderly',
				'MEDICAL': 'service',
				'FUNCTION': 'institution'
			}
			const theme = themeMap[icon.category]
			return theme ? `${theme} size="48rpx"` : 'size="48rpx" color="#666"'
		},
		
		copyIconCode() {
			if (!this.selectedIcon) return
			
			const code = `<Icon name="${this.selectedIcon.name}" size="32rpx" color="#ff8a00" />`
			
			// 在小程序中，无法直接复制到剪贴板，显示提示
			uni.showModal({
				title: '复制代码',
				content: code,
				showCancel: false,
				confirmText: '知道了'
			})
		},
		
		testIcon() {
			if (!this.selectedIcon) return
			
			// 跳转到图标测试页面
			uni.navigateTo({
				url: `/pages/test/icon-test?name=${this.selectedIcon.name}`
			})
		}
	}
}
</script>

<style scoped>
.container {
	padding: 20rpx;
	background-color: #f5f5f5;
	min-height: 100vh;
}

.header {
	text-align: center;
	margin-bottom: 30rpx;
}

.title {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 10rpx;
}

.subtitle {
	font-size: 28rpx;
	color: #666;
}

.search-section {
	background: white;
	border-radius: 20rpx;
	padding: 30rpx;
	margin-bottom: 30rpx;
	box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.search-bar {
	position: relative;
	margin-bottom: 30rpx;
}

.search-input {
	width: 100%;
	padding: 20rpx 60rpx 20rpx 20rpx;
	border: 2rpx solid #e0e0e0;
	border-radius: 15rpx;
	font-size: 28rpx;
	background: #f9f9f9;
}

.search-icon {
	position: absolute;
	right: 20rpx;
	top: 50%;
	transform: translateY(-50%);
	font-size: 32rpx;
	color: #666;
}

.filter-tabs {
	display: flex;
	flex-wrap: wrap;
	gap: 15rpx;
}

.filter-tab {
	padding: 15rpx 25rpx;
	border-radius: 25rpx;
	background: #f0f0f0;
	font-size: 24rpx;
	color: #666;
	border: 2rpx solid transparent;
}

.filter-tab.active {
	background: #ff8a00;
	color: white;
	border-color: #ff8a00;
}

.icons-grid {
	display: grid;
	grid-template-columns: repeat(auto-fill, minmax(200rpx, 1fr));
	gap: 20rpx;
}

.icon-item {
	background: white;
	border-radius: 20rpx;
	padding: 30rpx 20rpx;
	text-align: center;
	box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
	transition: transform 0.3s ease;
}

.icon-item:active {
	transform: scale(0.95);
}

.icon-display {
	margin-bottom: 20rpx;
}

.icon-name {
	font-size: 24rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 8rpx;
	word-break: break-all;
}

.icon-emoji {
	font-size: 20rpx;
	color: #666;
	margin-bottom: 8rpx;
}

.icon-desc {
	font-size: 22rpx;
	color: #999;
	margin-bottom: 15rpx;
}

.icon-category {
	padding: 6rpx 12rpx;
	background: #f0f0f0;
	border-radius: 12rpx;
	font-size: 20rpx;
	color: #666;
	display: inline-block;
}

.modal-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0,0,0,0.5);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 1000;
}

.modal-content {
	background: white;
	border-radius: 20rpx;
	width: 90%;
	max-width: 600rpx;
	max-height: 80vh;
	overflow-y: auto;
}

.modal-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 30rpx;
	border-bottom: 2rpx solid #f0f0f0;
}

.modal-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.modal-close {
	font-size: 36rpx;
	color: #666;
	padding: 10rpx;
}

.modal-body {
	padding: 30rpx;
}

.icon-preview {
	text-align: center;
	margin-bottom: 30rpx;
	padding: 40rpx;
	background: #f9f9f9;
	border-radius: 15rpx;
}

.icon-info {
	margin-bottom: 30rpx;
}

.info-item {
	display: flex;
	margin-bottom: 15rpx;
}

.info-label {
	font-size: 26rpx;
	color: #666;
	width: 120rpx;
	flex-shrink: 0;
}

.info-value {
	font-size: 26rpx;
	color: #333;
	flex: 1;
	word-break: break-all;
}

.usage-examples {
	margin-bottom: 30rpx;
}

.usage-title {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 15rpx;
}

.code-block {
	background: #f5f5f5;
	border-radius: 10rpx;
	padding: 20rpx;
	margin-bottom: 15rpx;
}

.code-text {
	font-size: 22rpx;
	color: #666;
	font-family: monospace;
	word-break: break-all;
}

.modal-actions {
	display: flex;
	gap: 20rpx;
}

.action-btn {
	flex: 1;
	padding: 25rpx;
	border-radius: 15rpx;
	font-size: 26rpx;
	border: none;
	color: white;
}

.copy-btn { background: #4caf50; }
.test-btn { background: #2196f3; }
</style>
