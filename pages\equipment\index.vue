<template>
	<view class="container">
		<!-- 头部横幅 -->
		<view class="header-banner">
			<view class="banner-content">
				<view class="banner-text">
					<text class="banner-title">辅具租赁</text>
					<text class="banner-subtitle">专业辅助器具，让生活更便利</text>
				</view>
				<view class="banner-icon">
					<Icon name="wheelchair-line" size="80rpx" color="white"></Icon>
				</view>
			</view>
		</view>

		<!-- 搜索栏 -->
		<view class="search-section">
			<view class="search-bar">
				<Icon name="search-line" size="32rpx" color="#999"></Icon>
				<input class="search-input" placeholder="搜索辅助器具" v-model="searchKeyword" @confirm="searchEquipment" />
				<button class="search-btn" @click="searchEquipment">搜索</button>
			</view>
		</view>

		<!-- 设备分类 -->
		<view class="section">
			<view class="section-header">
				<text class="section-title">设备分类</text>
				<text class="section-subtitle">选择您需要的辅助器具</text>
			</view>
			<view class="category-scroll">
				<scroll-view scroll-x="true" class="category-list">
					<view 
						class="category-item" 
						:class="{ active: activeCategory === item.key }"
						v-for="(item, index) in categoryList" 
						:key="index"
						@click="selectCategory(item.key)"
					>
						<Icon :name="item.icon" size="40rpx" :color="activeCategory === item.key ? 'white' : '#666'"></Icon>
						<text class="category-text">{{item.name}}</text>
					</view>
				</scroll-view>
			</view>
		</view>

		<!-- 热门设备 -->
		<view class="section">
			<view class="section-header">
				<text class="section-title">热门设备</text>
				<text class="section-subtitle">最受欢迎的辅助器具</text>
			</view>
			<view class="equipment-grid">
				<view class="equipment-item" v-for="(item, index) in equipmentList" :key="index" @click="viewEquipment(item)">
					<view class="equipment-image">
						<Icon name="wheelchair-line" size="60rpx" color="#ff8a00"></Icon>
					</view>
					<view class="equipment-content">
						<text class="equipment-name">{{item.name}}</text>
						<text class="equipment-desc">{{item.description}}</text>
						<view class="equipment-meta">
							<view class="price-info">
								<text class="price">¥{{item.price}}</text>
								<text class="price-unit">/天</text>
							</view>
							<view class="rating">
								<Icon name="star-fill" size="24rpx" color="#ffc107"></Icon>
								<text class="rating-text">{{item.rating}}</text>
							</view>
						</view>
						<view class="equipment-tags">
							<text class="equipment-tag" v-for="tag in item.tags" :key="tag">{{tag}}</text>
						</view>
					</view>
					<view class="equipment-action">
						<button class="rent-btn" @click.stop="rentEquipment(item)">租赁</button>
					</view>
				</view>
			</view>
		</view>

		<!-- 租赁流程 -->
		<view class="section">
			<view class="section-header">
				<text class="section-title">租赁流程</text>
				<text class="section-subtitle">简单三步，轻松租赁</text>
			</view>
			<view class="process-list">
				<view class="process-item">
					<view class="process-icon">
						<Icon name="search-line" size="40rpx" color="white"></Icon>
					</view>
					<view class="process-content">
						<text class="process-title">选择设备</text>
						<text class="process-desc">浏览并选择合适的辅助器具</text>
					</view>
				</view>
				<view class="process-item">
					<view class="process-icon">
						<Icon name="file-text-line" size="40rpx" color="white"></Icon>
					</view>
					<view class="process-content">
						<text class="process-title">提交申请</text>
						<text class="process-desc">填写租赁信息，提交申请</text>
					</view>
				</view>
				<view class="process-item">
					<view class="process-icon">
						<Icon name="truck-line" size="40rpx" color="white"></Icon>
					</view>
					<view class="process-content">
						<text class="process-title">配送使用</text>
						<text class="process-desc">专业配送，指导使用</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 底部操作 -->
		<view class="bottom-actions">
			<button class="action-btn secondary" @click="viewMyRentals">
				<Icon name="file-list-line" size="32rpx" color="#ff8a00"></Icon>
				<text>我的租赁</text>
			</button>
			<button class="action-btn primary" @click="consultService">
				<Icon name="customer-service-2-line" size="32rpx" color="white"></Icon>
				<text>咨询客服</text>
			</button>
		</view>
	</view>
</template>

<script>
import Icon from '@/components/Icon/Icon.vue'

export default {
	components: {
		Icon
	},
	data() {
		return {
			searchKeyword: '',
			activeCategory: 'all',
			categoryList: [
				{ key: 'all', name: '全部', icon: 'apps-line' },
				{ key: 'mobility', name: '行动辅助', icon: 'wheelchair-line' },
				{ key: 'daily', name: '日常生活', icon: 'home-gear-line' },
				{ key: 'medical', name: '医疗器械', icon: 'health-book-line' },
				{ key: 'safety', name: '安全防护', icon: 'shield-check-line' }
			],
			equipmentList: [
				{
					id: 1,
					name: '电动轮椅',
					description: '轻便电动轮椅，续航能力强',
					price: 50,
					rating: 4.8,
					tags: ['电动', '轻便', '续航长'],
					category: 'mobility'
				},
				{
					id: 2,
					name: '助行器',
					description: '四脚助行器，稳定安全',
					price: 15,
					rating: 4.6,
					tags: ['稳定', '安全', '轻便'],
					category: 'mobility'
				},
				{
					id: 3,
					name: '护理床',
					description: '多功能护理床，可调节高度',
					price: 80,
					rating: 4.9,
					tags: ['多功能', '可调节', '舒适'],
					category: 'medical'
				},
				{
					id: 4,
					name: '洗澡椅',
					description: '防滑洗澡椅，安全舒适',
					price: 20,
					rating: 4.5,
					tags: ['防滑', '安全', '舒适'],
					category: 'daily'
				}
			]
		}
	},
	methods: {
		selectCategory(category) {
			this.activeCategory = category;
			this.filterEquipment();
		},
		searchEquipment() {
			// 搜索设备逻辑
			console.log('搜索关键词:', this.searchKeyword);
		},
		filterEquipment() {
			// 筛选设备逻辑
			console.log('筛选分类:', this.activeCategory);
		},
		viewEquipment(equipment) {
			uni.navigateTo({
				url: `/pages/equipment/detail?id=${equipment.id}`
			});
		},
		rentEquipment(equipment) {
			uni.navigateTo({
				url: `/pages/equipment/rent?id=${equipment.id}`
			});
		},
		viewMyRentals() {
			uni.navigateTo({
				url: '/pages/equipment/my-rentals'
			});
		},
		consultService() {
			uni.makePhoneCall({
				phoneNumber: '************'
			});
		}
	}
}
</script>

<style scoped>
.container {
	background: linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%);
	min-height: 100vh;
	padding-bottom: 200rpx;
}

.header-banner {
	padding: 40rpx;
	margin-bottom: 20rpx;
}

.banner-content {
	background: rgba(255, 255, 255, 0.15);
	border-radius: 30rpx;
	padding: 40rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.banner-text {
	flex: 1;
}

.banner-title {
	font-size: 48rpx;
	font-weight: bold;
	color: white;
	display: block;
	margin-bottom: 15rpx;
}

.banner-subtitle {
	font-size: 28rpx;
	color: rgba(255, 255, 255, 0.9);
	display: block;
}

.banner-icon {
	margin-left: 30rpx;
}

.search-section {
	padding: 0 40rpx 40rpx;
}

.search-bar {
	background: white;
	border-radius: 25rpx;
	padding: 20rpx 30rpx;
	display: flex;
	align-items: center;
	gap: 20rpx;
	box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.search-input {
	flex: 1;
	font-size: 28rpx;
	border: none;
	outline: none;
}

.search-btn {
	background: linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%);
	color: white;
	border: none;
	padding: 15rpx 30rpx;
	border-radius: 20rpx;
	font-size: 26rpx;
}

.section {
	margin-bottom: 40rpx;
}

.section-header {
	padding: 0 40rpx 30rpx;
}

.section-title {
	font-size: 36rpx;
	font-weight: bold;
	color: white;
	display: block;
	margin-bottom: 10rpx;
}

.section-subtitle {
	font-size: 26rpx;
	color: rgba(255, 255, 255, 0.8);
	display: block;
}

.category-scroll {
	padding: 0 40rpx;
}

.category-list {
	white-space: nowrap;
}

.category-item {
	display: inline-flex;
	flex-direction: column;
	align-items: center;
	margin-right: 40rpx;
	padding: 20rpx;
	border-radius: 20rpx;
	background: white;
	min-width: 120rpx;
}

.category-item.active {
	background: linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%);
}

.category-text {
	font-size: 24rpx;
	color: #666;
	margin-top: 10rpx;
}

.category-item.active .category-text {
	color: white;
}

.equipment-grid {
	padding: 0 40rpx;
}

.equipment-item {
	background: white;
	border-radius: 30rpx;
	padding: 30rpx;
	margin-bottom: 30rpx;
	display: flex;
	align-items: flex-start;
	gap: 30rpx;
	box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.equipment-image {
	width: 120rpx;
	height: 120rpx;
	background: #f8f9fa;
	border-radius: 20rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.equipment-content {
	flex: 1;
}

.equipment-name {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 8rpx;
}

.equipment-desc {
	font-size: 26rpx;
	color: #666;
	display: block;
	margin-bottom: 15rpx;
}

.equipment-meta {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 15rpx;
}

.price-info {
	display: flex;
	align-items: baseline;
	gap: 5rpx;
}

.price {
	font-size: 32rpx;
	font-weight: bold;
	color: #ff8a00;
}

.price-unit {
	font-size: 24rpx;
	color: #999;
}

.rating {
	display: flex;
	align-items: center;
	gap: 8rpx;
}

.rating-text {
	font-size: 24rpx;
	color: #666;
}

.equipment-tags {
	display: flex;
	gap: 10rpx;
}

.equipment-tag {
	background: rgba(255, 138, 0, 0.1);
	color: #ff8a00;
	font-size: 22rpx;
	padding: 5rpx 15rpx;
	border-radius: 15rpx;
}

.equipment-action {
	display: flex;
	align-items: center;
}

.rent-btn {
	background: linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%);
	color: white;
	border: none;
	padding: 15rpx 30rpx;
	border-radius: 20rpx;
	font-size: 26rpx;
	font-weight: 500;
}

.process-list {
	padding: 0 40rpx;
	display: flex;
	gap: 30rpx;
}

.process-item {
	flex: 1;
	background: white;
	border-radius: 30rpx;
	padding: 30rpx 20rpx;
	text-align: center;
	box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.process-icon {
	width: 80rpx;
	height: 80rpx;
	background: linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	margin: 0 auto 20rpx;
}

.process-title {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 10rpx;
}

.process-desc {
	font-size: 24rpx;
	color: #666;
	display: block;
	line-height: 1.4;
}

.bottom-actions {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background: white;
	padding: 30rpx 40rpx;
	display: flex;
	gap: 20rpx;
	box-shadow: 0 -4rpx 20rpx rgba(0,0,0,0.1);
}

.action-btn {
	flex: 1;
	height: 100rpx;
	border-radius: 20rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 15rpx;
	font-size: 32rpx;
	font-weight: 500;
}

.action-btn.primary {
	background: linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%);
	color: white;
	border: none;
}

.action-btn.secondary {
	background: white;
	color: #ff8a00;
	border: 2rpx solid #ff8a00;
}
</style>
