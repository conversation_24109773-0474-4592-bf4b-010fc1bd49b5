<template>
	<view class="container">
		<PageHeader title="数据功能测试" showBack></PageHeader>
		
		<view class="content">
			<!-- 测试结果显示 -->
			<view class="test-section">
				<text class="section-title">测试结果</text>
				<view class="test-results">
					<view class="test-item" v-for="(result, index) in testResults" :key="index">
						<Icon :name="result.success ? 'check-line' : 'close-line'" 
							  :color="result.success ? '#4caf50' : '#f44336'" 
							  size="24rpx"></Icon>
						<text class="test-name">{{ result.name }}</text>
						<text class="test-status" :class="{ success: result.success, error: !result.success }">
							{{ result.success ? '通过' : '失败' }}
						</text>
					</view>
				</view>
			</view>

			<!-- 测试按钮 -->
			<view class="test-actions">
				<InteractiveButton 
					type="primary" 
					text="测试任务管理" 
					leftIcon="task-line"
					@click="testTaskCRUD"
					:loading="testing.task"
				/>
				<InteractiveButton 
					type="primary" 
					text="测试健康记录" 
					leftIcon="health-book-line"
					@click="testHealthCRUD"
					:loading="testing.health"
				/>
				<InteractiveButton
					type="primary"
					text="测试消息通知"
					leftIcon="mail-line"
					@click="testMessageCRUD"
					:loading="testing.message"
				/>
				<InteractiveButton
					type="primary"
					text="测试收藏管理"
					leftIcon="star-line"
					@click="testFavoriteCRUD"
					:loading="testing.favorite"
				/>
				<InteractiveButton
					type="secondary"
					text="运行全部测试"
					leftIcon="play-line"
					@click="runAllTests"
					:loading="testing.all"
				/>
			</view>

			<!-- 数据统计 -->
			<view class="stats-section">
				<text class="section-title">数据统计</text>
				<view class="stats-grid">
					<view class="stat-card">
						<text class="stat-value">{{ stats.tasks }}</text>
						<text class="stat-label">任务数量</text>
					</view>
					<view class="stat-card">
						<text class="stat-value">{{ stats.healthRecords }}</text>
						<text class="stat-label">健康记录</text>
					</view>
					<view class="stat-card">
						<text class="stat-value">{{ stats.messages }}</text>
						<text class="stat-label">消息数量</text>
					</view>
					<view class="stat-card">
						<text class="stat-value">{{ stats.medications }}</text>
						<text class="stat-label">用药提醒</text>
					</view>
				</view>
			</view>

			<!-- 清空数据 -->
			<view class="danger-section">
				<text class="section-title">危险操作</text>
				<InteractiveButton 
					type="danger" 
					text="清空所有数据" 
					leftIcon="delete-line"
					@click="clearAllData"
				/>
			</view>
		</view>
	</view>
</template>

<script>
import Icon from '@/components/Icon/Icon.vue'
import PageHeader from '@/components/PageHeader/PageHeader.vue'
import InteractiveButton from '@/components/InteractiveButton/InteractiveButton.vue'
import crudAPI from '@/utils/crudAPI.js'
import dataManager from '@/utils/dataManager.js'
import FeedbackUtils from '@/utils/feedback.js'

export default {
	name: 'CrudTest',
	components: {
		Icon,
		PageHeader,
		InteractiveButton
	},
	data() {
		return {
			testResults: [],
			testing: {
				task: false,
				health: false,
				message: false,
				favorite: false,
				all: false
			},
			stats: {
				tasks: 0,
				healthRecords: 0,
				messages: 0,
				medications: 0,
				favorites: 0
			}
		}
	},
	onLoad() {
		this.loadStats()
	},
	methods: {
		// 加载统计数据
		async loadStats() {
			try {
				const [taskResult, healthResult, messageResult, medicationResult, favoriteResult] = await Promise.all([
					crudAPI.getTasks(),
					crudAPI.getHealthRecords(),
					crudAPI.getMessages(),
					crudAPI.getMedications(),
					crudAPI.getFavorites()
				])

				this.stats.tasks = taskResult.success ? taskResult.data.total : 0
				this.stats.healthRecords = healthResult.success ? healthResult.data.total : 0
				this.stats.messages = messageResult.success ? messageResult.data.total : 0
				this.stats.medications = medicationResult.success ? medicationResult.data.length : 0
				this.stats.favorites = favoriteResult.success ? favoriteResult.data.total : 0
			} catch (error) {
				console.error('加载统计数据失败:', error)
			}
		},

		// 测试任务管理CRUD
		async testTaskCRUD() {
			this.testing.task = true
			const results = []

			try {
				// 测试创建任务
				const createResult = await crudAPI.createTask({
					title: '测试任务',
					description: '这是一个测试任务',
					category: '测试',
					priority: 'medium',
					dueDate: dataManager.formatDate(),
					dueTime: '10:00'
				})
				results.push({ name: '创建任务', success: createResult.success })

				if (createResult.success) {
					const taskId = createResult.data.id

					// 测试获取任务
					const getResult = await crudAPI.getTaskById(taskId)
					results.push({ name: '获取任务', success: getResult.success })

					// 测试更新任务
					const updateResult = await crudAPI.updateTask(taskId, { title: '更新后的测试任务' })
					results.push({ name: '更新任务', success: updateResult.success })

					// 测试删除任务
					const deleteResult = await crudAPI.deleteTask(taskId)
					results.push({ name: '删除任务', success: deleteResult.success })
				}

				// 测试获取任务列表
				const listResult = await crudAPI.getTasks()
				results.push({ name: '获取任务列表', success: listResult.success })

			} catch (error) {
				console.error('任务CRUD测试失败:', error)
				results.push({ name: '任务CRUD测试', success: false })
			}

			this.updateTestResults('任务管理', results)
			this.testing.task = false
			this.loadStats()
		},

		// 测试健康记录CRUD
		async testHealthCRUD() {
			this.testing.health = true
			const results = []

			try {
				// 测试创建健康记录
				const createResult = await crudAPI.createHealthRecord({
					type: '血压',
					value: '120/80',
					unit: 'mmHg',
					date: dataManager.formatDate(),
					time: dataManager.formatTime(),
					note: '测试记录'
				})
				results.push({ name: '创建健康记录', success: createResult.success })

				if (createResult.success) {
					const recordId = createResult.data.id

					// 测试更新健康记录
					const updateResult = await crudAPI.updateHealthRecord(recordId, { note: '更新后的测试记录' })
					results.push({ name: '更新健康记录', success: updateResult.success })

					// 测试删除健康记录
					const deleteResult = await crudAPI.deleteHealthRecord(recordId)
					results.push({ name: '删除健康记录', success: deleteResult.success })
				}

				// 测试获取健康记录列表
				const listResult = await crudAPI.getHealthRecords()
				results.push({ name: '获取健康记录列表', success: listResult.success })

			} catch (error) {
				console.error('健康记录CRUD测试失败:', error)
				results.push({ name: '健康记录CRUD测试', success: false })
			}

			this.updateTestResults('健康记录', results)
			this.testing.health = false
			this.loadStats()
		},

		// 测试消息通知CRUD
		async testMessageCRUD() {
			this.testing.message = true
			const results = []

			try {
				// 测试获取消息列表
				const listResult = await crudAPI.getMessages()
				results.push({ name: '获取消息列表', success: listResult.success })

				if (listResult.success && listResult.data.list.length > 0) {
					const messageId = listResult.data.list[0].id

					// 测试标记消息已读
					const readResult = await crudAPI.markMessageAsRead(messageId)
					results.push({ name: '标记消息已读', success: readResult.success })
				}

				// 测试标记所有消息已读
				const allReadResult = await crudAPI.markAllMessagesAsRead()
				results.push({ name: '标记所有消息已读', success: allReadResult.success })

			} catch (error) {
				console.error('消息通知CRUD测试失败:', error)
				results.push({ name: '消息通知CRUD测试', success: false })
			}

			this.updateTestResults('消息通知', results)
			this.testing.message = false
			this.loadStats()
		},

		// 测试收藏管理CRUD
		async testFavoriteCRUD() {
			this.testing.favorite = true
			const results = []

			try {
				// 测试获取收藏列表
				const listResult = await crudAPI.getFavorites()
				results.push({ name: '获取收藏列表', success: listResult.success })

				// 测试创建收藏
				const createData = {
					targetId: 'test_service_001',
					type: 'service',
					title: '测试服务收藏',
					description: '这是一个测试收藏项目',
					icon: 'heart-line',
					price: '99.00',
					tags: ['测试', '收藏']
				}
				const createResult = await crudAPI.createFavorite(createData)
				results.push({ name: '创建收藏', success: createResult.success })

				if (createResult.success) {
					const favoriteId = createResult.data.id

					// 测试检查收藏状态
					const checkResult = await crudAPI.checkFavoriteStatus('test_service_001', 'service')
					results.push({ name: '检查收藏状态', success: checkResult.success && checkResult.data.isFavorited })

					// 测试删除收藏
					const deleteResult = await crudAPI.deleteFavorite(favoriteId)
					results.push({ name: '删除收藏', success: deleteResult.success })
				}

			} catch (error) {
				console.error('收藏管理CRUD测试失败:', error)
				results.push({ name: '收藏管理CRUD测试', success: false })
			}

			this.updateTestResults('收藏管理', results)
			this.testing.favorite = false
			this.loadStats()
		},

		// 运行所有测试
		async runAllTests() {
			this.testing.all = true
			this.testResults = []

			await this.testTaskCRUD()
			await this.testHealthCRUD()
			await this.testMessageCRUD()
			await this.testFavoriteCRUD()

			this.testing.all = false
			FeedbackUtils.showSuccess('所有测试完成')
		},

		// 更新测试结果
		updateTestResults(category, results) {
			results.forEach(result => {
				this.testResults.push({
					...result,
					name: `${category} - ${result.name}`
				})
			})
		},

		// 清空所有数据
		async clearAllData() {
			try {
				await FeedbackUtils.showConfirm({
					title: '危险操作',
					content: '确定要清空所有数据吗？此操作不可恢复！',
					confirmText: '清空',
					cancelText: '取消'
				})

				FeedbackUtils.showLoading('清空中...')

				// 清空所有存储的数据
				const keys = ['tasks', 'health_records', 'medications', 'messages', 'favorites', 'emergency_contacts', 'checkup_appointments', 'consultations']
				keys.forEach(key => {
					dataManager.removeItem(key)
				})

				// 重新初始化数据
				dataManager.initializeData()

				FeedbackUtils.hideLoading()
				FeedbackUtils.showSuccess('所有数据已清空并重新初始化')

				this.loadStats()
				this.testResults = []
			} catch (error) {
				FeedbackUtils.hideLoading()
				console.log('用户取消操作')
			}
		}
	}
}
</script>

<style scoped>
.container {
	min-height: 100vh;
	background: #f5f5f5;
}

.content {
	padding: 140rpx 32rpx 40rpx;
}

.test-section, .stats-section, .danger-section {
	margin-bottom: 40rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 24rpx;
	display: block;
}

.test-results {
	background: white;
	border-radius: 16rpx;
	padding: 24rpx;
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

.test-item {
	display: flex;
	align-items: center;
	gap: 12rpx;
	padding: 16rpx 0;
	border-bottom: 1rpx solid #f0f0f0;
}

.test-item:last-child {
	border-bottom: none;
}

.test-name {
	flex: 1;
	font-size: 28rpx;
	color: #333;
}

.test-status {
	font-size: 24rpx;
	font-weight: 600;
}

.test-status.success {
	color: #4caf50;
}

.test-status.error {
	color: #f44336;
}

.test-actions {
	display: flex;
	flex-direction: column;
	gap: 16rpx;
	margin-bottom: 40rpx;
}

.stats-grid {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 16rpx;
}

.stat-card {
	background: white;
	border-radius: 16rpx;
	padding: 32rpx;
	text-align: center;
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

.stat-value {
	font-size: 48rpx;
	font-weight: 600;
	color: #ff8a00;
	display: block;
	margin-bottom: 8rpx;
}

.stat-label {
	font-size: 24rpx;
	color: #666;
}
</style>
