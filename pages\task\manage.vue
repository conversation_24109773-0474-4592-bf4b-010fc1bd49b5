<template>
	<view class="container">
		<!-- 页面头部 -->
		<PageHeader title="任务管理">
			<template #actions>
				<InteractiveButton
					type="primary"
					size="medium"
					text="添加任务"
					icon="add-line"
					@click="showAddForm"
				></InteractiveButton>
			</template>
		</PageHeader>

		<!-- 任务统计 -->
		<view class="stats-section">
			<view class="stats-grid">
				<view class="stat-card" v-for="(stat, index) in taskStats" :key="index">
					<view class="stat-icon" :style="{ backgroundColor: stat.bgColor }">
						<Icon :name="stat.icon" size="32rpx" :color="stat.color"></Icon>
					</view>
					<view class="stat-info">
						<text class="stat-value">{{ stat.value }}</text>
						<text class="stat-label">{{ stat.label }}</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 任务筛选 -->
		<view class="filter-section">
			<view class="filter-tabs">
				<view 
					v-for="(status, index) in taskStatuses" 
					:key="index"
					class="filter-tab"
					:class="{ active: selectedStatus === status.value }"
					@click="selectStatus(status.value)"
				>
					<text class="tab-text">{{ status.label }}</text>
				</view>
			</view>
		</view>

		<!-- 任务列表 -->
		<scroll-view 
			scroll-y="true" 
			class="task-list"
			@scrolltolower="loadMore"
			refresher-enabled="true"
			@refresherrefresh="refreshData"
			:refresher-triggered="refreshing"
		>
			<InteractiveCard 
				v-for="(item, index) in filteredTasks" 
				:key="item.id"
				class="task-item"
				:loading="false"
				@click="viewDetail(item)"
			>
				<view class="task-content">
					<view class="task-header">
						<text class="task-title">{{ item.title }}</text>
						<view class="task-priority" :class="item.priority">
							<text class="priority-text">{{ getPriorityText(item.priority) }}</text>
						</view>
					</view>
					<text class="task-description">{{ item.description }}</text>
					<view class="task-meta">
						<view class="meta-item">
							<Icon name="calendar-line" size="24rpx" color="#999"></Icon>
							<text class="meta-text">{{ formatDate(item.deadline) }}</text>
						</view>
						<view class="meta-item">
							<Icon name="location-line" size="24rpx" color="#999"></Icon>
							<text class="meta-text">{{ item.location || '无位置' }}</text>
						</view>
						<view class="meta-item">
							<Icon name="folder-line" size="24rpx" color="#999"></Icon>
							<text class="meta-text">{{ item.category }}</text>
						</view>
					</view>
					<view class="task-actions">
						<InteractiveButton 
							v-if="item.status === 'pending'"
							type="primary" 
							size="small" 
							text="完成" 
							icon="check-line"
							@click.stop="completeTask(item)"
						></InteractiveButton>
						<InteractiveButton 
							type="secondary" 
							size="small" 
							text="编辑" 
							icon="edit-line"
							@click.stop="editTask(item)"
						></InteractiveButton>
						<InteractiveButton
							type="danger"
							size="small"
							text="删除"
							icon="delete-line"
							@click.stop="deleteTask(item)"
						></InteractiveButton>
					</view>
				</view>
			</InteractiveCard>

			<!-- 空状态 -->
			<view class="empty" v-if="filteredTasks.length === 0 && !loading">
				<Icon name="task-line" size="120rpx" color="#ccc"></Icon>
				<text class="empty-text">暂无任务</text>
				<InteractiveButton 
					type="primary" 
					size="medium" 
					text="添加第一个任务" 
					@click="showAddForm"
				></InteractiveButton>
			</view>
		</scroll-view>

		<!-- 添加/编辑表单弹窗 -->
		<uni-popup ref="formPopup" type="bottom" :mask-click="false">
			<view class="form-popup">
				<FormBuilder
					:title="isEditing ? '编辑任务' : '添加任务'"
					:fields="formFields"
					:initial-data="currentTask"
					:submitting="submitting"
					@submit="handleSubmit"
					@cancel="hideForm"
				></FormBuilder>
			</view>
		</uni-popup>
	</view>
</template>

<script>
import InteractiveCard from '@/components/InteractiveCard/InteractiveCard.vue'
import InteractiveButton from '@/components/InteractiveButton/InteractiveButton.vue'
import FormBuilder from '@/components/FormBuilder/FormBuilder.vue'
import Icon from '@/components/Icon/Icon.vue'
import PageHeader from '@/components/PageHeader/PageHeader.vue'
import FeedbackUtils from '@/utils/feedback.js'
import crudAPI from '@/utils/crudAPI.js'

export default {
	components: {
		InteractiveCard,
		InteractiveButton,
		FormBuilder,
		Icon,
		PageHeader
	},
	data() {
		return {
			taskList: [],
			loading: false,
			refreshing: false,
			selectedStatus: '',
			
			// 表单相关
			isEditing: false,
			submitting: false,
			currentTask: {},
			
			// 任务状态
			taskStatuses: [
				{ label: '全部', value: '' },
				{ label: '待完成', value: 'pending' },
				{ label: '已完成', value: 'completed' },
				{ label: '已过期', value: 'overdue' }
			],
			
			// 表单字段配置
			formFields: [
				{
					key: 'title',
					label: '任务标题',
					type: 'text',
					placeholder: '请输入任务标题',
					required: true,
					validator: (value) => {
						if (value.length < 2) return '任务标题至少2个字符';
						if (value.length > 50) return '任务标题不能超过50个字符';
						return true;
					}
				},
				{
					key: 'description',
					label: '任务描述',
					type: 'textarea',
					placeholder: '请详细描述任务内容',
					required: true
				},
				{
					key: 'category',
					label: '任务分类',
					type: 'select',
					placeholder: '请选择任务分类',
					required: true,
					options: [
						{ label: '健康', value: '健康' },
						{ label: '医疗', value: '医疗' },
						{ label: '生活', value: '生活' },
						{ label: '社交', value: '社交' },
						{ label: '学习', value: '学习' },
						{ label: '其他', value: '其他' }
					]
				},
				{
					key: 'priority',
					label: '优先级',
					type: 'select',
					placeholder: '请选择优先级',
					required: true,
					options: [
						{ label: '高', value: 'high' },
						{ label: '中', value: 'medium' },
						{ label: '低', value: 'low' }
					]
				},
				{
					key: 'deadline',
					label: '截止日期',
					type: 'date',
					placeholder: '请选择截止日期',
					required: true
				},
				{
					key: 'time',
					label: '截止时间',
					type: 'time',
					placeholder: '请选择截止时间',
					required: false
				},
				{
					key: 'location',
					label: '任务地点',
					type: 'text',
					placeholder: '请输入任务地点（可选）'
				}
			]
		}
	},
	computed: {
		// 筛选后的任务列表
		filteredTasks() {
			if (!this.selectedStatus) {
				return this.taskList;
			}
			return this.taskList.filter(item => item.status === this.selectedStatus);
		},
		
		// 任务统计数据
		taskStats() {
			const total = this.taskList.length;
			const pending = this.taskList.filter(t => t.status === 'pending').length;
			const completed = this.taskList.filter(t => t.status === 'completed').length;
			const overdue = this.taskList.filter(t => t.status === 'overdue').length;
			
			return [
				{ label: '总任务', value: total, icon: 'task-line', color: '#fff', bgColor: '#ff8a00' },
				{ label: '待完成', value: pending, icon: 'time-line', color: '#fff', bgColor: '#ff9800' },
				{ label: '已完成', value: completed, icon: 'check-line', color: '#fff', bgColor: '#4caf50' },
				{ label: '已过期', value: overdue, icon: 'error-warning-line', color: '#fff', bgColor: '#f44336' }
			];
		}
	},
	onLoad() {
		this.loadTasks();
	},
	methods: {
		// 加载任务列表
		async loadTasks() {
			try {
				this.loading = true;

				// 使用新的CRUD API
				const params = {
					status: this.selectedStatus,
					page: 1,
					pageSize: 50
				};

				const result = await crudAPI.getTasks(params);

				if (result.success) {
					this.taskList = result.data.list;

					// 显示成功提示
					if (result.data.list.length > 0) {
						FeedbackUtils.showSuccess(`已加载 ${result.data.list.length} 个任务`);
					}
				} else {
					FeedbackUtils.showError(result.message || '数据加载失败，请重试');
					// 如果网络失败，显示空状态而不是错误
					this.taskList = [];
				}
			} catch (error) {
				console.error('加载任务列表失败:', error);
				FeedbackUtils.showError('数据加载失败，请重试');
				this.taskList = [];
			} finally {
				this.loading = false;
				this.refreshing = false;
			}
		},

		// 选择状态
		selectStatus(status) {
			FeedbackUtils.lightFeedback();
			this.selectedStatus = status;
		},
		
		// 下拉刷新
		refreshData() {
			FeedbackUtils.lightFeedback();
			this.refreshing = true;
			this.loadTasks();
		},
		
		// 上拉加载更多
		loadMore() {
			// 任务数据通常不会太多，暂不实现分页
		},

		// 查看详情
		viewDetail(item) {
			FeedbackUtils.lightFeedback();
			uni.navigateTo({
				url: `/pages/task/detail?id=${item.id}`
			});
		},

		// 显示添加表单
		showAddForm() {
			this.isEditing = false;
			this.currentTask = {
				deadline: new Date().toISOString().split('T')[0] // 默认今天
			};
			this.$refs.formPopup.open();
		},

		// 编辑任务
		editTask(item) {
			FeedbackUtils.lightFeedback();
			this.isEditing = true;
			this.currentTask = { 
				...item,
				deadline: item.deadline ? item.deadline.split('T')[0] : ''
			};
			this.$refs.formPopup.open();
		},

		// 完成任务
		async completeTask(item) {
			try {
				await FeedbackUtils.showConfirm({
					title: '完成任务',
					content: `确定要标记任务"${item.title}"为已完成吗？`,
					confirmText: '完成',
					cancelText: '取消'
				});

				FeedbackUtils.showLoading('处理中...');

				const result = await crudAPI.updateTask(item.id, { status: 'completed' });

				FeedbackUtils.hideLoading();

				if (result.success) {
					FeedbackUtils.showSuccess('任务已完成');
					this.loadTasks();
				} else {
					FeedbackUtils.showError(result.message || '操作失败');
				}
			} catch (error) {
				FeedbackUtils.hideLoading();
				console.log('用户取消操作');
			}
		},

		// 删除任务
		async deleteTask(item) {
			try {
				await FeedbackUtils.showConfirm({
					title: '删除确认',
					content: `确定要删除任务"${item.title}"吗？此操作不可恢复。`,
					confirmText: '删除',
					cancelText: '取消'
				});

				FeedbackUtils.showLoading('删除中...');

				const result = await crudAPI.deleteTask(item.id);

				FeedbackUtils.hideLoading();

				if (result.success) {
					FeedbackUtils.showSuccess('删除成功');
					this.loadTasks();
				} else {
					FeedbackUtils.showError(result.message || '删除失败');
				}
			} catch (error) {
				FeedbackUtils.hideLoading();
				console.log('用户取消删除');
			}
		},

		// 隐藏表单
		hideForm() {
			this.$refs.formPopup.close();
		},

		// 处理表单提交
		async handleSubmit(formData) {
			try {
				this.submitting = true;

				// 处理数据
				const submitData = {
					title: formData.title,
					description: formData.description,
					category: formData.category,
					priority: formData.priority,
					dueDate: formData.deadline,
					dueTime: formData.time || '09:00',
					location: formData.location || '',
					reminder: true,
					reminderTime: 15
				};

				let result;
				if (this.isEditing) {
					result = await crudAPI.updateTask(this.currentTask.id, submitData);
				} else {
					result = await crudAPI.createTask(submitData);
				}

				if (result.success) {
					FeedbackUtils.showSuccess(this.isEditing ? '更新成功' : '添加成功');
					this.hideForm();
					this.loadTasks();
				} else {
					FeedbackUtils.showError(result.message || '操作失败');
				}
			} catch (error) {
				console.error('提交失败:', error);
				FeedbackUtils.showError('操作失败，请重试');
			} finally {
				this.submitting = false;
			}
		},

		// 获取优先级文本
		getPriorityText(priority) {
			const priorityMap = {
				'high': '高',
				'medium': '中',
				'low': '低'
			};
			return priorityMap[priority] || '中';
		},

		// 格式化日期
		formatDate(dateString) {
			const date = new Date(dateString);
			const now = new Date();
			const diff = date - now;
			const days = Math.ceil(diff / (1000 * 60 * 60 * 24));
			
			if (days < 0) {
				return `已过期 ${Math.abs(days)} 天`;
			} else if (days === 0) {
				return '今天截止';
			} else if (days === 1) {
				return '明天截止';
			} else {
				return `${days} 天后截止`;
			}
		}
	}
}
</script>

<style scoped>
.container {
	background-color: #f5f5f5;
	min-height: 100vh;
	display: flex;
	flex-direction: column;
}

.content-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 30rpx 40rpx;
	background: white;
	border-bottom: 1rpx solid #f0f0f0;
}

.page-title {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
}

.stats-section {
	padding: 20rpx;
}

.stats-grid {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 20rpx;
}

.stat-card {
	background: white;
	border-radius: 20rpx;
	padding: 30rpx;
	display: flex;
	align-items: center;
	gap: 20rpx;
	box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.stat-icon {
	width: 60rpx;
	height: 60rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
}

.stat-info {
	flex: 1;
}

.stat-value {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	display: block;
}

.stat-label {
	font-size: 24rpx;
	color: #666;
	display: block;
	margin-top: 5rpx;
}

.filter-section {
	background: white;
	border-bottom: 1rpx solid #f0f0f0;
}

.filter-tabs {
	display: flex;
	padding: 0 20rpx;
}

.filter-tab {
	flex: 1;
	padding: 30rpx 20rpx;
	text-align: center;
	position: relative;
}

.filter-tab.active {
	border-bottom: 4rpx solid #ff8a00;
}

.tab-text {
	font-size: 28rpx;
	color: #666;
}

.filter-tab.active .tab-text {
	color: #ff8a00;
	font-weight: bold;
}

.task-list {
	flex: 1;
	padding: 20rpx;
}

.task-item {
	margin-bottom: 20rpx;
}

.task-content {
	padding: 30rpx;
}

.task-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 15rpx;
}

.task-title {
	flex: 1;
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-right: 20rpx;
}

.task-priority {
	padding: 6rpx 12rpx;
	border-radius: 12rpx;
	font-size: 22rpx;
}

.task-priority.high {
	background: rgba(244, 67, 54, 0.1);
	color: #f44336;
}

.task-priority.medium {
	background: rgba(255, 152, 0, 0.1);
	color: #ff9800;
}

.task-priority.low {
	background: rgba(76, 175, 80, 0.1);
	color: #4caf50;
}

.task-description {
	font-size: 26rpx;
	color: #666;
	line-height: 1.5;
	margin-bottom: 20rpx;
}

.task-meta {
	display: flex;
	flex-direction: column;
	gap: 10rpx;
	margin-bottom: 20rpx;
}

.meta-item {
	display: flex;
	align-items: center;
	gap: 10rpx;
}

.meta-text {
	font-size: 24rpx;
	color: #999;
}

.task-actions {
	display: flex;
	gap: 15rpx;
	flex-wrap: wrap;
}

.empty {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 100rpx 40rpx;
	gap: 30rpx;
}

.empty-text {
	font-size: 28rpx;
	color: #999;
}

.form-popup {
	background: white;
	border-radius: 30rpx 30rpx 0 0;
	max-height: 90vh;
	overflow-y: auto;
}
</style>
