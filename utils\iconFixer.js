/**
 * 图标修复工具
 * 自动检测和修复项目中的图标问题
 */

import { IconConfig, hasIcon, getIconEmoji } from './iconConfig.js'
import { getCompatibleIconName, DeprecatedIconMap } from './iconCompatibility.js'

// 图标问题类型
export const IconIssueTypes = {
  NOT_FOUND: 'not_found',           // 图标不存在
  DEPRECATED: 'deprecated',         // 图标已废弃
  EMOJI_ISSUE: 'emoji_issue',       // Emoji兼容性问题
  DUPLICATE_EMOJI: 'duplicate_emoji', // 重复的Emoji
  MISSING_SVG: 'missing_svg'        // 缺少SVG文件
}

// 问题严重程度
export const IssueSeverity = {
  HIGH: 'high',     // 高：影响功能
  MEDIUM: 'medium', // 中：影响体验
  LOW: 'low'        // 低：优化建议
}

/**
 * 扫描项目中的图标使用情况
 * @param {Array} iconUsages - 图标使用列表 [{name, file, line, context}]
 * @returns {Object} 扫描结果
 */
export function scanIconIssues(iconUsages) {
  const issues = []
  const statistics = {
    total: iconUsages.length,
    normal: 0,
    deprecated: 0,
    notFound: 0,
    emojiIssues: 0
  }

  iconUsages.forEach(usage => {
    const iconName = usage.name
    const compatibleName = getCompatibleIconName(iconName)
    
    // 检查图标是否存在
    if (!hasIcon(compatibleName)) {
      issues.push({
        type: IconIssueTypes.NOT_FOUND,
        severity: IssueSeverity.HIGH,
        iconName,
        file: usage.file,
        line: usage.line,
        context: usage.context,
        message: `图标 "${iconName}" 不存在`,
        suggestion: '使用其他可用图标或添加图标配置'
      })
      statistics.notFound++
      return
    }

    // 检查是否使用了废弃图标
    if (DeprecatedIconMap[iconName]) {
      issues.push({
        type: IconIssueTypes.DEPRECATED,
        severity: IssueSeverity.MEDIUM,
        iconName,
        compatibleName,
        file: usage.file,
        line: usage.line,
        context: usage.context,
        message: `图标 "${iconName}" 已废弃`,
        suggestion: `替换为 "${compatibleName}"`
      })
      statistics.deprecated++
      return
    }

    // 检查Emoji兼容性
    const emoji = getIconEmoji(compatibleName)
    if (hasEmojiCompatibilityIssue(emoji)) {
      issues.push({
        type: IconIssueTypes.EMOJI_ISSUE,
        severity: IssueSeverity.LOW,
        iconName,
        emoji,
        file: usage.file,
        line: usage.line,
        context: usage.context,
        message: `图标 "${iconName}" 的Emoji "${emoji}" 可能有兼容性问题`,
        suggestion: '考虑使用SVG图标替代'
      })
      statistics.emojiIssues++
      return
    }

    statistics.normal++
  })

  return {
    issues,
    statistics,
    summary: generateIssueSummary(issues, statistics)
  }
}

/**
 * 检查Emoji是否有兼容性问题
 * @param {string} emoji - Emoji字符
 * @returns {boolean} 是否有问题
 */
function hasEmojiCompatibilityIssue(emoji) {
  // 复合Emoji（可能在某些设备上显示异常）
  const problematicEmojis = [
    '👨‍👩‍👧‍👦', // 家庭
    '👨‍⚕️',      // 男医生
    '👩‍⚕️',      // 女医生
    '🏘️',        // 房屋
    '🍽️',        // 餐具
    '🗣️',        // 说话
    '🧑‍🦽',      // 轮椅使用者
    '🧑‍🦯'       // 盲人
  ]
  
  return problematicEmojis.includes(emoji)
}

/**
 * 生成问题摘要
 * @param {Array} issues - 问题列表
 * @param {Object} statistics - 统计信息
 * @returns {Object} 摘要信息
 */
function generateIssueSummary(issues, statistics) {
  const highIssues = issues.filter(issue => issue.severity === IssueSeverity.HIGH)
  const mediumIssues = issues.filter(issue => issue.severity === IssueSeverity.MEDIUM)
  const lowIssues = issues.filter(issue => issue.severity === IssueSeverity.LOW)

  return {
    totalIssues: issues.length,
    highPriorityIssues: highIssues.length,
    mediumPriorityIssues: mediumIssues.length,
    lowPriorityIssues: lowIssues.length,
    healthScore: calculateHealthScore(statistics),
    recommendations: generateRecommendations(issues)
  }
}

/**
 * 计算图标系统健康度评分
 * @param {Object} statistics - 统计信息
 * @returns {number} 评分 (0-100)
 */
function calculateHealthScore(statistics) {
  if (statistics.total === 0) return 100
  
  const normalRatio = statistics.normal / statistics.total
  const deprecatedPenalty = (statistics.deprecated / statistics.total) * 20
  const notFoundPenalty = (statistics.notFound / statistics.total) * 50
  const emojiPenalty = (statistics.emojiIssues / statistics.total) * 10
  
  const score = Math.max(0, (normalRatio * 100) - deprecatedPenalty - notFoundPenalty - emojiPenalty)
  return Math.round(score)
}

/**
 * 生成修复建议
 * @param {Array} issues - 问题列表
 * @returns {Array} 建议列表
 */
function generateRecommendations(issues) {
  const recommendations = []
  
  const notFoundIssues = issues.filter(issue => issue.type === IconIssueTypes.NOT_FOUND)
  if (notFoundIssues.length > 0) {
    recommendations.push({
      priority: IssueSeverity.HIGH,
      title: '修复不存在的图标',
      description: `有 ${notFoundIssues.length} 个图标不存在，需要立即修复`,
      action: '替换为可用图标或添加图标配置'
    })
  }
  
  const deprecatedIssues = issues.filter(issue => issue.type === IconIssueTypes.DEPRECATED)
  if (deprecatedIssues.length > 0) {
    recommendations.push({
      priority: IssueSeverity.MEDIUM,
      title: '更新废弃图标',
      description: `有 ${deprecatedIssues.length} 个废弃图标需要更新`,
      action: '使用兼容性映射自动替换'
    })
  }
  
  const emojiIssues = issues.filter(issue => issue.type === IconIssueTypes.EMOJI_ISSUE)
  if (emojiIssues.length > 0) {
    recommendations.push({
      priority: IssueSeverity.LOW,
      title: '优化Emoji兼容性',
      description: `有 ${emojiIssues.length} 个图标可能有兼容性问题`,
      action: '考虑替换为SVG图标'
    })
  }
  
  return recommendations
}

/**
 * 自动修复图标问题
 * @param {Array} issues - 问题列表
 * @returns {Object} 修复结果
 */
export function autoFixIcons(issues) {
  const fixResults = {
    fixed: [],
    failed: [],
    suggestions: []
  }
  
  issues.forEach(issue => {
    switch (issue.type) {
      case IconIssueTypes.DEPRECATED:
        // 自动替换废弃图标
        const fixResult = fixDeprecatedIcon(issue)
        if (fixResult.success) {
          fixResults.fixed.push({
            ...issue,
            fixAction: `替换为 "${fixResult.newIconName}"`
          })
        } else {
          fixResults.failed.push({
            ...issue,
            reason: fixResult.reason
          })
        }
        break
        
      case IconIssueTypes.NOT_FOUND:
        // 提供替代建议
        const suggestions = suggestAlternativeIcons(issue.iconName)
        fixResults.suggestions.push({
          ...issue,
          alternatives: suggestions
        })
        break
        
      case IconIssueTypes.EMOJI_ISSUE:
        // 提供SVG替代建议
        fixResults.suggestions.push({
          ...issue,
          suggestion: '建议使用SVG图标替代'
        })
        break
    }
  })
  
  return fixResults
}

/**
 * 修复废弃图标
 * @param {Object} issue - 图标问题
 * @returns {Object} 修复结果
 */
function fixDeprecatedIcon(issue) {
  const newIconName = getCompatibleIconName(issue.iconName)
  
  if (hasIcon(newIconName)) {
    return {
      success: true,
      newIconName,
      oldIconName: issue.iconName
    }
  }
  
  return {
    success: false,
    reason: `兼容图标 "${newIconName}" 也不存在`
  }
}

/**
 * 建议替代图标
 * @param {string} iconName - 图标名称
 * @returns {Array} 建议列表
 */
function suggestAlternativeIcons(iconName) {
  const suggestions = []
  
  // 基于名称相似性建议
  const allIconNames = Object.keys(IconConfig)
  const nameParts = iconName.toLowerCase().split('-')
  
  allIconNames.forEach(availableIcon => {
    const availableParts = availableIcon.toLowerCase().split('-')
    const commonParts = nameParts.filter(part => availableParts.includes(part))
    
    if (commonParts.length > 0) {
      suggestions.push({
        name: availableIcon,
        similarity: commonParts.length / nameParts.length,
        reason: `包含相同关键词: ${commonParts.join(', ')}`
      })
    }
  })
  
  // 按相似度排序
  suggestions.sort((a, b) => b.similarity - a.similarity)
  
  return suggestions.slice(0, 3) // 返回前3个建议
}

/**
 * 生成修复报告
 * @param {Object} scanResult - 扫描结果
 * @param {Object} fixResult - 修复结果
 * @returns {Object} 报告
 */
export function generateFixReport(scanResult, fixResult) {
  return {
    timestamp: new Date().toISOString(),
    summary: {
      totalIcons: scanResult.statistics.total,
      totalIssues: scanResult.issues.length,
      fixedIssues: fixResult.fixed.length,
      remainingIssues: fixResult.failed.length + fixResult.suggestions.length,
      healthScore: scanResult.summary.healthScore
    },
    details: {
      scanResult,
      fixResult
    },
    recommendations: scanResult.summary.recommendations
  }
}

export default {
  IconIssueTypes,
  IssueSeverity,
  scanIconIssues,
  autoFixIcons,
  generateFixReport
}
