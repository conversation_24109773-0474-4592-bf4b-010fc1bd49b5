<template>
	<view class="test-container">
		<view class="header">
			<text class="title">首页修复测试</text>
		</view>
		
		<view class="test-section">
			<text class="section-title">测试结果</text>
			
			<view class="test-item">
				<text class="test-label">原始首页:</text>
				<button class="test-btn" @click="testOriginalHome">测试原始首页</button>
			</view>
			
			<view class="test-item">
				<text class="test-label">简化首页:</text>
				<button class="test-btn" @click="testSimpleHome">测试简化首页</button>
			</view>
			
			<view class="test-item">
				<text class="test-label">系统信息:</text>
				<text class="test-result">{{ systemInfo }}</text>
			</view>
			
			<view class="test-item">
				<text class="test-label">错误日志:</text>
				<view class="error-log">
					<text v-if="errors.length === 0" class="no-error">暂无错误</text>
					<view v-else v-for="(error, index) in errors" :key="index" class="error-item">
						<text class="error-text">{{ error }}</text>
					</view>
				</view>
			</view>
		</view>
		
		<view class="actions">
			<button class="action-btn primary" @click="clearErrors">清除错误</button>
			<button class="action-btn" @click="goBack">返回</button>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			systemInfo: '',
			errors: []
		}
	},
	
	onLoad() {
		this.getSystemInfo()
	},
	
	methods: {
		getSystemInfo() {
			try {
				const info = uni.getSystemInfoSync()
				this.systemInfo = `${info.platform} ${info.screenWidth}x${info.screenHeight}`
			} catch (error) {
				this.addError('获取系统信息失败: ' + error.message)
			}
		},
		
		testOriginalHome() {
			try {
				uni.navigateTo({
					url: '/pages/home/<USER>',
					fail: (error) => {
						this.addError('跳转原始首页失败: ' + error.errMsg)
					}
				})
			} catch (error) {
				this.addError('测试原始首页失败: ' + error.message)
			}
		},
		
		testSimpleHome() {
			try {
				uni.navigateTo({
					url: '/pages/home/<USER>',
					fail: (error) => {
						this.addError('跳转简化首页失败: ' + error.errMsg)
					}
				})
			} catch (error) {
				this.addError('测试简化首页失败: ' + error.message)
			}
		},
		
		addError(error) {
			this.errors.unshift(`${new Date().toLocaleTimeString()}: ${error}`)
		},
		
		clearErrors() {
			this.errors = []
		},
		
		goBack() {
			uni.navigateBack()
		}
	}
}
</script>

<style scoped>
.test-container {
	padding: 20rpx;
	background: #f5f5f5;
	min-height: 100vh;
}

.header {
	text-align: center;
	margin-bottom: 40rpx;
}

.title {
	font-size: 40rpx;
	font-weight: bold;
	color: #333;
}

.test-section {
	background: white;
	border-radius: 16rpx;
	padding: 24rpx;
	margin-bottom: 24rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 20rpx;
	display: block;
}

.test-item {
	margin-bottom: 20rpx;
	padding-bottom: 20rpx;
	border-bottom: 1rpx solid #eee;
}

.test-item:last-child {
	border-bottom: none;
}

.test-label {
	font-size: 28rpx;
	color: #666;
	display: block;
	margin-bottom: 10rpx;
}

.test-btn {
	background: #ff8a00;
	color: white;
	border: none;
	border-radius: 12rpx;
	padding: 16rpx 32rpx;
	font-size: 26rpx;
}

.test-result {
	font-size: 26rpx;
	color: #333;
	background: #f9f9f9;
	padding: 16rpx;
	border-radius: 8rpx;
	display: block;
}

.error-log {
	max-height: 300rpx;
	overflow-y: auto;
}

.no-error {
	font-size: 26rpx;
	color: #999;
	text-align: center;
	padding: 20rpx;
	display: block;
}

.error-item {
	background: #fff5f5;
	border: 1rpx solid #fecaca;
	border-radius: 8rpx;
	padding: 12rpx;
	margin-bottom: 8rpx;
}

.error-text {
	font-size: 24rpx;
	color: #dc2626;
}

.actions {
	display: flex;
	gap: 20rpx;
	justify-content: center;
}

.action-btn {
	background: #6b7280;
	color: white;
	border: none;
	border-radius: 12rpx;
	padding: 20rpx 40rpx;
	font-size: 28rpx;
}

.action-btn.primary {
	background: #ff8a00;
}
</style>
