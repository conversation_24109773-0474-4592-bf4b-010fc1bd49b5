<template>
	<view class="container">
		<!-- 服务人员列表 -->
		<view class="service-list">
			<view class="service-item" v-for="(person, index) in servicePersons" :key="index" @click="contact<PERSON><PERSON>(person)">
				<view class="person-avatar">
					<image :src="person.avatar" class="avatar-image" mode="aspectFill" v-if="person.avatar"></image>
					<Icon name="user-line" size="40rpx" color="#999" v-else></Icon>
				</view>
				<view class="person-info">
					<view class="person-header">
						<text class="person-name">{{person.name}}</text>
						<view class="person-status" :class="person.status">
							<text class="status-text">{{getStatusText(person.status)}}</text>
						</view>
					</view>
					<text class="person-role">{{person.role}}</text>
					<text class="person-department">{{person.department}}</text>
					<view class="person-tags">
						<text class="person-tag" v-for="tag in person.tags" :key="tag">{{tag}}</text>
					</view>
				</view>
				<view class="contact-actions">
					<view class="action-icon call" @click.stop="makeCall(person)">
						<Icon name="phone-line" size="24rpx" color="white"></Icon>
					</view>
					<view class="action-icon message" @click.stop="sendMessage(person)">
						<Icon name="message-line" size="24rpx" color="white"></Icon>
					</view>
				</view>
			</view>
		</view>

		<!-- 服务分类 -->
		<view class="category-section">
			<view class="section-header">
				<Icon name="customer-service-line" size="32rpx" color="#ff8a00"></Icon>
				<text class="section-title">服务分类</text>
			</view>
			<view class="category-grid">
				<view class="category-item" v-for="(category, index) in serviceCategories" :key="index" @click="contactCategory(category)">
					<view class="category-icon" :style="{backgroundColor: category.color}">
						<Icon :name="category.icon" size="40rpx" color="white"></Icon>
					</view>
					<text class="category-title">{{category.title}}</text>
					<text class="category-desc">{{category.description}}</text>
				</view>
			</view>
		</view>

		<!-- 在线客服 -->
		<view class="online-service">
			<view class="section-header">
				<Icon name="chat-3-line" size="32rpx" color="#ff8a00"></Icon>
				<text class="section-title">在线客服</text>
			</view>
			<view class="service-options">
				<view class="option-item" @click="startChat">
					<view class="option-icon chat">
						<Icon name="chat-3-line" size="32rpx" color="white"></Icon>
					</view>
					<view class="option-content">
						<text class="option-title">在线咨询</text>
						<text class="option-desc">7×24小时在线服务</text>
					</view>
					<view class="option-status online">
						<text class="status-text">在线</text>
					</view>
				</view>

				<view class="option-item" @click="callHotline">
					<view class="option-icon hotline">
						<Icon name="phone-line" size="32rpx" color="white"></Icon>
					</view>
					<view class="option-content">
						<text class="option-title">客服热线</text>
						<text class="option-desc">************</text>
					</view>
					<Icon name="arrow-right-s-line" size="20rpx" color="#999"></Icon>
				</view>

				<view class="option-item" @click="submitFeedback">
					<view class="option-icon feedback">
						<Icon name="feedback-line" size="32rpx" color="white"></Icon>
					</view>
					<view class="option-content">
						<text class="option-title">意见反馈</text>
						<text class="option-desc">提交问题和建议</text>
					</view>
					<Icon name="arrow-right-s-line" size="20rpx" color="#999"></Icon>
				</view>
			</view>
		</view>

		<!-- 常见问题 -->
		<view class="faq-section">
			<view class="section-header">
				<Icon name="question-line" size="32rpx" color="#ff8a00"></Icon>
				<text class="section-title">常见问题</text>
				<text class="section-more" @click="viewAllFAQ">查看全部</text>
			</view>
			<view class="faq-list">
				<view class="faq-item" v-for="(faq, index) in frequentQuestions" :key="index" @click="toggleFAQ(index)">
					<view class="faq-question">
						<text class="question-text">{{faq.question}}</text>
						<Icon :name="faq.expanded ? 'arrow-up-s-line' : 'arrow-down-s-line'" size="24rpx" color="#999"></Icon>
					</view>
					<view class="faq-answer" v-if="faq.expanded">
						<text class="answer-text">{{faq.answer}}</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 服务时间 -->
		<view class="service-time">
			<view class="section-header">
				<Icon name="time-line" size="32rpx" color="#ff8a00"></Icon>
				<text class="section-title">服务时间</text>
			</view>
			<view class="time-info">
				<view class="time-item">
					<text class="time-label">在线客服</text>
					<text class="time-value">7×24小时</text>
				</view>
				<view class="time-item">
					<text class="time-label">电话客服</text>
					<text class="time-value">9:00-18:00</text>
				</view>
				<view class="time-item">
					<text class="time-label">上门服务</text>
					<text class="time-value">8:00-20:00</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import Icon from '@/components/Icon/Icon.vue'

export default {
	components: {
		Icon
	},
	data() {
		return {
			servicePersons: [
				{
					id: 1,
					name: '李护士',
					role: '专业护理师',
					department: '护理部',
					phone: '138****1234',
					avatar: '/static/avatar/nurse1.jpg',
					status: 'online',
					tags: ['专业护理', '经验丰富']
				},
				{
					id: 2,
					name: '王医生',
					role: '康复医师',
					department: '康复科',
					phone: '139****5678',
					avatar: '/static/avatar/doctor1.jpg',
					status: 'busy',
					tags: ['康复训练', '理疗专家']
				},
				{
					id: 3,
					name: '张社工',
					role: '社会工作者',
					department: '社工部',
					phone: '137****9012',
					avatar: '/static/avatar/social-worker1.jpg',
					status: 'online',
					tags: ['心理疏导', '生活指导']
				},
				{
					id: 4,
					name: '刘营养师',
					role: '营养师',
					department: '营养科',
					phone: '136****3456',
					avatar: '/static/avatar/nutritionist1.jpg',
					status: 'offline',
					tags: ['营养配餐', '健康饮食']
				}
			],
			serviceCategories: [
				{
					title: '医疗服务',
					description: '医生问诊、健康咨询',
					icon: 'stethoscope-line',
					color: '#4caf50'
				},
				{
					title: '护理服务',
					description: '专业护理、生活照料',
					icon: 'heart-3-line',
					color: '#e91e63'
				},
				{
					title: '康复训练',
					description: '康复指导、理疗服务',
					icon: 'heart-pulse-line',
					color: '#2196f3'
				},
				{
					title: '心理关怀',
					description: '心理疏导、情感支持',
					icon: 'emotion-line',
					color: '#9c27b0'
				},
				{
					title: '营养指导',
					description: '营养配餐、饮食建议',
					icon: 'restaurant-line',
					color: '#ff9800'
				},
				{
					title: '生活服务',
					description: '日常照料、家务协助',
					icon: 'home-4-line',
					color: '#607d8b'
				}
			],
			frequentQuestions: [
				{
					question: '如何预约上门服务？',
					answer: '您可以通过APP预约功能或拨打客服热线************预约上门服务，我们会在24小时内安排专业人员上门。',
					expanded: false
				},
				{
					question: '服务费用如何计算？',
					answer: '服务费用根据服务类型、时长和专业程度确定。基础护理服务50元/小时，专业医疗服务100元/小时。',
					expanded: false
				},
				{
					question: '紧急情况如何处理？',
					answer: '遇到紧急情况请立即拨打120急救电话，同时可通过APP紧急联系功能通知我们的服务团队。',
					expanded: false
				}
			]
		}
	},
	methods: {
		getStatusText(status) {
			const statusMap = {
				'online': '在线',
				'offline': '离线',
				'busy': '忙碌'
			};
			return statusMap[status] || '未知';
		},
		contactPerson(person) {
			uni.navigateTo({
				url: `/pages/contact/person-detail?id=${person.id}`
			});
		},
		makeCall(person) {
			uni.makePhoneCall({
				phoneNumber: person.phone.replace(/\*/g, '')
			});
		},
		sendMessage(person) {
			uni.navigateTo({
				url: `/pages/contact/message?personId=${person.id}`
			});
		},
		contactCategory(category) {
			uni.navigateTo({
				url: `/pages/service/category?type=${category.title}`
			});
		},
		startChat() {
			uni.navigateTo({
				url: '/pages/service/online-chat'
			});
		},
		callHotline() {
			uni.makePhoneCall({
				phoneNumber: '************'
			});
		},
		submitFeedback() {
			uni.navigateTo({
				url: '/pages/feedback/submit'
			});
		},
		viewAllFAQ() {
			uni.navigateTo({
				url: '/pages/help/faq'
			});
		},
		toggleFAQ(index) {
			this.frequentQuestions[index].expanded = !this.frequentQuestions[index].expanded;
		}
	}
}
</script>

<style scoped>
.container {
	background: #f8f9fa;
	min-height: 100vh;
}

.service-list {
	padding: 40rpx;
}

.service-item {
	background: white;
	border-radius: 30rpx;
	padding: 30rpx;
	margin-bottom: 30rpx;
	display: flex;
	align-items: center;
	gap: 25rpx;
	box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.person-avatar {
	width: 100rpx;
	height: 100rpx;
	border-radius: 50%;
	background: #f0f0f0;
	display: flex;
	align-items: center;
	justify-content: center;
	overflow: hidden;
}

.avatar-image {
	width: 100%;
	height: 100%;
}

.person-info {
	flex: 1;
}

.person-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 10rpx;
}

.person-name {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.person-status {
	padding: 6rpx 15rpx;
	border-radius: 15rpx;
	font-size: 20rpx;
}

.person-status.online {
	background: rgba(76, 175, 80, 0.1);
	color: #4caf50;
}

.person-status.offline {
	background: rgba(158, 158, 158, 0.1);
	color: #9e9e9e;
}

.person-status.busy {
	background: rgba(255, 152, 0, 0.1);
	color: #ff9800;
}

.person-role {
	font-size: 26rpx;
	color: #666;
	display: block;
	margin-bottom: 5rpx;
}

.person-department {
	font-size: 24rpx;
	color: #999;
	display: block;
	margin-bottom: 15rpx;
}

.person-tags {
	display: flex;
	gap: 8rpx;
}

.person-tag {
	background: rgba(255, 138, 0, 0.1);
	color: #ff8a00;
	font-size: 20rpx;
	padding: 4rpx 12rpx;
	border-radius: 12rpx;
}

.contact-actions {
	display: flex;
	flex-direction: column;
	gap: 15rpx;
}

.action-icon {
	width: 60rpx;
	height: 60rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
}

.action-icon.call {
	background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);
}

.action-icon.message {
	background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);
}

.category-section, .online-service, .faq-section, .service-time {
	background: white;
	margin: 40rpx;
	border-radius: 30rpx;
	padding: 40rpx;
	box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.section-header {
	display: flex;
	align-items: center;
	gap: 15rpx;
	margin-bottom: 30rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	flex: 1;
}

.section-more {
	font-size: 26rpx;
	color: #ff8a00;
}

.category-grid {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	gap: 25rpx;
}

.category-item {
	text-align: center;
	padding: 30rpx 20rpx;
	background: #f8f9fa;
	border-radius: 25rpx;
}

.category-icon {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	margin: 0 auto 20rpx;
}

.category-title {
	font-size: 26rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 8rpx;
}

.category-desc {
	font-size: 22rpx;
	color: #666;
	display: block;
}

.service-options {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

.option-item {
	display: flex;
	align-items: center;
	gap: 20rpx;
	padding: 25rpx;
	background: #f8f9fa;
	border-radius: 20rpx;
}

.option-icon {
	width: 60rpx;
	height: 60rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
}

.option-icon.chat {
	background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);
}

.option-icon.hotline {
	background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);
}

.option-icon.feedback {
	background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
}

.option-content {
	flex: 1;
}

.option-title {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 5rpx;
}

.option-desc {
	font-size: 24rpx;
	color: #666;
	display: block;
}

.option-status {
	padding: 6rpx 15rpx;
	border-radius: 15rpx;
	font-size: 20rpx;
}

.option-status.online {
	background: rgba(76, 175, 80, 0.1);
	color: #4caf50;
}

.faq-list {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

.faq-item {
	border: 1rpx solid #f0f0f0;
	border-radius: 20rpx;
	overflow: hidden;
}

.faq-question {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 25rpx 30rpx;
	background: #f8f9fa;
}

.question-text {
	font-size: 26rpx;
	color: #333;
	font-weight: 500;
}

.faq-answer {
	padding: 25rpx 30rpx;
	background: white;
}

.answer-text {
	font-size: 24rpx;
	color: #666;
	line-height: 1.6;
	display: block;
}

.time-info {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

.time-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20rpx;
	background: #f8f9fa;
	border-radius: 20rpx;
}

.time-label {
	font-size: 26rpx;
	color: #333;
	font-weight: bold;
}

.time-value {
	font-size: 26rpx;
	color: #ff8a00;
	font-weight: bold;
}
</style>
