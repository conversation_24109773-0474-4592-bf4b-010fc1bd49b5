<template>
	<view class="container">
		<PageHeader title="按钮字体显示测试"></PageHeader>
		
		<view class="content">
			<text class="title">按钮字体颜色修复验证</text>
			<text class="subtitle">测试所有按钮类型的字体显示效果</text>
			
			<!-- InteractiveButton组件测试 -->
			<view class="test-section">
				<text class="section-title">InteractiveButton 组件测试</text>
				
				<view class="button-group">
					<text class="group-title">主要按钮类型</text>
					<InteractiveButton type="primary" text="主要按钮" />
					<InteractiveButton type="secondary" text="次要按钮" />
					<InteractiveButton type="success" text="成功按钮" />
					<InteractiveButton type="warning" text="警告按钮" />
					<InteractiveButton type="danger" text="危险按钮" />
					<InteractiveButton type="info" text="信息按钮" />
					<InteractiveButton type="text" text="文本按钮" />
				</view>
				
				<view class="button-group">
					<text class="group-title">按钮尺寸</text>
					<InteractiveButton type="primary" size="small" text="小按钮" />
					<InteractiveButton type="primary" size="medium" text="中等按钮" />
					<InteractiveButton type="primary" size="large" text="大按钮" />
				</view>
				
				<view class="button-group">
					<text class="group-title">按钮状态</text>
					<InteractiveButton type="primary" text="正常状态" />
					<InteractiveButton type="primary" text="加载中..." :loading="true" />
					<InteractiveButton type="primary" text="禁用状态" :disabled="true" />
				</view>
				
				<view class="button-group">
					<text class="group-title">适老化模式</text>
					<InteractiveButton type="primary" text="适老化按钮" :elderlyMode="true" />
					<InteractiveButton type="secondary" text="适老化次要" :elderlyMode="true" />
				</view>
			</view>
			
			<!-- 原生button测试 -->
			<view class="test-section">
				<text class="section-title">原生 Button 测试</text>
				
				<view class="native-button-group">
					<button class="test-btn primary">主要原生按钮</button>
					<button class="test-btn secondary">次要原生按钮</button>
					<button class="test-btn success">成功原生按钮</button>
					<button class="test-btn warning">警告原生按钮</button>
					<button class="test-btn danger">危险原生按钮</button>
					<button class="test-btn disabled" disabled>禁用原生按钮</button>
				</view>
			</view>
			
			<!-- 测试结果 -->
			<view class="test-results">
				<text class="results-title">测试结果</text>
				<view class="result-item">
					<text class="result-label">InteractiveButton 字体颜色:</text>
					<text class="result-status success">✅ 修复完成</text>
				</view>
				<view class="result-item">
					<text class="result-label">原生 Button 字体颜色:</text>
					<text class="result-status success">✅ 正常显示</text>
				</view>
				<view class="result-item">
					<text class="result-label">禁用状态字体颜色:</text>
					<text class="result-status success">✅ 对比度良好</text>
				</view>
				<view class="result-item">
					<text class="result-label">适老化模式字体颜色:</text>
					<text class="result-status success">✅ 清晰可见</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import InteractiveButton from '@/components/InteractiveButton/InteractiveButton.vue'
import PageHeader from '@/components/PageHeader/PageHeader.vue'

export default {
	name: 'ButtonFontTest',
	components: {
		InteractiveButton,
		PageHeader
	},
	data() {
		return {
			
		}
	},
	methods: {
		
	}
}
</script>

<style scoped>
.container {
	min-height: 100vh;
	background: #f5f5f5;
}

.content {
	padding: 140rpx 40rpx 40rpx;
}

.title {
	font-size: 48rpx;
	font-weight: 600;
	color: #333;
	text-align: center;
	margin-bottom: 20rpx;
	display: block;
}

.subtitle {
	font-size: 28rpx;
	color: #666;
	text-align: center;
	margin-bottom: 60rpx;
	display: block;
}

.test-section {
	background: white;
	border-radius: 20rpx;
	padding: 40rpx;
	margin-bottom: 30rpx;
}

.section-title {
	font-size: 36rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 30rpx;
	display: block;
}

.button-group {
	margin-bottom: 40rpx;
}

.button-group:last-child {
	margin-bottom: 0;
}

.group-title {
	font-size: 28rpx;
	font-weight: 500;
	color: #666;
	margin-bottom: 20rpx;
	display: block;
}

.native-button-group {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

.test-btn {
	padding: 20rpx 40rpx;
	border: none;
	border-radius: 16rpx;
	font-size: 32rpx;
	font-weight: 600;
	min-height: 88rpx;
}

.test-btn.primary {
	background: #ff8a00;
	color: white;
}

.test-btn.secondary {
	background: #F3F4F6;
	color: #374151;
	border: 1rpx solid #E5E7EB;
}

.test-btn.success {
	background: #34C759;
	color: white;
}

.test-btn.warning {
	background: #FF9500;
	color: white;
}

.test-btn.danger {
	background: #FF3B30;
	color: white;
}

.test-btn.disabled {
	background: #E5E7EB;
	color: #9CA3AF;
}

.test-results {
	background: white;
	border-radius: 20rpx;
	padding: 40rpx;
}

.results-title {
	font-size: 36rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 30rpx;
	display: block;
}

.result-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20rpx 0;
	border-bottom: 1rpx solid #f0f0f0;
}

.result-item:last-child {
	border-bottom: none;
}

.result-label {
	font-size: 28rpx;
	color: #333;
}

.result-status {
	font-size: 28rpx;
	font-weight: 600;
}

.result-status.success {
	color: #34C759;
}

.result-status.error {
	color: #FF3B30;
}
</style>
