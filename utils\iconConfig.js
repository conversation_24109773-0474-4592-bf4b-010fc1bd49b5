/**
 * 图标配置管理
 * 统一管理项目中使用的所有图标
 */

// 图标类型枚举
export const IconTypes = {
  EMOJI: 'emoji',
  IMAGE: 'image',
  FONT: 'font'
}

// 图标分类
export const IconCategories = {
  NAVIGATION: 'navigation',
  FUNCTION: 'function',
  ACTION: 'action',
  STATUS: 'status',
  BUSINESS: 'business',
  ELDERLY: 'elderly',        // 适老化专用
  MEDICAL: 'medical',        // 医疗健康
  SOCIAL: 'social',          // 社交互动
  EMERGENCY: 'emergency',    // 紧急服务
  ENTERTAINMENT: 'entertainment' // 娱乐休闲
}

// 图标配置映射
export const IconConfig = {
  // 导航类图标
  'home-line': {
    type: IconTypes.IMAGE,
    emoji: '🏠',
    svg: '/static/icons/home-line.svg',
    category: IconCategories.NAVIGATION,
    description: '首页'
  },
  'user-line': {
    type: IconTypes.EMOJI,
    emoji: '👤',
    category: IconCategories.NAVIGATION,
    description: '用户'
  },
  'map-line': {
    type: IconTypes.EMOJI,
    emoji: '🗺️',
    category: IconCategories.NAVIGATION,
    description: '地图'
  },
  'search-line': {
    type: IconTypes.IMAGE,
    emoji: '🔍',
    svg: '/static/icons/search-line.svg',
    category: IconCategories.NAVIGATION,
    description: '搜索'
  },
  'menu-line': {
    type: IconTypes.EMOJI,
    emoji: '☰',
    category: IconCategories.NAVIGATION,
    description: '菜单'
  },

  // 功能类图标
  'building-line': {
    type: IconTypes.IMAGE,
    emoji: '🏢',
    svg: '/static/icons/building-line.svg',
    category: IconCategories.FUNCTION,
    description: '建筑/机构'
  },
  'heart-3-line': {
    type: IconTypes.IMAGE,
    emoji: '❤️',
    svg: '/static/icons/heart-3-line.svg',
    category: IconCategories.FUNCTION,
    description: '关爱/健康'
  },
  'money-cny-circle-line': {
    type: IconTypes.IMAGE,
    emoji: '💰',
    svg: '/static/icons/money-cny-circle-line.svg',
    category: IconCategories.FUNCTION,
    description: '金钱/补贴'
  },
  'settings-line': {
    type: IconTypes.EMOJI,
    emoji: '⚙️',
    category: IconCategories.FUNCTION,
    description: '设置'
  },
  'notification-3-line': {
    type: IconTypes.EMOJI,
    emoji: '🔔',
    category: IconCategories.FUNCTION,
    description: '通知'
  },
  'hospital-line': {
    type: IconTypes.EMOJI,
    emoji: '🏥',
    category: IconCategories.FUNCTION,
    description: '医院'
  },
  'community-line': {
    type: IconTypes.EMOJI,
    emoji: '🏘️',
    category: IconCategories.FUNCTION,
    description: '社区'
  },
  'restaurant-line': {
    type: IconTypes.EMOJI,
    emoji: '🍽️',
    category: IconCategories.FUNCTION,
    description: '餐厅'
  },

  // 操作类图标
  'add-line': {
    type: IconTypes.EMOJI,
    emoji: '➕',
    category: IconCategories.ACTION,
    description: '添加'
  },
  'edit-line': {
    type: IconTypes.EMOJI,
    emoji: '✏️',
    category: IconCategories.ACTION,
    description: '编辑'
  },
  'delete-line': {
    type: IconTypes.EMOJI,
    emoji: '🗑️',
    category: IconCategories.ACTION,
    description: '删除'
  },
  'share-line': {
    type: IconTypes.EMOJI,
    emoji: '📤',
    category: IconCategories.ACTION,
    description: '分享'
  },
  'download-line': {
    type: IconTypes.EMOJI,
    emoji: '⬇️',
    category: IconCategories.ACTION,
    description: '下载'
  },
  'upload-line': {
    type: IconTypes.EMOJI,
    emoji: '⬆️',
    category: IconCategories.ACTION,
    description: '上传'
  },
  'copy-line': {
    type: IconTypes.EMOJI,
    emoji: '📋',
    category: IconCategories.ACTION,
    description: '复制'
  },
  'refresh-line': {
    type: IconTypes.EMOJI,
    emoji: '🔄',
    category: IconCategories.ACTION,
    description: '刷新'
  },

  // 状态类图标
  'check-line': {
    type: IconTypes.EMOJI,
    emoji: '✅',
    category: IconCategories.STATUS,
    description: '成功/确认'
  },
  'close-line': {
    type: IconTypes.EMOJI,
    emoji: '❌',
    category: IconCategories.STATUS,
    description: '关闭/错误'
  },
  'error-warning-line': {
    type: IconTypes.EMOJI,
    emoji: '⚠️',
    category: IconCategories.STATUS,
    description: '警告'
  },
  'information-line': {
    type: IconTypes.EMOJI,
    emoji: 'ℹ️',
    category: IconCategories.STATUS,
    description: '信息'
  },
  'loading-line': {
    type: IconTypes.EMOJI,
    emoji: '⏳',
    category: IconCategories.STATUS,
    description: '加载中'
  },

  // 业务类图标
  'wheelchair-line': {
    type: IconTypes.EMOJI,
    emoji: '♿',
    category: IconCategories.ELDERLY,
    description: '无障碍'
  },
  'hotel-bed-line': {
    type: IconTypes.EMOJI,
    emoji: '🛏️',
    category: IconCategories.BUSINESS,
    description: '床位'
  },
  'gift-2-line': {
    type: IconTypes.EMOJI,
    emoji: '🎁',
    category: IconCategories.BUSINESS,
    description: '礼品/福利'
  },
  'government-line': {
    type: IconTypes.EMOJI,
    emoji: '🏛️',
    category: IconCategories.BUSINESS,
    description: '政府'
  },
  'health-book-line': {
    type: IconTypes.EMOJI,
    emoji: '📋',
    category: IconCategories.MEDICAL,
    description: '健康档案'
  },
  'shield-check-line': {
    type: IconTypes.EMOJI,
    emoji: '🛡️',
    category: IconCategories.BUSINESS,
    description: '安全保障'
  },
  'music-2-line': {
    type: IconTypes.EMOJI,
    emoji: '🎵',
    category: IconCategories.ENTERTAINMENT,
    description: '音乐/娱乐'
  },
  'database-line': {
    type: IconTypes.EMOJI,
    emoji: '💾',
    category: IconCategories.BUSINESS,
    description: '数据库'
  },
  'user-heart-line': {
    type: IconTypes.EMOJI,
    emoji: '💖',
    category: IconCategories.ELDERLY,
    description: '用户关怀'
  },
  'user-settings-line': {
    type: IconTypes.EMOJI,
    emoji: '👥',
    category: IconCategories.FUNCTION,
    description: '用户设置'
  },
  'home-gear-line': {
    type: IconTypes.EMOJI,
    emoji: '🏠',
    category: IconCategories.BUSINESS,
    description: '家居设置'
  },
  'customer-service-2-line': {
    type: IconTypes.EMOJI,
    emoji: '🎧',
    category: IconCategories.BUSINESS,
    description: '客服'
  },

  // 通用图标
  'star-line': {
    type: IconTypes.EMOJI,
    emoji: '☆',
    category: IconCategories.STATUS,
    description: '星级/收藏'
  },
  'star-fill': {
    type: IconTypes.EMOJI,
    emoji: '⭐',
    category: IconCategories.STATUS,
    description: '星级/收藏(填充)'
  },
  'phone-line': {
    type: IconTypes.EMOJI,
    emoji: '📞',
    category: IconCategories.ACTION,
    description: '电话'
  },
  'message-line': {
    type: IconTypes.EMOJI,
    emoji: '💬',
    category: IconCategories.ACTION,
    description: '消息'
  },
  'mail-line': {
    type: IconTypes.EMOJI,
    emoji: '📧',
    category: IconCategories.ACTION,
    description: '邮件'
  },
  'location-line': {
    type: IconTypes.EMOJI,
    emoji: '📍',
    category: IconCategories.NAVIGATION,
    description: '位置'
  },
  'calendar-line': {
    type: IconTypes.EMOJI,
    emoji: '📅',
    category: IconCategories.FUNCTION,
    description: '日历'
  },
  'calendar-check-line': {
    type: IconTypes.EMOJI,
    emoji: '📋',
    category: IconCategories.FUNCTION,
    description: '预约管理'
  },
  'time-line': {
    type: IconTypes.EMOJI,
    emoji: '⏰',
    category: IconCategories.FUNCTION,
    description: '时间'
  },
  'camera-line': {
    type: IconTypes.EMOJI,
    emoji: '📷',
    category: IconCategories.ACTION,
    description: '相机'
  },
  'image-line': {
    type: IconTypes.EMOJI,
    emoji: '🖼️',
    category: IconCategories.FUNCTION,
    description: '图片'
  },
  'file-line': {
    type: IconTypes.EMOJI,
    emoji: '📄',
    category: IconCategories.FUNCTION,
    description: '文件'
  },
  'file-pdf-line': {
    type: IconTypes.EMOJI,
    emoji: '📕',
    category: IconCategories.FUNCTION,
    description: 'PDF文件'
  },
  'file-word-line': {
    type: IconTypes.EMOJI,
    emoji: '📘',
    category: IconCategories.FUNCTION,
    description: 'Word文档'
  },
  'file-excel-line': {
    type: IconTypes.EMOJI,
    emoji: '📗',
    category: IconCategories.FUNCTION,
    description: 'Excel表格'
  },
  'folder-line': {
    type: IconTypes.EMOJI,
    emoji: '📁',
    category: IconCategories.FUNCTION,
    description: '文件夹'
  },
  'task-line': {
    type: IconTypes.EMOJI,
    emoji: '✅',
    category: IconCategories.FUNCTION,
    description: '任务'
  },
  'vip-crown-line': {
    type: IconTypes.EMOJI,
    emoji: '👑',
    category: IconCategories.STATUS,
    description: 'VIP皇冠'
  },
  'clipboard-line': {
    type: IconTypes.EMOJI,
    emoji: '📋',
    category: IconCategories.FUNCTION,
    description: '剪贴板'
  },
  'article-line': {
    type: IconTypes.EMOJI,
    emoji: '📰',
    category: IconCategories.FUNCTION,
    description: '文章'
  },

  // 首页专用图标
  'parent-line': {
    type: IconTypes.EMOJI,
    emoji: '👨‍👩‍👧‍👦',
    category: IconCategories.SOCIAL,
    description: '家庭/监护人'
  },
  'music-line': {
    type: IconTypes.EMOJI,
    emoji: '🎶',
    category: IconCategories.ENTERTAINMENT,
    description: '音乐'
  },

  // 箭头图标
  'arrow-right-line': {
    type: IconTypes.EMOJI,
    emoji: '➡️',
    category: IconCategories.NAVIGATION,
    description: '右箭头'
  },
  'arrow-left-line': {
    type: IconTypes.EMOJI,
    emoji: '⬅️',
    category: IconCategories.NAVIGATION,
    description: '左箭头'
  },
  'arrow-up-line': {
    type: IconTypes.EMOJI,
    emoji: '⬆️',
    category: IconCategories.NAVIGATION,
    description: '上箭头'
  },
  'arrow-down-line': {
    type: IconTypes.EMOJI,
    emoji: '⬇️',
    category: IconCategories.NAVIGATION,
    description: '下箭头'
  },

  // 表单图标
  'checkbox-circle-line': {
    type: IconTypes.EMOJI,
    emoji: '⭕',
    category: IconCategories.ACTION,
    description: '复选框(空)'
  },
  'checkbox-circle-fill': {
    type: IconTypes.EMOJI,
    emoji: '✅',
    category: IconCategories.ACTION,
    description: '复选框(选中)'
  },
  'radio-button-line': {
    type: IconTypes.EMOJI,
    emoji: '⚪',
    category: IconCategories.ACTION,
    description: '单选框(空)'
  },
  'radio-button-fill': {
    type: IconTypes.EMOJI,
    emoji: '🔘',
    category: IconCategories.ACTION,
    description: '单选框(选中)'
  },

  // 其他常用图标
  'more-line': {
    type: IconTypes.EMOJI,
    emoji: '⋯',
    category: IconCategories.ACTION,
    description: '更多'
  },
  'eye-line': {
    type: IconTypes.EMOJI,
    emoji: '👁️',
    category: IconCategories.ACTION,
    description: '查看'
  },
  'eye-off-line': {
    type: IconTypes.EMOJI,
    emoji: '🙈',
    category: IconCategories.ACTION,
    description: '隐藏'
  },
  'lock-line': {
    type: IconTypes.EMOJI,
    emoji: '🔒',
    category: IconCategories.STATUS,
    description: '锁定'
  },
  'unlock-line': {
    type: IconTypes.EMOJI,
    emoji: '🔓',
    category: IconCategories.STATUS,
    description: '解锁'
  },

  // 适老化专用图标
  'elderly-care-line': {
    type: IconTypes.EMOJI,
    emoji: '🤝',
    category: IconCategories.ELDERLY,
    description: '老人关怀'
  },
  'elderly-woman-line': {
    type: IconTypes.EMOJI,
    emoji: '👵',
    category: IconCategories.ELDERLY,
    description: '老年女性'
  },
  'walking-stick-line': {
    type: IconTypes.EMOJI,
    emoji: '🦯',
    category: IconCategories.ELDERLY,
    description: '拐杖'
  },
  'hearing-aid-line': {
    type: IconTypes.EMOJI,
    emoji: '🦻',
    category: IconCategories.ELDERLY,
    description: '助听器'
  },
  'glasses-line': {
    type: IconTypes.EMOJI,
    emoji: '👓',
    category: IconCategories.ELDERLY,
    description: '眼镜'
  },
  'pill-line': {
    type: IconTypes.EMOJI,
    emoji: '💊',
    category: IconCategories.ELDERLY,
    description: '药物'
  },
  'large-font-line': {
    type: IconTypes.EMOJI,
    emoji: '🔍',
    category: IconCategories.ELDERLY,
    description: '大字体'
  },
  'voice-line': {
    type: IconTypes.EMOJI,
    emoji: '🗣️',
    category: IconCategories.ELDERLY,
    description: '语音播报'
  },

  // 医疗健康图标
  'medical-cross-line': {
    type: IconTypes.EMOJI,
    emoji: '⚕️',
    category: IconCategories.MEDICAL,
    description: '医疗'
  },
  'ambulance-line': {
    type: IconTypes.EMOJI,
    emoji: '🚑',
    category: IconCategories.MEDICAL,
    description: '救护车'
  },
  'doctor-line': {
    type: IconTypes.EMOJI,
    emoji: '👨‍⚕️',
    category: IconCategories.MEDICAL,
    description: '医生'
  },
  'nurse-line': {
    type: IconTypes.EMOJI,
    emoji: '👩‍⚕️',
    category: IconCategories.MEDICAL,
    description: '护士'
  },
  'blood-pressure-line': {
    type: IconTypes.EMOJI,
    emoji: '🩺',
    category: IconCategories.MEDICAL,
    description: '血压计'
  },
  'heart-rate-line': {
    type: IconTypes.EMOJI,
    emoji: '💓',
    category: IconCategories.MEDICAL,
    description: '心率'
  },
  'temperature-line': {
    type: IconTypes.EMOJI,
    emoji: '🌡️',
    category: IconCategories.MEDICAL,
    description: '体温'
  },
  'medical-report-line': {
    type: IconTypes.EMOJI,
    emoji: '📊',
    category: IconCategories.MEDICAL,
    description: '医疗报告'
  },

  // 社交互动图标
  'family-line': {
    type: IconTypes.EMOJI,
    emoji: '👨‍👩‍👧‍👦',
    category: IconCategories.SOCIAL,
    description: '家庭'
  },
  'friends-line': {
    type: IconTypes.EMOJI,
    emoji: '👥',
    category: IconCategories.SOCIAL,
    description: '朋友'
  },
  'chat-line': {
    type: IconTypes.EMOJI,
    emoji: '💭',
    category: IconCategories.SOCIAL,
    description: '聊天'
  },
  'video-call-line': {
    type: IconTypes.EMOJI,
    emoji: '📹',
    category: IconCategories.SOCIAL,
    description: '视频通话'
  },
  'group-line': {
    type: IconTypes.EMOJI,
    emoji: '👫',
    category: IconCategories.SOCIAL,
    description: '群组'
  },

  // 紧急服务图标
  'emergency-line': {
    type: IconTypes.EMOJI,
    emoji: '🚨',
    category: IconCategories.EMERGENCY,
    description: '紧急情况'
  },
  'sos-line': {
    type: IconTypes.EMOJI,
    emoji: '🆘',
    category: IconCategories.EMERGENCY,
    description: 'SOS求救'
  },
  'fire-line': {
    type: IconTypes.EMOJI,
    emoji: '🔥',
    category: IconCategories.EMERGENCY,
    description: '火警'
  },
  'police-line': {
    type: IconTypes.EMOJI,
    emoji: '👮',
    category: IconCategories.EMERGENCY,
    description: '报警'
  },
  'first-aid-line': {
    type: IconTypes.EMOJI,
    emoji: '🚑',
    category: IconCategories.EMERGENCY,
    description: '急救'
  },

  // 娱乐休闲图标
  'game-line': {
    type: IconTypes.EMOJI,
    emoji: '🎮',
    category: IconCategories.ENTERTAINMENT,
    description: '游戏'
  },
  'book-line': {
    type: IconTypes.EMOJI,
    emoji: '📚',
    category: IconCategories.ENTERTAINMENT,
    description: '阅读'
  },
  'tv-line': {
    type: IconTypes.EMOJI,
    emoji: '📺',
    category: IconCategories.ENTERTAINMENT,
    description: '电视'
  },
  'radio-line': {
    type: IconTypes.EMOJI,
    emoji: '📻',
    category: IconCategories.ENTERTAINMENT,
    description: '收音机'
  },
  'chess-line': {
    type: IconTypes.EMOJI,
    emoji: '♟️',
    category: IconCategories.ENTERTAINMENT,
    description: '象棋'
  },
  'mahjong-line': {
    type: IconTypes.EMOJI,
    emoji: '🀄',
    category: IconCategories.ENTERTAINMENT,
    description: '麻将'
  },
  'dance-line': {
    type: IconTypes.EMOJI,
    emoji: '💃',
    category: IconCategories.ENTERTAINMENT,
    description: '舞蹈'
  },
  'exercise-line': {
    type: IconTypes.EMOJI,
    emoji: '🤸',
    category: IconCategories.ENTERTAINMENT,
    description: '运动'
  },

  // 地图和筛选专用图标
  'close-circle-fill': {
    type: IconTypes.EMOJI,
    emoji: '⊗',
    category: IconCategories.ACTION,
    description: '关闭圆形按钮'
  },
  'filter-line': {
    type: IconTypes.EMOJI,
    emoji: '🔍',
    category: IconCategories.ACTION,
    description: '筛选/过滤'
  },
  'sort-desc': {
    type: IconTypes.EMOJI,
    emoji: '🔻',
    category: IconCategories.ACTION,
    description: '降序排序'
  },
  'arrow-up-s-line': {
    type: IconTypes.EMOJI,
    emoji: '🔼',
    category: IconCategories.NAVIGATION,
    description: '上箭头(小)'
  },
  'arrow-down-s-line': {
    type: IconTypes.EMOJI,
    emoji: '🔽',
    category: IconCategories.NAVIGATION,
    description: '下箭头(小)'
  },
  'award-line': {
    type: IconTypes.EMOJI,
    emoji: '🏆',
    category: IconCategories.STATUS,
    description: '奖项/等级'
  },

  // 钱包和金融专用图标
  'subtract-line': {
    type: IconTypes.EMOJI,
    emoji: '➖',
    category: IconCategories.ACTION,
    description: '减号/提现'
  },
  'wallet-3-line': {
    type: IconTypes.EMOJI,
    emoji: '👛',
    category: IconCategories.BUSINESS,
    description: '钱包卡片'
  },
  'bank-card-line': {
    type: IconTypes.EMOJI,
    emoji: '💳',
    category: IconCategories.BUSINESS,
    description: '银行卡'
  },
  'exchange-line': {
    type: IconTypes.EMOJI,
    emoji: '💱',
    category: IconCategories.ACTION,
    description: '交换/转账'
  },
  'gift-line': {
    type: IconTypes.EMOJI,
    emoji: '🎁',
    category: IconCategories.BUSINESS,
    description: '礼品/红包'
  },

  // 登录页面专用图标
  'wechat-line': {
    type: IconTypes.EMOJI,
    emoji: '🟢',
    category: IconCategories.SOCIAL,
    description: '微信登录'
  },
  'fingerprint-line': {
    type: IconTypes.EMOJI,
    emoji: '👆',
    category: IconCategories.STATUS,
    description: '指纹识别'
  },
  'user-smile-line': {
    type: IconTypes.EMOJI,
    emoji: '😊',
    category: IconCategories.STATUS,
    description: '面容识别'
  },

  // 个人资料页面专用图标
  'file-list-line': {
    type: IconTypes.EMOJI,
    emoji: '📋',
    category: IconCategories.FUNCTION,
    description: '文件列表/我的订单'
  },
  'history-line': {
    type: IconTypes.EMOJI,
    emoji: '🕰️',
    category: IconCategories.FUNCTION,
    description: '历史记录'
  },
  'wallet-line': {
    type: IconTypes.EMOJI,
    emoji: '👛',
    category: IconCategories.BUSINESS,
    description: '钱包'
  },
  'arrow-right-s-line': {
    type: IconTypes.EMOJI,
    emoji: '👉',
    category: IconCategories.NAVIGATION,
    description: '右箭头(小)'
  },
  'arrow-left-s-line': {
    type: IconTypes.EMOJI,
    emoji: '👈',
    category: IconCategories.NAVIGATION,
    description: '左箭头(小)'
  },
  'calendar-2-line': {
    type: IconTypes.EMOJI,
    emoji: '🗓️',
    category: IconCategories.FUNCTION,
    description: '日历(周)'
  },
  'heart-pulse-line': {
    type: IconTypes.EMOJI,
    emoji: '💓',
    category: IconCategories.HEALTH,
    description: '心跳脉搏'
  },
  'drop-line': {
    type: IconTypes.EMOJI,
    emoji: '💧',
    category: IconCategories.HEALTH,
    description: '血滴'
  },
  'scales-3-line': {
    type: IconTypes.EMOJI,
    emoji: '⚖️',
    category: IconCategories.HEALTH,
    description: '体重秤'
  },
  'save-line': {
    type: IconTypes.EMOJI,
    emoji: '💾',
    category: IconCategories.FUNCTION,
    description: '保存'
  },
  'temp-line': {
    type: IconTypes.EMOJI,
    emoji: '🌡️',
    category: IconCategories.HEALTH,
    description: '温度计'
  },
  'pie-chart-line': {
    type: IconTypes.EMOJI,
    emoji: '📊',
    category: IconCategories.FUNCTION,
    description: '饼图'
  },
  'bar-chart-line': {
    type: IconTypes.EMOJI,
    emoji: '📊',
    category: IconCategories.FUNCTION,
    description: '柱状图'
  },
  'lightbulb-line': {
    type: IconTypes.EMOJI,
    emoji: '💡',
    category: IconCategories.FUNCTION,
    description: '灯泡'
  },
  'download-2-line': {
    type: IconTypes.EMOJI,
    emoji: '⬇️',
    category: IconCategories.FUNCTION,
    description: '下载'
  },
  'share-forward-line': {
    type: IconTypes.EMOJI,
    emoji: '📤',
    category: IconCategories.FUNCTION,
    description: '分享转发'
  },
  'eye-2-line': {
    type: IconTypes.EMOJI,
    emoji: '👁️',
    category: IconCategories.FUNCTION,
    description: '查看'
  },
  'alarm-warning-line': {
    type: IconTypes.EMOJI,
    emoji: '🚨',
    category: IconCategories.EMERGENCY,
    description: '警报/紧急'
  },
  'rocket-line': {
    type: IconTypes.EMOJI,
    emoji: '🚀',
    category: IconCategories.FUNCTION,
    description: '火箭/升级'
  },
  'brain-line': {
    type: IconTypes.EMOJI,
    emoji: '🧠',
    category: IconCategories.HEALTH,
    description: '大脑'
  },
  'shield-line': {
    type: IconTypes.EMOJI,
    emoji: '🛡️',
    category: IconCategories.EMERGENCY,
    description: '盾牌/保护'
  },
  'bug-line': {
    type: IconTypes.EMOJI,
    emoji: '🐛',
    category: IconCategories.FUNCTION,
    description: '错误/漏洞'
  },
  'vidicon-line': {
    type: IconTypes.EMOJI,
    emoji: '📹',
    category: IconCategories.FUNCTION,
    description: '视频摄像'
  },
  'lock-password-line': {
    type: IconTypes.EMOJI,
    emoji: '🔐',
    category: IconCategories.STATUS,
    description: '密码锁'
  },
  'computer-line': {
    type: IconTypes.EMOJI,
    emoji: '💻',
    category: IconCategories.FUNCTION,
    description: '电脑/显示设置'
  },
  'translate-line': {
    type: IconTypes.EMOJI,
    emoji: '🌐',
    category: IconCategories.FUNCTION,
    description: '翻译/语言'
  },
  'question-line': {
    type: IconTypes.EMOJI,
    emoji: '❔',
    category: IconCategories.STATUS,
    description: '问题/帮助'
  },
  'feedback-line': {
    type: IconTypes.EMOJI,
    emoji: '💭',
    category: IconCategories.ACTION,
    description: '反馈/评论'
  },
  'bug-line': {
    type: IconTypes.EMOJI,
    emoji: '🐛',
    category: IconCategories.STATUS,
    description: '错误/调试'
  },

  // 业务专用图标
  'nursing-home-line': {
    type: IconTypes.EMOJI,
    emoji: '🏥',
    category: IconCategories.BUSINESS,
    description: '养老院'
  },
  'day-care-line': {
    type: IconTypes.EMOJI,
    emoji: '🌅',
    category: IconCategories.BUSINESS,
    description: '日间照料'
  },
  'home-care-line': {
    type: IconTypes.EMOJI,
    emoji: '🏡',
    category: IconCategories.BUSINESS,
    description: '居家养老'
  },
  'meal-delivery-line': {
    type: IconTypes.EMOJI,
    emoji: '🍱',
    category: IconCategories.BUSINESS,
    description: '送餐服务'
  },
  'cleaning-line': {
    type: IconTypes.EMOJI,
    emoji: '🧹',
    category: IconCategories.BUSINESS,
    description: '清洁服务'
  },
  'laundry-line': {
    type: IconTypes.EMOJI,
    emoji: '👕',
    category: IconCategories.BUSINESS,
    description: '洗衣服务'
  },
  'shopping-line': {
    type: IconTypes.EMOJI,
    emoji: '🛍️',
    category: IconCategories.BUSINESS,
    description: '代购服务'
  },
  'transportation-line': {
    type: IconTypes.EMOJI,
    emoji: '🚐',
    category: IconCategories.BUSINESS,
    description: '接送服务'
  },
  'subsidy-line': {
    type: IconTypes.EMOJI,
    emoji: '💵',
    category: IconCategories.BUSINESS,
    description: '补贴申请'
  },
  'assessment-line': {
    type: IconTypes.EMOJI,
    emoji: '📊',
    category: IconCategories.BUSINESS,
    description: '能力评估'
  },
  'renovation-line': {
    type: IconTypes.EMOJI,
    emoji: '🔧',
    category: IconCategories.BUSINESS,
    description: '适老改造'
  },
  'equipment-line': {
    type: IconTypes.EMOJI,
    emoji: '🦽',
    category: IconCategories.BUSINESS,
    description: '辅助器具'
  }
}

/**
 * 获取图标配置
 * @param {string} iconName 图标名称
 * @returns {object} 图标配置对象
 */
export function getIconConfig(iconName) {
  return IconConfig[iconName] || {
    type: IconTypes.EMOJI,
    emoji: '🔍', // 使用搜索图标代替问号，更符合"查找未知图标"的语义
    category: IconCategories.STATUS,
    description: '未知图标'
  }
}

/**
 * 获取图标emoji
 * @param {string} iconName 图标名称
 * @returns {string} emoji字符
 */
export function getIconEmoji(iconName) {
  const config = getIconConfig(iconName)
  return config.emoji
}

/**
 * 检查图标是否存在
 * @param {string} iconName 图标名称
 * @returns {boolean} 是否存在
 */
export function hasIcon(iconName) {
  return IconConfig.hasOwnProperty(iconName)
}

/**
 * 获取分类下的所有图标
 * @param {string} category 分类名称
 * @returns {array} 图标列表
 */
export function getIconsByCategory(category) {
  return Object.keys(IconConfig).filter(iconName => {
    return IconConfig[iconName].category === category
  })
}

/**
 * 获取所有图标名称
 * @returns {array} 图标名称列表
 */
export function getAllIconNames() {
  return Object.keys(IconConfig)
}

export default {
  IconTypes,
  IconCategories,
  IconConfig,
  getIconConfig,
  getIconEmoji,
  hasIcon,
  getIconsByCategory,
  getAllIconNames
}
