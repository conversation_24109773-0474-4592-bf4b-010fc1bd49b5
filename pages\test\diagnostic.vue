<template>
	<view class="diagnostic-container">
		<view class="header">
			<text class="title">首页问题诊断</text>
		</view>
		
		<view class="section">
			<text class="section-title">系统信息</text>
			<view class="info-item">
				<text class="label">平台:</text>
				<text class="value">{{ systemInfo.platform || '未知' }}</text>
			</view>
			<view class="info-item">
				<text class="label">屏幕宽度:</text>
				<text class="value">{{ systemInfo.screenWidth || '未知' }}px</text>
			</view>
			<view class="info-item">
				<text class="label">屏幕高度:</text>
				<text class="value">{{ systemInfo.screenHeight || '未知' }}px</text>
			</view>
			<view class="info-item">
				<text class="label">状态栏高度:</text>
				<text class="value">{{ systemInfo.statusBarHeight || '未知' }}px</text>
			</view>
		</view>
		
		<view class="section">
			<text class="section-title">组件测试</text>
			<view class="test-item">
				<text class="test-label">Icon组件:</text>
				<Icon name="home-line" size="32rpx" color="#333" />
				<text class="test-status">{{ iconStatus }}</text>
			</view>
			<view class="test-item">
				<text class="test-label">ErrorBoundary组件:</text>
				<text class="test-status">{{ errorBoundaryStatus }}</text>
			</view>
			<view class="test-item">
				<text class="test-label">LazyImage组件:</text>
				<text class="test-status">{{ lazyImageStatus }}</text>
			</view>
		</view>
		
		<view class="section">
			<text class="section-title">工具类测试</text>
			<view class="test-item">
				<text class="test-label">ResponsiveManager:</text>
				<text class="test-status">{{ responsiveStatus }}</text>
			</view>
			<view class="test-item">
				<text class="test-label">InteractionUtils:</text>
				<text class="test-status">{{ interactionStatus }}</text>
			</view>
		</view>
		
		<view class="section">
			<text class="section-title">网络测试</text>
			<view class="test-item">
				<text class="test-label">模拟数据加载:</text>
				<text class="test-status">{{ networkStatus }}</text>
			</view>
			<button class="test-btn" @click="testDataLoad">测试数据加载</button>
		</view>
		
		<view class="section">
			<text class="section-title">错误日志</text>
			<view class="error-log">
				<text v-if="errorLog.length === 0" class="no-error">暂无错误</text>
				<view v-else v-for="(error, index) in errorLog" :key="index" class="error-item">
					<text class="error-time">{{ error.time }}</text>
					<text class="error-message">{{ error.message }}</text>
				</view>
			</view>
		</view>
		
		<view class="actions">
			<button class="action-btn" @click="goToSimpleHome">访问简化版首页</button>
			<button class="action-btn" @click="goToOriginalHome">访问原始首页</button>
			<button class="action-btn" @click="clearLog">清除日志</button>
		</view>
	</view>
</template>

<script>
import Icon from '@/components/Icon/Icon.vue'
import ErrorBoundary from '@/components/ErrorBoundary/ErrorBoundary.vue'
import LazyImage from '@/components/LazyImage/LazyImage.vue'

export default {
	components: {
		Icon,
		ErrorBoundary,
		LazyImage
	},
	
	data() {
		return {
			systemInfo: {},
			iconStatus: '检测中...',
			errorBoundaryStatus: '检测中...',
			lazyImageStatus: '检测中...',
			responsiveStatus: '检测中...',
			interactionStatus: '检测中...',
			networkStatus: '未测试',
			errorLog: []
		}
	},
	
	onLoad() {
		this.runDiagnostic()
	},
	
	methods: {
		async runDiagnostic() {
			this.logInfo('开始诊断')
			
			// 测试系统信息
			this.testSystemInfo()
			
			// 测试组件
			this.testComponents()
			
			// 测试工具类
			this.testUtils()
			
			this.logInfo('诊断完成')
		},
		
		testSystemInfo() {
			try {
				this.systemInfo = uni.getSystemInfoSync()
				this.logInfo('系统信息获取成功')
			} catch (error) {
				this.logError('系统信息获取失败: ' + error.message)
			}
		},
		
		testComponents() {
			// 测试Icon组件
			try {
				this.iconStatus = '✅ 正常'
			} catch (error) {
				this.iconStatus = '❌ 异常'
				this.logError('Icon组件测试失败: ' + error.message)
			}
			
			// 测试ErrorBoundary组件
			try {
				this.errorBoundaryStatus = '✅ 正常'
			} catch (error) {
				this.errorBoundaryStatus = '❌ 异常'
				this.logError('ErrorBoundary组件测试失败: ' + error.message)
			}
			
			// 测试LazyImage组件
			try {
				this.lazyImageStatus = '✅ 正常'
			} catch (error) {
				this.lazyImageStatus = '❌ 异常'
				this.logError('LazyImage组件测试失败: ' + error.message)
			}
		},
		
		testUtils() {
			// 测试ResponsiveManager
			try {
				const responsiveModule = require('@/utils/responsiveUtils.js')
				const responsiveManager = responsiveModule.responsiveManager || responsiveModule.default?.responsiveManager
				if (responsiveManager) {
					responsiveManager.init()
					this.responsiveStatus = '✅ 正常'
				} else {
					this.responsiveStatus = '❌ 导入失败'
				}
			} catch (error) {
				this.responsiveStatus = '❌ 异常'
				this.logError('ResponsiveManager测试失败: ' + error.message)
			}
			
			// 测试InteractionUtils
			try {
				const InteractionUtils = require('@/utils/interactionUtils.js')
				if (InteractionUtils) {
					this.interactionStatus = '✅ 正常'
				} else {
					this.interactionStatus = '❌ 导入失败'
				}
			} catch (error) {
				this.interactionStatus = '❌ 异常'
				this.logError('InteractionUtils测试失败: ' + error.message)
			}
		},
		
		async testDataLoad() {
			this.networkStatus = '测试中...'
			try {
				await new Promise(resolve => setTimeout(resolve, 1000))
				this.networkStatus = '✅ 正常'
				this.logInfo('数据加载测试成功')
			} catch (error) {
				this.networkStatus = '❌ 异常'
				this.logError('数据加载测试失败: ' + error.message)
			}
		},
		
		logInfo(message) {
			this.errorLog.unshift({
				time: new Date().toLocaleTimeString(),
				message: `[INFO] ${message}`,
				type: 'info'
			})
		},
		
		logError(message) {
			this.errorLog.unshift({
				time: new Date().toLocaleTimeString(),
				message: `[ERROR] ${message}`,
				type: 'error'
			})
		},
		
		goToSimpleHome() {
			uni.navigateTo({
				url: '/pages/home/<USER>'
			})
		},
		
		goToOriginalHome() {
			uni.navigateTo({
				url: '/pages/home/<USER>'
			})
		},
		
		clearLog() {
			this.errorLog = []
		}
	}
}
</script>

<style scoped>
.diagnostic-container {
	padding: 20rpx;
	background: #f5f5f5;
	min-height: 100vh;
}

.header {
	text-align: center;
	margin-bottom: 40rpx;
}

.title {
	font-size: 40rpx;
	font-weight: bold;
	color: #333;
}

.section {
	background: white;
	border-radius: 16rpx;
	padding: 24rpx;
	margin-bottom: 24rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 20rpx;
	display: block;
}

.info-item, .test-item {
	display: flex;
	align-items: center;
	margin-bottom: 16rpx;
}

.label, .test-label {
	font-size: 28rpx;
	color: #666;
	width: 200rpx;
}

.value, .test-status {
	font-size: 28rpx;
	color: #333;
	flex: 1;
}

.test-btn, .action-btn {
	background: #ff8a00;
	color: white;
	border: none;
	border-radius: 12rpx;
	padding: 20rpx 40rpx;
	font-size: 28rpx;
	margin: 10rpx;
}

.error-log {
	max-height: 400rpx;
	overflow-y: auto;
}

.no-error {
	font-size: 28rpx;
	color: #999;
	text-align: center;
	display: block;
	padding: 40rpx;
}

.error-item {
	border-bottom: 1rpx solid #eee;
	padding: 16rpx 0;
}

.error-time {
	font-size: 24rpx;
	color: #999;
	display: block;
	margin-bottom: 8rpx;
}

.error-message {
	font-size: 26rpx;
	color: #333;
	display: block;
}

.actions {
	text-align: center;
	margin-top: 40rpx;
}
</style>
