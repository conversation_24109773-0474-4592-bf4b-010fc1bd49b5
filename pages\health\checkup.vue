<template>
	<view class="container">
		<PageHeader title="体检预约" showBack></PageHeader>
		
		<view class="content">
			<!-- 体检套餐选择 -->
			<view class="section">
				<text class="section-title">选择体检套餐</text>
				<view class="package-list">
					<view 
						class="package-item" 
						:class="{ active: selectedPackage === item.id }"
						v-for="item in checkupPackages" 
						:key="item.id"
						@click="selectPackage(item)"
					>
						<view class="package-header">
							<text class="package-name">{{ item.name }}</text>
							<text class="package-price">¥{{ item.price }}</text>
						</view>
						<text class="package-desc">{{ item.description }}</text>
						<view class="package-items">
							<text class="item-tag" v-for="tag in item.items" :key="tag">{{ tag }}</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 预约信息 -->
			<view class="section">
				<text class="section-title">预约信息</text>
				<view class="form-card">
					<view class="form-item">
						<text class="form-label">预约日期</text>
						<picker 
							mode="date" 
							:value="formData.date" 
							:start="minDate"
							@change="onDateChange"
						>
							<view class="picker-display">
								<text class="picker-text">{{ formData.date || '请选择预约日期' }}</text>
								<Icon name="calendar-line" size="24rpx" color="#999"></Icon>
							</view>
						</picker>
					</view>
					
					<view class="form-item">
						<text class="form-label">预约时间</text>
						<picker 
							:value="timeIndex" 
							:range="timeSlots" 
							range-key="label"
							@change="onTimeChange"
						>
							<view class="picker-display">
								<text class="picker-text">{{ formData.time || '请选择预约时间' }}</text>
								<Icon name="time-line" size="24rpx" color="#999"></Icon>
							</view>
						</picker>
					</view>
					
					<view class="form-item">
						<text class="form-label">体检机构</text>
						<picker 
							:value="institutionIndex" 
							:range="institutions" 
							range-key="name"
							@change="onInstitutionChange"
						>
							<view class="picker-display">
								<text class="picker-text">{{ formData.institution || '请选择体检机构' }}</text>
								<Icon name="hospital-line" size="24rpx" color="#999"></Icon>
							</view>
						</picker>
					</view>
					
					<view class="form-item">
						<text class="form-label">联系电话</text>
						<input 
							class="form-input" 
							v-model="formData.phone" 
							placeholder="请输入联系电话"
							type="number"
							maxlength="11"
						/>
					</view>
					
					<view class="form-item">
						<text class="form-label">特殊需求</text>
						<textarea 
							class="form-textarea" 
							v-model="formData.requirements" 
							placeholder="请输入特殊需求（可选）"
							maxlength="200"
						/>
					</view>
				</view>
			</view>

			<!-- 预约须知 -->
			<view class="section">
				<text class="section-title">预约须知</text>
				<view class="notice-card">
					<view class="notice-item" v-for="(notice, index) in notices" :key="index">
						<Icon name="information-line" size="24rpx" color="#ff8a00"></Icon>
						<text class="notice-text">{{ notice }}</text>
					</view>
				</view>
			</view>

			<!-- 底部操作 -->
			<view class="bottom-actions">
				<view class="price-info">
					<text class="price-label">总费用：</text>
					<text class="price-value">¥{{ totalPrice }}</text>
				</view>
				<InteractiveButton 
					type="primary" 
					text="确认预约" 
					:loading="submitting"
					@click="submitBooking"
				/>
			</view>
		</view>
	</view>
</template>

<script>
import Icon from '@/components/Icon/Icon.vue'
import PageHeader from '@/components/PageHeader/PageHeader.vue'
import InteractiveButton from '@/components/InteractiveButton/InteractiveButton.vue'

export default {
	name: 'HealthCheckup',
	components: {
		Icon,
		PageHeader,
		InteractiveButton
	},
	data() {
		return {
			submitting: false,
			selectedPackage: null,
			timeIndex: 0,
			institutionIndex: 0,
			formData: {
				date: '',
				time: '',
				institution: '',
				phone: '',
				requirements: ''
			},
			checkupPackages: [
				{
					id: 1,
					name: '基础体检套餐',
					price: 299,
					description: '适合日常健康检查，包含基础项目',
					items: ['血常规', '尿常规', '心电图', '胸部X光', '肝功能']
				},
				{
					id: 2,
					name: '全面体检套餐',
					price: 599,
					description: '全面深度检查，适合中老年人群',
					items: ['血常规', '尿常规', '心电图', '胸部X光', '肝功能', 'B超', '骨密度', '眼底检查']
				},
				{
					id: 3,
					name: '高端体检套餐',
					price: 999,
					description: '高端定制检查，包含专家咨询',
					items: ['全面体检套餐', 'CT检查', 'MRI检查', '肿瘤标志物', '专家咨询']
				}
			],
			timeSlots: [
				{ label: '08:00-09:00', value: '08:00' },
				{ label: '09:00-10:00', value: '09:00' },
				{ label: '10:00-11:00', value: '10:00' },
				{ label: '14:00-15:00', value: '14:00' },
				{ label: '15:00-16:00', value: '15:00' },
				{ label: '16:00-17:00', value: '16:00' }
			],
			institutions: [
				{ name: '市人民医院体检中心', address: '市中心区人民路123号' },
				{ name: '康宁体检中心', address: '新区健康路456号' },
				{ name: '阳光健康体检中心', address: '老城区阳光街789号' }
			],
			notices: [
				'体检前一天晚上10点后禁食禁水',
				'体检当天请穿着宽松舒适的衣物',
				'如有慢性疾病请提前告知医生',
				'预约成功后请提前30分钟到达',
				'如需取消或改期请提前24小时联系'
			]
		}
	},
	computed: {
		minDate() {
			const tomorrow = new Date()
			tomorrow.setDate(tomorrow.getDate() + 1)
			return tomorrow.toISOString().split('T')[0]
		},
		totalPrice() {
			const selectedPkg = this.checkupPackages.find(pkg => pkg.id === this.selectedPackage)
			return selectedPkg ? selectedPkg.price : 0
		}
	},
	methods: {
		selectPackage(pkg) {
			this.selectedPackage = pkg.id
		},
		onDateChange(e) {
			this.formData.date = e.detail.value
		},
		onTimeChange(e) {
			this.timeIndex = e.detail.value
			this.formData.time = this.timeSlots[this.timeIndex].label
		},
		onInstitutionChange(e) {
			this.institutionIndex = e.detail.value
			this.formData.institution = this.institutions[this.institutionIndex].name
		},
		validate() {
			if (!this.selectedPackage) {
				uni.showToast({ title: '请选择体检套餐', icon: 'none' })
				return false
			}
			if (!this.formData.date) {
				uni.showToast({ title: '请选择预约日期', icon: 'none' })
				return false
			}
			if (!this.formData.time) {
				uni.showToast({ title: '请选择预约时间', icon: 'none' })
				return false
			}
			if (!this.formData.institution) {
				uni.showToast({ title: '请选择体检机构', icon: 'none' })
				return false
			}
			if (!this.formData.phone) {
				uni.showToast({ title: '请输入联系电话', icon: 'none' })
				return false
			}
			if (!/^1[3-9]\d{9}$/.test(this.formData.phone)) {
				uni.showToast({ title: '请输入正确的手机号', icon: 'none' })
				return false
			}
			return true
		},
		async submitBooking() {
			if (!this.validate()) return
			
			this.submitting = true
			try {
				// 模拟提交预约
				await new Promise(resolve => setTimeout(resolve, 2000))
				
				uni.showToast({
					title: '预约成功',
					icon: 'success'
				})
				
				setTimeout(() => {
					uni.navigateBack()
				}, 1500)
			} catch (error) {
				uni.showToast({
					title: '预约失败，请重试',
					icon: 'none'
				})
			} finally {
				this.submitting = false
			}
		}
	}
}
</script>

<style scoped>
.container {
	min-height: 100vh;
	background: #f5f5f5;
}

.content {
	padding: 140rpx 32rpx 200rpx;
}

.section {
	margin-bottom: 32rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 24rpx;
	display: block;
}

.package-list {
	display: flex;
	flex-direction: column;
	gap: 16rpx;
}

.package-item {
	background: white;
	border-radius: 16rpx;
	padding: 24rpx;
	border: 2rpx solid #f0f0f0;
	transition: all 0.3s ease;
}

.package-item.active {
	border-color: #ff8a00;
	background: rgba(255, 138, 0, 0.05);
}

.package-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 12rpx;
}

.package-name {
	font-size: 30rpx;
	font-weight: 600;
	color: #333;
}

.package-price {
	font-size: 32rpx;
	font-weight: 600;
	color: #ff8a00;
}

.package-desc {
	font-size: 26rpx;
	color: #666;
	margin-bottom: 16rpx;
	display: block;
}

.package-items {
	display: flex;
	flex-wrap: wrap;
	gap: 8rpx;
}

.item-tag {
	background: #f0f0f0;
	color: #666;
	font-size: 22rpx;
	padding: 4rpx 12rpx;
	border-radius: 12rpx;
}

.form-card {
	background: white;
	border-radius: 16rpx;
	padding: 24rpx;
}

.form-item {
	margin-bottom: 24rpx;
}

.form-item:last-child {
	margin-bottom: 0;
}

.form-label {
	font-size: 28rpx;
	color: #333;
	margin-bottom: 12rpx;
	display: block;
}

.picker-display {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20rpx;
	border: 1rpx solid #e0e0e0;
	border-radius: 12rpx;
	background: #fafafa;
}

.picker-text {
	font-size: 28rpx;
	color: #333;
}

.form-input {
	width: 100%;
	padding: 20rpx;
	border: 1rpx solid #e0e0e0;
	border-radius: 12rpx;
	font-size: 28rpx;
	background: #fafafa;
}

.form-textarea {
	width: 100%;
	min-height: 120rpx;
	padding: 20rpx;
	border: 1rpx solid #e0e0e0;
	border-radius: 12rpx;
	font-size: 28rpx;
	background: #fafafa;
	resize: none;
}

.notice-card {
	background: white;
	border-radius: 16rpx;
	padding: 24rpx;
}

.notice-item {
	display: flex;
	align-items: flex-start;
	gap: 12rpx;
	margin-bottom: 16rpx;
}

.notice-item:last-child {
	margin-bottom: 0;
}

.notice-text {
	font-size: 26rpx;
	color: #666;
	line-height: 1.5;
}

.bottom-actions {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background: white;
	padding: 24rpx 32rpx;
	border-top: 1rpx solid #f0f0f0;
	display: flex;
	align-items: center;
	gap: 24rpx;
}

.price-info {
	flex: 1;
}

.price-label {
	font-size: 28rpx;
	color: #666;
}

.price-value {
	font-size: 36rpx;
	font-weight: 600;
	color: #ff8a00;
}
</style>
