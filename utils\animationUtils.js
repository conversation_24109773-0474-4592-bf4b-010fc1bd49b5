/**
 * iOS风格动画工具类
 * 提供统一的动画效果和页面过渡
 * 基于iOS Human Interface Guidelines
 */

// iOS标准缓动函数
export const EASING = {
  // iOS标准缓动
  IOS_STANDARD: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)',
  // iOS快速缓动
  IOS_FAST: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)',
  // iOS慢速缓动
  IOS_SLOW: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)',
  // iOS弹性缓动
  IOS_SPRING: 'cubic-bezier(0.175, 0.885, 0.32, 1.275)',
  // iOS出现缓动
  IOS_EASE_OUT: 'cubic-bezier(0.25, 1, 0.5, 1)',
  // iOS消失缓动
  IOS_EASE_IN: 'cubic-bezier(0.42, 0, 1, 1)'
}

// iOS标准动画时长
export const DURATION = {
  FAST: 150,      // 快速动画 - 按钮反馈
  STANDARD: 250,  // 标准动画 - 卡片切换
  SLOW: 350,      // 慢速动画 - 页面过渡
  EXTRA_SLOW: 500 // 超慢动画 - 复杂过渡
}

/**
 * 页面进入动画类
 */
export class PageEnterAnimation {
  /**
   * 从右侧滑入（iOS标准页面切换）
   */
  static slideInRight(element, options = {}) {
    const {
      duration = DURATION.SLOW,
      easing = EASING.IOS_STANDARD,
      delay = 0
    } = options

    return element.animate([
      {
        transform: 'translateX(100%)',
        opacity: 0
      },
      {
        transform: 'translateX(0)',
        opacity: 1
      }
    ], {
      duration,
      easing,
      delay,
      fill: 'forwards'
    })
  }

  /**
   * 从底部滑入（iOS模态页面）
   */
  static slideInUp(element, options = {}) {
    const {
      duration = DURATION.SLOW,
      easing = EASING.IOS_STANDARD,
      delay = 0
    } = options

    return element.animate([
      {
        transform: 'translateY(100%)',
        opacity: 0
      },
      {
        transform: 'translateY(0)',
        opacity: 1
      }
    ], {
      duration,
      easing,
      delay,
      fill: 'forwards'
    })
  }

  /**
   * 缩放进入（iOS弹出效果）
   */
  static scaleIn(element, options = {}) {
    const {
      duration = DURATION.STANDARD,
      easing = EASING.IOS_SPRING,
      delay = 0
    } = options

    return element.animate([
      {
        transform: 'scale(0.8)',
        opacity: 0
      },
      {
        transform: 'scale(1)',
        opacity: 1
      }
    ], {
      duration,
      easing,
      delay,
      fill: 'forwards'
    })
  }

  /**
   * 淡入效果
   */
  static fadeIn(element, options = {}) {
    const {
      duration = DURATION.STANDARD,
      easing = EASING.IOS_STANDARD,
      delay = 0
    } = options

    return element.animate([
      { opacity: 0 },
      { opacity: 1 }
    ], {
      duration,
      easing,
      delay,
      fill: 'forwards'
    })
  }
}

/**
 * 页面退出动画类
 */
export class PageExitAnimation {
  /**
   * 向左滑出
   */
  static slideOutLeft(element, options = {}) {
    const {
      duration = DURATION.SLOW,
      easing = EASING.IOS_STANDARD,
      delay = 0
    } = options

    return element.animate([
      {
        transform: 'translateX(0)',
        opacity: 1
      },
      {
        transform: 'translateX(-100%)',
        opacity: 0
      }
    ], {
      duration,
      easing,
      delay,
      fill: 'forwards'
    })
  }

  /**
   * 向下滑出
   */
  static slideOutDown(element, options = {}) {
    const {
      duration = DURATION.SLOW,
      easing = EASING.IOS_STANDARD,
      delay = 0
    } = options

    return element.animate([
      {
        transform: 'translateY(0)',
        opacity: 1
      },
      {
        transform: 'translateY(100%)',
        opacity: 0
      }
    ], {
      duration,
      easing,
      delay,
      fill: 'forwards'
    })
  }

  /**
   * 缩放退出
   */
  static scaleOut(element, options = {}) {
    const {
      duration = DURATION.STANDARD,
      easing = EASING.IOS_EASE_IN,
      delay = 0
    } = options

    return element.animate([
      {
        transform: 'scale(1)',
        opacity: 1
      },
      {
        transform: 'scale(0.8)',
        opacity: 0
      }
    ], {
      duration,
      easing,
      delay,
      fill: 'forwards'
    })
  }

  /**
   * 淡出效果
   */
  static fadeOut(element, options = {}) {
    const {
      duration = DURATION.STANDARD,
      easing = EASING.IOS_STANDARD,
      delay = 0
    } = options

    return element.animate([
      { opacity: 1 },
      { opacity: 0 }
    ], {
      duration,
      easing,
      delay,
      fill: 'forwards'
    })
  }
}

/**
 * 微交互动画类
 */
export class MicroAnimation {
  /**
   * iOS按压效果
   */
  static pressDown(element, options = {}) {
    const {
      scale = 0.95,
      duration = DURATION.FAST,
      easing = EASING.IOS_FAST
    } = options

    return element.animate([
      { transform: 'scale(1)' },
      { transform: `scale(${scale})` }
    ], {
      duration,
      easing,
      fill: 'forwards'
    })
  }

  /**
   * iOS释放效果
   */
  static pressUp(element, options = {}) {
    const {
      duration = DURATION.FAST,
      easing = EASING.IOS_SPRING
    } = options

    return element.animate([
      { transform: 'scale(0.95)' },
      { transform: 'scale(1)' }
    ], {
      duration,
      easing,
      fill: 'forwards'
    })
  }

  /**
   * 震动效果
   */
  static shake(element, options = {}) {
    const {
      intensity = 10,
      duration = 500,
      easing = EASING.IOS_STANDARD
    } = options

    return element.animate([
      { transform: 'translateX(0)' },
      { transform: `translateX(-${intensity}rpx)` },
      { transform: `translateX(${intensity}rpx)` },
      { transform: 'translateX(0)' }
    ], {
      duration,
      easing,
      iterations: 3
    })
  }

  /**
   * 弹跳效果
   */
  static bounce(element, options = {}) {
    const {
      scale = 1.1,
      duration = DURATION.STANDARD,
      easing = EASING.IOS_SPRING
    } = options

    return element.animate([
      { transform: 'scale(1)' },
      { transform: `scale(${scale})` },
      { transform: 'scale(1)' }
    ], {
      duration,
      easing
    })
  }

  /**
   * 脉冲效果
   */
  static pulse(element, options = {}) {
    const {
      scale = 1.05,
      duration = 1000,
      easing = EASING.IOS_STANDARD
    } = options

    return element.animate([
      { transform: 'scale(1)', opacity: 1 },
      { transform: `scale(${scale})`, opacity: 0.7 },
      { transform: 'scale(1)', opacity: 1 }
    ], {
      duration,
      easing,
      iterations: Infinity
    })
  }
}

/**
 * 列表动画类
 */
export class ListAnimation {
  /**
   * 列表项依次进入
   */
  static staggerIn(elements, options = {}) {
    const {
      delay = 50,
      duration = DURATION.STANDARD,
      easing = EASING.IOS_STANDARD
    } = options

    const animations = []
    
    elements.forEach((element, index) => {
      const animation = PageEnterAnimation.fadeIn(element, {
        duration,
        easing,
        delay: index * delay
      })
      animations.push(animation)
    })

    return animations
  }

  /**
   * 列表项滑入效果
   */
  static slideInStagger(elements, options = {}) {
    const {
      delay = 50,
      duration = DURATION.STANDARD,
      easing = EASING.IOS_STANDARD,
      direction = 'right'
    } = options

    const animations = []
    
    elements.forEach((element, index) => {
      const animation = direction === 'right' 
        ? PageEnterAnimation.slideInRight(element, {
            duration,
            easing,
            delay: index * delay
          })
        : PageEnterAnimation.slideInUp(element, {
            duration,
            easing,
            delay: index * delay
          })
      animations.push(animation)
    })

    return animations
  }
}

/**
 * 统一动画管理器
 */
export class AnimationManager {
  constructor() {
    this.activeAnimations = new Set()
  }

  /**
   * 执行动画
   */
  async play(animation) {
    this.activeAnimations.add(animation)
    
    try {
      await animation.finished
    } catch (error) {
      console.warn('Animation interrupted:', error)
    } finally {
      this.activeAnimations.delete(animation)
    }
  }

  /**
   * 停止所有动画
   */
  stopAll() {
    this.activeAnimations.forEach(animation => {
      animation.cancel()
    })
    this.activeAnimations.clear()
  }

  /**
   * 暂停所有动画
   */
  pauseAll() {
    this.activeAnimations.forEach(animation => {
      animation.pause()
    })
  }

  /**
   * 恢复所有动画
   */
  resumeAll() {
    this.activeAnimations.forEach(animation => {
      animation.play()
    })
  }
}

// 创建全局动画管理器实例
export const animationManager = new AnimationManager()

// 导出默认对象
export default {
  EASING,
  DURATION,
  PageEnterAnimation,
  PageExitAnimation,
  MicroAnimation,
  ListAnimation,
  AnimationManager,
  animationManager
}
