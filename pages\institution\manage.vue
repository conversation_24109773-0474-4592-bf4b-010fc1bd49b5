<template>
	<view class="container">
		<!-- 页面头部 -->
		<PageHeader title="机构管理">
			<template #actions>
				<InteractiveButton
					type="primary"
					size="medium"
					text="添加机构"
					icon="add-line"
					@click="showAddForm"
				></InteractiveButton>
			</template>
		</PageHeader>

		<!-- 搜索和筛选 -->
		<SearchFilter
			placeholder="搜索机构名称或地址"
			:categories="categories"
			@search="onSearch"
			@filter="onFilter"
		></SearchFilter>

		<!-- 机构列表 -->
		<scroll-view 
			scroll-y="true" 
			class="institution-list"
			@scrolltolower="loadMore"
			refresher-enabled="true"
			@refresherrefresh="refreshData"
			:refresher-triggered="refreshing"
		>
			<InteractiveCard 
				v-for="(item, index) in institutionList" 
				:key="item.id"
				class="institution-item"
				:loading="false"
				@click="viewDetail(item)"
			>
				<view class="institution-content">
					<view class="institution-header">
						<text class="institution-name">{{ item.name }}</text>
						<view class="institution-rating">
							<text class="rating-score">{{ item.rating }}</text>
							<text class="rating-text">分</text>
						</view>
					</view>
					<text class="institution-address">{{ item.address }}</text>
					<view class="institution-tags">
						<text 
							class="tag" 
							v-for="(tag, tagIndex) in item.tags" 
							:key="tagIndex"
						>{{ tag }}</text>
					</view>
					<view class="institution-info">
						<text class="info-item">床位: {{ item.beds }}张</text>
						<text class="info-item">价格: ¥{{ item.price }}/月起</text>
						<text class="info-item">距离: {{ item.distance }}</text>
					</view>
					<view class="institution-actions">
						<InteractiveButton 
							type="secondary" 
							size="small" 
							text="编辑" 
							icon="edit-line"
							@click.stop="editInstitution(item)"
						></InteractiveButton>
						<InteractiveButton 
							type="danger" 
							size="small" 
							text="删除" 
							icon="delete-bin-line"
							@click.stop="deleteInstitution(item)"
						></InteractiveButton>
					</view>
				</view>
			</InteractiveCard>

			<!-- 加载状态 -->
			<view class="load-more" v-if="hasMore && loading">
				<view class="loading-spinner"></view>
				<text class="loading-text">加载中...</text>
			</view>
			<view class="no-more" v-else-if="!hasMore && institutionList.length > 0">
				<text>没有更多数据了</text>
			</view>
			<view class="empty" v-else-if="!loading && institutionList.length === 0">
				<Icon name="building-line" size="120rpx" color="#ccc"></Icon>
				<text class="empty-text">暂无机构信息</text>
				<InteractiveButton 
					type="primary" 
					size="medium" 
					text="添加第一个机构" 
					@click="showAddForm"
				></InteractiveButton>
			</view>
		</scroll-view>

		<!-- 添加/编辑表单弹窗 -->
		<uni-popup ref="formPopup" type="bottom" :mask-click="false">
			<view class="form-popup">
				<FormBuilder
					:title="isEditing ? '编辑机构' : '添加机构'"
					:fields="formFields"
					:initial-data="currentInstitution"
					:submitting="submitting"
					@submit="handleSubmit"
					@cancel="hideForm"
				></FormBuilder>
			</view>
		</uni-popup>
	</view>
</template>

<script>
import SearchFilter from '@/components/SearchFilter/SearchFilter.vue'
import InteractiveCard from '@/components/InteractiveCard/InteractiveCard.vue'
import InteractiveButton from '@/components/InteractiveButton/InteractiveButton.vue'
import FormBuilder from '@/components/FormBuilder/FormBuilder.vue'
import Icon from '@/components/Icon/Icon.vue'
import PageHeader from '@/components/PageHeader/PageHeader.vue'
import FeedbackUtils from '@/utils/feedback.js'
import MockAPI from '@/utils/mockData.js'

export default {
	components: {
		SearchFilter,
		InteractiveCard,
		InteractiveButton,
		FormBuilder,
		Icon,
		PageHeader
	},
	data() {
		return {
			institutionList: [],
			loading: false,
			refreshing: false,
			hasMore: true,
			page: 1,
			pageSize: 10,
			searchKeyword: '',
			filterData: {
				category: '',
				sort: 'default'
			},
			
			// 表单相关
			isEditing: false,
			submitting: false,
			currentInstitution: {},
			
			// 分类选项
			categories: [
				{ label: '全部', value: '' },
				{ label: '养老院', value: 'nursing_home' },
				{ label: '老年公寓', value: 'apartment' },
				{ label: '护理院', value: 'care_home' },
				{ label: '日间照料', value: 'day_care' }
			],
			
			// 表单字段配置
			formFields: [
				{
					key: 'name',
					label: '机构名称',
					type: 'text',
					placeholder: '请输入机构名称',
					required: true,
					validator: (value) => {
						if (value.length < 2) return '机构名称至少2个字符';
						if (value.length > 50) return '机构名称不能超过50个字符';
						return true;
					}
				},
				{
					key: 'address',
					label: '机构地址',
					type: 'text',
					placeholder: '请输入详细地址',
					required: true
				},
				{
					key: 'phone',
					label: '联系电话',
					type: 'text',
					placeholder: '请输入联系电话',
					required: true,
					validator: (value) => {
						const phoneRegex = /^1[3-9]\d{9}$|^0\d{2,3}-?\d{7,8}$/;
						if (!phoneRegex.test(value)) return '请输入正确的电话号码';
						return true;
					}
				},
				{
					key: 'type',
					label: '机构类型',
					type: 'select',
					placeholder: '请选择机构类型',
					required: true,
					options: [
						{ label: '养老院', value: 'nursing_home' },
						{ label: '老年公寓', value: 'apartment' },
						{ label: '护理院', value: 'care_home' },
						{ label: '日间照料', value: 'day_care' }
					]
				},
				{
					key: 'beds',
					label: '床位数量',
					type: 'number',
					placeholder: '请输入床位数量',
					required: true,
					validator: (value) => {
						const num = parseInt(value);
						if (isNaN(num) || num <= 0) return '床位数量必须大于0';
						if (num > 1000) return '床位数量不能超过1000';
						return true;
					}
				},
				{
					key: 'price',
					label: '起始价格',
					type: 'number',
					placeholder: '请输入起始价格（元/月）',
					required: true,
					validator: (value) => {
						const num = parseInt(value);
						if (isNaN(num) || num <= 0) return '价格必须大于0';
						return true;
					}
				},
				{
					key: 'rating',
					label: '评分',
					type: 'number',
					placeholder: '请输入评分（1-5分）',
					validator: (value) => {
						if (!value) return true; // 可选字段
						const num = parseFloat(value);
						if (isNaN(num) || num < 1 || num > 5) return '评分必须在1-5之间';
						return true;
					}
				},
				{
					key: 'tags',
					label: '特色标签',
					type: 'text',
					placeholder: '请输入特色标签，用逗号分隔',
					help: '例如：医养结合,环境优美,专业护理'
				},
				{
					key: 'description',
					label: '机构介绍',
					type: 'textarea',
					placeholder: '请输入机构详细介绍'
				}
			]
		}
	},
	onLoad() {
		this.loadInstitutions();
	},
	methods: {
		// 加载机构列表
		async loadInstitutions() {
			if (this.loading) return;

			// 直接使用离线数据，确保100%可用性
			try {
				const params = {
					page: this.page,
					pageSize: this.pageSize,
					keyword: this.searchKeyword,
					...this.filterData
				};

				const result = OfflineDataManager.getOfflineInstitutions(params);

				if (this.page === 1) {
					this.institutionList = result.data;
				} else {
					this.institutionList.push(...result.data);
				}

				this.hasMore = result.data.length === this.pageSize;
				this.loading = false;
				this.refreshing = false;

				// 显示成功提示
				if (this.page === 1 && result.data.length > 0) {
					FeedbackUtils.showSuccess(`已加载 ${result.data.length} 个机构`);
				}
			} catch (error) {
				this.loading = false;
				this.refreshing = false;
				FeedbackUtils.showError('数据加载失败，请重试');
				console.error('加载机构数据失败:', error);
			}
		},

		// 搜索功能
		onSearch(params) {
			this.searchKeyword = params.keyword;
			this.resetAndLoad();
		},
		
		// 筛选功能
		onFilter(params) {
			this.filterData = {
				...this.filterData,
				category: params.category,
				sort: params.sort
			};
			this.resetAndLoad();
		},
		
		// 重置并加载
		resetAndLoad() {
			this.page = 1;
			this.hasMore = true;
			this.institutionList = [];
			this.loadInstitutions();
		},
		
		// 下拉刷新
		refreshData() {
			FeedbackUtils.lightFeedback();
			this.refreshing = true;
			this.resetAndLoad();
		},
		
		// 上拉加载更多
		loadMore() {
			if (this.hasMore && !this.loading) {
				this.page++;
				this.loadInstitutions();
			}
		},

		// 查看详情
		viewDetail(item) {
			FeedbackUtils.lightFeedback();
			uni.navigateTo({
				url: `/pages/institution/detail?id=${item.id}`
			});
		},

		// 显示添加表单
		showAddForm() {
			this.isEditing = false;
			this.currentInstitution = {};
			this.$refs.formPopup.open();
		},

		// 编辑机构
		editInstitution(item) {
			FeedbackUtils.lightFeedback();
			this.isEditing = true;
			this.currentInstitution = { 
				...item,
				tags: item.tags ? item.tags.join(',') : ''
			};
			this.$refs.formPopup.open();
		},

		// 删除机构
		async deleteInstitution(item) {
			try {
				await FeedbackUtils.showConfirm({
					title: '删除确认',
					content: `确定要删除机构"${item.name}"吗？此操作不可恢复。`,
					confirmText: '删除',
					cancelText: '取消'
				});
				
				FeedbackUtils.showLoading('删除中...');
				
				const result = await MockAPI.deleteInstitution(item.id);
				
				FeedbackUtils.hideLoading();
				
				if (result.success) {
					FeedbackUtils.showSuccess('删除成功');
					this.resetAndLoad();
				} else {
					FeedbackUtils.showError(result.message || '删除失败');
				}
			} catch (error) {
				FeedbackUtils.hideLoading();
				console.log('用户取消删除');
			}
		},

		// 隐藏表单
		hideForm() {
			this.$refs.formPopup.close();
		},

		// 处理表单提交
		async handleSubmit(formData) {
			try {
				this.submitting = true;
				
				// 处理标签数据
				const submitData = {
					...formData,
					tags: formData.tags ? formData.tags.split(',').map(tag => tag.trim()).filter(tag => tag) : [],
					beds: parseInt(formData.beds),
					price: parseInt(formData.price),
					rating: formData.rating ? parseFloat(formData.rating) : 4.5,
					distance: '计算中...',
					availableBeds: Math.floor(parseInt(formData.beds) * 0.3) // 模拟可用床位
				};
				
				let result;
				if (this.isEditing) {
					result = await MockAPI.updateInstitution(this.currentInstitution.id, submitData);
				} else {
					result = await MockAPI.createInstitution(submitData);
				}
				
				if (result.success) {
					FeedbackUtils.showSuccess(this.isEditing ? '更新成功' : '添加成功');
					this.hideForm();
					this.resetAndLoad();
				} else {
					FeedbackUtils.showError(result.message || '操作失败');
				}
			} catch (error) {
				console.error('提交失败:', error);
				FeedbackUtils.showError('操作失败，请重试');
			} finally {
				this.submitting = false;
			}
		}
	}
}
</script>

<style scoped>
.container {
	background-color: #f5f5f5;
	min-height: 100vh;
	display: flex;
	flex-direction: column;
}

.page-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 30rpx 40rpx;
	background: white;
	border-bottom: 1rpx solid #f0f0f0;
}

.page-title {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
}

.institution-list {
	flex: 1;
	padding: 20rpx;
}

.institution-item {
	margin-bottom: 20rpx;
}

.institution-content {
	padding: 30rpx;
}

.institution-header {
	display: flex;
	align-items: center;
	margin-bottom: 15rpx;
}

.institution-name {
	flex: 1;
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.institution-rating {
	display: flex;
	align-items: center;
}

.rating-score {
	font-size: 28rpx;
	color: #ff9500;
	font-weight: bold;
}

.rating-text {
	font-size: 24rpx;
	color: #999;
	margin-left: 5rpx;
}

.institution-address {
	font-size: 26rpx;
	color: #666;
	margin-bottom: 15rpx;
}

.institution-tags {
	display: flex;
	gap: 10rpx;
	margin-bottom: 20rpx;
	flex-wrap: wrap;
}

.tag {
	padding: 8rpx 16rpx;
	background-color: rgba(255, 138, 0, 0.1);
	color: #ff8a00;
	font-size: 22rpx;
	border-radius: 15rpx;
}

.institution-info {
	display: flex;
	gap: 30rpx;
	margin-bottom: 20rpx;
	flex-wrap: wrap;
}

.info-item {
	font-size: 24rpx;
	color: #666;
}

.institution-actions {
	display: flex;
	gap: 20rpx;
}

.load-more {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 40rpx;
	gap: 20rpx;
}

.loading-spinner {
	width: 40rpx;
	height: 40rpx;
	border: 3rpx solid rgba(255, 138, 0, 0.2);
	border-top: 3rpx solid #ff8a00;
	border-radius: 50%;
	animation: loading 1s linear infinite;
}

.loading-text {
	font-size: 26rpx;
	color: #999;
}

.no-more {
	text-align: center;
	padding: 40rpx;
	color: #999;
	font-size: 26rpx;
}

.empty {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 100rpx 40rpx;
	gap: 30rpx;
}

.empty-text {
	font-size: 28rpx;
	color: #999;
}

.form-popup {
	background: white;
	border-radius: 30rpx 30rpx 0 0;
	max-height: 90vh;
	overflow-y: auto;
}

@keyframes loading {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}
</style>
