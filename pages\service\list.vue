<template>
	<view class="container">
		<!-- 页面头部 -->
		<PageHeader title="找服务" />

		<!-- 搜索栏 - 优化版 -->
		<view class="search-section">
			<view class="search-bar">
				<view class="search-input-wrapper">
					<Icon name="search-line" size="md" color="#999" class="search-icon" />
					<input
						class="search-input text-body"
						placeholder="搜索服务名称或关键词"
						v-model="searchKeyword"
						@confirm="searchService"
					/>
					<Icon
						v-if="searchKeyword"
						name="close-line"
						size="md"
						color="#999"
						class="clear-icon"
						@click="clearSearch"
					/>
				</view>
				<InteractiveButton
					type="primary"
					size="medium"
					text="搜索"
					@click="searchService"
				/>
			</view>
		</view>
		
		<!-- 服务分类 - 优化版 -->
		<view class="category-section">
			<scroll-view scroll-x="true" class="category-scroll">
				<view
					class="category-item"
					:class="{ active: activeCategory === category.key }"
					v-for="(category, index) in categoryList"
					:key="index"
					@click="selectCategory(category.key)"
				>
					<view class="category-icon-wrapper">
						<Icon
							:name="category.iconName"
							size="lg"
							:color="activeCategory === category.key ? '#fff' : '#666'"
						/>
					</view>
					<text class="category-text text-caption">{{category.name}}</text>
				</view>
			</scroll-view>
		</view>
		
		<!-- 服务列表 -->
		<scroll-view 
			scroll-y="true" 
			class="service-list"
			@scrolltolower="loadMore"
			refresher-enabled="true"
			@refresherrefresh="refreshData"
			:refresher-triggered="refreshing"
		>
			<view
				class="service-item"
				v-for="(item, index) in serviceList"
				:key="index"
				@click="viewDetail(item)"
			>
				<LazyImage
					:src="item.image"
					:width="120"
					:height="120"
					:border-radius="16"
					placeholder-icon="service-line"
					class="service-image"
				/>
				<view class="service-content">
					<view class="service-header">
						<text class="service-name text-headline">{{item.name}}</text>
						<view class="service-rating">
							<Icon name="star-fill" size="sm" warning />
							<text class="rating-score text-callout">{{item.rating}}</text>
							<text class="rating-text text-caption">分</text>
						</view>
					</view>
					<text class="service-desc text-body">{{item.description}}</text>
					<view class="service-provider">
						<Icon name="building-line" size="sm" secondary />
						<text class="provider-label text-caption">服务机构：</text>
						<text class="provider-name text-callout">{{item.provider}}</text>
					</view>
					<view class="service-info">
						<view class="info-item">
							<Icon name="money-cny-circle-line" size="sm" warning />
							<text class="info-label text-caption">价格：</text>
							<text class="info-value price text-callout">{{item.priceText || '¥' + item.price + '/小时'}}</text>
						</view>
						<view class="info-item">
							<Icon name="time-line" size="sm" info />
							<text class="info-label text-caption">服务时间：</text>
							<text class="info-value text-callout">{{item.serviceTime || '8:00-18:00'}}</text>
						</view>
					</view>
					<view class="service-coverage" v-if="item.coverage">
						<Icon name="map-pin-line" size="sm" secondary />
						<text class="coverage-label text-caption">服务范围：</text>
						<text class="coverage-value text-callout">{{item.coverage}}</text>
					</view>
					<view class="service-tags">
						<text class="tag text-caption" v-for="(tag, tagIndex) in item.tags" :key="tagIndex">{{tag}}</text>
					</view>
					<view class="service-actions">
						<InteractiveButton
							type="secondary"
							size="small"
							text="联系服务商"
							icon="phone-line"
							@click="contactProvider(item)"
						/>
						<InteractiveButton
							type="primary"
							size="small"
							text="立即预约"
							icon="calendar-check-line"
							@click="bookService(item)"
						/>
					</view>
				</view>
			</view>
			
			<!-- 加载更多 - 优化版 -->
			<view class="load-more" v-if="hasMore">
				<Icon v-if="loading" name="loader-line" size="md" secondary loading />
				<text class="load-text text-callout" v-if="loading">加载中...</text>
				<text class="load-text text-callout" v-else>上拉加载更多</text>
			</view>
			<view class="no-more" v-else-if="serviceList.length > 0">
				<Icon name="check-line" size="md" success />
				<text class="no-more-text text-callout">没有更多数据了</text>
			</view>
			<view class="empty" v-else-if="!loading">
				<view class="empty-icon">
					<Icon name="service-line" size="2xl" secondary />
				</view>
				<text class="empty-text text-subheadline">暂无服务信息</text>
				<text class="empty-desc text-caption">请尝试调整搜索条件或分类筛选</text>
			</view>
		</scroll-view>
	</view>
</template>

<script>
import FeedbackUtils from '@/utils/feedback.js'
import MockAPI from '@/utils/mockData.js'
import OfflineDataManager from '@/utils/offlineData.js'
import PageHeader from '@/components/PageHeader/PageHeader.vue'
import Icon from '@/components/Icon/Icon.vue'
import InteractiveButton from '@/components/InteractiveButton/InteractiveButton.vue'
import LazyImage from '@/components/LazyImage/LazyImage.vue'

export default {
	components: {
		PageHeader,
		Icon,
		InteractiveButton,
		LazyImage
	},
	data() {
		return {
			searchKeyword: '',
			activeCategory: '',
			categoryList: [
				{ key: '', name: '全部', iconName: 'apps-line' },
				{ key: '医疗护理', name: '医疗护理', iconName: 'health-book-line' },
				{ key: '生活照料', name: '生活照料', iconName: 'home-heart-line' },
				{ key: '康复训练', name: '康复训练', iconName: 'wheelchair-line' },
				{ key: '心理关怀', name: '心理关怀', iconName: 'heart-3-line' },
				{ key: '法律服务', name: '法律服务', iconName: 'scales-3-line' },
				{ key: '紧急服务', name: '紧急服务', iconName: 'emergency-line' }
			],
			serviceList: [],
			loading: false,
			refreshing: false,
			hasMore: true,
			page: 1,
			pageSize: 10
		}
	},
	onLoad() {
		// 初始化离线数据
		OfflineDataManager.initOfflineData();
		this.loadServices();
	},
	methods: {
		async loadServices() {
			if (this.loading) return;

			this.loading = true;

			try {
				// 直接使用离线数据，确保100%可用性
				const params = {
					page: this.page,
					pageSize: this.pageSize,
					keyword: this.searchKeyword,
					category: this.activeCategory === 'all' ? undefined : this.activeCategory
				};

				const result = OfflineDataManager.getOfflineServices(params);

				if (this.page === 1) {
					this.serviceList = result.data;
				} else {
					this.serviceList.push(...result.data);
				}

				this.hasMore = result.data.length === this.pageSize;
				this.loading = false;
				this.refreshing = false;

				// 显示成功提示
				if (this.page === 1 && result.data.length > 0) {
					FeedbackUtils.showSuccess(`已加载 ${result.data.length} 个服务`);
				}
			} catch (error) {
				this.loading = false;
				this.refreshing = false;
				FeedbackUtils.showError('数据加载失败，请重试');
				console.error('加载服务数据失败:', error);
			}
		},
		selectCategory(category) {
			this.activeCategory = category;
			this.page = 1;
			this.hasMore = true;
			this.loadServices();
		},
		searchService() {
			if (!this.searchKeyword.trim()) {
				uni.showToast({
					title: '请输入搜索关键词',
					icon: 'none'
				});
				return;
			}

			this.page = 1;
			this.hasMore = true;
			this.loadServices();
		},
		clearSearch() {
			this.searchKeyword = '';
			this.page = 1;
			this.hasMore = true;
			this.loadServices();
		},
		refreshData() {
			this.refreshing = true;
			this.page = 1;
			this.hasMore = true;
			this.loadServices();
		},
		loadMore() {
			if (this.hasMore && !this.loading) {
				this.page++;
				this.loadServices();
			}
		},
		viewDetail(item) {
			uni.navigateTo({
				url: `/pages/service/detail?id=${item.id}`
			});
		},
		contactProvider(item) {
			uni.makePhoneCall({
				phoneNumber: item.phone
			});
		},
		bookService(item) {
			uni.navigateTo({
				url: `/pages/service/book?id=${item.id}`
			});
		}
	}
}
</script>

<style scoped>
.container {
	background-color: var(--background-color, #f5f5f5);
	height: 100vh;
	display: flex;
	flex-direction: column;
}

/* 搜索区域 - 使用设计系统 */
.search-section {
	background-color: var(--background-primary, #fff);
	padding: var(--spacing-10, 20rpx);
	border-bottom: 1rpx solid var(--border-color, #eee);
	box-shadow: var(--shadow-xs, 0 2rpx 4rpx rgba(0, 0, 0, 0.02));
}

.search-bar {
	display: flex;
	align-items: center;
	gap: var(--spacing-6, 12rpx);
}

.search-input-wrapper {
	flex: 1;
	position: relative;
	display: flex;
	align-items: center;
	background: var(--background-secondary, #f8f8f8);
	border-radius: var(--radius-input, 12rpx);
	border: 1rpx solid var(--border-color, #ddd);
	padding: 0 var(--spacing-8, 16rpx);
	min-height: var(--touch-target-min, 44rpx);
}

.search-icon {
	margin-right: var(--spacing-4, 8rpx);
}

.search-input {
	flex: 1;
	border: none;
	background: transparent;
	font-size: 28rpx;
	color: var(--text-primary, #333);
}

.clear-icon {
	margin-left: var(--spacing-4, 8rpx);
	cursor: pointer;
}

.search-btn {
	width: 120rpx;
	height: 70rpx;
	background: linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%);
	color: #fff;
	border: none;
	border-radius: 35rpx;
	font-size: 28rpx;
}

.category-section {
	background-color: #fff;
	border-bottom: 1rpx solid #eee;
}

.category-scroll {
	white-space: nowrap;
	padding: 20rpx;
}

.category-item {
	display: inline-flex;
	flex-direction: column;
	align-items: center;
	margin-right: 40rpx;
	padding: 15rpx 10rpx;
	border-radius: 15rpx;
}

.category-item.active {
	background: rgba(255, 138, 0, 0.1);
}

.category-icon-wrapper {
	width: 50rpx;
	height: 50rpx;
	margin-bottom: 8rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.category-text {
	font-size: 24rpx;
	color: #666;
}

.category-item.active .category-text {
	color: #ff8a00;
	font-weight: bold;
}

.service-list {
	flex: 1;
	padding: 20rpx;
}

/* 服务列表项 - 使用设计系统 */
.service-item {
	background-color: var(--background-primary, #fff);
	border-radius: var(--radius-card, 20rpx);
	margin: var(--spacing-10, 20rpx);
	padding: var(--spacing-10, 20rpx);
	box-shadow: var(--shadow-card, 0 2rpx 4rpx rgba(0, 0, 0, 0.02), 0 4rpx 8rpx rgba(0, 0, 0, 0.04));
	border: 1rpx solid var(--border-light, #f0f0f0);
	display: flex;
	gap: var(--spacing-10, 20rpx);
	transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.service-item:active {
	transform: scale(0.98);
	box-shadow: var(--shadow-lg, 0 8rpx 24rpx rgba(0, 0, 0, 0.08));
}

.service-image {
	width: 120rpx;
	height: 120rpx;
	border-radius: var(--radius-md, 12rpx);
	flex-shrink: 0;
}

.service-content {
	flex: 1;
	display: flex;
	flex-direction: column;
	gap: var(--spacing-4, 8rpx);
}

.service-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.service-name {
	flex: 1;
	color: var(--text-primary, #333);
	margin-right: var(--spacing-6, 12rpx);
}

.service-rating {
	display: flex;
	align-items: center;
	gap: var(--spacing-2, 4rpx);
}

.rating-score {
	color: var(--warning-color, #ff9500);
	font-weight: 600;
}

.rating-text {
	color: var(--text-tertiary, #999);
}

.service-desc {
	font-size: 26rpx;
	color: #666;
	line-height: 1.5;
	margin-bottom: 15rpx;
}

.service-provider {
	display: flex;
	align-items: center;
	margin-bottom: 20rpx;
}

.provider-label {
	font-size: 24rpx;
	color: #999;
}

.provider-name {
	font-size: 24rpx;
	color: #ff8a00;
	font-weight: bold;
}

.service-info {
	display: flex;
	justify-content: space-between;
	margin-bottom: 15rpx;
}

.service-coverage {
	display: flex;
	align-items: center;
	margin-bottom: 20rpx;
}

.coverage-label {
	font-size: 24rpx;
	color: #999;
}

.coverage-value {
	font-size: 24rpx;
	color: #666;
}

.info-item {
	display: flex;
	align-items: center;
}

.info-label {
	font-size: 24rpx;
	color: #999;
}

.info-value {
	font-size: 24rpx;
	color: #333;
}

.info-value.price {
	color: #ff8a00;
	font-weight: bold;
	font-size: 28rpx;
}

.info-unit {
	font-size: 22rpx;
	color: #999;
	margin-left: 2rpx;
}

.service-tags {
	display: flex;
	flex-wrap: wrap;
	gap: 10rpx;
	margin-bottom: 20rpx;
}

.tag {
	padding: 8rpx 16rpx;
	background-color: rgba(255, 138, 0, 0.1);
	color: #ff8a00;
	font-size: 22rpx;
	border-radius: 15rpx;
}

.service-actions {
	display: flex;
	gap: 20rpx;
}

.action-btn {
	flex: 1;
	height: 70rpx;
	border-radius: 35rpx;
	font-size: 28rpx;
	border: 1rpx solid #ddd;
	background-color: #fff;
	color: #666;
}

.action-btn.primary {
	background: linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%);
	color: #fff;
	border-color: #ff8a00;
}

.load-more, .no-more {
	text-align: center;
	padding: 30rpx;
	font-size: 28rpx;
	color: #999;
}

.empty {
	text-align: center;
	padding: 100rpx 0;
}

.empty-icon {
	margin-bottom: 30rpx;
}

.empty-text {
	font-size: 28rpx;
	color: #999;
}
</style>
