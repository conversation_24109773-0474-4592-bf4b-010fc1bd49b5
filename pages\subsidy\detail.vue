<template>
	<view class="subsidy-detail-container">
		<!-- 页面头部 -->
		<PageHeader
			title="补贴详情"
			right-icon="share-line"
			@right-click="shareSubsidy"
		/>
		
		<!-- 内容区域 -->
		<scroll-view scroll-y="true" class="content-area">
			<!-- 加载状态 -->
			<LoadingSkeleton v-if="loading" type="article" />
			
			<!-- 补贴详情 -->
			<view v-else-if="subsidyInfo" class="subsidy-detail">
				<!-- 补贴头部信息 -->
				<InteractiveCard class="subsidy-header">
					<view class="header-content">
						<view class="subsidy-icon">
							<Icon name="money-cny-circle-line" size="60rpx" color="#ff8a00" />
						</view>
						<view class="subsidy-basic">
							<text class="subsidy-title">{{ subsidyInfo.title }}</text>
							<text class="subsidy-category">{{ subsidyInfo.category }}</text>
							<view class="subsidy-amount">
								<text class="amount-label">补贴金额：</text>
								<text class="amount-value">{{ subsidyInfo.amount }}</text>
							</view>
						</view>
						<view class="subsidy-status" :class="subsidyInfo.status">
							<text class="status-text">{{ getStatusText(subsidyInfo.status) }}</text>
						</view>
					</view>
				</InteractiveCard>
				
				<!-- 申请条件 -->
				<InteractiveCard class="info-section">
					<view class="section-header">
						<Icon name="user-settings-line" size="32rpx" color="#4caf50" />
						<text class="section-title">申请条件</text>
					</view>
					<view class="condition-list">
						<view v-for="(condition, index) in subsidyInfo.requirements" :key="index" class="condition-item">
							<Icon name="check-line" size="24rpx" color="#4caf50" />
							<text class="condition-text">{{ condition }}</text>
						</view>
					</view>
				</InteractiveCard>
				
				<!-- 申请材料 -->
				<InteractiveCard class="info-section">
					<view class="section-header">
						<Icon name="file-line" size="32rpx" color="#2196f3" />
						<text class="section-title">申请材料</text>
					</view>
					<view class="material-list">
						<view v-for="(material, index) in subsidyInfo.materials" :key="index" class="material-item">
							<Icon name="article-line" size="24rpx" color="#666" />
							<text class="material-text">{{ material }}</text>
						</view>
					</view>
				</InteractiveCard>
				
				<!-- 申请流程 -->
				<InteractiveCard class="info-section">
					<view class="section-header">
						<Icon name="task-line" size="32rpx" color="#ff9800" />
						<text class="section-title">申请流程</text>
					</view>
					<view class="process-list">
						<view v-for="(step, index) in subsidyInfo.process" :key="index" class="process-item">
							<view class="step-number">{{ index + 1 }}</view>
							<text class="step-text">{{ step }}</text>
						</view>
					</view>
				</InteractiveCard>
				
				<!-- 补贴说明 -->
				<InteractiveCard class="info-section">
					<view class="section-header">
						<Icon name="information-line" size="32rpx" color="#9c27b0" />
						<text class="section-title">补贴说明</text>
					</view>
					<view class="description-content">
						<text class="description-text">{{ subsidyInfo.description }}</text>
						
						<view v-if="subsidyInfo.deadline" class="deadline-info">
							<Icon name="time-line" size="24rpx" color="#f44336" />
							<text class="deadline-text">申请截止时间：{{ subsidyInfo.deadline }}</text>
						</view>
					</view>
				</InteractiveCard>
				
				<!-- 联系方式 -->
				<InteractiveCard class="info-section">
					<view class="section-header">
						<Icon name="phone-line" size="32rpx" color="#607d8b" />
						<text class="section-title">联系方式</text>
					</view>
					<view class="contact-info">
						<view class="contact-item" @click="makeCall('************')">
							<Icon name="customer-service-2-line" size="24rpx" color="#4caf50" />
							<text class="contact-text">咨询热线：************</text>
						</view>
						<view class="contact-item">
							<Icon name="location-line" size="24rpx" color="#2196f3" />
							<text class="contact-text">办理地址：市民政局一楼大厅</text>
						</view>
						<view class="contact-item">
							<Icon name="time-line" size="24rpx" color="#ff9800" />
							<text class="contact-text">办理时间：周一至周五 9:00-17:00</text>
						</view>
					</view>
				</InteractiveCard>
			</view>
			
			<!-- 错误状态 -->
			<ErrorBoundary 
				v-else-if="error"
				:error-type="error.type"
				:error-message="error.message"
				@retry="loadSubsidyDetail"
			/>
		</scroll-view>
		
		<!-- 底部操作栏 -->
		<view v-if="subsidyInfo && !loading" class="bottom-actions">
			<InteractiveButton 
				v-if="subsidyInfo.status === 'available'"
				type="primary" 
				text="立即申请"
				block
				@click="applySubsidy"
			/>
			<InteractiveButton 
				v-else-if="subsidyInfo.status === 'applied'"
				type="secondary" 
				text="查看申请进度"
				block
				@click="viewProgress"
			/>
			<view v-else class="action-buttons">
				<InteractiveButton 
					type="secondary" 
					text="收藏"
					left-icon="star-line"
					@click="toggleFavorite"
				/>
				<InteractiveButton 
					type="primary" 
					text="咨询客服"
					left-icon="customer-service-2-line"
					@click="contactService"
				/>
			</view>
		</view>
	</view>
</template>

<script>
import OfflineDataManager from '@/utils/offlineData.js'
import FeedbackUtils from '@/utils/feedback.js'

export default {
	data() {
		return {
			loading: true,
			subsidyId: null,
			subsidyInfo: null,
			error: null,
			isFavorited: false
		}
	},
	onLoad(options) {
		this.subsidyId = options.id
		this.loadSubsidyDetail()
	},
	methods: {
		async loadSubsidyDetail() {
			this.loading = true
			this.error = null
			
			try {
				// 模拟网络延迟
				await this.delay(800)
				
				// 从离线数据获取补贴信息
				const subsidies = OfflineDataManager.getOfflineSubsidies().data
				this.subsidyInfo = subsidies.find(item => item.id == this.subsidyId)
				
				if (!this.subsidyInfo) {
					throw new Error('补贴信息不存在')
				}
				
				// 检查是否已收藏
				this.checkFavoriteStatus()
				
			} catch (error) {
				console.error('加载补贴详情失败:', error)
				this.error = {
					type: 'data',
					message: error.message || '加载失败'
				}
			} finally {
				this.loading = false
			}
		},
		
		checkFavoriteStatus() {
			// 检查本地存储中的收藏状态
			const favorites = uni.getStorageSync('favorite_subsidies') || []
			this.isFavorited = favorites.includes(this.subsidyId)
		},
		
		applySubsidy() {
			uni.navigateTo({
				url: `/pages/subsidy/apply?id=${this.subsidyId}`
			})
		},
		
		viewProgress() {
			uni.navigateTo({
				url: `/pages/subsidy/progress?id=${this.subsidyId}`
			})
		},
		
		toggleFavorite() {
			const favorites = uni.getStorageSync('favorite_subsidies') || []
			
			if (this.isFavorited) {
				// 取消收藏
				const index = favorites.indexOf(this.subsidyId)
				if (index > -1) {
					favorites.splice(index, 1)
				}
				FeedbackUtils.showSuccess('已取消收藏')
			} else {
				// 添加收藏
				favorites.push(this.subsidyId)
				FeedbackUtils.showSuccess('已添加收藏')
			}
			
			uni.setStorageSync('favorite_subsidies', favorites)
			this.isFavorited = !this.isFavorited
		},
		
		contactService() {
			FeedbackUtils.showActionSheet(['拨打电话', '在线咨询'], '联系客服')
				.then(index => {
					if (index === 0) {
						this.makeCall('************')
					} else if (index === 1) {
						uni.navigateTo({
							url: '/pages/service/chat'
						})
					}
				})
		},
		
		makeCall(phone) {
			uni.makePhoneCall({
				phoneNumber: phone,
				success: () => {
					console.log('拨打电话成功')
				},
				fail: () => {
					FeedbackUtils.showError('拨打电话失败')
				}
			})
		},
		
		shareSubsidy() {
			FeedbackUtils.showShareMenu({
				title: this.subsidyInfo.title,
				path: `/pages/subsidy/detail?id=${this.subsidyId}`,
				imageUrl: '/static/share/subsidy.png'
			})
		},
		
		getStatusText(status) {
			const statusMap = {
				available: '可申请',
				applied: '已申请',
				approved: '已通过',
				rejected: '已拒绝',
				expired: '已过期'
			}
			return statusMap[status] || '未知状态'
		},
		
		delay(ms) {
			return new Promise(resolve => setTimeout(resolve, ms))
		}
	}
}
</script>

<style scoped>
.subsidy-detail-container {
	background: #f5f5f5;
	min-height: 100vh;
	padding-bottom: 120rpx;
}

.content-area {
	padding: 120rpx 20rpx 40rpx;
	height: calc(100vh - 120rpx);
}

.subsidy-detail {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

.subsidy-header {
	background: linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%);
	color: white;
}

.header-content {
	display: flex;
	align-items: flex-start;
	gap: 20rpx;
	padding: 30rpx;
}

.subsidy-icon {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.2);
	display: flex;
	align-items: center;
	justify-content: center;
}

.subsidy-basic {
	flex: 1;
}

.subsidy-title {
	display: block;
	font-size: 32rpx;
	font-weight: bold;
	margin-bottom: 8rpx;
}

.subsidy-category {
	display: block;
	font-size: 24rpx;
	opacity: 0.8;
	margin-bottom: 15rpx;
}

.subsidy-amount {
	display: flex;
	align-items: center;
	gap: 10rpx;
}

.amount-label {
	font-size: 26rpx;
	opacity: 0.9;
}

.amount-value {
	font-size: 28rpx;
	font-weight: bold;
}

.subsidy-status {
	padding: 8rpx 16rpx;
	border-radius: 12rpx;
	background: rgba(255, 255, 255, 0.2);
}

.status-text {
	font-size: 24rpx;
	font-weight: bold;
}

.info-section {
	margin-bottom: 0;
}

.section-header {
	display: flex;
	align-items: center;
	gap: 10rpx;
	margin-bottom: 25rpx;
	padding: 30rpx 30rpx 0;
}

.section-title {
	font-size: 30rpx;
	font-weight: bold;
	color: #333;
}

.condition-list,
.material-list {
	padding: 0 30rpx 30rpx;
}

.condition-item,
.material-item {
	display: flex;
	align-items: flex-start;
	gap: 15rpx;
	margin-bottom: 15rpx;
}

.condition-text,
.material-text {
	flex: 1;
	font-size: 26rpx;
	color: #666;
	line-height: 1.5;
}

.process-list {
	padding: 0 30rpx 30rpx;
}

.process-item {
	display: flex;
	align-items: flex-start;
	gap: 20rpx;
	margin-bottom: 20rpx;
}

.step-number {
	width: 40rpx;
	height: 40rpx;
	border-radius: 50%;
	background: #ff8a00;
	color: white;
	font-size: 24rpx;
	font-weight: bold;
	display: flex;
	align-items: center;
	justify-content: center;
	flex-shrink: 0;
}

.step-text {
	flex: 1;
	font-size: 26rpx;
	color: #666;
	line-height: 1.5;
	padding-top: 8rpx;
}

.description-content {
	padding: 0 30rpx 30rpx;
}

.description-text {
	display: block;
	font-size: 26rpx;
	color: #666;
	line-height: 1.6;
	margin-bottom: 20rpx;
}

.deadline-info {
	display: flex;
	align-items: center;
	gap: 10rpx;
	padding: 15rpx;
	background: #ffebee;
	border-radius: 8rpx;
}

.deadline-text {
	font-size: 24rpx;
	color: #f44336;
}

.contact-info {
	padding: 0 30rpx 30rpx;
}

.contact-item {
	display: flex;
	align-items: center;
	gap: 15rpx;
	margin-bottom: 15rpx;
	padding: 15rpx;
	background: #f8f9fa;
	border-radius: 8rpx;
}

.contact-text {
	font-size: 26rpx;
	color: #666;
}

.bottom-actions {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background: white;
	padding: 20rpx;
	border-top: 1rpx solid #e0e0e0;
	box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.action-buttons {
	display: flex;
	gap: 20rpx;
}
</style>
