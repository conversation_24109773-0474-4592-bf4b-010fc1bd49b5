<template>
	<view class="container">
		<!-- 页面头部 -->
		<view class="content-header">
			<text class="page-title">健康数据管理</text>
			<InteractiveButton
				type="primary"
				size="medium"
				text="添加记录"
				icon="add-line"
				@click="showAddForm"
			></InteractiveButton>
		</view>

		<!-- 数据统计卡片 -->
		<view class="stats-section">
			<view class="stats-grid">
				<view class="stat-card" v-for="(stat, index) in healthStats" :key="index">
					<view class="stat-icon">
						<Icon :name="stat.icon" size="48rpx" :color="stat.color"></Icon>
					</view>
					<view class="stat-info">
						<text class="stat-value">{{ stat.value }}</text>
						<text class="stat-label">{{ stat.label }}</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 筛选和搜索 -->
		<view class="filter-section">
			<view class="filter-tabs">
				<view 
					v-for="(type, index) in healthTypes" 
					:key="index"
					class="filter-tab"
					:class="{ active: selectedType === type.value }"
					@click="selectType(type.value)"
				>
					<text class="tab-text">{{ type.label }}</text>
				</view>
			</view>
		</view>

		<!-- 健康记录列表 -->
		<scroll-view 
			scroll-y="true" 
			class="health-list"
			@scrolltolower="loadMore"
			refresher-enabled="true"
			@refresherrefresh="refreshData"
			:refresher-triggered="refreshing"
		>
			<InteractiveCard 
				v-for="(item, index) in filteredHealthData" 
				:key="item.id"
				class="health-item"
				:loading="false"
				@click="viewDetail(item)"
			>
				<view class="health-content">
					<view class="health-header">
						<view class="health-type">
							<Icon :name="getTypeIcon(item.type)" size="32rpx" :color="getTypeColor(item.type)"></Icon>
							<text class="type-text">{{ item.type }}</text>
						</view>
						<view class="health-status" :class="item.status">
							<text class="status-text">{{ getStatusText(item.status) }}</text>
						</view>
					</view>
					<view class="health-value">
						<text class="value-text">{{ item.value }}</text>
						<text class="unit-text">{{ item.unit }}</text>
					</view>
					<view class="health-meta">
						<text class="time-text">{{ formatDateTime(item.time) }}</text>
						<text class="normal-range">正常范围: {{ item.normal }}</text>
					</view>
					<view class="health-actions">
						<InteractiveButton 
							type="secondary" 
							size="small" 
							text="编辑" 
							icon="edit-line"
							@click.stop="editRecord(item)"
						></InteractiveButton>
						<InteractiveButton
							type="danger"
							size="small"
							text="删除"
							icon="delete-line"
							@click.stop="deleteRecord(item)"
						></InteractiveButton>
					</view>
				</view>
			</InteractiveCard>

			<!-- 加载状态 -->
			<view class="load-more" v-if="hasMore && loading">
				<view class="loading-spinner"></view>
				<text class="loading-text">加载中...</text>
			</view>
			<view class="no-more" v-else-if="!hasMore && filteredHealthData.length > 0">
				<text>没有更多数据了</text>
			</view>
			<view class="empty" v-else-if="!loading && filteredHealthData.length === 0">
				<Icon name="heart-pulse-line" size="120rpx" color="#ccc"></Icon>
				<text class="empty-text">暂无健康记录</text>
				<InteractiveButton 
					type="primary" 
					size="medium" 
					text="添加第一条记录" 
					@click="showAddForm"
				></InteractiveButton>
			</view>
		</scroll-view>

		<!-- 添加/编辑表单弹窗 -->
		<uni-popup ref="formPopup" type="bottom" :mask-click="false">
			<view class="form-popup">
				<FormBuilder
					:title="isEditing ? '编辑健康记录' : '添加健康记录'"
					:fields="formFields"
					:initial-data="currentRecord"
					:submitting="submitting"
					@submit="handleSubmit"
					@cancel="hideForm"
				></FormBuilder>
			</view>
		</uni-popup>
	</view>
</template>

<script>
import InteractiveCard from '@/components/InteractiveCard/InteractiveCard.vue'
import InteractiveButton from '@/components/InteractiveButton/InteractiveButton.vue'
import FormBuilder from '@/components/FormBuilder/FormBuilder.vue'
import Icon from '@/components/Icon/Icon.vue'
import FeedbackUtils from '@/utils/feedback.js'
import crudAPI from '@/utils/crudAPI.js'

export default {
	components: {
		InteractiveCard,
		InteractiveButton,
		FormBuilder,
		Icon
	},
	data() {
		return {
			healthData: [],
			loading: false,
			refreshing: false,
			hasMore: true,
			page: 1,
			pageSize: 10,
			selectedType: '',
			
			// 表单相关
			isEditing: false,
			submitting: false,
			currentRecord: {},
			
			// 健康数据类型
			healthTypes: [
				{ label: '全部', value: '' },
				{ label: '血压', value: '血压' },
				{ label: '血糖', value: '血糖' },
				{ label: '心率', value: '心率' },
				{ label: '体重', value: '体重' },
				{ label: '体温', value: '体温' }
			],
			
			// 表单字段配置
			formFields: [
				{
					key: 'type',
					label: '数据类型',
					type: 'select',
					placeholder: '请选择数据类型',
					required: true,
					options: [
						{ label: '血压', value: '血压' },
						{ label: '血糖', value: '血糖' },
						{ label: '心率', value: '心率' },
						{ label: '体重', value: '体重' },
						{ label: '体温', value: '体温' }
					]
				},
				{
					key: 'value',
					label: '测量值',
					type: 'text',
					placeholder: '请输入测量值',
					required: true,
					help: '血压格式：135/85，其他类型输入数值即可'
				},
				{
					key: 'unit',
					label: '单位',
					type: 'text',
					placeholder: '请输入单位',
					required: true,
					help: '如：mmHg、mmol/L、bpm、kg、℃等'
				},
				{
					key: 'time',
					label: '测量时间',
					type: 'datetime-local',
					placeholder: '请选择测量时间',
					required: true
				},
				{
					key: 'normal',
					label: '正常范围',
					type: 'text',
					placeholder: '请输入正常范围',
					help: '如：90-140/60-90、3.9-6.1等'
				},
				{
					key: 'note',
					label: '备注',
					type: 'textarea',
					placeholder: '请输入备注信息（可选）'
				}
			]
		}
	},
	computed: {
		// 筛选后的健康数据
		filteredHealthData() {
			if (!this.selectedType) {
				return this.healthData;
			}
			return this.healthData.filter(item => item.type === this.selectedType);
		},
		
		// 健康统计数据
		healthStats() {
			const stats = [
				{ label: '总记录数', value: this.healthData.length, icon: 'file-line', color: '#ff8a00' },
				{ label: '本月记录', value: this.getMonthlyCount(), icon: 'calendar-line', color: '#4caf50' },
				{ label: '异常记录', value: this.getAbnormalCount(), icon: 'alert-line', color: '#f44336' },
				{ label: '数据类型', value: this.getTypeCount(), icon: 'pie-chart-line', color: '#2196f3' }
			];
			return stats;
		}
	},
	onLoad() {
		this.loadHealthData();
	},
	methods: {
		// 加载健康数据
		async loadHealthData() {
			if (this.loading) return;

			try {
				this.loading = true;

				// 使用新的CRUD API
				const params = {
					page: this.page,
					pageSize: this.pageSize,
					type: this.selectedType || undefined
				};

				const result = await crudAPI.getHealthRecords(params);

				if (result.success) {
					if (this.page === 1) {
						this.healthData = result.data.list;
					} else {
						this.healthData.push(...result.data.list);
					}

					this.hasMore = result.data.hasMore;

					// 显示成功提示
					if (this.page === 1 && result.data.list.length > 0) {
						FeedbackUtils.showSuccess(`已加载 ${result.data.list.length} 条健康记录`);
					}
				} else {
					FeedbackUtils.showError(result.message || '数据加载失败，请重试');
					// 如果网络失败，显示空状态而不是错误
					this.healthData = [];
				}
			} catch (error) {
				console.error('加载健康数据失败:', error);
				FeedbackUtils.showError('数据加载失败，请重试');
				this.healthData = [];
			} finally {
				this.loading = false;
				this.refreshing = false;
			}
		},

		// 选择类型
		selectType(type) {
			FeedbackUtils.lightFeedback();
			this.selectedType = type;
		},
		
		// 重置并加载
		resetAndLoad() {
			this.page = 1;
			this.hasMore = true;
			this.healthData = [];
			this.loadHealthData();
		},
		
		// 下拉刷新
		refreshData() {
			FeedbackUtils.lightFeedback();
			this.refreshing = true;
			this.resetAndLoad();
		},
		
		// 上拉加载更多
		loadMore() {
			if (this.hasMore && !this.loading) {
				this.page++;
				this.loadHealthData();
			}
		},

		// 查看详情
		viewDetail(item) {
			FeedbackUtils.lightFeedback();
			uni.navigateTo({
				url: `/pages/health/detail?id=${item.id}`
			});
		},

		// 显示添加表单
		showAddForm() {
			this.isEditing = false;
			this.currentRecord = {
				time: new Date().toISOString().slice(0, 16) // 默认当前时间
			};
			this.$refs.formPopup.open();
		},

		// 编辑记录
		editRecord(item) {
			FeedbackUtils.lightFeedback();
			this.isEditing = true;
			this.currentRecord = { 
				...item,
				time: item.time ? new Date(item.time).toISOString().slice(0, 16) : ''
			};
			this.$refs.formPopup.open();
		},

		// 删除记录
		async deleteRecord(item) {
			try {
				await FeedbackUtils.showConfirm({
					title: '删除确认',
					content: `确定要删除这条${item.type}记录吗？此操作不可恢复。`,
					confirmText: '删除',
					cancelText: '取消'
				});

				FeedbackUtils.showLoading('删除中...');

				const result = await crudAPI.deleteHealthRecord(item.id);

				FeedbackUtils.hideLoading();

				if (result.success) {
					FeedbackUtils.showSuccess('删除成功');
					this.resetAndLoad();
				} else {
					FeedbackUtils.showError(result.message || '删除失败');
				}
			} catch (error) {
				FeedbackUtils.hideLoading();
				console.log('用户取消删除');
			}
		},

		// 隐藏表单
		hideForm() {
			this.$refs.formPopup.close();
		},

		// 处理表单提交
		async handleSubmit(formData) {
			try {
				this.submitting = true;

				// 处理数据
				const submitData = {
					type: formData.type,
					value: formData.value,
					unit: formData.unit,
					date: formData.time.split('T')[0],
					time: formData.time.split('T')[1],
					note: formData.note || '',
					status: this.calculateStatus(formData.type, formData.value, formData.normal)
				};

				let result;
				if (this.isEditing) {
					result = await crudAPI.updateHealthRecord(this.currentRecord.id, submitData);
				} else {
					result = await crudAPI.createHealthRecord(submitData);
				}

				if (result.success) {
					FeedbackUtils.showSuccess(this.isEditing ? '更新成功' : '添加成功');
					this.hideForm();
					this.resetAndLoad();
				} else {
					FeedbackUtils.showError(result.message || '操作失败');
				}
			} catch (error) {
				console.error('提交失败:', error);
				FeedbackUtils.showError('操作失败，请重试');
			} finally {
				this.submitting = false;
			}
		},

		// 计算健康状态
		calculateStatus(type, value, normal) {
			// 简单的状态判断逻辑，实际应用中需要更复杂的算法
			if (!normal || !value) return 'normal';
			
			// 这里可以根据不同类型和正常范围判断状态
			// 暂时返回正常状态
			return 'normal';
		},

		// 获取类型图标
		getTypeIcon(type) {
			const iconMap = {
				'血压': 'heart-pulse-line',
				'血糖': 'drop-line',
				'心率': 'heart-line',
				'体重': 'scales-3-line',
				'体温': 'temp-line'
			};
			return iconMap[type] || 'health-book-line';
		},

		// 获取类型颜色
		getTypeColor(type) {
			const colorMap = {
				'血压': '#f44336',
				'血糖': '#2196f3',
				'心率': '#ff9800',
				'体重': '#4caf50',
				'体温': '#9c27b0'
			};
			return colorMap[type] || '#666';
		},

		// 获取状态文本
		getStatusText(status) {
			const statusMap = {
				'normal': '正常',
				'warning': '偏高',
				'danger': '异常'
			};
			return statusMap[status] || '正常';
		},

		// 格式化日期时间
		formatDateTime(dateString) {
			const date = new Date(dateString);
			return date.toLocaleString('zh-CN', {
				year: 'numeric',
				month: '2-digit',
				day: '2-digit',
				hour: '2-digit',
				minute: '2-digit'
			});
		},

		// 获取本月记录数
		getMonthlyCount() {
			const now = new Date();
			const currentMonth = now.getMonth();
			const currentYear = now.getFullYear();
			
			return this.healthData.filter(item => {
				const itemDate = new Date(item.time);
				return itemDate.getMonth() === currentMonth && itemDate.getFullYear() === currentYear;
			}).length;
		},

		// 获取异常记录数
		getAbnormalCount() {
			return this.healthData.filter(item => item.status === 'warning' || item.status === 'danger').length;
		},

		// 获取数据类型数
		getTypeCount() {
			const types = new Set(this.healthData.map(item => item.type));
			return types.size;
		}
	}
}
</script>

<style scoped>
.container {
	background-color: #f5f5f5;
	min-height: 100vh;
	display: flex;
	flex-direction: column;
}

.content-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 30rpx 40rpx;
	background: white;
	border-bottom: 1rpx solid #f0f0f0;
}

.page-title {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
}

.stats-section {
	padding: 20rpx;
}

.stats-grid {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 20rpx;
}

.stat-card {
	background: white;
	border-radius: 20rpx;
	padding: 30rpx;
	display: flex;
	align-items: center;
	gap: 20rpx;
	box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.stat-icon {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	background: rgba(255, 138, 0, 0.1);
	display: flex;
	align-items: center;
	justify-content: center;
}

.stat-info {
	flex: 1;
}

.stat-value {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	display: block;
}

.stat-label {
	font-size: 24rpx;
	color: #666;
	display: block;
	margin-top: 5rpx;
}

.filter-section {
	background: white;
	border-bottom: 1rpx solid #f0f0f0;
}

.filter-tabs {
	display: flex;
	padding: 0 20rpx;
	overflow-x: auto;
}

.filter-tab {
	flex-shrink: 0;
	padding: 30rpx 20rpx;
	text-align: center;
	position: relative;
}

.filter-tab.active {
	border-bottom: 4rpx solid #ff8a00;
}

.tab-text {
	font-size: 28rpx;
	color: #666;
}

.filter-tab.active .tab-text {
	color: #ff8a00;
	font-weight: bold;
}

.health-list {
	flex: 1;
	padding: 20rpx;
}

.health-item {
	margin-bottom: 20rpx;
}

.health-content {
	padding: 30rpx;
}

.health-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20rpx;
}

.health-type {
	display: flex;
	align-items: center;
	gap: 10rpx;
}

.type-text {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
}

.health-status {
	padding: 6rpx 12rpx;
	border-radius: 12rpx;
	font-size: 22rpx;
}

.health-status.normal {
	background: rgba(76, 175, 80, 0.1);
	color: #4caf50;
}

.health-status.warning {
	background: rgba(255, 152, 0, 0.1);
	color: #ff9800;
}

.health-status.danger {
	background: rgba(244, 67, 54, 0.1);
	color: #f44336;
}

.health-value {
	display: flex;
	align-items: baseline;
	gap: 10rpx;
	margin-bottom: 15rpx;
}

.value-text {
	font-size: 48rpx;
	font-weight: bold;
	color: #333;
}

.unit-text {
	font-size: 24rpx;
	color: #666;
}

.health-meta {
	display: flex;
	justify-content: space-between;
	margin-bottom: 20rpx;
	flex-wrap: wrap;
	gap: 10rpx;
}

.time-text {
	font-size: 24rpx;
	color: #999;
}

.normal-range {
	font-size: 24rpx;
	color: #666;
}

.health-actions {
	display: flex;
	gap: 20rpx;
}

.load-more {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 40rpx;
	gap: 20rpx;
}

.loading-spinner {
	width: 40rpx;
	height: 40rpx;
	border: 3rpx solid rgba(255, 138, 0, 0.2);
	border-top: 3rpx solid #ff8a00;
	border-radius: 50%;
	animation: loading 1s linear infinite;
}

.loading-text {
	font-size: 26rpx;
	color: #999;
}

.no-more {
	text-align: center;
	padding: 40rpx;
	color: #999;
	font-size: 26rpx;
}

.empty {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 100rpx 40rpx;
	gap: 30rpx;
}

.empty-text {
	font-size: 28rpx;
	color: #999;
}

.form-popup {
	background: white;
	border-radius: 30rpx 30rpx 0 0;
	max-height: 90vh;
	overflow-y: auto;
}

@keyframes loading {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}
</style>
