<template>
	<view class="home-container">
		<!-- 错误显示 -->
		<view v-if="error" class="error-container">
			<view class="error-content">
				<text class="error-title">页面加载失败</text>
				<text class="error-message">{{ error }}</text>
				<button class="retry-btn" @click="retryLoad">重试</button>
			</view>
		</view>
		
		<!-- 加载状态 -->
		<view v-else-if="loading" class="loading-container">
			<view class="loading-content">
				<text class="loading-text">正在加载...</text>
			</view>
		</view>
		
		<!-- 正常内容 -->
		<view v-else class="content-container">
			<!-- 导航栏 -->
			<view class="navbar">
				<view class="navbar-content">
					<text class="location-text">北京市朝阳区</text>
					<text class="notification-icon" @click="showNotifications">🔔</text>
				</view>
			</view>
			
			<!-- 主标题 -->
			<view class="hero-section">
				<text class="hero-title">智慧养老</text>
				<text class="hero-subtitle">让养老更智慧，让生活更美好</text>
			</view>
			
			<!-- 功能菜单 -->
			<view class="function-section">
				<view class="function-grid">
					<view class="function-item" @click="navigateTo('/pages/institution/list')">
						<view class="function-icon institution">🏢</view>
						<text class="function-text">选机构</text>
					</view>
					<view class="function-item" @click="navigateTo('/pages/service/list')">
						<view class="function-icon service">🔍</view>
						<text class="function-text">找服务</text>
					</view>
					<view class="function-item" @click="navigateTo('/pages/subsidy/list')">
						<view class="function-icon subsidy">💰</view>
						<text class="function-text">申补贴</text>
					</view>
					<view class="function-item" @click="showComingSoon">
						<view class="function-icon elderly">👴</view>
						<text class="function-text">适老版</text>
					</view>
				</view>
			</view>
			
			<!-- 紧急服务 -->
			<view class="emergency-section">
				<view class="emergency-card call-center" @click="callCenter">
					<text class="card-icon">📞</text>
					<text class="card-title">呼叫中心</text>
				</view>
				<view class="emergency-card guardian" @click="contactGuardian">
					<text class="card-icon">👨‍👩‍👧‍👦</text>
					<text class="card-title">联系监护人</text>
				</view>
			</view>
			
			<!-- 资讯列表 -->
			<view class="news-section">
				<view class="section-header">
					<text class="section-title">最新资讯</text>
				</view>
				<view class="news-list">
					<view class="news-item" v-for="(item, index) in newsList" :key="index" @click="viewNews(item)">
						<view class="news-content">
							<text class="news-title">{{ item.title }}</text>
							<text class="news-summary">{{ item.summary }}</text>
							<text class="news-time">{{ item.time }}</text>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			loading: false,
			error: null,
			newsList: []
		}
	},
	
	async onLoad() {
		console.log('简化版首页加载')
		await this.loadPageData()
	},
	
	methods: {
		async loadPageData() {
			this.loading = true
			this.error = null
			
			try {
				// 模拟数据加载
				await new Promise(resolve => setTimeout(resolve, 1000))
				
				this.newsList = [
					{
						id: 1,
						title: '养老服务新政策发布',
						summary: '政府出台新的养老服务补贴政策，惠及更多老年人',
						time: '2024-01-15'
					},
					{
						id: 2,
						title: '智慧养老技术创新',
						summary: '最新的智能设备助力老年人生活更便利',
						time: '2024-01-14'
					}
				]
				
				console.log('简化版首页数据加载完成')
			} catch (error) {
				console.error('简化版首页加载失败:', error)
				this.error = error.message || '页面加载失败'
			} finally {
				this.loading = false
			}
		},
		
		retryLoad() {
			this.loadPageData()
		},
		
		navigateTo(url) {
			uni.navigateTo({ url })
		},
		
		showNotifications() {
			uni.showToast({
				title: '通知功能开发中',
				icon: 'none'
			})
		},
		
		showComingSoon() {
			uni.showToast({
				title: '功能即将推出',
				icon: 'none'
			})
		},
		
		callCenter() {
			uni.showModal({
				title: '呼叫中心',
				content: '是否拨打客服热线：************？',
				success: (res) => {
					if (res.confirm) {
						uni.makePhoneCall({
							phoneNumber: '************'
						})
					}
				}
			})
		},
		
		contactGuardian() {
			uni.showToast({
				title: '联系监护人功能开发中',
				icon: 'none'
			})
		},
		
		viewNews(item) {
			uni.navigateTo({
				url: `/pages/news/detail?id=${item.id}`
			})
		}
	}
}
</script>

<style scoped>
.home-container {
	min-height: 100vh;
	background: linear-gradient(135deg, #ff8a00 0%, #f57c00 100%);
}

.error-container, .loading-container {
	display: flex;
	align-items: center;
	justify-content: center;
	min-height: 100vh;
	padding: 40rpx;
}

.error-content, .loading-content {
	text-align: center;
	color: white;
}

.error-title {
	font-size: 36rpx;
	font-weight: bold;
	margin-bottom: 20rpx;
	display: block;
}

.error-message {
	font-size: 28rpx;
	margin-bottom: 40rpx;
	display: block;
}

.retry-btn {
	background: rgba(255, 255, 255, 0.2);
	color: white;
	border: 1rpx solid rgba(255, 255, 255, 0.3);
	border-radius: 20rpx;
	padding: 20rpx 40rpx;
}

.loading-text {
	font-size: 32rpx;
	color: white;
}

.content-container {
	padding-bottom: 100rpx;
}

.navbar {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 1000;
	background: rgba(255, 255, 255, 0.1);
	backdrop-filter: blur(20rpx);
}

.navbar-content {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 60rpx 32rpx 20rpx;
	color: white;
}

.location-text {
	font-size: 32rpx;
	font-weight: 500;
}

.notification-icon {
	font-size: 36rpx;
	padding: 8rpx;
}

.hero-section {
	padding: 140rpx 32rpx 40rpx;
	color: white;
	text-align: center;
}

.hero-title {
	font-size: 60rpx;
	font-weight: bold;
	display: block;
	margin-bottom: 16rpx;
}

.hero-subtitle {
	font-size: 32rpx;
	opacity: 0.9;
	display: block;
}

.function-section {
	padding: 0 24rpx 40rpx;
}

.function-grid {
	display: grid;
	grid-template-columns: repeat(4, 1fr);
	gap: 16rpx;
	background: rgba(255, 255, 255, 0.1);
	border-radius: 20rpx;
	padding: 24rpx;
}

.function-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 20rpx;
	border-radius: 16rpx;
	transition: all 0.3s;
}

.function-item:active {
	transform: scale(0.95);
	background: rgba(255, 255, 255, 0.1);
}

.function-icon {
	width: 80rpx;
	height: 80rpx;
	border-radius: 20rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-bottom: 12rpx;
	font-size: 36rpx;
}

.function-icon.institution { background: #ff6b6b; }
.function-icon.service { background: #4ecdc4; }
.function-icon.subsidy { background: #45b7d1; }
.function-icon.elderly { background: #96ceb4; }

.function-text {
	font-size: 26rpx;
	color: white;
	font-weight: 500;
}

.emergency-section {
	display: flex;
	gap: 16rpx;
	padding: 0 24rpx 40rpx;
}

.emergency-card {
	flex: 1;
	height: 120rpx;
	border-radius: 20rpx;
	padding: 20rpx;
	display: flex;
	align-items: center;
	color: white;
	transition: all 0.3s;
}

.emergency-card:active {
	transform: scale(0.95);
}

.emergency-card.call-center { background: #FF3B30; }
.emergency-card.guardian { background: #FF9500; }

.card-icon {
	font-size: 36rpx;
	margin-right: 16rpx;
}

.card-title {
	font-size: 28rpx;
	font-weight: 600;
}

.news-section {
	background: rgba(255, 255, 255, 0.95);
	margin: 0 24rpx;
	border-radius: 20rpx;
	padding: 32rpx;
}

.section-header {
	margin-bottom: 24rpx;
}

.section-title {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
}

.news-list {
	display: flex;
	flex-direction: column;
	gap: 16rpx;
}

.news-item {
	background: #f5f5f5;
	border-radius: 12rpx;
	padding: 20rpx;
	transition: all 0.3s;
}

.news-item:active {
	transform: scale(0.98);
	background: #eeeeee;
}

.news-title {
	font-size: 30rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 8rpx;
	display: block;
}

.news-summary {
	font-size: 26rpx;
	color: #666;
	margin-bottom: 8rpx;
	display: block;
}

.news-time {
	font-size: 22rpx;
	color: #999;
}
</style>
