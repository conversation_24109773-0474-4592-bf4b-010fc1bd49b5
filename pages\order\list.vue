<template>
	<view class="container" :class="{ 'elderly-mode': isElderlyMode }">
		<!-- 自定义导航栏 -->
		<view class="custom-navbar" :class="{ 'elderly-mode': isElderlyMode }">
			<view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
			<view class="navbar-content">
				<view class="navbar-left" @click="goBack">
					<Icon
						name="arrow-left-line"
						:size="isElderlyMode ? '40rpx' : '36rpx'"
						color="#333"
					></Icon>
					<text class="back-text">返回</text>
				</view>
				<view class="navbar-center">
					<text class="navbar-title">我的订单</text>
				</view>
				<view class="navbar-right"></view>
			</view>
		</view>

		<!-- 订单状态筛选 -->
		<view class="filter-tabs">
			<view class="tab-item" :class="{ active: activeTab === tab.key }" v-for="tab in orderTabs" :key="tab.key" @click="switchTab(tab.key)">
				<text class="tab-text">{{tab.name}}</text>
				<view class="tab-badge" v-if="tab.count > 0">{{tab.count}}</view>
			</view>
		</view>

		<!-- 订单列表 -->
		<view class="order-list">
			<view class="order-item" v-for="(order, index) in filteredOrders" :key="index" @click="viewOrderDetail(order)">
				<view class="order-header">
					<text class="order-number">订单号：{{order.orderNumber}}</text>
					<view class="order-status" :class="order.status">
						<text class="status-text">{{getStatusText(order.status)}}</text>
					</view>
				</view>
				
				<view class="order-content">
					<view class="service-info">
						<Icon :name="order.serviceIcon" size="40rpx" color="#ff8a00"></Icon>
						<view class="service-details">
							<text class="service-name">{{order.serviceName}}</text>
							<text class="service-desc">{{order.serviceDesc}}</text>
							<text class="service-time">{{order.serviceTime}}</text>
						</view>
					</view>
					<view class="order-amount">
						<text class="amount-label">订单金额</text>
						<text class="amount-value">¥{{order.amount}}</text>
					</view>
				</view>
				
				<view class="order-footer">
					<text class="order-time">下单时间：{{order.createTime}}</text>
					<view class="order-actions">
						<button class="action-btn secondary" @click.stop="cancelOrder(order)" v-if="order.status === 'pending'">取消订单</button>
						<button class="action-btn secondary" @click.stop="contactService(order)" v-if="order.status === 'processing'">联系客服</button>
						<button class="action-btn primary" @click.stop="payOrder(order)" v-if="order.status === 'pending'">立即支付</button>
						<button class="action-btn primary" @click.stop="evaluateOrder(order)" v-if="order.status === 'completed' && !order.evaluated">评价</button>
						<button class="action-btn secondary" @click.stop="reorder(order)" v-if="order.status === 'completed'">再次预订</button>
					</view>
				</view>
			</view>
		</view>

		<!-- 空状态 -->
		<view class="empty-state" v-if="filteredOrders.length === 0">
			<Icon name="file-list-line" size="120rpx" color="#ccc"></Icon>
			<text class="empty-text">暂无{{getTabName(activeTab)}}订单</text>
			<button class="empty-btn" @click="goToServices">去预订服务</button>
		</view>
	</view>
</template>

<script>
import Icon from '@/components/Icon/Icon.vue'
import { elderlyModeManager } from '@/utils/elderlyModeUtils.js'

export default {
	components: {
		Icon
	},
	data() {
		return {
			statusBarHeight: 0, // 状态栏高度
			isElderlyMode: false, // 适老化模式
			activeTab: 'all',
			orderTabs: [
				{ key: 'all', name: '全部', count: 0 },
				{ key: 'pending', name: '待支付', count: 2 },
				{ key: 'processing', name: '进行中', count: 1 },
				{ key: 'completed', name: '已完成', count: 5 },
				{ key: 'cancelled', name: '已取消', count: 1 }
			],
			orderList: [
				{
					orderNumber: 'LD202401150001',
					serviceName: '居家护理服务',
					serviceDesc: '专业护理师上门服务',
					serviceTime: '2024-01-20 09:00-11:00',
					serviceIcon: 'heart-3-line',
					amount: '120.00',
					status: 'pending',
					createTime: '2024-01-15 14:30',
					evaluated: false
				},
				{
					orderNumber: 'LD202401140002',
					serviceName: '康复训练',
					serviceDesc: '物理治疗师指导训练',
					serviceTime: '2024-01-18 14:00-16:00',
					serviceIcon: 'heart-pulse-line',
					amount: '200.00',
					status: 'processing',
					createTime: '2024-01-14 10:15',
					evaluated: false
				},
				{
					orderNumber: 'LD202401130003',
					serviceName: '营养配餐',
					serviceDesc: '老年营养餐配送',
					serviceTime: '2024-01-16 11:30-12:00',
					serviceIcon: 'restaurant-line',
					amount: '35.00',
					status: 'completed',
					createTime: '2024-01-13 16:20',
					evaluated: true
				},
				{
					orderNumber: 'LD202401120004',
					serviceName: '辅具租赁',
					serviceDesc: '轮椅租赁服务',
					serviceTime: '2024-01-15 - 2024-02-15',
					serviceIcon: 'wheelchair-line',
					amount: '300.00',
					status: 'completed',
					createTime: '2024-01-12 09:45',
					evaluated: false
				},
				{
					orderNumber: 'LD202401110005',
					serviceName: '健康体检',
					serviceDesc: '上门健康检查服务',
					serviceTime: '2024-01-14 08:00-10:00',
					serviceIcon: 'stethoscope-line',
					amount: '180.00',
					status: 'cancelled',
					createTime: '2024-01-11 13:10',
					evaluated: false
				}
			]
		}
	},
	computed: {
		filteredOrders() {
			if (this.activeTab === 'all') {
				return this.orderList;
			}
			return this.orderList.filter(order => order.status === this.activeTab);
		}
	},
	onLoad() {
		// 获取状态栏高度
		const systemInfo = uni.getSystemInfoSync();
		this.statusBarHeight = systemInfo.statusBarHeight || 44;

		// 初始化适老化模式
		this.isElderlyMode = elderlyModeManager.getElderlyMode();
		elderlyModeManager.onElderlyModeChange((isEnabled) => {
			this.isElderlyMode = isEnabled;
		});

		this.updateTabCounts();
	},
	methods: {
		switchTab(tabKey) {
			this.activeTab = tabKey;
		},
		updateTabCounts() {
			this.orderTabs.forEach(tab => {
				if (tab.key === 'all') {
					tab.count = this.orderList.length;
				} else {
					tab.count = this.orderList.filter(order => order.status === tab.key).length;
				}
			});
		},
		getStatusText(status) {
			const statusMap = {
				'pending': '待支付',
				'processing': '进行中',
				'completed': '已完成',
				'cancelled': '已取消'
			};
			return statusMap[status] || '未知';
		},
		getTabName(tabKey) {
			const tab = this.orderTabs.find(t => t.key === tabKey);
			return tab ? tab.name : '';
		},
		viewOrderDetail(order) {
			uni.showToast({
				title: '订单详情功能开发中',
				icon: 'none'
			});
		},
		cancelOrder(order) {
			uni.showModal({
				title: '确认取消',
				content: '确定要取消这个订单吗？',
				success: (res) => {
					if (res.confirm) {
						order.status = 'cancelled';
						this.updateTabCounts();
						uni.showToast({
							title: '订单已取消',
							icon: 'success'
						});
					}
				}
			});
		},
		contactService(order) {
			uni.showToast({
				title: '客服功能开发中',
				icon: 'none'
			});
		},
		payOrder(order) {
			uni.showToast({
				title: '支付功能开发中',
				icon: 'none'
			});
		},
		evaluateOrder(order) {
			uni.showToast({
				title: '评价功能开发中',
				icon: 'none'
			});
		},
		reorder(order) {
			uni.showToast({
				title: '正在为您重新预订',
				icon: 'loading'
			});
			
			setTimeout(() => {
				uni.showToast({
					title: '预订成功',
					icon: 'success'
				});
			}, 2000);
		},
		goToServices() {
			uni.switchTab({
				url: '/pages/workspace/workspace'
			});
		},

		// 返回上一页
		goBack() {
			uni.navigateBack({
				fail: () => {
					uni.switchTab({
						url: '/pages/profile/profile'
					});
				}
			});
		}
	}
}
</script>

<style scoped>
.container {
	background: #f8f9fa;
	min-height: 100vh;
}

/* 导航栏样式 */
.custom-navbar {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 1000;
	background: rgba(255, 255, 255, 0.95);
	backdrop-filter: blur(20rpx);
	-webkit-backdrop-filter: blur(20rpx);
	border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);
}

.navbar-content {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 20rpx 32rpx;
	min-height: 88rpx;
}

.navbar-left {
	display: flex;
	align-items: center;
	gap: 12rpx;
	padding: 8rpx 16rpx 8rpx 0;
	border-radius: 20rpx;
	transition: all 0.2s ease;
}

.navbar-left:active {
	background-color: rgba(0, 0, 0, 0.04);
	transform: scale(0.96);
}

.back-text {
	font-size: 32rpx;
	color: #333;
	font-weight: 500;
}

.navbar-center {
	position: absolute;
	left: 50%;
	transform: translateX(-50%);
}

.navbar-title {
	font-size: 34rpx;
	font-weight: 600;
	color: #333;
}

.navbar-right {
	display: flex;
	align-items: center;
}

.filter-tabs {
	background: white;
	display: flex;
	padding: 0 20rpx;
	box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
	margin-top: 220rpx; /* 为导航栏留出空间 */
}

.tab-item {
	flex: 1;
	text-align: center;
	padding: 30rpx 20rpx;
	position: relative;
}

.tab-item.active {
	border-bottom: 4rpx solid #ff8a00;
}

.tab-text {
	font-size: 28rpx;
	color: #666;
}

.tab-item.active .tab-text {
	color: #ff8a00;
	font-weight: bold;
}

.tab-badge {
	position: absolute;
	top: 15rpx;
	right: 15rpx;
	background: #ff4444;
	color: white;
	font-size: 20rpx;
	padding: 4rpx 8rpx;
	border-radius: 12rpx;
	min-width: 24rpx;
	text-align: center;
}

.order-list {
	padding: 20rpx;
}

.order-item {
	background: white;
	border-radius: 20rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.order-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20rpx;
}

.order-number {
	font-size: 26rpx;
	color: #333;
	font-weight: bold;
}

.order-status {
	padding: 8rpx 16rpx;
	border-radius: 12rpx;
	font-size: 22rpx;
}

.order-status.pending {
	background: rgba(255, 152, 0, 0.1);
	color: #ff9800;
}

.order-status.processing {
	background: rgba(33, 150, 243, 0.1);
	color: #2196f3;
}

.order-status.completed {
	background: rgba(76, 175, 80, 0.1);
	color: #4caf50;
}

.order-status.cancelled {
	background: rgba(158, 158, 158, 0.1);
	color: #9e9e9e;
}

.order-content {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20rpx;
}

.service-info {
	display: flex;
	align-items: center;
	gap: 20rpx;
	flex: 1;
}

.service-details {
	flex: 1;
}

.service-name {
	font-size: 28rpx;
	color: #333;
	font-weight: bold;
	display: block;
	margin-bottom: 8rpx;
}

.service-desc {
	font-size: 24rpx;
	color: #666;
	display: block;
	margin-bottom: 8rpx;
}

.service-time {
	font-size: 22rpx;
	color: #999;
	display: block;
}

.order-amount {
	text-align: right;
}

.amount-label {
	font-size: 22rpx;
	color: #666;
	display: block;
	margin-bottom: 5rpx;
}

.amount-value {
	font-size: 32rpx;
	color: #ff8a00;
	font-weight: bold;
	display: block;
}

.order-footer {
	display: flex;
	justify-content: space-between;
	align-items: center;
	border-top: 1rpx solid #f0f0f0;
	padding-top: 20rpx;
}

.order-time {
	font-size: 22rpx;
	color: #999;
}

.order-actions {
	display: flex;
	gap: 15rpx;
}

.action-btn {
	padding: 12rpx 24rpx;
	border-radius: 20rpx;
	font-size: 24rpx;
	border: none;
}

.action-btn.primary {
	background: #ff8a00;
	color: white;
}

.action-btn.secondary {
	background: white;
	color: #ff8a00;
	border: 2rpx solid #ff8a00;
}

.empty-state {
	text-align: center;
	padding: 120rpx 40rpx;
}

.empty-text {
	font-size: 28rpx;
	color: #999;
	display: block;
	margin: 30rpx 0 40rpx;
}

.empty-btn {
	background: #ff8a00;
	color: white;
	border: none;
	border-radius: 25rpx;
	padding: 20rpx 40rpx;
	font-size: 28rpx;
}

/* ===== 适老化模式样式 ===== */
.elderly-mode .navbar-content {
	padding: 24rpx 36rpx;
	min-height: 96rpx;
}

.elderly-mode .navbar-left {
	gap: 16rpx;
	padding: 12rpx 20rpx 12rpx 0;
}

.elderly-mode .back-text {
	font-size: 36rpx;
	font-weight: 600;
}

.elderly-mode .navbar-title {
	font-size: 38rpx;
	font-weight: 700;
}

.elderly-mode .filter-tabs {
	margin-top: 240rpx; /* 适老化模式下导航栏更高 */
}

.elderly-mode .tab-item {
	padding: 30rpx 20rpx;
}

.elderly-mode .tab-text {
	font-size: 32rpx;
}

.elderly-mode .order-item {
	padding: 40rpx 30rpx;
}

.elderly-mode .order-title {
	font-size: 36rpx;
}

.elderly-mode .order-desc {
	font-size: 30rpx;
}

.elderly-mode .order-time {
	font-size: 28rpx;
}

.elderly-mode .order-price {
	font-size: 36rpx;
}

.elderly-mode .order-status {
	font-size: 28rpx;
	padding: 12rpx 24rpx;
}
</style>
