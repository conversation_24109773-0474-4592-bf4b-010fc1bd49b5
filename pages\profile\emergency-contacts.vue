<template>
	<view class="container">
		<!-- 页面头部 -->
		<PageHeader title="紧急联系人">
			<template #actions>
				<InteractiveButton
					type="primary"
					size="small"
					text="添加"
					icon="add-line"
					@click="addContact"
				></InteractiveButton>
			</template>
		</PageHeader>

		<!-- 联系人列表 -->
		<view class="contacts-list">
			<InteractiveCard 
				v-for="(contact, index) in emergencyContacts" 
				:key="contact.id"
				class="contact-item"
			>
				<view class="contact-content">
					<!-- 联系人头像 -->
					<view class="contact-avatar">
						<image 
							v-if="contact.avatar" 
							:src="contact.avatar" 
							class="avatar-image" 
							mode="aspectFill"
						></image>
						<view v-else class="avatar-placeholder">
							<Icon name="user-line" size="40rpx" color="#ff8a00"></Icon>
						</view>
					</view>

					<!-- 联系人信息 -->
					<view class="contact-info">
						<view class="contact-header">
							<text class="contact-name">{{ contact.name }}</text>
							<view class="contact-priority" :class="getPriorityClass(contact.priority)">
								<text class="priority-text">{{ getPriorityText(contact.priority) }}</text>
							</view>
						</view>
						
						<view class="contact-details">
							<view class="detail-item">
								<Icon name="phone-line" size="20rpx" color="#999"></Icon>
								<text class="detail-text">{{ contact.phone }}</text>
							</view>
							<view class="detail-item" v-if="contact.relationship">
								<Icon name="team-line" size="20rpx" color="#999"></Icon>
								<text class="detail-text">{{ contact.relationship }}</text>
							</view>
							<view class="detail-item" v-if="contact.address">
								<Icon name="map-pin-line" size="20rpx" color="#999"></Icon>
								<text class="detail-text">{{ contact.address }}</text>
							</view>
						</view>
					</view>

					<!-- 操作按钮 -->
					<view class="contact-actions">
						<InteractiveButton 
							type="success" 
							size="small" 
							text="呼叫" 
							icon="phone-line"
							@click="callContact(contact)"
						></InteractiveButton>
						<InteractiveButton 
							type="secondary" 
							size="small" 
							text="编辑" 
							icon="edit-line"
							@click="editContact(contact)"
						></InteractiveButton>
						<InteractiveButton 
							type="danger" 
							size="small" 
							text="删除" 
							icon="delete-bin-line"
							@click="deleteContact(contact, index)"
						></InteractiveButton>
					</view>
				</view>
			</InteractiveCard>

			<!-- 空状态 -->
			<view class="empty" v-if="emergencyContacts.length === 0">
				<Icon name="contacts-line" size="120rpx" color="#ccc"></Icon>
				<text class="empty-text">暂无紧急联系人</text>
				<text class="empty-tip">点击右上角添加联系人</text>
			</view>
		</view>

		<!-- 添加/编辑联系人弹窗 -->
		<uni-popup ref="contactPopup" type="bottom" background-color="#fff">
			<view class="popup-content">
				<view class="popup-header">
					<text class="popup-title">{{ editingContact ? '编辑联系人' : '添加联系人' }}</text>
					<view class="popup-close" @click="closePopup">
						<Icon name="close-line" size="32rpx" color="#999"></Icon>
					</view>
				</view>

				<view class="form-content">
					<!-- 头像上传 -->
					<view class="form-item">
						<text class="form-label">头像</text>
						<view class="avatar-upload" @click="uploadAvatar">
							<image 
								v-if="formData.avatar" 
								:src="formData.avatar" 
								class="upload-avatar" 
								mode="aspectFill"
							></image>
							<view v-else class="upload-placeholder">
								<Icon name="camera-line" size="40rpx" color="#ccc"></Icon>
								<text class="upload-text">点击上传</text>
							</view>
						</view>
					</view>

					<!-- 基本信息 -->
					<view class="form-item">
						<text class="form-label">姓名 *</text>
						<input 
							class="form-input" 
							v-model="formData.name" 
							placeholder="请输入联系人姓名"
						/>
					</view>

					<view class="form-item">
						<text class="form-label">电话 *</text>
						<input 
							class="form-input" 
							v-model="formData.phone" 
							placeholder="请输入联系人电话"
							type="number"
						/>
					</view>

					<view class="form-item">
						<text class="form-label">关系</text>
						<input 
							class="form-input" 
							v-model="formData.relationship" 
							placeholder="请输入与您的关系"
						/>
					</view>

					<view class="form-item">
						<text class="form-label">优先级</text>
						<view class="priority-options">
							<view 
								v-for="(option, index) in priorityOptions" 
								:key="index"
								class="priority-option"
								:class="{ active: formData.priority === option.value }"
								@click="selectPriority(option.value)"
							>
								<text class="option-text">{{ option.label }}</text>
							</view>
						</view>
					</view>

					<view class="form-item">
						<text class="form-label">地址</text>
						<textarea 
							class="form-textarea" 
							v-model="formData.address" 
							placeholder="请输入联系人地址（选填）"
						></textarea>
					</view>

					<view class="form-item">
						<text class="form-label">备注</text>
						<textarea 
							class="form-textarea" 
							v-model="formData.note" 
							placeholder="请输入备注信息（选填）"
						></textarea>
					</view>
				</view>

				<view class="popup-actions">
					<InteractiveButton
						type="secondary"
						size="large"
						text="取消"
						@click="closePopup"
					></InteractiveButton>
					<InteractiveButton
						type="primary"
						size="large"
						text="保存"
						:loading="saving"
						@click="saveContact"
					></InteractiveButton>
				</view>
			</view>
		</uni-popup>
	</view>
</template>

<script>
import InteractiveCard from '@/components/InteractiveCard/InteractiveCard.vue'
import InteractiveButton from '@/components/InteractiveButton/InteractiveButton.vue'
import Icon from '@/components/Icon/Icon.vue'
import PageHeader from '@/components/PageHeader/PageHeader.vue'
import FeedbackUtils from '@/utils/feedback.js'

export default {
	components: {
		InteractiveCard,
		InteractiveButton,
		Icon,
		PageHeader
	},
	data() {
		return {
			emergencyContacts: [],
			editingContact: null,
			saving: false,
			formData: {
				avatar: '',
				name: '',
				phone: '',
				relationship: '',
				priority: 1,
				address: '',
				note: ''
			},
			
			// 优先级选项
			priorityOptions: [
				{ label: '第一联系人', value: 1 },
				{ label: '第二联系人', value: 2 },
				{ label: '第三联系人', value: 3 },
				{ label: '其他联系人', value: 4 }
			],
			
			// 模拟数据
			mockContacts: [
				{
					id: 1,
					name: '张小明',
					phone: '138****5678',
					relationship: '儿子',
					priority: 1,
					address: '北京市海淀区中关村大街1号',
					note: '工作时间可能不方便接听',
					avatar: ''
				},
				{
					id: 2,
					name: '李小红',
					phone: '139****9876',
					relationship: '女儿',
					priority: 2,
					address: '北京市朝阳区建国路88号',
					note: '晚上8点后联系',
					avatar: ''
				}
			]
		}
	},
	onLoad() {
		this.loadContacts();
	},
	methods: {
		// 加载联系人列表
		loadContacts() {
			// 模拟加载数据
			this.emergencyContacts = [...this.mockContacts];
		},

		// 获取优先级样式类
		getPriorityClass(priority) {
			const classMap = {
				1: 'priority-high',
				2: 'priority-medium',
				3: 'priority-normal',
				4: 'priority-low'
			};
			return classMap[priority] || 'priority-normal';
		},

		// 获取优先级文本
		getPriorityText(priority) {
			const textMap = {
				1: '第一联系人',
				2: '第二联系人',
				3: '第三联系人',
				4: '其他联系人'
			};
			return textMap[priority] || '其他联系人';
		},

		// 添加联系人
		addContact() {
			FeedbackUtils.lightFeedback();
			this.editingContact = null;
			this.resetForm();
			this.$refs.contactPopup.open();
		},

		// 编辑联系人
		editContact(contact) {
			FeedbackUtils.lightFeedback();
			this.editingContact = contact;
			this.formData = { ...contact };
			this.$refs.contactPopup.open();
		},

		// 删除联系人
		deleteContact(contact, index) {
			FeedbackUtils.lightFeedback();
			uni.showModal({
				title: '删除联系人',
				content: `确定要删除联系人"${contact.name}"吗？`,
				success: (res) => {
					if (res.confirm) {
						this.emergencyContacts.splice(index, 1);
						FeedbackUtils.showSuccess('联系人删除成功');
					}
				}
			});
		},

		// 呼叫联系人
		callContact(contact) {
			FeedbackUtils.lightFeedback();
			uni.makePhoneCall({
				phoneNumber: contact.phone.replace(/\*/g, '')
			});
		},

		// 上传头像
		uploadAvatar() {
			FeedbackUtils.lightFeedback();
			uni.chooseImage({
				count: 1,
				sizeType: ['compressed'],
				sourceType: ['album', 'camera'],
				success: (res) => {
					this.formData.avatar = res.tempFilePaths[0];
					FeedbackUtils.showSuccess('头像上传成功');
				},
				fail: () => {
					FeedbackUtils.showError('头像上传失败');
				}
			});
		},

		// 选择优先级
		selectPriority(priority) {
			FeedbackUtils.lightFeedback();
			this.formData.priority = priority;
		},

		// 保存联系人
		async saveContact() {
			// 表单验证
			if (!this.validateForm()) {
				return;
			}

			this.saving = true;
			FeedbackUtils.showLoading('保存中...');

			try {
				// 模拟保存联系人
				await new Promise(resolve => setTimeout(resolve, 1500));
				
				if (this.editingContact) {
					// 编辑模式
					const index = this.emergencyContacts.findIndex(c => c.id === this.editingContact.id);
					if (index > -1) {
						this.emergencyContacts[index] = { ...this.formData, id: this.editingContact.id };
					}
				} else {
					// 添加模式
					const newContact = {
						...this.formData,
						id: Date.now()
					};
					this.emergencyContacts.push(newContact);
				}
				
				FeedbackUtils.hideLoading();
				FeedbackUtils.showSuccess('联系人保存成功');
				this.closePopup();
			} catch (error) {
				FeedbackUtils.hideLoading();
				FeedbackUtils.showError('保存失败，请重试');
			} finally {
				this.saving = false;
			}
		},

		// 关闭弹窗
		closePopup() {
			this.$refs.contactPopup.close();
			this.resetForm();
		},

		// 重置表单
		resetForm() {
			this.formData = {
				avatar: '',
				name: '',
				phone: '',
				relationship: '',
				priority: 1,
				address: '',
				note: ''
			};
		},

		// 表单验证
		validateForm() {
			if (!this.formData.name) {
				FeedbackUtils.showError('请输入联系人姓名');
				return false;
			}
			if (!this.formData.phone) {
				FeedbackUtils.showError('请输入联系人电话');
				return false;
			}
			return true;
		}
	}
}
</script>

<style scoped>
.container {
	background-color: #f5f5f5;
	min-height: 100vh;
}

.contacts-list {
	padding: 20rpx;
}

.contact-item {
	margin-bottom: 20rpx;
}

.contact-content {
	display: flex;
	gap: 20rpx;
	padding: 25rpx;
}

.contact-avatar {
	width: 80rpx;
	height: 80rpx;
}

.avatar-image,
.avatar-placeholder {
	width: 100%;
	height: 100%;
	border-radius: 50%;
	background: #f0f0f0;
	display: flex;
	align-items: center;
	justify-content: center;
}

.contact-info {
	flex: 1;
}

.contact-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 15rpx;
}

.contact-name {
	font-size: 30rpx;
	font-weight: bold;
	color: #333;
}

.contact-priority {
	padding: 5rpx 15rpx;
	border-radius: 15rpx;
	font-size: 20rpx;
}

.contact-priority.priority-high { background: #f44336; }
.contact-priority.priority-medium { background: #ff9800; }
.contact-priority.priority-normal { background: #4caf50; }
.contact-priority.priority-low { background: #9e9e9e; }

.priority-text {
	color: white;
	font-size: 20rpx;
}

.contact-details {
	display: flex;
	flex-direction: column;
	gap: 8rpx;
}

.detail-item {
	display: flex;
	align-items: center;
	gap: 8rpx;
}

.detail-text {
	font-size: 24rpx;
	color: #666;
}

.contact-actions {
	display: flex;
	flex-direction: column;
	gap: 10rpx;
	align-items: flex-end;
}

.empty {
	text-align: center;
	padding: 100rpx 40rpx;
	color: #999;
}

.empty-text {
	font-size: 28rpx;
	color: #999;
	display: block;
	margin: 20rpx 0 10rpx;
}

.empty-tip {
	font-size: 24rpx;
	color: #ccc;
}

.popup-content {
	padding: 40rpx;
	max-height: 80vh;
	overflow-y: auto;
}

.popup-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 30rpx;
}

.popup-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.popup-close {
	padding: 10rpx;
}

.form-item {
	margin-bottom: 25rpx;
}

.form-label {
	font-size: 28rpx;
	color: #333;
	display: block;
	margin-bottom: 10rpx;
}

.form-input,
.form-textarea {
	width: 100%;
	background: #f8f9fa;
	border: 1rpx solid #e0e0e0;
	border-radius: 10rpx;
	padding: 20rpx;
	font-size: 28rpx;
	color: #333;
}

.form-textarea {
	min-height: 120rpx;
}

.avatar-upload {
	width: 120rpx;
	height: 120rpx;
	border: 2rpx dashed #ccc;
	border-radius: 50%;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	gap: 5rpx;
}

.upload-avatar {
	width: 100%;
	height: 100%;
	border-radius: 50%;
}

.upload-text {
	font-size: 22rpx;
	color: #ccc;
}

.priority-options {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	gap: 15rpx;
}

.priority-option {
	padding: 15rpx 20rpx;
	border: 2rpx solid #e0e0e0;
	border-radius: 25rpx;
	text-align: center;
	background: #fff;
}

.priority-option.active {
	border-color: #ff8a00;
	background: rgba(255, 138, 0, 0.1);
}

.option-text {
	font-size: 26rpx;
	color: #333;
}

.popup-actions {
	display: flex;
	gap: 20rpx;
	margin-top: 40rpx;
}
</style>
