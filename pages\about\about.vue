<template>
	<view class="container" :class="{ 'elderly-mode': isElderlyMode }">
		<!-- 自定义导航栏 -->
		<view class="custom-navbar" :class="{ 'elderly-mode': isElderlyMode }">
			<view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
			<view class="navbar-content">
				<view class="navbar-left" @click="goBack">
					<Icon
						name="arrow-left-line"
						:size="isElderlyMode ? '40rpx' : '36rpx'"
						color="#333"
					></Icon>
					<text class="back-text">返回</text>
				</view>
				<view class="navbar-center">
					<text class="navbar-title">关于我们</text>
				</view>
				<view class="navbar-right"></view>
			</view>
		</view>

		<!-- 应用信息 -->
		<view class="app-info">
			<view class="app-logo">
				<image src="/static/logo.png" class="logo-image" mode="aspectFit"></image>
			</view>
			<text class="app-name">智慧养老</text>
			<text class="app-version">版本 {{appVersion}}</text>
			<text class="app-desc">专业的智慧养老服务平台</text>
		</view>

		<!-- 应用设置 -->
		<view class="settings-section">
			<view class="section-header">
				<Icon name="settings-line" size="32rpx" color="#ff8a00"></Icon>
				<text class="section-title">应用设置</text>
			</view>

			<view class="settings-list">
				<view class="setting-item" @click="checkUpdate">
					<Icon name="download-line" size="32rpx" color="#4caf50"></Icon>
					<view class="setting-info">
						<text class="setting-title">检查更新</text>
						<text class="setting-desc">当前版本：{{appVersion}}</text>
					</view>
					<Icon name="arrow-right-s-line" size="20rpx" color="#999"></Icon>
				</view>

				<view class="setting-item" @click="clearCache">
					<Icon name="delete-bin-line" size="32rpx" color="#ff9800"></Icon>
					<view class="setting-info">
						<text class="setting-title">清理缓存</text>
						<text class="setting-desc">释放存储空间</text>
					</view>
					<Icon name="arrow-right-s-line" size="20rpx" color="#999"></Icon>
				</view>

				<view class="setting-item" @click="exportData">
					<Icon name="file-download-line" size="32rpx" color="#2196f3"></Icon>
					<view class="setting-info">
						<text class="setting-title">导出数据</text>
						<text class="setting-desc">备份个人数据</text>
					</view>
					<Icon name="arrow-right-s-line" size="20rpx" color="#999"></Icon>
				</view>

				<view class="setting-item" @click="resetApp">
					<Icon name="restart-line" size="32rpx" color="#f44336"></Icon>
					<view class="setting-info">
						<text class="setting-title">重置应用</text>
						<text class="setting-desc">恢复默认设置</text>
					</view>
					<Icon name="arrow-right-s-line" size="20rpx" color="#999"></Icon>
				</view>
			</view>
		</view>

		<!-- 联系方式 -->
		<view class="contact-section">
			<view class="section-header">
				<Icon name="phone-line" size="32rpx" color="#ff8a00"></Icon>
				<text class="section-title">联系我们</text>
			</view>
			
			<view class="contact-list">
				<view class="contact-item" @click="callService">
					<Icon name="customer-service-line" size="32rpx" color="#4caf50"></Icon>
					<view class="contact-info">
						<text class="contact-title">客服热线</text>
						<text class="contact-value">************</text>
					</view>
					<Icon name="arrow-right-s-line" size="20rpx" color="#999"></Icon>
				</view>
				
				<view class="contact-item" @click="sendEmail">
					<Icon name="mail-line" size="32rpx" color="#2196f3"></Icon>
					<view class="contact-info">
						<text class="contact-title">邮箱地址</text>
						<text class="contact-value"><EMAIL></text>
					</view>
					<Icon name="arrow-right-s-line" size="20rpx" color="#999"></Icon>
				</view>
				
				<view class="contact-item" @click="viewAddress">
					<Icon name="map-pin-line" size="32rpx" color="#ff9800"></Icon>
					<view class="contact-info">
						<text class="contact-title">公司地址</text>
						<text class="contact-value">北京市朝阳区建国路88号</text>
					</view>
					<Icon name="arrow-right-s-line" size="20rpx" color="#999"></Icon>
				</view>
				
				<view class="contact-item" @click="viewWebsite">
					<Icon name="global-line" size="32rpx" color="#9c27b0"></Icon>
					<view class="contact-info">
						<text class="contact-title">官方网站</text>
						<text class="contact-value">www.zhyl.com</text>
					</view>
					<Icon name="arrow-right-s-line" size="20rpx" color="#999"></Icon>
				</view>
			</view>
		</view>

		<!-- 法律信息 -->
		<view class="legal-section">
			<view class="section-header">
				<Icon name="file-text-line" size="32rpx" color="#ff8a00"></Icon>
				<text class="section-title">法律信息</text>
			</view>
			
			<view class="legal-list">
				<view class="legal-item" @click="viewUserAgreement">
					<text class="legal-title">用户服务协议</text>
					<Icon name="arrow-right-s-line" size="20rpx" color="#999"></Icon>
				</view>
				<view class="legal-item" @click="viewPrivacyPolicy">
					<text class="legal-title">隐私政策</text>
					<Icon name="arrow-right-s-line" size="20rpx" color="#999"></Icon>
				</view>
				<view class="legal-item" @click="viewCopyright">
					<text class="legal-title">版权声明</text>
					<Icon name="arrow-right-s-line" size="20rpx" color="#999"></Icon>
				</view>
			</view>
		</view>

		<!-- 版权信息 -->
		<view class="copyright">
			<text class="copyright-text">© 2024 智慧养老科技有限公司</text>
			<text class="copyright-text">版权所有 京ICP备12345678号</text>
		</view>
	</view>
</template>

<script>
import Icon from '@/components/Icon/Icon.vue'
import { elderlyModeManager } from '@/utils/elderlyModeUtils.js'

export default {
	components: {
		Icon
	},
	data() {
		return {
			statusBarHeight: 0, // 状态栏高度
			isElderlyMode: false, // 适老化模式
			appVersion: '1.0.0'
		}
	},
	onLoad() {
		// 获取状态栏高度
		const systemInfo = uni.getSystemInfoSync();
		this.statusBarHeight = systemInfo.statusBarHeight || 44;

		// 初始化适老化模式
		this.isElderlyMode = elderlyModeManager.getElderlyMode();
		elderlyModeManager.onElderlyModeChange((isEnabled) => {
			this.isElderlyMode = isEnabled;
		});
	},
	methods: {
		checkUpdate() {
			uni.showLoading({
				title: '检查更新中...'
			});

			// 模拟检查更新
			setTimeout(() => {
				uni.hideLoading();
				uni.showToast({
					title: '已是最新版本',
					icon: 'success'
				});
			}, 2000);
		},
		clearCache() {
			uni.showModal({
				title: '清理缓存',
				content: '确定要清理应用缓存吗？',
				success: (res) => {
					if (res.confirm) {
						uni.showLoading({
							title: '清理中...'
						});

						// 清理缓存逻辑
						setTimeout(() => {
							uni.hideLoading();
							uni.showToast({
								title: '缓存清理完成',
								icon: 'success'
							});
						}, 1500);
					}
				}
			});
		},
		exportData() {
			uni.showLoading({
				title: '导出数据中...'
			});

			// 模拟导出数据
			setTimeout(() => {
				uni.hideLoading();
				uni.showToast({
					title: '数据导出完成',
					icon: 'success'
				});
			}, 2000);
		},
		resetApp() {
			uni.showModal({
				title: '重置应用',
				content: '确定要重置应用吗？这将清除所有个人设置和数据。',
				success: (res) => {
					if (res.confirm) {
						uni.showLoading({
							title: '重置中...'
						});

						// 重置应用逻辑
						setTimeout(() => {
							uni.hideLoading();
							uni.showToast({
								title: '应用重置完成',
								icon: 'success'
							});
						}, 2000);
					}
				}
			});
		},
		callService() {
			uni.makePhoneCall({
				phoneNumber: '************'
			});
		},
		sendEmail() {
			uni.showToast({
				title: '邮箱地址已复制',
				icon: 'success'
			});
		},
		viewAddress() {
			uni.openLocation({
				latitude: 39.9042,
				longitude: 116.4074,
				name: '智慧养老科技有限公司',
				address: '北京市朝阳区建国路88号'
			});
		},
		viewWebsite() {
			uni.showToast({
				title: '网站地址已复制',
				icon: 'success'
			});
		},
		viewUserAgreement() {
			uni.navigateTo({
				url: '/pages/legal/user-agreement'
			});
		},
		viewPrivacyPolicy() {
			uni.navigateTo({
				url: '/pages/legal/privacy-policy'
			});
		},
		viewCopyright() {
			uni.navigateTo({
				url: '/pages/legal/copyright'
			});
		},

		// 返回上一页
		goBack() {
			uni.navigateBack({
				fail: () => {
					uni.switchTab({
						url: '/pages/profile/profile'
					});
				}
			});
		}
	}
}
</script>

<style scoped>
.container {
	background: #f8f9fa;
	min-height: 100vh;
}

/* 导航栏样式 */
.custom-navbar {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 1000;
	background: rgba(255, 255, 255, 0.95);
	backdrop-filter: blur(20rpx);
	-webkit-backdrop-filter: blur(20rpx);
	border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);
}

.navbar-content {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 20rpx 32rpx;
	min-height: 88rpx;
}

.navbar-left {
	display: flex;
	align-items: center;
	gap: 12rpx;
	padding: 8rpx 16rpx 8rpx 0;
	border-radius: 20rpx;
	transition: all 0.2s ease;
}

.navbar-left:active {
	background-color: rgba(0, 0, 0, 0.04);
	transform: scale(0.96);
}

.back-text {
	font-size: 32rpx;
	color: #333;
	font-weight: 500;
}

.navbar-center {
	position: absolute;
	left: 50%;
	transform: translateX(-50%);
}

.navbar-title {
	font-size: 34rpx;
	font-weight: 600;
	color: #333;
}

.navbar-right {
	display: flex;
	align-items: center;
}

.app-info {
	background: white;
	text-align: center;
	padding: 60rpx 40rpx;
	margin-top: 220rpx; /* 为导航栏留出空间 */
	margin-bottom: 20rpx;
}

.app-logo {
	width: 120rpx;
	height: 120rpx;
	margin: 0 auto 30rpx;
	border-radius: 30rpx;
	overflow: hidden;
	background: #f0f0f0;
}

.logo-image {
	width: 100%;
	height: 100%;
}

.app-name {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 10rpx;
}

.app-version {
	font-size: 24rpx;
	color: #666;
	display: block;
	margin-bottom: 20rpx;
}

.app-desc {
	font-size: 26rpx;
	color: #999;
	display: block;
}

.settings-section, .contact-section, .legal-section {
	background: white;
	margin: 20rpx;
	border-radius: 30rpx;
	padding: 40rpx;
}

.section-header {
	display: flex;
	align-items: center;
	gap: 15rpx;
	margin-bottom: 30rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.settings-list {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

.setting-item {
	display: flex;
	align-items: center;
	gap: 20rpx;
	padding: 25rpx;
	background: #f8f9fa;
	border-radius: 20rpx;
}

.setting-info {
	flex: 1;
}

.setting-title {
	font-size: 28rpx;
	color: #333;
	font-weight: bold;
	display: block;
	margin-bottom: 5rpx;
}

.setting-desc {
	font-size: 24rpx;
	color: #666;
	display: block;
}

.contact-list, .legal-list {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

.contact-item, .legal-item {
	display: flex;
	align-items: center;
	gap: 20rpx;
	padding: 25rpx;
	background: #f8f9fa;
	border-radius: 20rpx;
}

.contact-info {
	flex: 1;
}

.contact-title, .legal-title {
	font-size: 28rpx;
	color: #333;
	font-weight: bold;
	display: block;
	margin-bottom: 5rpx;
}

.contact-value {
	font-size: 24rpx;
	color: #666;
	display: block;
}

.legal-item {
	justify-content: space-between;
}

.copyright {
	text-align: center;
	padding: 40rpx;
}

.copyright-text {
	font-size: 22rpx;
	color: #999;
	display: block;
	margin-bottom: 10rpx;
}

/* ===== 适老化模式样式 ===== */
.elderly-mode .navbar-content {
	padding: 24rpx 36rpx;
	min-height: 96rpx;
}

.elderly-mode .navbar-left {
	gap: 16rpx;
	padding: 12rpx 20rpx 12rpx 0;
}

.elderly-mode .back-text {
	font-size: 36rpx;
	font-weight: 600;
}

.elderly-mode .navbar-title {
	font-size: 38rpx;
	font-weight: 700;
}

.elderly-mode .about-content {
	margin-top: 240rpx;
	padding: 50rpx 30rpx;
}

.elderly-mode .about-title {
	font-size: 48rpx;
}

.elderly-mode .about-text {
	font-size: 36rpx;
	line-height: 1.8;
}
</style>
