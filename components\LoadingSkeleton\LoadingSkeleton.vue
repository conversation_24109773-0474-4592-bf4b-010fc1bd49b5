<template>
	<view class="skeleton-container" :class="{ 'elderly-mode': elderlyMode }">
		<!-- 列表类型骨架屏 -->
		<view v-if="type === 'list'" class="skeleton-list">
			<view 
				v-for="index in count" 
				:key="index" 
				class="skeleton-item"
			>
				<view class="skeleton-avatar"></view>
				<view class="skeleton-content">
					<view class="skeleton-line skeleton-title"></view>
					<view class="skeleton-line skeleton-text"></view>
					<view class="skeleton-line skeleton-text short"></view>
				</view>
			</view>
		</view>
		
		<!-- 卡片类型骨架屏 -->
		<view v-else-if="type === 'card'" class="skeleton-cards">
			<view 
				v-for="index in count" 
				:key="index" 
				class="skeleton-card"
			>
				<view class="skeleton-image"></view>
				<view class="skeleton-card-content">
					<view class="skeleton-line skeleton-title"></view>
					<view class="skeleton-line skeleton-text"></view>
				</view>
			</view>
		</view>
		
		<!-- 网格类型骨架屏 -->
		<view v-else-if="type === 'grid'" class="skeleton-grid">
			<view 
				v-for="index in count" 
				:key="index" 
				class="skeleton-grid-item"
			>
				<view class="skeleton-grid-icon"></view>
				<view class="skeleton-line skeleton-grid-text"></view>
			</view>
		</view>
		
		<!-- 文章类型骨架屏 -->
		<view v-else-if="type === 'article'" class="skeleton-article">
			<view class="skeleton-line skeleton-title long"></view>
			<view class="skeleton-line skeleton-text"></view>
			<view class="skeleton-line skeleton-text"></view>
			<view class="skeleton-line skeleton-text short"></view>
			<view class="skeleton-image article-image"></view>
			<view class="skeleton-line skeleton-text"></view>
			<view class="skeleton-line skeleton-text"></view>
		</view>
		
		<!-- 默认类型骨架屏 -->
		<view v-else class="skeleton-default">
			<view 
				v-for="index in count" 
				:key="index" 
				class="skeleton-line"
				:class="{ 'short': index % 3 === 0 }"
			></view>
		</view>
	</view>
</template>

<script>
export default {
	name: 'LoadingSkeleton',
	props: {
		type: {
			type: String,
			default: 'default', // default, list, card, grid, article
			validator: value => ['default', 'list', 'card', 'grid', 'article'].includes(value)
		},
		count: {
			type: Number,
			default: 3
		},
		elderlyMode: {
			type: Boolean,
			default: false
		}
	}
}
</script>

<style scoped>
.skeleton-container {
	padding: 20rpx;
}

.skeleton-container.elderly-mode {
	padding: 30rpx;
}

/* 骨架屏基础样式 */
.skeleton-line {
	height: 24rpx;
	background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
	background-size: 200% 100%;
	animation: skeleton-loading 1.5s infinite;
	border-radius: 4rpx;
	margin-bottom: 16rpx;
}

.skeleton-line.short {
	width: 60%;
}

.skeleton-line.long {
	width: 90%;
}

.skeleton-title {
	height: 32rpx;
	width: 80%;
	margin-bottom: 20rpx;
}

.skeleton-text {
	height: 24rpx;
	width: 100%;
}

.skeleton-avatar {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
	background-size: 200% 100%;
	animation: skeleton-loading 1.5s infinite;
	flex-shrink: 0;
}

.skeleton-image {
	width: 100%;
	height: 200rpx;
	background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
	background-size: 200% 100%;
	animation: skeleton-loading 1.5s infinite;
	border-radius: 8rpx;
	margin-bottom: 20rpx;
}

.skeleton-image.article-image {
	height: 300rpx;
	margin: 30rpx 0;
}

/* 列表类型 */
.skeleton-list {
	display: flex;
	flex-direction: column;
	gap: 30rpx;
}

.skeleton-item {
	display: flex;
	gap: 20rpx;
	align-items: flex-start;
}

.skeleton-content {
	flex: 1;
}

/* 卡片类型 */
.skeleton-cards {
	display: flex;
	flex-direction: column;
	gap: 30rpx;
}

.skeleton-card {
	border-radius: 12rpx;
	overflow: hidden;
	background: white;
	box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.skeleton-card-content {
	padding: 20rpx;
}

/* 网格类型 */
.skeleton-grid {
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(150rpx, 1fr));
	gap: 20rpx;
}

.skeleton-grid-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 20rpx;
	background: white;
	border-radius: 12rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.skeleton-grid-icon {
	width: 60rpx;
	height: 60rpx;
	border-radius: 50%;
	background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
	background-size: 200% 100%;
	animation: skeleton-loading 1.5s infinite;
	margin-bottom: 16rpx;
}

.skeleton-grid-text {
	width: 80rpx;
	height: 20rpx;
}

/* 文章类型 */
.skeleton-article {
	padding: 20rpx 0;
}

/* 默认类型 */
.skeleton-default {
	display: flex;
	flex-direction: column;
	gap: 16rpx;
}

/* 适老版样式 */
.elderly-mode .skeleton-line {
	height: 32rpx;
	margin-bottom: 24rpx;
}

.elderly-mode .skeleton-title {
	height: 40rpx;
	margin-bottom: 30rpx;
}

.elderly-mode .skeleton-avatar {
	width: 100rpx;
	height: 100rpx;
}

.elderly-mode .skeleton-grid-icon {
	width: 80rpx;
	height: 80rpx;
}

/* 动画效果 */
@keyframes skeleton-loading {
	0% {
		background-position: -200% 0;
	}
	100% {
		background-position: 200% 0;
	}
}
</style>
