<template>
	<view class="container">
		<view class="custom-navbar">
			<view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
			<view class="navbar-content">
				<view class="navbar-left" @click="goBack">
					<Icon name="arrow-left-line" size="36rpx" color="#333"></Icon>
					<text class="back-text">返回</text>
				</view>
				<view class="navbar-center">
					<text class="navbar-title">跳转测试</text>
				</view>
				<view class="navbar-right"></view>
			</view>
		</view>

		<view class="content">
			<text class="title">Profile页面跳转测试</text>
			<text class="subtitle">点击下方按钮测试各个页面的跳转功能</text>

			<view class="test-section">
				<text class="section-title">快捷功能页面</text>
				<button class="test-btn" @click="testNavigation('/pages/order/list')">我的订单</button>
				<button class="test-btn" @click="testNavigation('/pages/favorite/list')">我的收藏</button>
				<button class="test-btn" @click="testNavigation('/pages/history/list')">浏览历史</button>
				<button class="test-btn" @click="testNavigation('/pages/wallet/wallet')">我的钱包</button>
			</view>

			<view class="test-section">
				<text class="section-title">个人信息管理</text>
				<button class="test-btn" @click="testNavigation('/pages/profile/info')">基本信息</button>
				<button class="test-btn" @click="testNavigation('/pages/profile/contact')">联系方式</button>
				<button class="test-btn" @click="testNavigation('/pages/profile/emergency')">紧急联系人</button>
			</view>

			<view class="test-section">
				<text class="section-title">账户安全</text>
				<button class="test-btn" @click="testNavigation('/pages/account/security')">安全设置</button>
				<button class="test-btn" @click="testNavigation('/pages/account/password')">密码管理</button>
				<button class="test-btn" @click="testNavigation('/pages/account/privacy')">隐私设置</button>
			</view>

			<view class="test-section">
				<text class="section-title">应用设置</text>
				<button class="test-btn" @click="testNavigation('/pages/data/manage')">数据管理</button>
				<button class="test-btn" @click="testNavigation('/pages/settings/notification')">通知设置</button>
				<button class="test-btn" @click="testNavigation('/pages/settings/display')">显示设置</button>
				<button class="test-btn" @click="testNavigation('/pages/settings/language')">语言设置</button>
			</view>

			<view class="test-section">
				<text class="section-title">帮助与反馈</text>
				<button class="test-btn" @click="testNavigation('/pages/help/faq')">常见问题</button>
				<button class="test-btn" @click="testNavigation('/pages/help/service')">在线客服</button>
				<button class="test-btn" @click="testNavigation('/pages/help/feedback')">意见反馈</button>
				<button class="test-btn" @click="testNavigation('/pages/help/about')">关于我们</button>
			</view>

			<view class="test-results">
				<text class="results-title">测试结果</text>
				<view class="result-item" v-for="(result, index) in testResults" :key="index">
					<text class="result-url">{{result.url}}</text>
					<text class="result-status" :class="result.success ? 'success' : 'error'">
						{{result.success ? '✅ 成功' : '❌ 失败'}}
					</text>
					<text class="result-message" v-if="result.message">{{result.message}}</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import Icon from '@/components/Icon/Icon.vue'

export default {
	components: {
		Icon
	},
	data() {
		return {
			statusBarHeight: 0,
			testResults: []
		}
	},
	onLoad() {
		const systemInfo = uni.getSystemInfoSync();
		this.statusBarHeight = systemInfo.statusBarHeight || 44;
	},
	methods: {
		testNavigation(url) {
			const startTime = Date.now();
			
			uni.navigateTo({
				url: url,
				success: () => {
					const duration = Date.now() - startTime;
					this.testResults.unshift({
						url: url,
						success: true,
						message: `跳转成功 (${duration}ms)`,
						timestamp: new Date().toLocaleTimeString()
					});
					console.log('测试成功:', url);
				},
				fail: (error) => {
					this.testResults.unshift({
						url: url,
						success: false,
						message: `错误: ${error.errMsg || error.message || '未知错误'}`,
						timestamp: new Date().toLocaleTimeString()
					});
					console.error('测试失败:', url, error);
				}
			});
		},
		goBack() {
			uni.navigateBack({
				fail: () => {
					uni.switchTab({
						url: '/pages/profile/profile'
					});
				}
			});
		}
	}
}
</script>

<style scoped>
.container {
	background: #f8f9fa;
	min-height: 100vh;
}

.custom-navbar {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 1000;
	background: rgba(255, 255, 255, 0.95);
	backdrop-filter: blur(20rpx);
	border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);
}

.navbar-content {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 20rpx 32rpx;
	min-height: 88rpx;
}

.navbar-left {
	display: flex;
	align-items: center;
	gap: 12rpx;
	padding: 8rpx 16rpx 8rpx 0;
	border-radius: 20rpx;
	transition: all 0.2s ease;
}

.navbar-left:active {
	background-color: rgba(0, 0, 0, 0.04);
	transform: scale(0.96);
}

.back-text {
	font-size: 32rpx;
	color: #333;
	font-weight: 500;
}

.navbar-center {
	position: absolute;
	left: 50%;
	transform: translateX(-50%);
}

.navbar-title {
	font-size: 34rpx;
	font-weight: 600;
	color: #333;
}

.content {
	padding: 240rpx 40rpx 40rpx;
}

.title {
	font-size: 48rpx;
	font-weight: 600;
	color: #333;
	text-align: center;
	margin-bottom: 20rpx;
	display: block;
}

.subtitle {
	font-size: 28rpx;
	color: #666;
	text-align: center;
	margin-bottom: 60rpx;
	display: block;
}

.test-section {
	background: white;
	border-radius: 20rpx;
	padding: 30rpx;
	margin-bottom: 30rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 30rpx;
	display: block;
}

.test-btn {
	display: block;
	width: 100%;
	padding: 25rpx;
	margin-bottom: 20rpx;
	background: #ff8a00;
	color: white;
	border: none;
	border-radius: 15rpx;
	font-size: 28rpx;
	font-weight: 500;
}

.test-btn:active {
	background: #e67c00;
	transform: scale(0.98);
}

.test-btn:last-child {
	margin-bottom: 0;
}

.test-results {
	background: white;
	border-radius: 20rpx;
	padding: 30rpx;
	margin-top: 30rpx;
}

.results-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 30rpx;
	display: block;
}

.result-item {
	padding: 20rpx;
	border-bottom: 1rpx solid #f0f0f0;
	margin-bottom: 20rpx;
}

.result-item:last-child {
	border-bottom: none;
	margin-bottom: 0;
}

.result-url {
	font-size: 24rpx;
	color: #666;
	display: block;
	margin-bottom: 10rpx;
}

.result-status {
	font-size: 28rpx;
	font-weight: 600;
	display: block;
	margin-bottom: 10rpx;
}

.result-status.success {
	color: #4caf50;
}

.result-status.error {
	color: #f44336;
}

.result-message {
	font-size: 24rpx;
	color: #999;
	display: block;
}
</style>
