<template>
	<button 
		class="interactive-button" 
		:class="[
			`btn-${type}`,
			`btn-${size}`,
			{ 'btn-loading': loading },
			{ 'btn-disabled': disabled },
			{ 'btn-block': block },
			{ 'btn-round': round },
			{ 'elderly-mode': elderlyMode },
			customClass
		]"
		:style="buttonStyle"
		:disabled="disabled || loading"
		@click="handleClick"
		@touchstart="handleTouchStart"
		@touchend="handleTouchEnd"
	>
		<!-- 加载状态 -->
		<view v-if="loading" class="btn-loading-content">
			<view class="loading-spinner"></view>
			<text v-if="loadingText" class="btn-text-content">{{ loadingText }}</text>
		</view>

		<!-- 正常状态 -->
		<view v-else class="btn-content">
			<!-- 左侧图标 -->
			<Icon
				v-if="leftIcon"
				:name="leftIcon"
				:size="iconSize"
				:color="iconColor"
				class="btn-icon left"
			/>

			<!-- 按钮文字 -->
			<text class="btn-text-content">
				<slot>{{ text }}</slot>
			</text>

			<!-- 右侧图标 -->
			<Icon
				v-if="rightIcon"
				:name="rightIcon"
				:size="iconSize"
				:color="iconColor"
				class="btn-icon right"
			/>
		</view>
	</button>
</template>

<script>
import Icon from '@/components/Icon/Icon.vue'

export default {
	name: 'InteractiveButton',
	components: {
		Icon
	},
	props: {
		type: {
			type: String,
			default: 'primary',
			validator: value => ['primary', 'secondary', 'success', 'warning', 'danger', 'info', 'text'].includes(value)
		},
		size: {
			type: String,
			default: 'medium',
			validator: value => ['small', 'medium', 'large'].includes(value)
		},
		text: {
			type: String,
			default: ''
		},
		loading: {
			type: Boolean,
			default: false
		},
		loadingText: {
			type: String,
			default: '加载中...'
		},
		disabled: {
			type: Boolean,
			default: false
		},
		block: {
			type: Boolean,
			default: false
		},
		round: {
			type: Boolean,
			default: false
		},
		elderlyMode: {
			type: Boolean,
			default: false
		},
		leftIcon: {
			type: String,
			default: ''
		},
		rightIcon: {
			type: String,
			default: ''
		},
		customClass: {
			type: String,
			default: ''
		},
		width: {
			type: [String, Number],
			default: ''
		},
		height: {
			type: [String, Number],
			default: ''
		}
	},
	computed: {
		buttonStyle() {
			const style = {}
			
			if (this.width) {
				style.width = typeof this.width === 'number' ? this.width + 'rpx' : this.width
			}
			
			if (this.height) {
				style.height = typeof this.height === 'number' ? this.height + 'rpx' : this.height
			}
			
			return style
		},
		iconSize() {
			const sizeMap = {
				small: '24rpx',
				medium: '28rpx',
				large: '32rpx'
			}
			return this.elderlyMode ? '32rpx' : sizeMap[this.size]
		},
		iconColor() {
			if (this.type === 'text') {
				return '#ff8a00'
			}
			return this.type === 'secondary' ? '#666' : '#fff'
		}
	},
	methods: {
		handleClick(e) {
			if (this.disabled || this.loading) {
				return
			}
			
			// 添加点击反馈
			this.addClickFeedback()
			
			this.$emit('click', e)
		},
		handleTouchStart() {
			if (this.disabled || this.loading) {
				return
			}
		},
		handleTouchEnd() {
			// 触摸结束处理
		},
		addClickFeedback() {
			// 添加轻微的震动反馈
			try {
				uni.vibrateShort({
					type: 'light'
				})
			} catch (e) {
				// 忽略不支持震动的设备
			}
		}
	}
}
</script>

<style scoped>
/* ================================
   iOS风格InteractiveButton组件
   基于iOS Human Interface Guidelines
   ================================ */

.interactive-button {
	border: none;
	border-radius: 16rpx; /* iOS标准圆角 */
	font-size: 34rpx; /* iOS Body字体大小 */
	font-weight: 600; /* iOS Semibold字重 */
	font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'SF Pro Text', 'Helvetica Neue', Helvetica, Arial, sans-serif;
	text-align: center;
	cursor: pointer;
	transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94); /* iOS标准缓动 */
	position: relative;
	overflow: hidden;
	display: inline-flex;
	align-items: center;
	justify-content: center;
	box-sizing: border-box;
	box-shadow: 0 2rpx 16rpx rgba(0, 0, 0, 0.06); /* iOS风格阴影 */
	-webkit-tap-highlight-color: transparent; /* 移除iOS点击高亮 */
	user-select: none;
	-webkit-user-select: none;
}

.interactive-button::after {
	border: none;
}

/* iOS风格按钮类型样式 - 使用设计系统变量 */
.btn-primary {
	background: var(--primary-color, #ff8a00);
	color: var(--text-inverse, white);
	border: 1rpx solid var(--primary-color, #ff8a00);
}

.btn-primary:active {
	background: var(--primary-dark, #e67700);
	border-color: var(--primary-dark, #e67700);
}

.btn-primary:focus {
	box-shadow: var(--shadow-focus, 0 0 0 4rpx rgba(255, 138, 0, 0.25));
}

.btn-secondary {
	background: var(--background-secondary, #F4F5F7);
	color: var(--text-primary, #374151);
	border: 1rpx solid var(--border-color, #E8EAED);
}

.btn-secondary:active {
	background: var(--background-tertiary, #E8EAED);
	color: var(--text-primary, #1F2937);
	border-color: var(--border-medium, #DADCE0);
}

.btn-secondary:focus {
	box-shadow: var(--shadow-focus-ring, 0 0 0 2rpx rgba(255, 138, 0, 0.5));
}

.btn-success {
	background: var(--success-color, #34C759);
	color: var(--text-inverse, white);
	border: 1rpx solid var(--success-color, #34C759);
}

.btn-success:active {
	background: var(--success-color-dark, #28A745);
	border-color: var(--success-color-dark, #28A745);
}

.btn-success:focus {
	box-shadow: var(--shadow-success, 0 0 0 4rpx rgba(52, 199, 89, 0.25));
}

.btn-warning {
	background: var(--warning-color, #FF9500);
	color: var(--text-inverse, white);
	border: 1rpx solid var(--warning-color, #FF9500);
}

.btn-warning:active {
	background: var(--warning-color-dark, #E68900);
	border-color: var(--warning-color-dark, #E68900);
}

.btn-danger {
	background: var(--error-color, #FF3B30);
	color: var(--text-inverse, white);
	border: 1rpx solid var(--error-color, #FF3B30);
}

.btn-danger:active {
	background: var(--error-color-dark, #DC3545);
	border-color: var(--error-color-dark, #DC3545);
}

.btn-danger:focus {
	box-shadow: var(--shadow-error, 0 0 0 4rpx rgba(255, 59, 48, 0.25));
}

.btn-info {
	background: var(--info-color, #007AFF);
	color: var(--text-inverse, white);
	border: 1rpx solid var(--info-color, #007AFF);
}

.btn-info:active {
	background: var(--info-color-dark, #0056CC);
	border-color: var(--info-color-dark, #0056CC);
}

/* 文本类型按钮样式 - 使用设计系统变量 */
.btn-text {
	background: transparent;
	color: var(--primary-color, #ff8a00);
	border: none;
	box-shadow: none;
}

.btn-text:active {
	background: var(--primary-tint, rgba(255, 138, 0, 0.1));
}

.btn-text:focus {
	box-shadow: var(--shadow-focus-ring, 0 0 0 2rpx rgba(255, 138, 0, 0.5));
}

/* iOS风格按钮尺寸 - 使用设计系统变量 */
.btn-small {
	padding: var(--spacing-6, 12rpx) var(--spacing-12, 24rpx);
	font-size: 26rpx; /* iOS Footnote字体 */
	border-radius: var(--radius-button, 16rpx);
	min-height: var(--touch-target-min, 44rpx);
	gap: var(--spacing-4, 8rpx);
}

.btn-medium {
	padding: var(--spacing-8, 16rpx) var(--spacing-16, 32rpx);
	font-size: 34rpx; /* iOS Body字体 */
	border-radius: var(--radius-button, 16rpx);
	min-height: var(--touch-target-comfortable, 56rpx);
	gap: var(--spacing-6, 12rpx);
}

.btn-large {
	padding: var(--spacing-10, 20rpx) var(--spacing-20, 40rpx);
	font-size: 36rpx; /* iOS Headline字体 */
	border-radius: var(--radius-button, 16rpx);
	min-height: var(--touch-target-large, 64rpx);
	gap: var(--spacing-8, 16rpx);
}

/* iOS风格按钮状态 - 使用设计系统变量 */
.btn-loading {
	pointer-events: none;
	opacity: 0.6;
	cursor: wait;
}

.btn-disabled {
	pointer-events: none;
	opacity: 0.4;
	background: var(--background-tertiary, #E8EAED) !important;
	color: var(--text-disabled, #9CA3AF) !important;
	border-color: var(--border-light, #E8EAED) !important;
	box-shadow: none !important;
	cursor: not-allowed;
}

.btn-disabled .btn-text-content {
	color: var(--text-disabled, #9CA3AF) !important;
}

/* 适老化增强 */
.elderly-mode .interactive-button,
.ios-elderly-mode .interactive-button {
	min-height: var(--touch-target-large, 64rpx) !important;
	font-size: 38rpx !important; /* 增大字体 */
	padding: var(--spacing-12, 24rpx) var(--spacing-20, 40rpx) !important;
	border-width: 2rpx !important; /* 增强边框 */
	box-shadow: var(--shadow-lg, 0 8rpx 24rpx rgba(0, 0, 0, 0.15)) !important;
}

.elderly-mode .btn-small,
.ios-elderly-mode .btn-small {
	min-height: var(--touch-target-comfortable, 56rpx) !important;
	font-size: 32rpx !important;
}

.elderly-mode .btn-medium,
.ios-elderly-mode .btn-medium {
	min-height: var(--touch-target-large, 64rpx) !important;
	font-size: 38rpx !important;
}

.elderly-mode .btn-large,
.ios-elderly-mode .btn-large {
	min-height: 80rpx !important;
	font-size: 42rpx !important;
}

.btn-block {
	width: 100%;
	display: flex;
}

.btn-round {
	border-radius: 50rpx; /* 完全圆角 */
}

/* iOS风格按钮内容 */
.btn-content,
.btn-loading-content {
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 8rpx; /* iOS标准间距 */
}

/* 按钮内文字样式 */
.btn-text-content {
	line-height: 1.2; /* iOS标准行高 */
	letter-spacing: -0.01em; /* iOS字母间距 */
	color: inherit; /* 继承按钮的字体颜色 */
}

/* 确保不同按钮类型的文字颜色正确 */
.btn-primary .btn-text-content,
.btn-success .btn-text-content,
.btn-warning .btn-text-content,
.btn-danger .btn-text-content,
.btn-info .btn-text-content {
	color: white; /* 深色背景按钮使用白色文字 */
}

.btn-secondary .btn-text-content {
	color: #374151; /* 浅色背景按钮使用深色文字 */
}

.btn-text .btn-text-content {
	color: #ff8a00; /* 文本按钮使用品牌色文字 */
}

.btn-icon.left {
	margin-right: 8rpx; /* iOS标准图标间距 */
}

.btn-icon.right {
	margin-left: 8rpx; /* iOS标准图标间距 */
}

/* iOS风格加载动画 */
.loading-spinner {
	width: 32rpx; /* 更大的加载指示器 */
	height: 32rpx;
	border: 4rpx solid rgba(255, 255, 255, 0.2);
	border-top: 4rpx solid currentColor;
	border-radius: 50%;
	animation: iosSpinning 1s linear infinite;
}

/* iOS风格交互效果 */
.interactive-button:not(.btn-disabled):not(.btn-loading):active {
	transform: scale(0.96); /* iOS标准按压缩放 */
	transition: transform 0.1s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.interactive-button:not(.btn-disabled):not(.btn-loading):not(:active) {
	transform: scale(1);
	transition: transform 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* iOS风格悬停效果（仅在支持hover的设备上） */
@media (hover: hover) {
	.interactive-button:not(.btn-disabled):not(.btn-loading):hover {
		opacity: 0.85;
		transition: opacity 0.15s cubic-bezier(0.25, 0.46, 0.45, 0.94);
	}
}

/* iOS风格适老化样式 */
.elderly-mode {
	font-size: calc(34rpx * 1.2) !important; /* 基于iOS Body字体放大 */
	font-weight: 600 !important; /* 使用Semibold字重 */
	padding: calc(16rpx * 1.5) calc(32rpx * 1.5) !important; /* 放大内边距 */
	border-radius: 20rpx !important; /* 更大的圆角 */
	min-height: 112rpx !important; /* 更大的触摸目标 */
	box-shadow: 0 4rpx 24rpx rgba(0, 0, 0, 0.08) !important; /* 更明显的阴影 */
	border: 2rpx solid #b0b0b0 !important; /* 高对比度边框 */
}

.elderly-mode.btn-small {
	font-size: calc(26rpx * 1.2) !important;
	padding: calc(12rpx * 1.5) calc(24rpx * 1.5) !important;
	min-height: 88rpx !important;
}

.elderly-mode.btn-large {
	font-size: calc(36rpx * 1.2) !important;
	padding: calc(20rpx * 1.5) calc(40rpx * 1.5) !important;
	min-height: 136rpx !important;
}

/* 适老化按压效果增强 */
.elderly-mode:not(.btn-disabled):not(.btn-loading):active {
	transform: scale(0.94) !important; /* 更明显的按压效果 */
	box-shadow: 0 2rpx 16rpx rgba(255, 138, 0, 0.3) !important;
}

/* iOS风格动画关键帧 */
@keyframes iosSpinning {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

/* iOS风格响应式设计 */
@media (max-width: 375px) {
	/* iPhone SE */
	.btn-small {
		padding: 10rpx 20rpx;
		font-size: 24rpx;
		min-height: 56rpx;
	}

	.btn-medium {
		padding: 14rpx 28rpx;
		font-size: 30rpx;
		min-height: 80rpx;
	}

	.btn-large {
		padding: 18rpx 36rpx;
		font-size: 32rpx;
		min-height: 104rpx;
	}
}

@media (min-width: 768px) {
	/* iPad */
	.btn-small {
		padding: 16rpx 32rpx;
		font-size: 28rpx;
		min-height: 72rpx;
	}

	.btn-medium {
		padding: 20rpx 40rpx;
		font-size: 36rpx;
		min-height: 96rpx;
	}

	.btn-large {
		padding: 24rpx 48rpx;
		font-size: 40rpx;
		min-height: 120rpx;
	}
}
</style>
