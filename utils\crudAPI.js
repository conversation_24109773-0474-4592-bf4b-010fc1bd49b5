/**
 * CRUD API 接口
 * 提供统一的数据操作接口，模拟真实的后端API
 */

import dataManager from './dataManager.js'

class CrudAPI {
	constructor() {
		this.dataManager = dataManager
	}

	/**
	 * 通用响应格式
	 */
	createResponse(success, data = null, message = '', code = 200) {
		return {
			success,
			data,
			message,
			code,
			timestamp: new Date().toISOString()
		}
	}

	/**
	 * 任务管理 CRUD
	 */
	async getTasks(params = {}) {
		await this.dataManager.simulateNetworkDelay()
		
		if (this.dataManager.simulateNetworkError()) {
			return this.createResponse(false, null, '网络连接失败，请重试', 500)
		}

		try {
			let tasks = this.dataManager.getItem('tasks') || []
			
			// 筛选
			if (params.status) {
				tasks = tasks.filter(task => task.status === params.status)
			}
			if (params.category) {
				tasks = tasks.filter(task => task.category === params.category)
			}
			if (params.priority) {
				tasks = tasks.filter(task => task.priority === params.priority)
			}

			// 搜索
			if (params.keyword) {
				const keyword = params.keyword.toLowerCase()
				tasks = tasks.filter(task => 
					task.title.toLowerCase().includes(keyword) ||
					task.description.toLowerCase().includes(keyword)
				)
			}

			// 排序
			tasks.sort((a, b) => new Date(b.updatedAt) - new Date(a.updatedAt))

			// 分页
			const page = parseInt(params.page) || 1
			const pageSize = parseInt(params.pageSize) || 10
			const start = (page - 1) * pageSize
			const end = start + pageSize
			const paginatedTasks = tasks.slice(start, end)

			return this.createResponse(true, {
				list: paginatedTasks,
				total: tasks.length,
				page,
				pageSize,
				hasMore: end < tasks.length
			}, '获取成功')
		} catch (error) {
			console.error('获取任务失败:', error)
			return this.createResponse(false, null, '获取任务失败', 500)
		}
	}

	async createTask(taskData) {
		await this.dataManager.simulateNetworkDelay()
		
		if (this.dataManager.simulateNetworkError()) {
			return this.createResponse(false, null, '网络连接失败，请重试', 500)
		}

		try {
			const tasks = this.dataManager.getItem('tasks') || []
			const now = new Date()
			
			const newTask = {
				id: this.dataManager.generateId(),
				...taskData,
				status: 'pending',
				createdAt: this.dataManager.formatDateTime(now),
				updatedAt: this.dataManager.formatDateTime(now)
			}

			tasks.unshift(newTask)
			this.dataManager.setItem('tasks', tasks)

			return this.createResponse(true, newTask, '创建成功')
		} catch (error) {
			console.error('创建任务失败:', error)
			return this.createResponse(false, null, '创建任务失败', 500)
		}
	}

	async updateTask(taskId, taskData) {
		await this.dataManager.simulateNetworkDelay()
		
		if (this.dataManager.simulateNetworkError()) {
			return this.createResponse(false, null, '网络连接失败，请重试', 500)
		}

		try {
			const tasks = this.dataManager.getItem('tasks') || []
			const taskIndex = tasks.findIndex(task => task.id === taskId)

			if (taskIndex === -1) {
				return this.createResponse(false, null, '任务不存在', 404)
			}

			const updatedTask = {
				...tasks[taskIndex],
				...taskData,
				updatedAt: this.dataManager.formatDateTime(new Date())
			}

			tasks[taskIndex] = updatedTask
			this.dataManager.setItem('tasks', tasks)

			return this.createResponse(true, updatedTask, '更新成功')
		} catch (error) {
			console.error('更新任务失败:', error)
			return this.createResponse(false, null, '更新任务失败', 500)
		}
	}

	async deleteTask(taskId) {
		await this.dataManager.simulateNetworkDelay()
		
		if (this.dataManager.simulateNetworkError()) {
			return this.createResponse(false, null, '网络连接失败，请重试', 500)
		}

		try {
			const tasks = this.dataManager.getItem('tasks') || []
			const taskIndex = tasks.findIndex(task => task.id === taskId)

			if (taskIndex === -1) {
				return this.createResponse(false, null, '任务不存在', 404)
			}

			tasks.splice(taskIndex, 1)
			this.dataManager.setItem('tasks', tasks)

			return this.createResponse(true, null, '删除成功')
		} catch (error) {
			console.error('删除任务失败:', error)
			return this.createResponse(false, null, '删除任务失败', 500)
		}
	}

	async getTaskById(taskId) {
		await this.dataManager.simulateNetworkDelay()
		
		try {
			const tasks = this.dataManager.getItem('tasks') || []
			const task = tasks.find(task => task.id === taskId)

			if (!task) {
				return this.createResponse(false, null, '任务不存在', 404)
			}

			return this.createResponse(true, task, '获取成功')
		} catch (error) {
			console.error('获取任务详情失败:', error)
			return this.createResponse(false, null, '获取任务详情失败', 500)
		}
	}

	/**
	 * 健康记录 CRUD
	 */
	async getHealthRecords(params = {}) {
		await this.dataManager.simulateNetworkDelay()
		
		if (this.dataManager.simulateNetworkError()) {
			return this.createResponse(false, null, '网络连接失败，请重试', 500)
		}

		try {
			let records = this.dataManager.getItem('health_records') || []
			
			// 筛选
			if (params.type) {
				records = records.filter(record => record.type === params.type)
			}
			if (params.status) {
				records = records.filter(record => record.status === params.status)
			}
			if (params.dateFrom) {
				records = records.filter(record => record.date >= params.dateFrom)
			}
			if (params.dateTo) {
				records = records.filter(record => record.date <= params.dateTo)
			}

			// 排序
			records.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))

			// 分页
			const page = parseInt(params.page) || 1
			const pageSize = parseInt(params.pageSize) || 10
			const start = (page - 1) * pageSize
			const end = start + pageSize
			const paginatedRecords = records.slice(start, end)

			return this.createResponse(true, {
				list: paginatedRecords,
				total: records.length,
				page,
				pageSize,
				hasMore: end < records.length
			}, '获取成功')
		} catch (error) {
			console.error('获取健康记录失败:', error)
			return this.createResponse(false, null, '获取健康记录失败', 500)
		}
	}

	async createHealthRecord(recordData) {
		await this.dataManager.simulateNetworkDelay()
		
		if (this.dataManager.simulateNetworkError()) {
			return this.createResponse(false, null, '网络连接失败，请重试', 500)
		}

		try {
			const records = this.dataManager.getItem('health_records') || []
			const now = new Date()
			
			const newRecord = {
				id: this.dataManager.generateId(),
				...recordData,
				createdAt: this.dataManager.formatDateTime(now)
			}

			records.unshift(newRecord)
			this.dataManager.setItem('health_records', records)

			return this.createResponse(true, newRecord, '记录成功')
		} catch (error) {
			console.error('创建健康记录失败:', error)
			return this.createResponse(false, null, '创建健康记录失败', 500)
		}
	}

	async updateHealthRecord(recordId, recordData) {
		await this.dataManager.simulateNetworkDelay()
		
		if (this.dataManager.simulateNetworkError()) {
			return this.createResponse(false, null, '网络连接失败，请重试', 500)
		}

		try {
			const records = this.dataManager.getItem('health_records') || []
			const recordIndex = records.findIndex(record => record.id === recordId)

			if (recordIndex === -1) {
				return this.createResponse(false, null, '记录不存在', 404)
			}

			const updatedRecord = {
				...records[recordIndex],
				...recordData,
				updatedAt: this.dataManager.formatDateTime(new Date())
			}

			records[recordIndex] = updatedRecord
			this.dataManager.setItem('health_records', records)

			return this.createResponse(true, updatedRecord, '更新成功')
		} catch (error) {
			console.error('更新健康记录失败:', error)
			return this.createResponse(false, null, '更新健康记录失败', 500)
		}
	}

	async deleteHealthRecord(recordId) {
		await this.dataManager.simulateNetworkDelay()
		
		if (this.dataManager.simulateNetworkError()) {
			return this.createResponse(false, null, '网络连接失败，请重试', 500)
		}

		try {
			const records = this.dataManager.getItem('health_records') || []
			const recordIndex = records.findIndex(record => record.id === recordId)

			if (recordIndex === -1) {
				return this.createResponse(false, null, '记录不存在', 404)
			}

			records.splice(recordIndex, 1)
			this.dataManager.setItem('health_records', records)

			return this.createResponse(true, null, '删除成功')
		} catch (error) {
			console.error('删除健康记录失败:', error)
			return this.createResponse(false, null, '删除健康记录失败', 500)
		}
	}

	/**
	 * 用药提醒 CRUD
	 */
	async getMedications(params = {}) {
		await this.dataManager.simulateNetworkDelay()

		if (this.dataManager.simulateNetworkError()) {
			return this.createResponse(false, null, '网络连接失败，请重试', 500)
		}

		try {
			let medications = this.dataManager.getItem('medications') || []

			// 搜索
			if (params.keyword) {
				const keyword = params.keyword.toLowerCase()
				medications = medications.filter(med =>
					med.name.toLowerCase().includes(keyword)
				)
			}

			// 排序
			medications.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))

			return this.createResponse(true, medications, '获取成功')
		} catch (error) {
			console.error('获取用药提醒失败:', error)
			return this.createResponse(false, null, '获取用药提醒失败', 500)
		}
	}

	async createMedication(medicationData) {
		await this.dataManager.simulateNetworkDelay()

		if (this.dataManager.simulateNetworkError()) {
			return this.createResponse(false, null, '网络连接失败，请重试', 500)
		}

		try {
			const medications = this.dataManager.getItem('medications') || []
			const now = new Date()

			const newMedication = {
				id: this.dataManager.generateId(),
				...medicationData,
				createdAt: this.dataManager.formatDateTime(now)
			}

			medications.unshift(newMedication)
			this.dataManager.setItem('medications', medications)

			return this.createResponse(true, newMedication, '添加成功')
		} catch (error) {
			console.error('创建用药提醒失败:', error)
			return this.createResponse(false, null, '创建用药提醒失败', 500)
		}
	}

	async updateMedication(medicationId, medicationData) {
		await this.dataManager.simulateNetworkDelay()

		if (this.dataManager.simulateNetworkError()) {
			return this.createResponse(false, null, '网络连接失败，请重试', 500)
		}

		try {
			const medications = this.dataManager.getItem('medications') || []
			const medicationIndex = medications.findIndex(med => med.id === medicationId)

			if (medicationIndex === -1) {
				return this.createResponse(false, null, '用药提醒不存在', 404)
			}

			const updatedMedication = {
				...medications[medicationIndex],
				...medicationData,
				updatedAt: this.dataManager.formatDateTime(new Date())
			}

			medications[medicationIndex] = updatedMedication
			this.dataManager.setItem('medications', medications)

			return this.createResponse(true, updatedMedication, '更新成功')
		} catch (error) {
			console.error('更新用药提醒失败:', error)
			return this.createResponse(false, null, '更新用药提醒失败', 500)
		}
	}

	async deleteMedication(medicationId) {
		await this.dataManager.simulateNetworkDelay()

		if (this.dataManager.simulateNetworkError()) {
			return this.createResponse(false, null, '网络连接失败，请重试', 500)
		}

		try {
			const medications = this.dataManager.getItem('medications') || []
			const medicationIndex = medications.findIndex(med => med.id === medicationId)

			if (medicationIndex === -1) {
				return this.createResponse(false, null, '用药提醒不存在', 404)
			}

			medications.splice(medicationIndex, 1)
			this.dataManager.setItem('medications', medications)

			return this.createResponse(true, null, '删除成功')
		} catch (error) {
			console.error('删除用药提醒失败:', error)
			return this.createResponse(false, null, '删除用药提醒失败', 500)
		}
	}

	/**
	 * 消息通知 CRUD
	 */
	async getMessages(params = {}) {
		await this.dataManager.simulateNetworkDelay()

		if (this.dataManager.simulateNetworkError()) {
			return this.createResponse(false, null, '网络连接失败，请重试', 500)
		}

		try {
			let messages = this.dataManager.getItem('messages') || []

			// 筛选
			if (params.type) {
				messages = messages.filter(msg => msg.type === params.type)
			}
			if (params.isRead !== undefined) {
				messages = messages.filter(msg => msg.isRead === params.isRead)
			}

			// 排序
			messages.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))

			// 分页
			const page = parseInt(params.page) || 1
			const pageSize = parseInt(params.pageSize) || 10
			const start = (page - 1) * pageSize
			const end = start + pageSize
			const paginatedMessages = messages.slice(start, end)

			return this.createResponse(true, {
				list: paginatedMessages,
				total: messages.length,
				page,
				pageSize,
				hasMore: end < messages.length
			}, '获取成功')
		} catch (error) {
			console.error('获取消息失败:', error)
			return this.createResponse(false, null, '获取消息失败', 500)
		}
	}

	async markMessageAsRead(messageId) {
		await this.dataManager.simulateNetworkDelay()

		if (this.dataManager.simulateNetworkError()) {
			return this.createResponse(false, null, '网络连接失败，请重试', 500)
		}

		try {
			const messages = this.dataManager.getItem('messages') || []
			const messageIndex = messages.findIndex(msg => msg.id === messageId)

			if (messageIndex === -1) {
				return this.createResponse(false, null, '消息不存在', 404)
			}

			messages[messageIndex].isRead = true
			this.dataManager.setItem('messages', messages)

			return this.createResponse(true, messages[messageIndex], '标记成功')
		} catch (error) {
			console.error('标记消息失败:', error)
			return this.createResponse(false, null, '标记消息失败', 500)
		}
	}

	async markAllMessagesAsRead() {
		await this.dataManager.simulateNetworkDelay()

		if (this.dataManager.simulateNetworkError()) {
			return this.createResponse(false, null, '网络连接失败，请重试', 500)
		}

		try {
			const messages = this.dataManager.getItem('messages') || []
			messages.forEach(msg => msg.isRead = true)
			this.dataManager.setItem('messages', messages)

			return this.createResponse(true, null, '全部标记成功')
		} catch (error) {
			console.error('标记所有消息失败:', error)
			return this.createResponse(false, null, '标记所有消息失败', 500)
		}
	}

	async deleteMessage(messageId) {
		await this.dataManager.simulateNetworkDelay()

		if (this.dataManager.simulateNetworkError()) {
			return this.createResponse(false, null, '网络连接失败，请重试', 500)
		}

		try {
			const messages = this.dataManager.getItem('messages') || []
			const messageIndex = messages.findIndex(msg => msg.id === messageId)

			if (messageIndex === -1) {
				return this.createResponse(false, null, '消息不存在', 404)
			}

			messages.splice(messageIndex, 1)
			this.dataManager.setItem('messages', messages)

			return this.createResponse(true, null, '删除成功')
		} catch (error) {
			console.error('删除消息失败:', error)
			return this.createResponse(false, null, '删除消息失败', 500)
		}
	}

	async createMessage(messageData) {
		await this.dataManager.simulateNetworkDelay()

		if (this.dataManager.simulateNetworkError()) {
			return this.createResponse(false, null, '网络连接失败，请重试', 500)
		}

		try {
			const messages = this.dataManager.getItem('messages') || []
			const now = new Date()

			const newMessage = {
				id: this.dataManager.generateId(),
				...messageData,
				isRead: false,
				createdAt: this.dataManager.formatDateTime(now),
				time: this.dataManager.formatDateTime(now)
			}

			messages.unshift(newMessage)
			this.dataManager.setItem('messages', messages)

			return this.createResponse(true, newMessage, '消息发送成功')
		} catch (error) {
			console.error('创建消息失败:', error)
			return this.createResponse(false, null, '消息发送失败', 500)
		}
	}

	/**
	 * 收藏管理 CRUD
	 */
	async getFavorites(params = {}) {
		await this.dataManager.simulateNetworkDelay()

		if (this.dataManager.simulateNetworkError()) {
			return this.createResponse(false, null, '网络连接失败，请重试', 500)
		}

		try {
			let favorites = this.dataManager.getItem('favorites') || []

			// 筛选
			if (params.type) {
				favorites = favorites.filter(fav => fav.type === params.type)
			}

			// 排序
			favorites.sort((a, b) => new Date(b.favoriteTime) - new Date(a.favoriteTime))

			// 分页
			const page = parseInt(params.page) || 1
			const pageSize = parseInt(params.pageSize) || 20
			const start = (page - 1) * pageSize
			const end = start + pageSize
			const paginatedFavorites = favorites.slice(start, end)

			return this.createResponse(true, {
				list: paginatedFavorites,
				total: favorites.length,
				page,
				pageSize,
				hasMore: end < favorites.length
			}, '获取成功')
		} catch (error) {
			console.error('获取收藏失败:', error)
			return this.createResponse(false, null, '获取收藏失败', 500)
		}
	}

	async createFavorite(favoriteData) {
		await this.dataManager.simulateNetworkDelay()

		if (this.dataManager.simulateNetworkError()) {
			return this.createResponse(false, null, '网络连接失败，请重试', 500)
		}

		try {
			const favorites = this.dataManager.getItem('favorites') || []
			const now = new Date()

			// 检查是否已收藏
			const existingFavorite = favorites.find(fav =>
				fav.targetId === favoriteData.targetId && fav.type === favoriteData.type
			)

			if (existingFavorite) {
				return this.createResponse(false, null, '已经收藏过了', 400)
			}

			const newFavorite = {
				id: this.dataManager.generateId(),
				...favoriteData,
				favoriteTime: this.dataManager.formatDateTime(now),
				createdAt: this.dataManager.formatDateTime(now)
			}

			favorites.unshift(newFavorite)
			this.dataManager.setItem('favorites', favorites)

			return this.createResponse(true, newFavorite, '收藏成功')
		} catch (error) {
			console.error('创建收藏失败:', error)
			return this.createResponse(false, null, '收藏失败', 500)
		}
	}

	async deleteFavorite(favoriteId) {
		await this.dataManager.simulateNetworkDelay()

		if (this.dataManager.simulateNetworkError()) {
			return this.createResponse(false, null, '网络连接失败，请重试', 500)
		}

		try {
			const favorites = this.dataManager.getItem('favorites') || []
			const favoriteIndex = favorites.findIndex(fav => fav.id === favoriteId)

			if (favoriteIndex === -1) {
				return this.createResponse(false, null, '收藏不存在', 404)
			}

			favorites.splice(favoriteIndex, 1)
			this.dataManager.setItem('favorites', favorites)

			return this.createResponse(true, null, '取消收藏成功')
		} catch (error) {
			console.error('删除收藏失败:', error)
			return this.createResponse(false, null, '取消收藏失败', 500)
		}
	}

	async deleteFavoriteByTarget(targetId, type) {
		await this.dataManager.simulateNetworkDelay()

		if (this.dataManager.simulateNetworkError()) {
			return this.createResponse(false, null, '网络连接失败，请重试', 500)
		}

		try {
			const favorites = this.dataManager.getItem('favorites') || []
			const favoriteIndex = favorites.findIndex(fav =>
				fav.targetId === targetId && fav.type === type
			)

			if (favoriteIndex === -1) {
				return this.createResponse(false, null, '收藏不存在', 404)
			}

			favorites.splice(favoriteIndex, 1)
			this.dataManager.setItem('favorites', favorites)

			return this.createResponse(true, null, '取消收藏成功')
		} catch (error) {
			console.error('删除收藏失败:', error)
			return this.createResponse(false, null, '取消收藏失败', 500)
		}
	}

	async checkFavoriteStatus(targetId, type) {
		try {
			const favorites = this.dataManager.getItem('favorites') || []
			const isFavorited = favorites.some(fav =>
				fav.targetId === targetId && fav.type === type
			)

			return this.createResponse(true, { isFavorited }, '检查成功')
		} catch (error) {
			console.error('检查收藏状态失败:', error)
			return this.createResponse(false, null, '检查收藏状态失败', 500)
		}
	}

	/**
	 * 用户设置 CRUD
	 */
	async getUserSettings() {
		try {
			const settings = this.dataManager.getItem('user_settings') || this.dataManager.getDefaultSettings()
			return this.createResponse(true, settings, '获取设置成功')
		} catch (error) {
			console.error('获取用户设置失败:', error)
			return this.createResponse(false, null, '获取设置失败', 500)
		}
	}

	async updateUserSettings(settingsData) {
		await this.dataManager.simulateNetworkDelay()

		if (this.dataManager.simulateNetworkError()) {
			return this.createResponse(false, null, '网络连接失败，请重试', 500)
		}

		try {
			const currentSettings = this.dataManager.getItem('user_settings') || this.dataManager.getDefaultSettings()
			const updatedSettings = { ...currentSettings, ...settingsData }

			this.dataManager.setItem('user_settings', updatedSettings)

			return this.createResponse(true, updatedSettings, '设置保存成功')
		} catch (error) {
			console.error('更新用户设置失败:', error)
			return this.createResponse(false, null, '设置保存失败', 500)
		}
	}

	async resetUserSettings() {
		await this.dataManager.simulateNetworkDelay()

		if (this.dataManager.simulateNetworkError()) {
			return this.createResponse(false, null, '网络连接失败，请重试', 500)
		}

		try {
			const defaultSettings = this.dataManager.getDefaultSettings()
			this.dataManager.setItem('user_settings', defaultSettings)

			return this.createResponse(true, defaultSettings, '设置已重置为默认值')
		} catch (error) {
			console.error('重置用户设置失败:', error)
			return this.createResponse(false, null, '重置设置失败', 500)
		}
	}

	async deleteUserSettings() {
		await this.dataManager.simulateNetworkDelay()

		if (this.dataManager.simulateNetworkError()) {
			return this.createResponse(false, null, '网络连接失败，请重试', 500)
		}

		try {
			this.dataManager.removeItem('user_settings')
			return this.createResponse(true, null, '设置已清除')
		} catch (error) {
			console.error('删除用户设置失败:', error)
			return this.createResponse(false, null, '清除设置失败', 500)
		}
	}
}

// 创建全局实例
const crudAPI = new CrudAPI()

export default crudAPI
