<template>
	<view class="container">
		<!-- 自定义导航栏 -->
		<view class="custom-navbar">
			<view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
			<view class="navbar-content">
				<view class="navbar-left" @click="goBack">
					<Icon name="arrow-left-line" size="36rpx" color="#333"></Icon>
					<text class="back-text">返回</text>
				</view>
				<view class="navbar-center">
					<text class="navbar-title">提现</text>
				</view>
				<view class="navbar-right"></view>
			</view>
		</view>

		<!-- 账户余额显示 -->
		<view class="balance-section">
			<view class="balance-info">
				<text class="balance-label">可提现余额</text>
				<text class="balance-amount">¥1,258.50</text>
			</view>
			<view class="balance-tips">
				<Icon name="information-line" size="20rpx" color="#666"></Icon>
				<text class="tips-text">提现金额不能超过可用余额</text>
			</view>
		</view>

		<!-- 提现金额输入 -->
		<view class="amount-section">
			<view class="section-title">提现金额</view>
			<view class="amount-input-container">
				<text class="currency-symbol">¥</text>
				<input class="amount-input" 
					type="digit" 
					placeholder="请输入提现金额" 
					v-model="withdrawAmount"
					@input="onAmountInput" />
			</view>
			<view class="quick-amounts">
				<button class="quick-btn" @click="setQuickAmount(100)">100元</button>
				<button class="quick-btn" @click="setQuickAmount(500)">500元</button>
				<button class="quick-btn" @click="setAllAmount">全部</button>
			</view>
		</view>

		<!-- 提现银行卡选择 -->
		<view class="card-section">
			<view class="section-title">选择提现银行卡</view>
			<view class="card-list">
				<view class="bank-card" 
					:class="{ active: selectedCard === card.id }"
					v-for="card in bankCards" 
					:key="card.id"
					@click="selectCard(card.id)">
					<view class="card-info">
						<text class="bank-name">{{card.bankName}}</text>
						<text class="card-number">****  ****  ****  {{card.lastFour}}</text>
						<view class="card-tags">
							<text class="card-tag default" v-if="card.isDefault">默认</text>
							<text class="card-tag type">{{card.type}}</text>
						</view>
					</view>
					<view class="card-radio">
						<Icon :name="selectedCard === card.id ? 'radio-button-line' : 'checkbox-blank-circle-line'" 
							size="24rpx" 
							:color="selectedCard === card.id ? '#ff8a00' : '#ccc'"></Icon>
					</view>
				</view>
			</view>
			<button class="add-card-btn" @click="addBankCard">
				<Icon name="add-line" size="24rpx" color="#ff8a00"></Icon>
				<text>添加银行卡</text>
			</button>
		</view>

		<!-- 提现说明 -->
		<view class="notice-section">
			<view class="notice-title">
				<Icon name="information-line" size="24rpx" color="#ff8a00"></Icon>
				<text>提现说明</text>
			</view>
			<view class="notice-content">
				<text class="notice-item">• 提现申请提交后，资金将在1-3个工作日内到账</text>
				<text class="notice-item">• 单次提现金额不得少于10元，不得超过5000元</text>
				<text class="notice-item">• 提现手续费：每笔2元（100元以下免费）</text>
				<text class="notice-item">• 工作日16:00前申请，当日处理</text>
			</view>
		</view>

		<!-- 提现按钮 */
		<view class="action-section">
			<view class="fee-info" v-if="withdrawFee > 0">
				<text class="fee-label">手续费：</text>
				<text class="fee-amount">¥{{withdrawFee}}</text>
			</view>
			<view class="amount-summary">
				<text class="summary-label">实际到账：</text>
				<text class="summary-amount">¥{{actualAmount}}</text>
			</view>
			<button class="withdraw-btn" 
				:disabled="!canWithdraw" 
				@click="confirmWithdraw">
				申请提现
			</button>
		</view>

		<!-- 功能开发中提示 -->
		<view class="dev-notice">
			<Icon name="tools-line" size="32rpx" color="#ff8a00"></Icon>
			<text class="dev-text">此功能正在开发中，敬请期待</text>
		</view>
	</view>
</template>

<script>
import Icon from '@/components/Icon/Icon.vue'

export default {
	components: {
		Icon
	},
	data() {
		return {
			statusBarHeight: 0,
			withdrawAmount: '',
			selectedCard: 1,
			availableBalance: 1258.50,
			bankCards: [
				{
					id: 1,
					bankName: '中国工商银行',
					lastFour: '6688',
					type: '储蓄卡',
					isDefault: true
				},
				{
					id: 2,
					bankName: '中国建设银行',
					lastFour: '8899',
					type: '信用卡',
					isDefault: false
				}
			]
		}
	},
	computed: {
		withdrawFee() {
			const amount = parseFloat(this.withdrawAmount) || 0;
			return amount >= 100 ? 2 : 0;
		},
		actualAmount() {
			const amount = parseFloat(this.withdrawAmount) || 0;
			return Math.max(0, amount - this.withdrawFee).toFixed(2);
		},
		canWithdraw() {
			const amount = parseFloat(this.withdrawAmount) || 0;
			return amount >= 10 && amount <= 5000 && amount <= this.availableBalance && this.selectedCard;
		}
	},
	onLoad() {
		const systemInfo = uni.getSystemInfoSync();
		this.statusBarHeight = systemInfo.statusBarHeight || 44;
	},
	methods: {
		onAmountInput() {
			// 限制输入格式
			this.withdrawAmount = this.withdrawAmount.replace(/[^\d.]/g, '');
		},
		setQuickAmount(amount) {
			this.withdrawAmount = amount.toString();
		},
		setAllAmount() {
			this.withdrawAmount = this.availableBalance.toString();
		},
		selectCard(cardId) {
			this.selectedCard = cardId;
		},
		addBankCard() {
			uni.navigateTo({
				url: '/pages/wallet/add-card'
			});
		},
		confirmWithdraw() {
			if (!this.canWithdraw) return;
			
			const selectedCardInfo = this.bankCards.find(card => card.id === this.selectedCard);
			
			uni.showModal({
				title: '确认提现',
				content: `确定要提现 ¥${this.withdrawAmount} 到 ${selectedCardInfo.bankName}(${selectedCardInfo.lastFour}) 吗？`,
				success: (res) => {
					if (res.confirm) {
						this.processWithdraw();
					}
				}
			});
		},
		processWithdraw() {
			uni.showLoading({
				title: '正在处理...',
				mask: true
			});
			
			// 模拟提现流程
			setTimeout(() => {
				uni.hideLoading();
				uni.showToast({
					title: '功能开发中，敬请期待',
					icon: 'none',
					duration: 2000
				});
			}, 2000);
		},
		goBack() {
			uni.navigateBack({
				fail: () => {
					uni.navigateTo({
						url: '/pages/wallet/wallet'
					});
				}
			});
		}
	}
}
</script>

<style scoped>
.container {
	background: #f8f9fa;
	min-height: 100vh;
}

/* 导航栏样式 */
.custom-navbar {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 1000;
	background: rgba(255, 255, 255, 0.95);
	backdrop-filter: blur(20rpx);
	border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);
}

.navbar-content {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 20rpx 32rpx;
	min-height: 88rpx;
}

.navbar-left {
	display: flex;
	align-items: center;
	gap: 12rpx;
	padding: 8rpx 16rpx 8rpx 0;
	border-radius: 20rpx;
	transition: all 0.2s ease;
}

.navbar-left:active {
	background-color: rgba(0, 0, 0, 0.04);
	transform: scale(0.96);
}

.back-text {
	font-size: 32rpx;
	color: #333;
	font-weight: 500;
}

.navbar-center {
	position: absolute;
	left: 50%;
	transform: translateX(-50%);
}

.navbar-title {
	font-size: 34rpx;
	font-weight: 600;
	color: #333;
}

.navbar-right {
	display: flex;
	align-items: center;
}

/* 余额显示 */
.balance-section {
	background: linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%);
	margin: 20rpx;
	margin-top: 220rpx;
	border-radius: 30rpx;
	padding: 40rpx;
	color: white;
}

.balance-info {
	text-align: center;
	margin-bottom: 20rpx;
}

.balance-label {
	font-size: 26rpx;
	opacity: 0.9;
	display: block;
	margin-bottom: 15rpx;
}

.balance-amount {
	font-size: 48rpx;
	font-weight: bold;
	display: block;
}

.balance-tips {
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 8rpx;
	opacity: 0.8;
}

.tips-text {
	font-size: 22rpx;
}

/* 内容区域 */
.amount-section, .card-section, .notice-section {
	background: white;
	margin: 20rpx;
	border-radius: 30rpx;
	padding: 40rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 30rpx;
}

.amount-input-container {
	display: flex;
	align-items: center;
	background: #f8f9fa;
	border-radius: 20rpx;
	padding: 25rpx;
	border: 2rpx solid transparent;
	margin-bottom: 20rpx;
}

.amount-input-container:focus-within {
	border-color: #ff8a00;
	background: white;
}

.currency-symbol {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-right: 10rpx;
}

.amount-input {
	flex: 1;
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	border: none;
	background: transparent;
}

.quick-amounts {
	display: flex;
	gap: 15rpx;
}

.quick-btn {
	padding: 15rpx 25rpx;
	background: rgba(255, 138, 0, 0.1);
	color: #ff8a00;
	border: 1rpx solid #ff8a00;
	border-radius: 15rpx;
	font-size: 24rpx;
}

.quick-btn:active {
	background: #ff8a00;
	color: white;
}

.card-list {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
	margin-bottom: 30rpx;
}

.bank-card {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 25rpx;
	background: #f8f9fa;
	border-radius: 20rpx;
	border: 2rpx solid transparent;
	transition: all 0.2s ease;
}

.bank-card.active {
	background: rgba(255, 138, 0, 0.1);
	border-color: #ff8a00;
}

.card-info {
	flex: 1;
}

.bank-name {
	font-size: 28rpx;
	color: #333;
	font-weight: bold;
	display: block;
	margin-bottom: 8rpx;
}

.card-number {
	font-size: 24rpx;
	color: #666;
	display: block;
	margin-bottom: 10rpx;
}

.card-tags {
	display: flex;
	gap: 10rpx;
}

.card-tag {
	font-size: 20rpx;
	padding: 4rpx 10rpx;
	border-radius: 10rpx;
}

.card-tag.default {
	background: rgba(255, 138, 0, 0.1);
	color: #ff8a00;
}

.card-tag.type {
	background: #f0f0f0;
	color: #666;
}

.add-card-btn {
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 10rpx;
	width: 100%;
	padding: 25rpx;
	background: rgba(255, 138, 0, 0.1);
	color: #ff8a00;
	border: 2rpx dashed #ff8a00;
	border-radius: 20rpx;
	font-size: 26rpx;
}

.notice-title {
	display: flex;
	align-items: center;
	gap: 10rpx;
	font-size: 28rpx;
	font-weight: bold;
	color: #ff8a00;
	margin-bottom: 20rpx;
}

.notice-content {
	display: flex;
	flex-direction: column;
	gap: 15rpx;
}

.notice-item {
	font-size: 26rpx;
	color: #666;
	line-height: 1.5;
}

.action-section {
	padding: 40rpx;
}

.fee-info, .amount-summary {
	text-align: center;
	margin-bottom: 20rpx;
}

.fee-label, .summary-label {
	font-size: 26rpx;
	color: #666;
}

.fee-amount {
	font-size: 28rpx;
	color: #f44336;
	font-weight: bold;
}

.summary-amount {
	font-size: 36rpx;
	font-weight: bold;
	color: #4caf50;
}

.withdraw-btn {
	width: 100%;
	height: 100rpx;
	background: #ff8a00;
	color: white;
	border: none;
	border-radius: 25rpx;
	font-size: 32rpx;
	font-weight: bold;
}

.withdraw-btn:disabled {
	background: #ccc;
	color: #999;
}

.dev-notice {
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 15rpx;
	padding: 30rpx;
	margin: 20rpx;
	background: rgba(255, 138, 0, 0.1);
	border-radius: 20rpx;
}

.dev-text {
	font-size: 26rpx;
	color: #ff8a00;
}
</style>
