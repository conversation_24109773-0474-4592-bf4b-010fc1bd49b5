<template>
	<view 
		class="interactive-card" 
		:class="[
			{ 'card-loading': loading },
			{ 'card-disabled': disabled },
			{ 'card-elevated': elevated },
			{ 'elderly-mode': elderlyMode },
			customClass
		]"
		:style="cardStyle"
		@click="handleClick"
		@touchstart="handleTouchStart"
		@touchend="handleTouchEnd"
	>
		<!-- 加载状态遮罩 -->
		<view v-if="loading" class="loading-overlay">
			<view class="loading-spinner"></view>
		</view>
		
		<!-- 卡片内容 -->
		<slot></slot>
		
		<!-- 右侧箭头指示器 -->
		<view v-if="showArrow" class="arrow-indicator">
			<Icon name="arrow-right-line" size="24rpx" color="#ccc" />
		</view>
	</view>
</template>

<script>
import Icon from '@/components/Icon/Icon.vue'

export default {
	name: 'InteractiveCard',
	components: {
		Icon
	},
	props: {
		loading: {
			type: Boolean,
			default: false
		},
		disabled: {
			type: Boolean,
			default: false
		},
		elevated: {
			type: Boolean,
			default: true
		},
		elderlyMode: {
			type: Boolean,
			default: false
		},
		showArrow: {
			type: Boolean,
			default: false
		},
		borderRadius: {
			type: [String, Number],
			default: 12
		},
		padding: {
			type: [String, Number],
			default: 20
		},
		margin: {
			type: [String, Number],
			default: 0
		},
		backgroundColor: {
			type: String,
			default: '#ffffff'
		},
		customClass: {
			type: String,
			default: ''
		}
	},
	data() {
		return {
			isPressed: false
		}
	},
	computed: {
		cardStyle() {
			const style = {
				backgroundColor: this.backgroundColor
			}
			
			if (this.borderRadius) {
				const radius = typeof this.borderRadius === 'number' 
					? this.borderRadius + 'rpx' 
					: this.borderRadius
				style.borderRadius = radius
			}
			
			if (this.padding) {
				const padding = typeof this.padding === 'number' 
					? this.padding + 'rpx' 
					: this.padding
				style.padding = padding
			}
			
			if (this.margin) {
				const margin = typeof this.margin === 'number' 
					? this.margin + 'rpx' 
					: this.margin
				style.margin = margin
			}
			
			return style
		}
	},
	methods: {
		handleClick(e) {
			if (this.disabled || this.loading) {
				return
			}
			
			// 添加点击反馈
			this.addClickFeedback()
			
			this.$emit('click', e)
		},
		handleTouchStart() {
			if (this.disabled || this.loading) {
				return
			}
			this.isPressed = true
		},
		handleTouchEnd() {
			this.isPressed = false
		},
		addClickFeedback() {
			// 添加轻微的震动反馈（如果设备支持）
			try {
				uni.vibrateShort({
					type: 'light'
				})
			} catch (e) {
				// 忽略不支持震动的设备
			}
		}
	}
}
</script>

<style scoped>
/* ================================
   iOS风格InteractiveCard组件
   基于iOS Human Interface Guidelines
   ================================ */

.interactive-card {
	position: relative;
	background: #ffffff;
	border-radius: 16rpx; /* iOS标准卡片圆角 */
	transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94); /* iOS标准缓动 */
	overflow: hidden;
	-webkit-tap-highlight-color: transparent; /* 移除iOS点击高亮 */
	user-select: none;
	-webkit-user-select: none;
}

/* iOS风格交互效果 */
.interactive-card:not(.card-disabled):not(.card-loading):active {
	transform: scale(0.98); /* iOS标准按压缩放 */
	transition: transform 0.1s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.interactive-card:not(.card-disabled):not(.card-loading):not(:active) {
	transform: scale(1);
	transition: transform 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* iOS风格阴影系统 */
.card-elevated {
	box-shadow:
		0 2rpx 8rpx rgba(0, 0, 0, 0.04),
		0 4rpx 24rpx rgba(0, 0, 0, 0.06); /* iOS分层阴影 */
}

/* iOS风格悬停效果（仅在支持hover的设备上） */
@media (hover: hover) {
	.card-elevated:not(.card-disabled):not(.card-loading):hover {
		box-shadow:
			0 4rpx 16rpx rgba(0, 0, 0, 0.08),
			0 8rpx 32rpx rgba(0, 0, 0, 0.12); /* 悬停时的增强阴影 */
		transform: translateY(-2rpx);
		transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
	}
}

/* iOS风格状态样式 */
.card-loading {
	pointer-events: none;
	opacity: 0.6; /* iOS风格的加载透明度 */
}

.card-disabled {
	pointer-events: none;
	opacity: 0.4; /* iOS风格的禁用透明度 */
	background: #F3F4F6; /* iOS风格的禁用背景 */
}

/* iOS风格加载遮罩 */
.loading-overlay {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(255, 255, 255, 0.9); /* iOS风格的遮罩透明度 */
	backdrop-filter: blur(10rpx); /* iOS毛玻璃效果 */
	-webkit-backdrop-filter: blur(10rpx);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 10;
	border-radius: 16rpx; /* 与卡片圆角保持一致 */
}

/* iOS风格加载动画 */
.loading-spinner {
	width: 48rpx; /* 更大的加载指示器 */
	height: 48rpx;
	border: 4rpx solid rgba(255, 138, 0, 0.2); /* iOS风格的边框 */
	border-top: 4rpx solid #ff8a00;
	border-radius: 50%;
	animation: iosCardSpinning 1s linear infinite;
}

/* iOS风格箭头指示器 */
.arrow-indicator {
	position: absolute;
	right: 24rpx; /* iOS标准间距 */
	top: 50%;
	transform: translateY(-50%);
	z-index: 5;
	opacity: 0.6; /* iOS风格的次要元素透明度 */
	transition: opacity 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.interactive-card:hover .arrow-indicator {
	opacity: 0.8; /* 悬停时增加透明度 */
}

/* iOS风格适老化样式 */
.elderly-mode {
	border-radius: 20rpx !important; /* 更大的圆角 */
	padding: calc(32rpx * 1.5) !important; /* 放大内边距 */
	min-height: 112rpx !important; /* 更大的触摸目标 */
	border: 2rpx solid #b0b0b0 !important; /* 高对比度边框 */
}

.elderly-mode.card-elevated {
	box-shadow:
		0 4rpx 16rpx rgba(0, 0, 0, 0.08),
		0 8rpx 32rpx rgba(0, 0, 0, 0.12) !important; /* 更明显的阴影 */
}

.elderly-mode .arrow-indicator {
	right: calc(24rpx * 1.5) !important; /* 适老化间距 */
	opacity: 0.8 !important; /* 更明显的箭头 */
}

/* 适老化按压效果增强 */
.elderly-mode:not(.card-disabled):not(.card-loading):active {
	transform: scale(0.96) !important; /* 更明显的按压效果 */
	box-shadow:
		0 2rpx 8rpx rgba(255, 138, 0, 0.2),
		0 4rpx 16rpx rgba(255, 138, 0, 0.1) !important;
}

/* iOS风格特殊状态样式 */
.interactive-card.success {
	border-left: 8rpx solid #34C759; /* iOS绿色，更粗的边框 */
	background: rgba(52, 199, 89, 0.02); /* 淡色背景 */
}

.interactive-card.warning {
	border-left: 8rpx solid #FF9500; /* iOS橙色 */
	background: rgba(255, 149, 0, 0.02);
}

.interactive-card.error {
	border-left: 8rpx solid #FF3B30; /* iOS红色 */
	background: rgba(255, 59, 48, 0.02);
}

.interactive-card.info {
	border-left: 8rpx solid #007AFF; /* iOS蓝色 */
	background: rgba(0, 122, 255, 0.02);
}

/* iOS风格动画效果 */
@keyframes iosCardSpinning {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

@keyframes iosCardFadeIn {
	from {
		opacity: 0;
		transform: translateY(16rpx); /* iOS标准位移 */
	}
	to {
		opacity: 1;
		transform: translateY(0);
	}
}

/* 卡片入场动画 */
.interactive-card {
	animation: iosCardFadeIn 0.35s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* iOS风格响应式设计 */
@media (max-width: 375px) {
	/* iPhone SE */
	.interactive-card {
		border-radius: 12rpx;
	}

	.elderly-mode {
		border-radius: 16rpx !important;
		padding: calc(24rpx * 1.5) !important;
	}

	.arrow-indicator {
		right: 16rpx;
	}
}

@media (min-width: 376px) and (max-width: 414px) {
	/* 标准iPhone */
	.interactive-card {
		border-radius: 14rpx;
	}

	.elderly-mode {
		border-radius: 18rpx !important;
	}
}

@media (min-width: 768px) {
	/* iPad */
	.interactive-card {
		border-radius: 20rpx;
	}

	.elderly-mode {
		border-radius: 24rpx !important;
		padding: calc(40rpx * 1.5) !important;
	}

	.arrow-indicator {
		right: 32rpx;
	}

	/* iPad悬停效果增强 */
	.card-elevated:hover {
		transform: translateY(-4rpx);
	}
}

/* 兼容原有响应式断点 */
@media (max-width: 750rpx) {
	.interactive-card {
		border-radius: 12rpx;
	}

	.elderly-mode {
		border-radius: 16rpx !important;
	}
}
</style>
