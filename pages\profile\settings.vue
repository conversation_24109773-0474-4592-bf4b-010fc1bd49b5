<template>
	<view class="container" :class="{ 'elderly-mode': isElderlyMode }">
		<!-- 自定义导航栏 -->
		<view class="custom-navbar" :class="{ 'elderly-mode': isElderlyMode }">
			<view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
			<view class="navbar-content">
				<view class="navbar-left" @click="goBack">
					<Icon
						name="arrow-left-line"
						:size="isElderlyMode ? '40rpx' : '36rpx'"
						color="#333"
					></Icon>
					<text class="back-text">返回</text>
				</view>
				<view class="navbar-center">
					<text class="navbar-title">设置</text>
				</view>
				<view class="navbar-right"></view>
			</view>
		</view>

		<!-- 通知设置 -->
		<view class="settings-section">
			<view class="section-header">
				<Icon name="notification-3-line" size="32rpx" color="#ff8a00"></Icon>
				<text class="section-title">通知设置</text>
			</view>
			<view class="settings-list">
				<view class="setting-item">
					<view class="setting-info">
						<text class="setting-title">推送通知</text>
						<text class="setting-desc">接收系统推送的重要消息</text>
					</view>
					<switch :checked="settings.pushNotification" @change="updateSetting('pushNotification', $event)" color="#ff8a00"></switch>
				</view>
				<view class="setting-item">
					<view class="setting-info">
						<text class="setting-title">健康提醒</text>
						<text class="setting-desc">用药、体检等健康相关提醒</text>
					</view>
					<switch :checked="settings.healthReminder" @change="updateSetting('healthReminder', $event)" color="#ff8a00"></switch>
				</view>
				<view class="setting-item">
					<view class="setting-info">
						<text class="setting-title">活动通知</text>
						<text class="setting-desc">文娱活动和社交活动通知</text>
					</view>
					<switch :checked="settings.activityNotification" @change="updateSetting('activityNotification', $event)" color="#ff8a00"></switch>
				</view>
				<view class="setting-item">
					<view class="setting-info">
						<text class="setting-title">声音提醒</text>
						<text class="setting-desc">通知时播放提示音</text>
					</view>
					<switch :checked="settings.soundAlert" @change="updateSetting('soundAlert', $event)" color="#ff8a00"></switch>
				</view>
			</view>
		</view>

		<!-- 显示设置 -->
		<view class="settings-section">
			<view class="section-header">
				<Icon name="eye-line" size="32rpx" color="#ff8a00"></Icon>
				<text class="section-title">显示设置</text>
			</view>
			<view class="settings-list">
				<view class="setting-item" @click="showFontSizeSelector">
					<view class="setting-info">
						<text class="setting-title">字体大小</text>
						<text class="setting-desc">调整应用内字体大小</text>
					</view>
					<view class="setting-value">
						<text class="value-text">{{getFontSizeText(settings.fontSize)}}</text>
						<Icon name="arrow-right-s-line" size="20rpx" color="#999"></Icon>
					</view>
				</view>
				<view class="setting-item">
					<view class="setting-info">
						<text class="setting-title">夜间模式</text>
						<text class="setting-desc">深色主题，保护视力</text>
					</view>
					<switch :checked="settings.darkMode" @change="updateSetting('darkMode', $event)" color="#ff8a00"></switch>
				</view>
				<view class="setting-item">
					<view class="setting-info">
						<text class="setting-title">简化界面</text>
						<text class="setting-desc">隐藏复杂功能，简化操作</text>
					</view>
					<switch :checked="settings.simpleMode" @change="updateSetting('simpleMode', $event)" color="#ff8a00"></switch>
				</view>
			</view>
		</view>

		<!-- 隐私设置 -->
		<view class="settings-section">
			<view class="section-header">
				<Icon name="lock-password-line" size="32rpx" color="#ff8a00"></Icon>
				<text class="section-title">隐私设置</text>
			</view>
			<view class="settings-list">
				<view class="setting-item" @click="navigateTo('/pages/profile/password')">
					<view class="setting-info">
						<text class="setting-title">修改密码</text>
						<text class="setting-desc">更改登录密码</text>
					</view>
					<Icon name="arrow-right-s-line" size="20rpx" color="#999"></Icon>
				</view>
				<view class="setting-item">
					<view class="setting-info">
						<text class="setting-title">生物识别</text>
						<text class="setting-desc">指纹或面部识别登录</text>
					</view>
					<switch :checked="settings.biometricAuth" @change="updateSetting('biometricAuth', $event)" color="#ff8a00"></switch>
				</view>
				<view class="setting-item">
					<view class="setting-info">
						<text class="setting-title">数据同步</text>
						<text class="setting-desc">自动同步健康数据到云端</text>
					</view>
					<switch :checked="settings.dataSync" @change="updateSetting('dataSync', $event)" color="#ff8a00"></switch>
				</view>
				<view class="setting-item" @click="navigateTo('/pages/profile/privacy')">
					<view class="setting-info">
						<text class="setting-title">隐私政策</text>
						<text class="setting-desc">查看隐私保护政策</text>
					</view>
					<Icon name="arrow-right-s-line" size="20rpx" color="#999"></Icon>
				</view>
			</view>
		</view>

		<!-- 系统设置 -->
		<view class="settings-section">
			<view class="section-header">
				<Icon name="settings-line" size="32rpx" color="#ff8a00"></Icon>
				<text class="section-title">系统设置</text>
			</view>
			<view class="settings-list">
				<view class="setting-item" @click="showLanguageSelector">
					<view class="setting-info">
						<text class="setting-title">语言设置</text>
						<text class="setting-desc">选择应用显示语言</text>
					</view>
					<view class="setting-value">
						<text class="value-text">{{getLanguageText(settings.language)}}</text>
						<Icon name="arrow-right-s-line" size="20rpx" color="#999"></Icon>
					</view>
				</view>
				<view class="setting-item">
					<view class="setting-info">
						<text class="setting-title">自动更新</text>
						<text class="setting-desc">WiFi环境下自动更新应用</text>
					</view>
					<switch :checked="settings.autoUpdate" @change="updateSetting('autoUpdate', $event)" color="#ff8a00"></switch>
				</view>
				<view class="setting-item" @click="clearCache">
					<view class="setting-info">
						<text class="setting-title">清理缓存</text>
						<text class="setting-desc">清理应用缓存数据</text>
					</view>
					<view class="setting-value">
						<text class="value-text">{{cacheSize}}</text>
						<Icon name="arrow-right-s-line" size="20rpx" color="#999"></Icon>
					</view>
				</view>
				<view class="setting-item" @click="checkUpdate">
					<view class="setting-info">
						<text class="setting-title">检查更新</text>
						<text class="setting-desc">检查应用是否有新版本</text>
					</view>
					<view class="setting-value">
						<text class="value-text">v1.0.0</text>
						<Icon name="arrow-right-s-line" size="20rpx" color="#999"></Icon>
					</view>
				</view>
			</view>
		</view>

		<!-- 帮助与反馈 -->
		<view class="settings-section">
			<view class="section-header">
				<Icon name="question-line" size="32rpx" color="#ff8a00"></Icon>
				<text class="section-title">帮助与反馈</text>
			</view>
			<view class="settings-list">
				<view class="setting-item" @click="navigateTo('/pages/help/faq')">
					<view class="setting-info">
						<text class="setting-title">常见问题</text>
						<text class="setting-desc">查看常见问题解答</text>
					</view>
					<Icon name="arrow-right-s-line" size="20rpx" color="#999"></Icon>
				</view>
				<view class="setting-item" @click="navigateTo('/pages/help/tutorial')">
					<view class="setting-info">
						<text class="setting-title">使用教程</text>
						<text class="setting-desc">学习如何使用应用功能</text>
					</view>
					<Icon name="arrow-right-s-line" size="20rpx" color="#999"></Icon>
				</view>
				<view class="setting-item" @click="contactSupport">
					<view class="setting-info">
						<text class="setting-title">联系客服</text>
						<text class="setting-desc">获取人工客服帮助</text>
					</view>
					<Icon name="arrow-right-s-line" size="20rpx" color="#999"></Icon>
				</view>
				<view class="setting-item" @click="navigateTo('/pages/profile/feedback')">
					<view class="setting-info">
						<text class="setting-title">意见反馈</text>
						<text class="setting-desc">提交使用建议和问题</text>
					</view>
					<Icon name="arrow-right-s-line" size="20rpx" color="#999"></Icon>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import Icon from '@/components/Icon/Icon.vue'
import { elderlyModeManager } from '@/utils/elderlyModeUtils.js'

export default {
	components: {
		Icon
	},
	data() {
		return {
			statusBarHeight: 0, // 状态栏高度
			isElderlyMode: false, // 适老化模式
			settings: {
				pushNotification: true,
				healthReminder: true,
				activityNotification: false,
				soundAlert: true,
				fontSize: 'medium',
				darkMode: false,
				simpleMode: false,
				biometricAuth: false,
				dataSync: true,
				language: 'zh-CN',
				autoUpdate: true
			},
			cacheSize: '12.5MB'
		}
	},
	onLoad() {
		// 获取状态栏高度
		const systemInfo = uni.getSystemInfoSync();
		this.statusBarHeight = systemInfo.statusBarHeight || 44;

		// 初始化适老化模式
		this.isElderlyMode = elderlyModeManager.getElderlyMode();
		elderlyModeManager.onElderlyModeChange((isEnabled) => {
			this.isElderlyMode = isEnabled;
		});

		this.loadSettings();
	},
	methods: {
		loadSettings() {
			// 从本地存储加载设置
			const savedSettings = uni.getStorageSync('appSettings');
			if (savedSettings) {
				this.settings = { ...this.settings, ...savedSettings };
			}
		},
		saveSettings() {
			// 保存设置到本地存储
			uni.setStorageSync('appSettings', this.settings);
		},
		updateSetting(key, event) {
			this.settings[key] = event.detail.value;
			this.saveSettings();
			
			// 应用设置变更
			this.applySettingChange(key, event.detail.value);
		},
		applySettingChange(key, value) {
			switch (key) {
				case 'darkMode':
					// 切换主题模式
					console.log('切换主题模式:', value);
					break;
				case 'fontSize':
					// 调整字体大小
					console.log('调整字体大小:', value);
					break;
				case 'simpleMode':
					// 切换简化模式
					console.log('切换简化模式:', value);
					break;
				default:
					console.log('更新设置:', key, value);
			}
		},
		getFontSizeText(size) {
			const sizeMap = {
				'small': '小',
				'medium': '中',
				'large': '大',
				'extra-large': '特大'
			};
			return sizeMap[size] || '中';
		},
		getLanguageText(lang) {
			const langMap = {
				'zh-CN': '简体中文',
				'zh-TW': '繁體中文',
				'en-US': 'English'
			};
			return langMap[lang] || '简体中文';
		},
		showFontSizeSelector() {
			const sizes = ['小', '中', '大', '特大'];
			const sizeKeys = ['small', 'medium', 'large', 'extra-large'];
			
			uni.showActionSheet({
				itemList: sizes,
				success: (res) => {
					this.settings.fontSize = sizeKeys[res.tapIndex];
					this.saveSettings();
					this.applySettingChange('fontSize', this.settings.fontSize);
				}
			});
		},
		showLanguageSelector() {
			const languages = ['简体中文', '繁體中文', 'English'];
			const langKeys = ['zh-CN', 'zh-TW', 'en-US'];
			
			uni.showActionSheet({
				itemList: languages,
				success: (res) => {
					this.settings.language = langKeys[res.tapIndex];
					this.saveSettings();
					uni.showToast({
						title: '语言设置已更新',
						icon: 'success'
					});
				}
			});
		},
		clearCache() {
			uni.showModal({
				title: '清理缓存',
				content: '确定要清理应用缓存吗？这将删除临时文件和图片缓存。',
				success: (res) => {
					if (res.confirm) {
						uni.showLoading({
							title: '清理中...'
						});
						
						// 模拟清理过程
						setTimeout(() => {
							uni.hideLoading();
							this.cacheSize = '0MB';
							uni.showToast({
								title: '缓存清理完成',
								icon: 'success'
							});
						}, 2000);
					}
				}
			});
		},
		checkUpdate() {
			uni.showLoading({
				title: '检查更新...'
			});
			
			// 模拟检查更新
			setTimeout(() => {
				uni.hideLoading();
				uni.showModal({
					title: '检查更新',
					content: '当前已是最新版本',
					showCancel: false
				});
			}, 2000);
		},
		contactSupport() {
			uni.showActionSheet({
				itemList: ['电话客服', '在线客服', '微信客服'],
				success: (res) => {
					switch (res.tapIndex) {
						case 0:
							uni.makePhoneCall({
								phoneNumber: '************'
							});
							break;
						case 1:
							uni.navigateTo({
								url: '/pages/help/online-service'
							});
							break;
						case 2:
							uni.showToast({
								title: '请添加微信：yanglaofuwu',
								icon: 'none',
								duration: 3000
							});
							break;
					}
				}
			});
		},
		navigateTo(url) {
			uni.navigateTo({
				url: url
			});
		},

		// 返回上一页
		goBack() {
			uni.navigateBack({
				fail: () => {
					uni.switchTab({
						url: '/pages/profile/profile'
					});
				}
			});
		}
	}
}
</script>

<style scoped>
.container {
	background: #f8f9fa;
	min-height: 100vh;
	padding: 40rpx 0;
}

/* 导航栏样式 */
.custom-navbar {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 1000;
	background: rgba(255, 255, 255, 0.95);
	backdrop-filter: blur(20rpx);
	-webkit-backdrop-filter: blur(20rpx);
	border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);
}

.navbar-content {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 20rpx 32rpx;
	min-height: 88rpx;
}

.navbar-left {
	display: flex;
	align-items: center;
	gap: 12rpx;
	padding: 8rpx 16rpx 8rpx 0;
	border-radius: 20rpx;
	transition: all 0.2s ease;
}

.navbar-left:active {
	background-color: rgba(0, 0, 0, 0.04);
	transform: scale(0.96);
}

.back-text {
	font-size: 32rpx;
	color: #333;
	font-weight: 500;
}

.navbar-center {
	position: absolute;
	left: 50%;
	transform: translateX(-50%);
}

.navbar-title {
	font-size: 34rpx;
	font-weight: 600;
	color: #333;
}

.navbar-right {
	display: flex;
	align-items: center;
}

.settings-section {
	background: white;
	margin: 0 40rpx 40rpx;
	border-radius: 30rpx;
	overflow: hidden;
	box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.settings-section:first-child {
	margin-top: 220rpx; /* 为导航栏留出空间 */
}

.section-header {
	display: flex;
	align-items: center;
	gap: 20rpx;
	padding: 40rpx 40rpx 30rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.section-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.settings-list {
	padding: 0 40rpx;
}

.setting-item {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 30rpx 0;
	border-bottom: 1rpx solid #f0f0f0;
}

.setting-item:last-child {
	border-bottom: none;
}

.setting-info {
	flex: 1;
}

.setting-title {
	font-size: 30rpx;
	color: #333;
	display: block;
	margin-bottom: 8rpx;
}

.setting-desc {
	font-size: 24rpx;
	color: #666;
	display: block;
}

.setting-value {
	display: flex;
	align-items: center;
	gap: 10rpx;
}

.value-text {
	font-size: 28rpx;
	color: #666;
}

/* ===== 适老化模式样式 ===== */
.elderly-mode .navbar-content {
	padding: 24rpx 36rpx;
	min-height: 96rpx;
}

.elderly-mode .navbar-left {
	gap: 16rpx;
	padding: 12rpx 20rpx 12rpx 0;
}

.elderly-mode .back-text {
	font-size: 36rpx;
	font-weight: 600;
}

.elderly-mode .navbar-title {
	font-size: 38rpx;
	font-weight: 700;
}

.elderly-mode .settings-section {
	margin-top: 240rpx; /* 适老化模式下导航栏更高 */
	padding: 50rpx 30rpx 30rpx;
}

.elderly-mode .section-header {
	font-size: 36rpx;
	padding: 30rpx 0;
}

.elderly-mode .setting-item {
	padding: 40rpx 30rpx;
}

.elderly-mode .setting-label {
	font-size: 36rpx;
}

.elderly-mode .setting-desc {
	font-size: 30rpx;
}

.elderly-mode .cache-info {
	font-size: 32rpx;
}
</style>
