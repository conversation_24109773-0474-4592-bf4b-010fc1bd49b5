<template>
	<view class="container">
		<!-- 自定义导航栏 -->
		<view class="custom-navbar">
			<view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
			<view class="navbar-content">
				<view class="navbar-left" @click="goBack">
					<Icon name="arrow-left-line" size="36rpx" color="#333"></Icon>
					<text class="back-text">返回</text>
				</view>
				<view class="navbar-center">
					<text class="navbar-title">充值</text>
				</view>
				<view class="navbar-right"></view>
			</view>
		</view>

		<!-- 充值金额选择 -->
		<view class="amount-section">
			<view class="section-title">选择充值金额</view>
			<view class="amount-grid">
				<view class="amount-item" 
					:class="{ active: selectedAmount === amount }" 
					v-for="amount in amountOptions" 
					:key="amount"
					@click="selectAmount(amount)">
					<text class="amount-text">¥{{amount}}</text>
				</view>
			</view>
			
			<view class="custom-amount">
				<text class="custom-label">自定义金额</text>
				<input class="amount-input" 
					type="digit" 
					placeholder="请输入充值金额" 
					v-model="customAmount"
					@input="onCustomAmountInput" />
			</view>
		</view>

		<!-- 支付方式选择 -->
		<view class="payment-section">
			<view class="section-title">选择支付方式</view>
			<view class="payment-methods">
				<view class="payment-item" 
					:class="{ active: selectedPayment === method.key }"
					v-for="method in paymentMethods" 
					:key="method.key"
					@click="selectPayment(method.key)">
					<view class="payment-icon">
						<Icon :name="method.icon" size="40rpx" :color="method.color"></Icon>
					</view>
					<view class="payment-info">
						<text class="payment-name">{{method.name}}</text>
						<text class="payment-desc">{{method.desc}}</text>
					</view>
					<view class="payment-radio">
						<Icon :name="selectedPayment === method.key ? 'radio-button-line' : 'checkbox-blank-circle-line'" 
							size="24rpx" 
							:color="selectedPayment === method.key ? '#ff8a00' : '#ccc'"></Icon>
					</view>
				</view>
			</view>
		</view>

		<!-- 充值说明 -->
		<view class="notice-section">
			<view class="notice-title">
				<Icon name="information-line" size="24rpx" color="#ff8a00"></Icon>
				<text>充值说明</text>
			</view>
			<view class="notice-content">
				<text class="notice-item">• 充值金额将实时到账，可用于支付各类服务费用</text>
				<text class="notice-item">• 单次充值金额不得超过5000元</text>
				<text class="notice-item">• 充值过程中请勿关闭页面或切换应用</text>
				<text class="notice-item">• 如有疑问，请联系客服：400-123-4567</text>
			</view>
		</view>

		<!-- 充值按钮 -->
		<view class="action-section">
			<view class="amount-summary">
				<text class="summary-label">充值金额：</text>
				<text class="summary-amount">¥{{finalAmount}}</text>
			</view>
			<button class="recharge-btn" 
				:disabled="!canRecharge" 
				@click="confirmRecharge">
				立即充值
			</button>
		</view>

		<!-- 功能开发中提示 -->
		<view class="dev-notice">
			<Icon name="tools-line" size="32rpx" color="#ff8a00"></Icon>
			<text class="dev-text">此功能正在开发中，敬请期待</text>
		</view>
	</view>
</template>

<script>
import Icon from '@/components/Icon/Icon.vue'

export default {
	components: {
		Icon
	},
	data() {
		return {
			statusBarHeight: 0,
			selectedAmount: '',
			customAmount: '',
			selectedPayment: 'wechat',
			amountOptions: ['50', '100', '200', '500', '1000', '2000'],
			paymentMethods: [
				{
					key: 'wechat',
					name: '微信支付',
					desc: '推荐使用',
					icon: 'wechat-line',
					color: '#07c160'
				},
				{
					key: 'alipay',
					name: '支付宝',
					desc: '安全便捷',
					icon: 'alipay-line',
					color: '#1677ff'
				},
				{
					key: 'bank',
					name: '银行卡',
					desc: '储蓄卡/信用卡',
					icon: 'bank-card-line',
					color: '#ff8a00'
				}
			]
		}
	},
	computed: {
		finalAmount() {
			return this.customAmount || this.selectedAmount || '0';
		},
		canRecharge() {
			const amount = parseFloat(this.finalAmount);
			return amount > 0 && amount <= 5000 && this.selectedPayment;
		}
	},
	onLoad() {
		const systemInfo = uni.getSystemInfoSync();
		this.statusBarHeight = systemInfo.statusBarHeight || 44;
	},
	methods: {
		selectAmount(amount) {
			this.selectedAmount = amount;
			this.customAmount = '';
		},
		onCustomAmountInput() {
			this.selectedAmount = '';
		},
		selectPayment(key) {
			this.selectedPayment = key;
		},
		confirmRecharge() {
			if (!this.canRecharge) return;
			
			uni.showModal({
				title: '确认充值',
				content: `确定要充值 ¥${this.finalAmount} 吗？`,
				success: (res) => {
					if (res.confirm) {
						this.processRecharge();
					}
				}
			});
		},
		processRecharge() {
			uni.showLoading({
				title: '正在跳转支付...',
				mask: true
			});
			
			// 模拟支付流程
			setTimeout(() => {
				uni.hideLoading();
				uni.showToast({
					title: '功能开发中，敬请期待',
					icon: 'none',
					duration: 2000
				});
			}, 2000);
		},
		goBack() {
			uni.navigateBack({
				fail: () => {
					uni.navigateTo({
						url: '/pages/wallet/wallet'
					});
				}
			});
		}
	}
}
</script>

<style scoped>
.container {
	background: #f8f9fa;
	min-height: 100vh;
}

/* 导航栏样式 */
.custom-navbar {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 1000;
	background: rgba(255, 255, 255, 0.95);
	backdrop-filter: blur(20rpx);
	border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);
}

.navbar-content {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 20rpx 32rpx;
	min-height: 88rpx;
}

.navbar-left {
	display: flex;
	align-items: center;
	gap: 12rpx;
	padding: 8rpx 16rpx 8rpx 0;
	border-radius: 20rpx;
	transition: all 0.2s ease;
}

.navbar-left:active {
	background-color: rgba(0, 0, 0, 0.04);
	transform: scale(0.96);
}

.back-text {
	font-size: 32rpx;
	color: #333;
	font-weight: 500;
}

.navbar-center {
	position: absolute;
	left: 50%;
	transform: translateX(-50%);
}

.navbar-title {
	font-size: 34rpx;
	font-weight: 600;
	color: #333;
}

.navbar-right {
	display: flex;
	align-items: center;
}

/* 内容区域 */
.amount-section, .payment-section, .notice-section {
	background: white;
	margin: 20rpx;
	border-radius: 30rpx;
	padding: 40rpx;
}

.amount-section {
	margin-top: 220rpx; /* 为导航栏留出空间 */
}

.section-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 30rpx;
}

.amount-grid {
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	gap: 20rpx;
	margin-bottom: 30rpx;
}

.amount-item {
	padding: 30rpx 20rpx;
	background: #f8f9fa;
	border-radius: 20rpx;
	text-align: center;
	border: 2rpx solid transparent;
	transition: all 0.2s ease;
}

.amount-item.active {
	background: rgba(255, 138, 0, 0.1);
	border-color: #ff8a00;
}

.amount-text {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
}

.amount-item.active .amount-text {
	color: #ff8a00;
}

.custom-amount {
	border-top: 1rpx solid #f0f0f0;
	padding-top: 30rpx;
}

.custom-label {
	font-size: 28rpx;
	color: #333;
	display: block;
	margin-bottom: 20rpx;
}

.amount-input {
	width: 100%;
	padding: 25rpx;
	background: #f8f9fa;
	border-radius: 15rpx;
	font-size: 28rpx;
	border: 2rpx solid transparent;
}

.amount-input:focus {
	border-color: #ff8a00;
	background: white;
}

.payment-methods {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

.payment-item {
	display: flex;
	align-items: center;
	padding: 25rpx;
	background: #f8f9fa;
	border-radius: 20rpx;
	border: 2rpx solid transparent;
	transition: all 0.2s ease;
}

.payment-item.active {
	background: rgba(255, 138, 0, 0.1);
	border-color: #ff8a00;
}

.payment-icon {
	margin-right: 20rpx;
}

.payment-info {
	flex: 1;
}

.payment-name {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 5rpx;
}

.payment-desc {
	font-size: 24rpx;
	color: #666;
	display: block;
}

.notice-title {
	display: flex;
	align-items: center;
	gap: 10rpx;
	font-size: 28rpx;
	font-weight: bold;
	color: #ff8a00;
	margin-bottom: 20rpx;
}

.notice-content {
	display: flex;
	flex-direction: column;
	gap: 15rpx;
}

.notice-item {
	font-size: 26rpx;
	color: #666;
	line-height: 1.5;
}

.action-section {
	padding: 40rpx;
}

.amount-summary {
	text-align: center;
	margin-bottom: 30rpx;
}

.summary-label {
	font-size: 28rpx;
	color: #666;
}

.summary-amount {
	font-size: 36rpx;
	font-weight: bold;
	color: #ff8a00;
}

.recharge-btn {
	width: 100%;
	height: 100rpx;
	background: #ff8a00;
	color: white;
	border: none;
	border-radius: 25rpx;
	font-size: 32rpx;
	font-weight: bold;
}

.recharge-btn:disabled {
	background: #ccc;
	color: #999;
}

.dev-notice {
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 15rpx;
	padding: 30rpx;
	margin: 20rpx;
	background: rgba(255, 138, 0, 0.1);
	border-radius: 20rpx;
}

.dev-text {
	font-size: 26rpx;
	color: #ff8a00;
}
</style>
