<template>
	<view class="container">
		<view class="header">
			<text class="title">图标系统诊断</text>
			<text class="subtitle">检查图标显示问题和兼容性</text>
		</view>
		
		<view class="content">
			<!-- 快速诊断结果 -->
			<view class="summary-card">
				<view class="summary-item">
					<text class="summary-label">总图标数</text>
					<text class="summary-value">{{ totalIcons }}</text>
				</view>
				<view class="summary-item">
					<text class="summary-label">正常显示</text>
					<text class="summary-value success">{{ normalIcons }}</text>
				</view>
				<view class="summary-item">
					<text class="summary-label">显示异常</text>
					<text class="summary-value error">{{ errorIcons }}</text>
				</view>
				<view class="summary-item">
					<text class="summary-label">兼容性问题</text>
					<text class="summary-value warning">{{ compatibilityIssues.length }}</text>
				</view>
			</view>

			<!-- 图标显示测试 -->
			<view class="section">
				<view class="section-header">
					<text class="section-title">核心图标测试</text>
					<text class="section-desc">测试首页和主要功能使用的图标</text>
				</view>
				<view class="icon-test-grid">
					<view 
						v-for="(icon, index) in coreTestIcons" 
						:key="index"
						class="icon-test-item"
						:class="{ 'icon-error': icon.hasError, 'icon-warning': icon.hasWarning }"
					>
						<view class="icon-display">
							<Icon 
								:name="icon.name" 
								size="48rpx" 
								:color="icon.color || '#333'"
							/>
						</view>
						<text class="icon-name">{{ icon.name }}</text>
						<text class="icon-emoji">{{ icon.emoji }}</text>
						<text class="icon-status" :class="icon.status">
							{{ getStatusText(icon.status) }}
						</text>
					</view>
				</view>
			</view>

			<!-- 兼容性问题列表 -->
			<view class="section" v-if="compatibilityIssues.length > 0">
				<view class="section-header">
					<text class="section-title">兼容性问题</text>
					<text class="section-desc">需要修复的图标问题</text>
				</view>
				<view class="issue-list">
					<view 
						v-for="(issue, index) in compatibilityIssues" 
						:key="index"
						class="issue-item"
						:class="issue.type"
					>
						<view class="issue-icon">
							<text>{{ issue.type === 'error' ? '❌' : '⚠️' }}</text>
						</view>
						<view class="issue-content">
							<text class="issue-title">{{ issue.title }}</text>
							<text class="issue-description">{{ issue.description }}</text>
							<text v-if="issue.suggestion" class="issue-suggestion">
								💡 {{ issue.suggestion }}
							</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 修复建议 -->
			<view class="section">
				<view class="section-header">
					<text class="section-title">修复建议</text>
					<text class="section-desc">优化图标系统的建议</text>
				</view>
				<view class="suggestion-list">
					<view 
						v-for="(suggestion, index) in suggestions" 
						:key="index"
						class="suggestion-item"
						:class="suggestion.priority"
					>
						<view class="suggestion-priority">
							<text>{{ getPriorityIcon(suggestion.priority) }}</text>
						</view>
						<view class="suggestion-content">
							<text class="suggestion-title">{{ suggestion.title }}</text>
							<text class="suggestion-description">{{ suggestion.description }}</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 操作按钮 -->
			<view class="actions">
				<button class="action-btn primary" @click="refreshDiagnostic">
					🔄 重新诊断
				</button>
				<button class="action-btn secondary" @click="exportReport">
					📋 导出报告
				</button>
				<button class="action-btn" @click="viewIconGallery">
					🎨 查看图标库
				</button>
				<button class="action-btn test" @click="quickTest">
					⚡ 快速测试
				</button>
			</view>
		</view>
	</view>
</template>

<script>
import { IconConfig, getAllIconNames, hasIcon, getIconEmoji } from '@/utils/iconConfig.js'
import { getCompatibleIconName, checkIconsCompatibility } from '@/utils/iconCompatibility.js'

export default {
	name: 'IconDiagnostic',
	data() {
		return {
			systemInfo: {},
			coreTestIcons: [],
			compatibilityIssues: [],
			suggestions: [],
			totalIcons: 0,
			normalIcons: 0,
			errorIcons: 0
		}
	},
	onLoad() {
		this.initDiagnostic()
	},
	methods: {
		async initDiagnostic() {
			// 获取系统信息
			this.systemInfo = uni.getSystemInfoSync()
			
			// 初始化核心图标测试
			this.initCoreIconTest()
			
			// 检查兼容性问题
			this.checkCompatibility()
			
			// 生成修复建议
			this.generateSuggestions()
			
			// 统计结果
			this.calculateStats()
		},
		
		initCoreIconTest() {
			// 首页使用的核心图标
			const coreIcons = [
				'location-line', 'notification-3-line', 'building-line', 'search-line',
				'money-cny-circle-line', 'user-settings-line', 'customer-service-2-line',
				'parent-line', 'user-heart-line', 'heart-3-line', 'article-line',
				'health-book-line', 'hotel-bed-line', 'community-line', 'restaurant-line',
				'home-gear-line', 'camera-line', 'music-line'
			]
			
			this.coreTestIcons = coreIcons.map(iconName => {
				const compatibleName = getCompatibleIconName(iconName)
				const exists = hasIcon(compatibleName)
				const emoji = exists ? getIconEmoji(compatibleName) : '🔧' // 使用工具图标表示需要修复
				
				let status = 'normal'
				let hasError = false
				let hasWarning = false
				
				if (!exists) {
					status = 'error'
					hasError = true
				} else if (iconName !== compatibleName) {
					status = 'warning'
					hasWarning = true
				}
				
				return {
					name: iconName,
					compatibleName,
					emoji,
					exists,
					status,
					hasError,
					hasWarning,
					color: this.getIconColor(iconName)
				}
			})
		},
		
		checkCompatibility() {
			const allIcons = this.coreTestIcons.map(icon => icon.name)
			const result = checkIconsCompatibility(allIcons)
			
			this.compatibilityIssues = []
			
			// 检查废弃图标
			result.deprecated.forEach(item => {
				this.compatibilityIssues.push({
					type: 'warning',
					title: `废弃图标: ${item.old}`,
					description: `图标 "${item.old}" 已废弃，建议替换为 "${item.new}"`,
					suggestion: `将所有 "${item.old}" 替换为 "${item.new}"`
				})
			})
			
			// 检查不存在的图标
			this.coreTestIcons.forEach(icon => {
				if (!icon.exists) {
					this.compatibilityIssues.push({
						type: 'error',
						title: `图标不存在: ${icon.name}`,
						description: `图标 "${icon.name}" 在配置文件中不存在，将显示为问号`,
						suggestion: `添加图标配置或使用其他可用图标`
					})
				}
			})
			
			// 检查emoji兼容性
			this.checkEmojiCompatibility()
		},
		
		checkEmojiCompatibility() {
			// 检查可能有兼容性问题的emoji
			const problematicEmojis = ['👨‍👩‍👧‍👦', '👨‍⚕️', '👩‍⚕️', '🏘️', '🍽️', '🗣️']
			
			this.coreTestIcons.forEach(icon => {
				if (problematicEmojis.includes(icon.emoji)) {
					this.compatibilityIssues.push({
						type: 'warning',
						title: `Emoji兼容性: ${icon.name}`,
						description: `图标 "${icon.name}" 使用的emoji "${icon.emoji}" 在某些设备上可能显示异常`,
						suggestion: `考虑替换为SVG图标或更简单的emoji`
					})
				}
			})
		},
		
		generateSuggestions() {
			this.suggestions = [
				{
					priority: 'high',
					title: '替换废弃图标',
					description: '立即更新所有使用废弃图标的地方，确保向前兼容性'
				},
				{
					priority: 'high',
					title: '添加SVG图标',
					description: '为核心功能添加SVG图标文件，提升显示效果和兼容性'
				},
				{
					priority: 'medium',
					title: '统一图标尺寸',
					description: '建立标准的图标尺寸规范，确保视觉一致性'
				},
				{
					priority: 'medium',
					title: '优化适老化图标',
					description: '为老年用户优化图标设计，使用更清晰易懂的图标'
				},
				{
					priority: 'low',
					title: '添加图标动画',
					description: '为交互图标添加适当的动画效果，提升用户体验'
				}
			]
		},
		
		calculateStats() {
			this.totalIcons = this.coreTestIcons.length
			this.normalIcons = this.coreTestIcons.filter(icon => icon.status === 'normal').length
			this.errorIcons = this.coreTestIcons.filter(icon => icon.status === 'error').length
		},
		
		getStatusText(status) {
			const statusMap = {
				'normal': '正常',
				'warning': '警告',
				'error': '错误'
			}
			return statusMap[status] || '未知'
		},
		
		getIconColor(iconName) {
			// 根据图标用途返回合适的颜色
			if (iconName.includes('building') || iconName.includes('institution')) return '#ff6b6b'
			if (iconName.includes('service') || iconName.includes('heart')) return '#4ecdc4'
			if (iconName.includes('money') || iconName.includes('subsidy')) return '#ff8a00'
			if (iconName.includes('elderly') || iconName.includes('user')) return '#96ceb4'
			return '#333'
		},
		
		getPriorityIcon(priority) {
			const icons = {
				'high': '🔴',
				'medium': '🟡',
				'low': '🟢'
			}
			return icons[priority] || '⚪'
		},
		
		refreshDiagnostic() {
			uni.showLoading({ title: '诊断中...' })
			setTimeout(() => {
				this.initDiagnostic()
				uni.hideLoading()
				uni.showToast({ title: '诊断完成', icon: 'success' })
			}, 1000)
		},
		
		exportReport() {
			// 生成诊断报告
			const report = {
				timestamp: new Date().toLocaleString(),
				summary: {
					total: this.totalIcons,
					normal: this.normalIcons,
					error: this.errorIcons,
					issues: this.compatibilityIssues.length
				},
				issues: this.compatibilityIssues,
				suggestions: this.suggestions
			}
			
			console.log('图标诊断报告:', report)
			uni.showToast({ title: '报告已生成，请查看控制台', icon: 'none' })
		},
		
		viewIconGallery() {
			uni.navigateTo({
				url: '/pages/test/icons-gallery'
			})
		},

		// 添加快速测试方法
		quickTest() {
			// 测试首页核心图标
			const testResults = []
			const coreIcons = ['location-line', 'building-line', 'search-line', 'money-cny-circle-line', 'settings-line']

			coreIcons.forEach(iconName => {
				const exists = hasIcon(iconName)
				const emoji = exists ? getIconEmoji(iconName) : '⚠️' // 使用警告图标表示测试失败
				testResults.push({
					name: iconName,
					exists,
					emoji,
					status: exists ? '✅' : '❌'
				})
			})

			console.log('快速测试结果:', testResults)

			const successCount = testResults.filter(r => r.exists).length
			uni.showToast({
				title: `测试完成: ${successCount}/${testResults.length} 正常`,
				icon: successCount === testResults.length ? 'success' : 'none'
			})
		}
	}
}
</script>

<style scoped>
.container {
	padding: 20rpx;
	background-color: #f5f5f5;
	min-height: 100vh;
}

.header {
	text-align: center;
	margin-bottom: 30rpx;
}

.title {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 10rpx;
}

.subtitle {
	font-size: 28rpx;
	color: #666;
}

.summary-card {
	background: white;
	border-radius: 20rpx;
	padding: 30rpx;
	margin-bottom: 30rpx;
	display: flex;
	justify-content: space-between;
	box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.summary-item {
	text-align: center;
	flex: 1;
}

.summary-label {
	font-size: 24rpx;
	color: #666;
	display: block;
	margin-bottom: 10rpx;
}

.summary-value {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.summary-value.success { color: #4caf50; }
.summary-value.error { color: #f44336; }
.summary-value.warning { color: #ff9800; }

.section {
	background: white;
	border-radius: 20rpx;
	padding: 30rpx;
	margin-bottom: 30rpx;
	box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.section-header {
	margin-bottom: 30rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 10rpx;
}

.section-desc {
	font-size: 26rpx;
	color: #666;
}

.icon-test-grid {
	display: grid;
	grid-template-columns: repeat(auto-fill, minmax(200rpx, 1fr));
	gap: 20rpx;
}

.icon-test-item {
	text-align: center;
	padding: 20rpx;
	border-radius: 15rpx;
	background: #f9f9f9;
	border: 2rpx solid transparent;
}

.icon-test-item.icon-error {
	border-color: #f44336;
	background: #ffebee;
}

.icon-test-item.icon-warning {
	border-color: #ff9800;
	background: #fff3e0;
}

.icon-display {
	margin-bottom: 15rpx;
}

.icon-name {
	font-size: 22rpx;
	color: #333;
	display: block;
	margin-bottom: 5rpx;
	word-break: break-all;
}

.icon-emoji {
	font-size: 20rpx;
	color: #666;
	display: block;
	margin-bottom: 10rpx;
}

.icon-status {
	font-size: 20rpx;
	padding: 4rpx 8rpx;
	border-radius: 8rpx;
	color: white;
}

.icon-status.normal { background: #4caf50; }
.icon-status.warning { background: #ff9800; }
.icon-status.error { background: #f44336; }

.issue-list, .suggestion-list {
	space-y: 20rpx;
}

.issue-item, .suggestion-item {
	display: flex;
	padding: 20rpx;
	border-radius: 15rpx;
	margin-bottom: 15rpx;
}

.issue-item.error { background: #ffebee; }
.issue-item.warning { background: #fff3e0; }

.suggestion-item.high { background: #ffebee; }
.suggestion-item.medium { background: #fff3e0; }
.suggestion-item.low { background: #e8f5e8; }

.issue-icon, .suggestion-priority {
	margin-right: 20rpx;
	font-size: 32rpx;
}

.issue-content, .suggestion-content {
	flex: 1;
}

.issue-title, .suggestion-title {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 8rpx;
}

.issue-description, .suggestion-description {
	font-size: 24rpx;
	color: #666;
	margin-bottom: 8rpx;
}

.issue-suggestion {
	font-size: 22rpx;
	color: #4caf50;
	font-style: italic;
}

.actions {
	display: flex;
	gap: 20rpx;
	margin-top: 30rpx;
}

.action-btn {
	flex: 1;
	padding: 25rpx;
	border-radius: 15rpx;
	font-size: 28rpx;
	border: none;
	color: white;
	background: #666;
}

.action-btn.primary { background: #ff8a00; }
.action-btn.secondary { background: #4caf50; }
.action-btn.test { background: #2196f3; }
</style>
