<template>
	<view class="container">
		<PageHeader title="用药提醒" showBack></PageHeader>
		
		<view class="content">
			<!-- 今日用药 -->
			<view class="section">
				<view class="section-header">
					<text class="section-title">今日用药</text>
					<text class="date-text">{{ todayDate }}</text>
				</view>
				<view class="today-medications">
					<view 
						class="medication-card" 
						:class="{ completed: med.taken }"
						v-for="med in todayMedications" 
						:key="med.id"
						@click="toggleMedication(med)"
					>
						<view class="med-time">
							<text class="time-text">{{ med.time }}</text>
							<view class="status-indicator" :class="{ taken: med.taken }">
								<Icon :name="med.taken ? 'check-line' : 'time-line'" size="20rpx" color="white"></Icon>
							</view>
						</view>
						<view class="med-info">
							<text class="med-name">{{ med.name }}</text>
							<text class="med-dosage">{{ med.dosage }}</text>
							<text class="med-note" v-if="med.note">{{ med.note }}</text>
						</view>
						<view class="med-action">
							<InteractiveButton 
								:type="med.taken ? 'secondary' : 'primary'" 
								:text="med.taken ? '已服用' : '标记服用'"
								size="small"
								@click.stop="toggleMedication(med)"
							/>
						</view>
					</view>
				</view>
			</view>

			<!-- 用药统计 -->
			<view class="section">
				<text class="section-title">本周统计</text>
				<view class="stats-grid">
					<view class="stat-item">
						<text class="stat-value">{{ weekStats.total }}</text>
						<text class="stat-label">总次数</text>
					</view>
					<view class="stat-item">
						<text class="stat-value">{{ weekStats.completed }}</text>
						<text class="stat-label">已完成</text>
					</view>
					<view class="stat-item">
						<text class="stat-value">{{ weekStats.rate }}%</text>
						<text class="stat-label">完成率</text>
					</view>
					<view class="stat-item">
						<text class="stat-value">{{ weekStats.missed }}</text>
						<text class="stat-label">漏服次数</text>
					</view>
				</view>
			</view>

			<!-- 我的药品 -->
			<view class="section">
				<view class="section-header">
					<text class="section-title">我的药品</text>
					<InteractiveButton 
						type="primary" 
						text="添加药品" 
						size="small"
						leftIcon="add-line"
						@click="showAddMedication"
					/>
				</view>
				<view class="medication-list">
					<view class="medication-item" v-for="med in medicationList" :key="med.id">
						<view class="med-icon">
							<Icon name="capsule-line" size="32rpx" color="#4caf50"></Icon>
						</view>
						<view class="med-details">
							<text class="med-name">{{ med.name }}</text>
							<text class="med-spec">{{ med.specification }}</text>
							<text class="med-schedule">{{ med.schedule }}</text>
						</view>
						<view class="med-actions">
							<button class="action-btn edit" @click="editMedication(med)">
								<Icon name="edit-line" size="20rpx" color="#ff8a00"></Icon>
							</button>
							<button class="action-btn delete" @click="deleteMedication(med)">
								<Icon name="delete-line" size="20rpx" color="#f44336"></Icon>
							</button>
						</view>
					</view>
				</view>
			</view>

			<!-- 提醒设置 -->
			<view class="section">
				<text class="section-title">提醒设置</text>
				<view class="settings-card">
					<view class="setting-item">
						<view class="setting-info">
							<text class="setting-name">声音提醒</text>
							<text class="setting-desc">开启后将播放提醒铃声</text>
						</view>
						<switch 
							:checked="settings.soundEnabled" 
							@change="toggleSetting('soundEnabled', $event)"
							color="#ff8a00"
						/>
					</view>
					<view class="setting-item">
						<view class="setting-info">
							<text class="setting-name">振动提醒</text>
							<text class="setting-desc">开启后将震动提醒</text>
						</view>
						<switch 
							:checked="settings.vibrationEnabled" 
							@change="toggleSetting('vibrationEnabled', $event)"
							color="#ff8a00"
						/>
					</view>
					<view class="setting-item">
						<view class="setting-info">
							<text class="setting-name">提前提醒</text>
							<text class="setting-desc">在用药时间前{{ settings.advanceMinutes }}分钟提醒</text>
						</view>
						<picker 
							:value="advanceIndex" 
							:range="advanceOptions" 
							range-key="label"
							@change="onAdvanceChange"
						>
							<view class="picker-display">
								<text class="picker-text">{{ settings.advanceMinutes }}分钟</text>
								<Icon name="arrow-down-s-line" size="20rpx" color="#999"></Icon>
							</view>
						</picker>
					</view>
				</view>
			</view>
		</view>

		<!-- 添加药品弹窗 -->
		<uni-popup ref="addPopup" type="bottom">
			<view class="popup-content">
				<view class="popup-header">
					<text class="popup-title">{{ isEditing ? '编辑药品' : '添加药品' }}</text>
					<button class="close-btn" @click="hideAddMedication">
						<Icon name="close-line" size="24rpx" color="#999"></Icon>
					</button>
				</view>
				<view class="popup-form">
					<view class="form-item">
						<text class="form-label">药品名称</text>
						<input 
							class="form-input" 
							v-model="medicationForm.name" 
							placeholder="请输入药品名称"
						/>
					</view>
					<view class="form-item">
						<text class="form-label">规格</text>
						<input 
							class="form-input" 
							v-model="medicationForm.specification" 
							placeholder="如：50mg/片"
						/>
					</view>
					<view class="form-item">
						<text class="form-label">用法用量</text>
						<input 
							class="form-input" 
							v-model="medicationForm.dosage" 
							placeholder="如：每次1片"
						/>
					</view>
					<view class="form-item">
						<text class="form-label">服用时间</text>
						<view class="time-picker-group">
							<picker 
								mode="multiSelector" 
								:value="timePickerValue" 
								:range="timePickerRange"
								@change="onTimePickerChange"
							>
								<view class="picker-display">
									<text class="picker-text">{{ medicationForm.schedule || '请选择服用时间' }}</text>
									<Icon name="time-line" size="20rpx" color="#999"></Icon>
								</view>
							</picker>
						</view>
					</view>
					<view class="form-item">
						<text class="form-label">备注</text>
						<textarea 
							class="form-textarea" 
							v-model="medicationForm.note" 
							placeholder="如：饭后服用"
							maxlength="100"
						/>
					</view>
				</view>
				<view class="popup-actions">
					<InteractiveButton 
						type="secondary" 
						text="取消" 
						@click="hideAddMedication"
					/>
					<InteractiveButton 
						type="primary" 
						text="保存" 
						:loading="saving"
						@click="saveMedication"
					/>
				</view>
			</view>
		</uni-popup>
	</view>
</template>

<script>
import Icon from '@/components/Icon/Icon.vue'
import PageHeader from '@/components/PageHeader/PageHeader.vue'
import InteractiveButton from '@/components/InteractiveButton/InteractiveButton.vue'
import crudAPI from '@/utils/crudAPI.js'
import FeedbackUtils from '@/utils/feedback.js'

export default {
	name: 'HealthMedication',
	components: {
		Icon,
		PageHeader,
		InteractiveButton
	},
	data() {
		return {
			saving: false,
			isEditing: false,
			loading: false,
			advanceIndex: 1,
			timePickerValue: [0, 0],
			todayMedications: [],
			medicationList: [],
			settings: {
				soundEnabled: true,
				vibrationEnabled: true,
				advanceMinutes: 15
			},
			medicationForm: {
				name: '',
				specification: '',
				dosage: '',
				schedule: '',
				note: ''
			},
			advanceOptions: [
				{ label: '5分钟', value: 5 },
				{ label: '15分钟', value: 15 },
				{ label: '30分钟', value: 30 },
				{ label: '60分钟', value: 60 }
			],
			timePickerRange: [
				['每日1次', '每日2次', '每日3次'],
				['08:00', '12:00', '18:00', '20:00']
			]
		}
	},
	computed: {
		todayDate() {
			const today = new Date()
			return `${today.getMonth() + 1}月${today.getDate()}日`
		},
		weekStats() {
			// 模拟统计数据
			return {
				total: 21,
				completed: 18,
				rate: 86,
				missed: 3
			}
		}
	},
	onLoad() {
		this.loadMedications()
	},
	methods: {
		// 加载用药数据
		async loadMedications() {
			try {
				this.loading = true

				const result = await crudAPI.getMedications()

				if (result.success) {
					this.medicationList = result.data
					this.generateTodayMedications()

					if (result.data.length > 0) {
						FeedbackUtils.showSuccess(`已加载 ${result.data.length} 个用药提醒`)
					}
				} else {
					FeedbackUtils.showError(result.message || '数据加载失败，请重试')
					this.medicationList = []
				}
			} catch (error) {
				console.error('加载用药数据失败:', error)
				FeedbackUtils.showError('数据加载失败，请重试')
				this.medicationList = []
			} finally {
				this.loading = false
			}
		},

		// 生成今日用药计划
		generateTodayMedications() {
			this.todayMedications = []
			this.medicationList.forEach(med => {
				if (med.times && med.times.length > 0) {
					med.times.forEach(time => {
						this.todayMedications.push({
							id: `${med.id}_${time}`,
							medicationId: med.id,
							name: med.name,
							dosage: med.dosage,
							time: time,
							taken: false,
							note: med.note || ''
						})
					})
				}
			})

			// 按时间排序
			this.todayMedications.sort((a, b) => a.time.localeCompare(b.time))
		},
		toggleMedication(med) {
			med.taken = !med.taken
			uni.showToast({
				title: med.taken ? '已标记服用' : '已取消标记',
				icon: 'success'
			})
		},
		showAddMedication() {
			this.isEditing = false
			this.medicationForm = {
				name: '',
				specification: '',
				dosage: '',
				schedule: '',
				note: ''
			}
			this.$refs.addPopup.open()
		},
		hideAddMedication() {
			this.$refs.addPopup.close()
		},
		editMedication(med) {
			this.isEditing = true
			this.medicationForm = { ...med }
			this.$refs.addPopup.open()
		},
		async deleteMedication(med) {
			try {
				await FeedbackUtils.showConfirm({
					title: '确认删除',
					content: `确定要删除药品"${med.name}"吗？`,
					confirmText: '删除',
					cancelText: '取消'
				})

				FeedbackUtils.showLoading('删除中...')

				const result = await crudAPI.deleteMedication(med.id)

				FeedbackUtils.hideLoading()

				if (result.success) {
					FeedbackUtils.showSuccess('删除成功')
					this.loadMedications()
				} else {
					FeedbackUtils.showError(result.message || '删除失败')
				}
			} catch (error) {
				FeedbackUtils.hideLoading()
				console.log('用户取消删除')
			}
		},
		toggleSetting(key, event) {
			this.settings[key] = event.detail.value
		},
		onAdvanceChange(e) {
			this.advanceIndex = e.detail.value
			this.settings.advanceMinutes = this.advanceOptions[this.advanceIndex].value
		},
		onTimePickerChange(e) {
			this.timePickerValue = e.detail.value
			const frequency = this.timePickerRange[0][e.detail.value[0]]
			const time = this.timePickerRange[1][e.detail.value[1]]
			this.medicationForm.schedule = `${frequency}，${time}`
		},
		async saveMedication() {
			if (!this.medicationForm.name.trim()) {
				FeedbackUtils.showError('请输入药品名称')
				return
			}

			this.saving = true
			try {
				// 处理时间数据
				const times = this.medicationForm.schedule.includes('每日2次') ? ['08:00', '18:00'] : ['08:00']

				const submitData = {
					name: this.medicationForm.name,
					specification: this.medicationForm.specification,
					dosage: this.medicationForm.dosage,
					schedule: this.medicationForm.schedule,
					times: times,
					note: this.medicationForm.note,
					reminder: true
				}

				let result
				if (this.isEditing) {
					result = await crudAPI.updateMedication(this.medicationForm.id, submitData)
				} else {
					result = await crudAPI.createMedication(submitData)
				}

				if (result.success) {
					FeedbackUtils.showSuccess(this.isEditing ? '修改成功' : '添加成功')
					this.hideAddMedication()
					this.loadMedications()
				} else {
					FeedbackUtils.showError(result.message || '操作失败')
				}
			} catch (error) {
				console.error('保存用药提醒失败:', error)
				FeedbackUtils.showError('操作失败，请重试')
			} finally {
				this.saving = false
			}
		}
	}
}
</script>

<style scoped>
.container {
	min-height: 100vh;
	background: #f5f5f5;
}

.content {
	padding: 140rpx 32rpx 40rpx;
}

.section {
	margin-bottom: 32rpx;
}

.section-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 24rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
}

.date-text {
	font-size: 26rpx;
	color: #666;
}

.today-medications {
	display: flex;
	flex-direction: column;
	gap: 16rpx;
}

.medication-card {
	background: white;
	border-radius: 16rpx;
	padding: 24rpx;
	display: flex;
	align-items: center;
	gap: 16rpx;
	border-left: 6rpx solid #ff8a00;
}

.medication-card.completed {
	border-left-color: #4caf50;
	opacity: 0.7;
}

.med-time {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 8rpx;
}

.time-text {
	font-size: 28rpx;
	font-weight: 600;
	color: #333;
}

.status-indicator {
	width: 32rpx;
	height: 32rpx;
	border-radius: 50%;
	background: #ff8a00;
	display: flex;
	align-items: center;
	justify-content: center;
}

.status-indicator.taken {
	background: #4caf50;
}

.med-info {
	flex: 1;
}

.med-name {
	font-size: 30rpx;
	font-weight: 600;
	color: #333;
	display: block;
	margin-bottom: 4rpx;
}

.med-dosage {
	font-size: 26rpx;
	color: #666;
	display: block;
	margin-bottom: 4rpx;
}

.med-note {
	font-size: 24rpx;
	color: #999;
	display: block;
}

.stats-grid {
	display: grid;
	grid-template-columns: repeat(4, 1fr);
	gap: 16rpx;
}

.stat-item {
	background: white;
	border-radius: 16rpx;
	padding: 24rpx;
	text-align: center;
}

.stat-value {
	font-size: 36rpx;
	font-weight: 600;
	color: #ff8a00;
	display: block;
	margin-bottom: 8rpx;
}

.stat-label {
	font-size: 24rpx;
	color: #666;
}

.medication-list {
	display: flex;
	flex-direction: column;
	gap: 16rpx;
}

.medication-item {
	background: white;
	border-radius: 16rpx;
	padding: 24rpx;
	display: flex;
	align-items: center;
	gap: 16rpx;
}

.med-icon {
	width: 48rpx;
	height: 48rpx;
	border-radius: 50%;
	background: rgba(76, 175, 80, 0.1);
	display: flex;
	align-items: center;
	justify-content: center;
}

.med-details {
	flex: 1;
}

.med-spec {
	font-size: 24rpx;
	color: #666;
	display: block;
	margin: 4rpx 0;
}

.med-schedule {
	font-size: 24rpx;
	color: #999;
	display: block;
}

.med-actions {
	display: flex;
	gap: 8rpx;
}

.action-btn {
	width: 48rpx;
	height: 48rpx;
	border-radius: 50%;
	border: none;
	display: flex;
	align-items: center;
	justify-content: center;
}

.action-btn.edit {
	background: rgba(255, 138, 0, 0.1);
}

.action-btn.delete {
	background: rgba(244, 67, 54, 0.1);
}

.settings-card {
	background: white;
	border-radius: 16rpx;
	padding: 24rpx;
}

.setting-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 24rpx;
}

.setting-item:last-child {
	margin-bottom: 0;
}

.setting-info {
	flex: 1;
}

.setting-name {
	font-size: 28rpx;
	color: #333;
	display: block;
	margin-bottom: 4rpx;
}

.setting-desc {
	font-size: 24rpx;
	color: #666;
}

.picker-display {
	display: flex;
	align-items: center;
	gap: 8rpx;
	padding: 8rpx 16rpx;
	background: #f5f5f5;
	border-radius: 8rpx;
}

.picker-text {
	font-size: 26rpx;
	color: #333;
}

.popup-content {
	background: white;
	border-radius: 24rpx 24rpx 0 0;
	padding: 32rpx;
	max-height: 80vh;
}

.popup-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 32rpx;
}

.popup-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
}

.close-btn {
	width: 48rpx;
	height: 48rpx;
	border-radius: 50%;
	border: none;
	background: #f5f5f5;
	display: flex;
	align-items: center;
	justify-content: center;
}

.popup-form {
	margin-bottom: 32rpx;
}

.form-item {
	margin-bottom: 24rpx;
}

.form-label {
	font-size: 28rpx;
	color: #333;
	margin-bottom: 12rpx;
	display: block;
}

.form-input {
	width: 100%;
	padding: 20rpx;
	border: 1rpx solid #e0e0e0;
	border-radius: 12rpx;
	font-size: 28rpx;
	background: #fafafa;
}

.form-textarea {
	width: 100%;
	min-height: 80rpx;
	padding: 20rpx;
	border: 1rpx solid #e0e0e0;
	border-radius: 12rpx;
	font-size: 28rpx;
	background: #fafafa;
	resize: none;
}

.popup-actions {
	display: flex;
	gap: 16rpx;
}
</style>
