<template>
	<view class="container">
		<!-- 补贴信息 -->
		<view class="subsidy-info">
			<text class="subsidy-title">{{subsidyInfo.title}}</text>
			<text class="subsidy-desc">{{subsidyInfo.description}}</text>
			<view class="subsidy-amount">
				<text class="amount-label">补贴金额：</text>
				<text class="amount-value">¥{{subsidyInfo.amount}}</text>
			</view>
			<view class="subsidy-deadline">
				<text class="deadline-label">申请截止：</text>
				<text class="deadline-value">{{subsidyInfo.deadline}}</text>
			</view>
		</view>
		
		<!-- 申请条件 -->
		<view class="conditions-section">
			<view class="section-title">申请条件</view>
			<view class="conditions-list">
				<view class="condition-item" v-for="(condition, index) in subsidyInfo.conditions" :key="index">
					<view class="check-icon">
						<Icon name="check-line" size="24rpx" color="#4caf50" />
					</view>
					<text class="condition-text">{{condition}}</text>
				</view>
			</view>
		</view>
		
		<!-- 申请表单 -->
		<view class="form-section">
			<view class="section-title">申请信息</view>
			<form @submit="submitApplication">
				<view class="form-group">
					<text class="form-label">申请人姓名 <text class="required">*</text></text>
					<input 
						class="form-input" 
						type="text" 
						placeholder="请输入申请人姓名"
						v-model="formData.name"
					/>
				</view>
				
				<view class="form-group">
					<text class="form-label">身份证号 <text class="required">*</text></text>
					<input 
						class="form-input" 
						type="text" 
						placeholder="请输入身份证号"
						v-model="formData.idCard"
					/>
				</view>
				
				<view class="form-group">
					<text class="form-label">联系电话 <text class="required">*</text></text>
					<input 
						class="form-input" 
						type="text" 
						placeholder="请输入联系电话"
						v-model="formData.phone"
					/>
				</view>
				
				<view class="form-group">
					<text class="form-label">家庭住址 <text class="required">*</text></text>
					<textarea 
						class="form-textarea" 
						placeholder="请输入详细家庭住址"
						v-model="formData.address"
					></textarea>
				</view>
				
				<view class="form-group">
					<text class="form-label">申请理由 <text class="required">*</text></text>
					<textarea 
						class="form-textarea" 
						placeholder="请详细说明申请理由"
						v-model="formData.reason"
					></textarea>
				</view>
				
				<!-- 上传材料 -->
				<view class="upload-section">
					<text class="form-label">上传材料 <text class="required">*</text></text>
					<view class="upload-list">
						<view class="upload-item" v-for="(item, index) in requiredDocuments" :key="index">
							<text class="doc-name">{{item.name}}</text>
							<view class="upload-actions">
								<button 
									class="upload-btn" 
									@click="uploadDocument(index)"
									:class="{ uploaded: item.uploaded }"
								>
									{{item.uploaded ? '已上传' : '上传'}}
								</button>
								<button 
									class="preview-btn" 
									v-if="item.uploaded"
									@click="previewDocument(index)"
								>
									预览
								</button>
							</view>
						</view>
					</view>
				</view>
				
				<!-- 协议确认 -->
				<view class="agreement-section">
					<label class="agreement-item">
						<checkbox 
							:checked="formData.agreeTerms" 
							@change="onAgreeChange"
						/>
						<text class="agreement-text">
							我已阅读并同意
							<text class="link" @click="viewTerms">《补贴申请协议》</text>
						</text>
					</label>
				</view>
				
				<!-- 提交按钮 -->
				<view class="submit-section">
					<button 
						class="submit-btn"
						:class="{ disabled: !canSubmit }"
						:disabled="!canSubmit"
						@click="submitApplication"
					>
						提交申请
					</button>
				</view>
			</form>
		</view>
		
		<!-- 申请流程 -->
		<view class="process-section">
			<view class="section-title">申请流程</view>
			<view class="process-steps">
				<view class="step-item" v-for="(step, index) in applicationProcess" :key="index">
					<view class="step-number">{{index + 1}}</view>
					<view class="step-content">
						<text class="step-title">{{step.title}}</text>
						<text class="step-desc">{{step.description}}</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import Icon from '@/components/Icon/Icon.vue'

export default {
	components: {
		Icon
	},
	data() {
		return {
			subsidyId: '',
			subsidyInfo: {
				title: '高龄老人护理补贴',
				description: '为80岁以上失能老人提供护理服务补贴',
				amount: 500,
				deadline: '2024-03-31'
			},
			formData: {
				name: '',
				idCard: '',
				phone: '',
				address: '',
				reason: '',
				agreeTerms: false
			},
			requiredDocuments: [
				{
					name: '身份证正反面',
					uploaded: false,
					file: null
				},
				{
					name: '户口本首页及本人页',
					uploaded: false,
					file: null
				},
				{
					name: '医疗诊断证明',
					uploaded: false,
					file: null
				},
				{
					name: '收入证明',
					uploaded: false,
					file: null
				}
			],
			applicationProcess: [
				{
					title: '提交申请',
					description: '填写申请表格并上传相关材料'
				},
				{
					title: '初步审核',
					description: '相关部门进行材料初步审核'
				},
				{
					title: '实地核查',
					description: '工作人员上门核实情况'
				},
				{
					title: '审批决定',
					description: '审核通过后发放补贴'
				}
			]
		}
	},
	computed: {
		canSubmit() {
			const { name, idCard, phone, address, reason, agreeTerms } = this.formData;
			const allDocumentsUploaded = this.requiredDocuments.every(doc => doc.uploaded);
			
			return name && idCard && phone && address && reason && agreeTerms && allDocumentsUploaded;
		}
	},
	onLoad(options) {
		if (options.id) {
			this.subsidyId = options.id;
			this.loadSubsidyInfo();
		}
	},
	methods: {
		loadSubsidyInfo() {
			// 模拟加载补贴信息
			console.log('加载补贴信息:', this.subsidyId);
			
			// 根据补贴类型设置申请条件
			this.subsidyInfo.conditions = [
				'年龄在80岁以上',
				'具有本市户籍',
				'经医疗机构认定为失能老人',
				'家庭经济困难'
			];
		},
		uploadDocument(index) {
			uni.chooseImage({
				count: 1,
				sizeType: ['compressed'],
				sourceType: ['album', 'camera'],
				success: (res) => {
					// 模拟上传文件
					uni.showLoading({
						title: '上传中...'
					});
					
					setTimeout(() => {
						uni.hideLoading();
						this.requiredDocuments[index].uploaded = true;
						this.requiredDocuments[index].file = res.tempFilePaths[0];
						
						uni.showToast({
							title: '上传成功',
							icon: 'success'
						});
					}, 2000);
				},
				fail: (err) => {
					uni.showToast({
						title: '上传失败',
						icon: 'none'
					});
				}
			});
		},
		previewDocument(index) {
			const doc = this.requiredDocuments[index];
			if (doc.file) {
				uni.previewImage({
					urls: [doc.file],
					current: doc.file
				});
			}
		},
		onAgreeChange(e) {
			this.formData.agreeTerms = e.detail.value.length > 0;
		},
		viewTerms() {
			uni.navigateTo({
				url: '/pages/subsidy/terms'
			});
		},
		validateForm() {
			const { name, idCard, phone, address, reason } = this.formData;
			
			if (!name.trim()) {
				uni.showToast({
					title: '请输入申请人姓名',
					icon: 'none'
				});
				return false;
			}
			
			if (!idCard.trim()) {
				uni.showToast({
					title: '请输入身份证号',
					icon: 'none'
				});
				return false;
			}
			
			// 简单的身份证号验证
			if (!/^\d{17}[\dXx]$/.test(idCard)) {
				uni.showToast({
					title: '身份证号格式不正确',
					icon: 'none'
				});
				return false;
			}
			
			if (!phone.trim()) {
				uni.showToast({
					title: '请输入联系电话',
					icon: 'none'
				});
				return false;
			}
			
			// 简单的手机号验证
			if (!/^1[3-9]\d{9}$/.test(phone)) {
				uni.showToast({
					title: '手机号格式不正确',
					icon: 'none'
				});
				return false;
			}
			
			if (!address.trim()) {
				uni.showToast({
					title: '请输入家庭住址',
					icon: 'none'
				});
				return false;
			}
			
			if (!reason.trim()) {
				uni.showToast({
					title: '请输入申请理由',
					icon: 'none'
				});
				return false;
			}
			
			if (!this.formData.agreeTerms) {
				uni.showToast({
					title: '请同意申请协议',
					icon: 'none'
				});
				return false;
			}
			
			const allDocumentsUploaded = this.requiredDocuments.every(doc => doc.uploaded);
			if (!allDocumentsUploaded) {
				uni.showToast({
					title: '请上传所有必需材料',
					icon: 'none'
				});
				return false;
			}
			
			return true;
		},
		submitApplication() {
			if (!this.validateForm()) {
				return;
			}
			
			uni.showModal({
				title: '确认提交',
				content: '确定要提交补贴申请吗？提交后无法修改。',
				success: (res) => {
					if (res.confirm) {
						this.performSubmit();
					}
				}
			});
		},
		performSubmit() {
			uni.showLoading({
				title: '提交中...'
			});
			
			// 模拟提交申请
			setTimeout(() => {
				uni.hideLoading();
				
				uni.showModal({
					title: '申请提交成功',
					content: '您的补贴申请已提交成功，我们将在3-5个工作日内完成初步审核。',
					showCancel: false,
					success: () => {
						// 跳转到申请状态页面
						uni.redirectTo({
							url: '/pages/subsidy/status'
						});
					}
				});
			}, 2000);
		}
	}
}
</script>

<style scoped>
.container {
	background-color: #f5f5f5;
	padding: 20rpx;
	padding-bottom: 40rpx;
}

.subsidy-info {
	background-color: #fff;
	border-radius: 20rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
}

.subsidy-title {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 15rpx;
}

.subsidy-desc {
	font-size: 28rpx;
	color: #666;
	line-height: 1.5;
	display: block;
	margin-bottom: 20rpx;
}

.subsidy-amount {
	display: flex;
	align-items: center;
	margin-bottom: 10rpx;
}

.amount-label {
	font-size: 28rpx;
	color: #666;
}

.amount-value {
	font-size: 32rpx;
	color: #ff6b35;
	font-weight: bold;
	margin-left: 10rpx;
}

.subsidy-deadline {
	display: flex;
	align-items: center;
}

.deadline-label {
	font-size: 28rpx;
	color: #666;
}

.deadline-value {
	font-size: 28rpx;
	color: #ff4757;
	font-weight: bold;
	margin-left: 10rpx;
}

.conditions-section, .form-section, .process-section {
	background-color: #fff;
	border-radius: 20rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 30rpx;
}

.conditions-list {
	display: flex;
	flex-direction: column;
	gap: 15rpx;
}

.condition-item {
	display: flex;
	align-items: center;
}

.check-icon {
	width: 30rpx;
	height: 30rpx;
	margin-right: 15rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.condition-text {
	font-size: 28rpx;
	color: #333;
}

.form-group {
	margin-bottom: 30rpx;
}

.form-label {
	font-size: 28rpx;
	color: #333;
	display: block;
	margin-bottom: 15rpx;
}

.required {
	color: #ff4757;
}

.form-input, .form-textarea {
	width: 100%;
	padding: 20rpx;
	border: 1rpx solid #ddd;
	border-radius: 10rpx;
	font-size: 28rpx;
	background-color: #fff;
}

.form-textarea {
	height: 120rpx;
	resize: none;
}

.upload-section {
	margin-bottom: 30rpx;
}

.upload-list {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

.upload-item {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 20rpx;
	background-color: #f9f9f9;
	border-radius: 10rpx;
}

.doc-name {
	font-size: 26rpx;
	color: #333;
}

.upload-actions {
	display: flex;
	gap: 15rpx;
}

.upload-btn, .preview-btn {
	padding: 10rpx 20rpx;
	border-radius: 20rpx;
	font-size: 24rpx;
	border: 1rpx solid #ddd;
	background-color: #fff;
	color: #666;
}

.upload-btn.uploaded {
	background-color: #4caf50;
	color: #fff;
	border-color: #4caf50;
}

.preview-btn {
	background-color: #4A90E2;
	color: #fff;
	border-color: #4A90E2;
}

.agreement-section {
	margin-bottom: 30rpx;
}

.agreement-item {
	display: flex;
	align-items: center;
}

.agreement-text {
	font-size: 26rpx;
	color: #666;
	margin-left: 15rpx;
}

.link {
	color: #4A90E2;
	text-decoration: underline;
}

.submit-section {
	text-align: center;
}

.submit-btn {
	width: 100%;
	height: 80rpx;
	background-color: #4A90E2;
	color: #fff;
	border: none;
	border-radius: 40rpx;
	font-size: 32rpx;
	font-weight: bold;
}

.submit-btn.disabled {
	background-color: #ccc;
	color: #999;
}

.process-steps {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

.step-item {
	display: flex;
	align-items: flex-start;
}

.step-number {
	width: 60rpx;
	height: 60rpx;
	background-color: #4A90E2;
	color: #fff;
	border-radius: 30rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 24rpx;
	font-weight: bold;
	margin-right: 20rpx;
	flex-shrink: 0;
}

.step-content {
	flex: 1;
}

.step-title {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 8rpx;
}

.step-desc {
	font-size: 24rpx;
	color: #666;
}
</style>
