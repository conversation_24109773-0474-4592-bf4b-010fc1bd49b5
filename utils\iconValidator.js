/**
 * 图标使用验证工具
 * 帮助开发者检查图标使用的规范性和一致性
 */

import { IconConfig, IconCategories } from './iconConfig.js'
import { DeprecatedIconMap, getCompatibleIconName } from './iconCompatibility.js'
import { IconSizes, validateIconSize } from './iconSizeConfig.js'

// 图标使用规则定义
export const IconUsageRules = {
  // 尺寸使用规则
  sizeRules: {
    'navbar': { recommended: '32rpx', allowed: ['24rpx', '32rpx', '36rpx'] },
    'list-item': { recommended: '24rpx', allowed: ['20rpx', '24rpx', '28rpx'] },
    'function-card': { recommended: '48rpx', allowed: ['40rpx', '48rpx', '56rpx'] },
    'button': { recommended: '32rpx', allowed: ['24rpx', '32rpx', '40rpx'] },
    'placeholder': { recommended: '48rpx', allowed: ['48rpx', '64rpx', '72rpx'] }
  },
  
  // 颜色使用规则
  colorRules: {
    'primary': '#ff8a00',
    'institution': '#ff6b6b',
    'service': '#4ecdc4',
    'elderly': '#96ceb4',
    'secondary': '#666666',
    'emergency': '#e74c3c'
  },
  
  // 场景使用规则
  contextRules: {
    'navigation': ['home-line', 'user-line', 'map-line', 'search-line', 'location-line'],
    'emergency': ['emergency-line', 'sos-line', 'first-aid-line', 'fire-line', 'police-line'],
    'elderly': ['elderly-care-line', 'walking-stick-line', 'hearing-aid-line', 'glasses-line', 'large-font-line', 'voice-line'],
    'medical': ['medical-cross-line', 'ambulance-line', 'doctor-line', 'nurse-line', 'blood-pressure-line', 'heart-rate-line']
  }
}

// 验证单个图标使用
export function validateIconUsage(iconName, size, color, context) {
  const issues = []
  const warnings = []
  const suggestions = []
  
  // 1. 检查图标是否存在
  const compatibleName = getCompatibleIconName(iconName)
  if (!IconConfig[compatibleName]) {
    issues.push({
      type: 'icon_not_found',
      message: `图标 "${iconName}" 不存在`,
      severity: 'error'
    })
    return { valid: false, issues, warnings, suggestions }
  }
  
  // 2. 检查是否使用了废弃图标
  if (DeprecatedIconMap[iconName]) {
    warnings.push({
      type: 'deprecated_icon',
      message: `图标 "${iconName}" 已废弃，建议使用 "${DeprecatedIconMap[iconName]}"`,
      severity: 'warning'
    })
  }
  
  // 3. 验证尺寸
  if (size && !validateIconSize(size)) {
    issues.push({
      type: 'invalid_size',
      message: `图标尺寸 "${size}" 无效，应使用标准尺寸`,
      severity: 'error'
    })
  }
  
  // 4. 检查尺寸是否符合场景规范
  if (context && size && IconUsageRules.sizeRules[context]) {
    const rule = IconUsageRules.sizeRules[context]
    if (!rule.allowed.includes(size)) {
      suggestions.push({
        type: 'size_recommendation',
        message: `在 "${context}" 场景中，建议使用 ${rule.recommended} 尺寸`,
        severity: 'info'
      })
    }
  }
  
  // 5. 检查图标是否适合当前场景
  if (context && IconUsageRules.contextRules[context]) {
    const allowedIcons = IconUsageRules.contextRules[context]
    if (!allowedIcons.includes(compatibleName)) {
      suggestions.push({
        type: 'context_mismatch',
        message: `图标 "${compatibleName}" 可能不适合 "${context}" 场景`,
        severity: 'info'
      })
    }
  }
  
  // 6. 检查颜色使用
  if (color && !Object.values(IconUsageRules.colorRules).includes(color)) {
    suggestions.push({
      type: 'color_recommendation',
      message: `建议使用主题色而非自定义颜色 "${color}"`,
      severity: 'info'
    })
  }
  
  return {
    valid: issues.length === 0,
    issues,
    warnings,
    suggestions,
    iconInfo: IconConfig[compatibleName]
  }
}

// 批量验证图标使用
export function validateMultipleIcons(iconUsages) {
  const results = {
    total: iconUsages.length,
    valid: 0,
    issues: 0,
    warnings: 0,
    suggestions: 0,
    details: []
  }
  
  iconUsages.forEach(usage => {
    const validation = validateIconUsage(
      usage.iconName,
      usage.size,
      usage.color,
      usage.context
    )
    
    results.details.push({
      ...usage,
      validation
    })
    
    if (validation.valid) {
      results.valid++
    }
    
    results.issues += validation.issues.length
    results.warnings += validation.warnings.length
    results.suggestions += validation.suggestions.length
  })
  
  return results
}

// 检查图标使用一致性
export function checkIconConsistency(iconUsages) {
  const consistencyIssues = []
  
  // 检查同一功能是否使用了不同图标
  const functionIconMap = new Map()
  iconUsages.forEach(usage => {
    if (usage.function) {
      if (!functionIconMap.has(usage.function)) {
        functionIconMap.set(usage.function, new Set())
      }
      functionIconMap.get(usage.function).add(getCompatibleIconName(usage.iconName))
    }
  })
  
  functionIconMap.forEach((icons, func) => {
    if (icons.size > 1) {
      consistencyIssues.push({
        type: 'inconsistent_function_icons',
        function: func,
        icons: Array.from(icons),
        message: `功能 "${func}" 使用了多个不同的图标: ${Array.from(icons).join(', ')}`
      })
    }
  })
  
  // 检查同一场景是否使用了不同尺寸
  const contextSizeMap = new Map()
  iconUsages.forEach(usage => {
    if (usage.context && usage.size) {
      if (!contextSizeMap.has(usage.context)) {
        contextSizeMap.set(usage.context, new Set())
      }
      contextSizeMap.get(usage.context).add(usage.size)
    }
  })
  
  contextSizeMap.forEach((sizes, context) => {
    if (sizes.size > 2) { // 允许少量尺寸变化
      consistencyIssues.push({
        type: 'inconsistent_context_sizes',
        context: context,
        sizes: Array.from(sizes),
        message: `场景 "${context}" 使用了过多不同的尺寸: ${Array.from(sizes).join(', ')}`
      })
    }
  })
  
  return consistencyIssues
}

// 生成图标使用报告
export function generateIconUsageReport(iconUsages) {
  const validation = validateMultipleIcons(iconUsages)
  const consistency = checkIconConsistency(iconUsages)
  
  // 统计图标使用频率
  const iconFrequency = new Map()
  iconUsages.forEach(usage => {
    const iconName = getCompatibleIconName(usage.iconName)
    iconFrequency.set(iconName, (iconFrequency.get(iconName) || 0) + 1)
  })
  
  // 统计尺寸使用
  const sizeUsage = new Map()
  iconUsages.forEach(usage => {
    if (usage.size) {
      sizeUsage.set(usage.size, (sizeUsage.get(usage.size) || 0) + 1)
    }
  })
  
  // 统计分类使用
  const categoryUsage = new Map()
  iconUsages.forEach(usage => {
    const iconName = getCompatibleIconName(usage.iconName)
    const iconConfig = IconConfig[iconName]
    if (iconConfig) {
      const category = iconConfig.category
      categoryUsage.set(category, (categoryUsage.get(category) || 0) + 1)
    }
  })
  
  return {
    summary: {
      total: validation.total,
      valid: validation.valid,
      issues: validation.issues,
      warnings: validation.warnings,
      suggestions: validation.suggestions,
      consistencyIssues: consistency.length
    },
    validation,
    consistency,
    statistics: {
      iconFrequency: Object.fromEntries(iconFrequency),
      sizeUsage: Object.fromEntries(sizeUsage),
      categoryUsage: Object.fromEntries(categoryUsage)
    },
    recommendations: generateRecommendations(validation, consistency)
  }
}

// 生成优化建议
function generateRecommendations(validation, consistency) {
  const recommendations = []
  
  // 基于验证结果的建议
  if (validation.warnings > 0) {
    recommendations.push({
      type: 'update_deprecated',
      priority: 'high',
      message: `发现 ${validation.warnings} 个废弃图标使用，建议及时更新`
    })
  }
  
  if (validation.issues > 0) {
    recommendations.push({
      type: 'fix_errors',
      priority: 'critical',
      message: `发现 ${validation.issues} 个图标使用错误，需要立即修复`
    })
  }
  
  // 基于一致性检查的建议
  consistency.forEach(issue => {
    if (issue.type === 'inconsistent_function_icons') {
      recommendations.push({
        type: 'unify_function_icons',
        priority: 'medium',
        message: `统一功能 "${issue.function}" 的图标使用`
      })
    }
    
    if (issue.type === 'inconsistent_context_sizes') {
      recommendations.push({
        type: 'standardize_sizes',
        priority: 'medium',
        message: `标准化场景 "${issue.context}" 的图标尺寸`
      })
    }
  })
  
  return recommendations
}

// 获取图标使用建议
export function getIconSuggestions(context, functionality) {
  const suggestions = []
  
  // 基于场景推荐图标
  if (IconUsageRules.contextRules[context]) {
    suggestions.push({
      type: 'context_icons',
      icons: IconUsageRules.contextRules[context],
      message: `适合 "${context}" 场景的图标`
    })
  }
  
  // 基于功能推荐图标
  const functionalIcons = Object.entries(IconConfig)
    .filter(([name, config]) => 
      config.description.includes(functionality) || 
      name.includes(functionality)
    )
    .map(([name]) => name)
  
  if (functionalIcons.length > 0) {
    suggestions.push({
      type: 'functional_icons',
      icons: functionalIcons,
      message: `与 "${functionality}" 功能相关的图标`
    })
  }
  
  return suggestions
}

export default {
  IconUsageRules,
  validateIconUsage,
  validateMultipleIcons,
  checkIconConsistency,
  generateIconUsageReport,
  getIconSuggestions
}
