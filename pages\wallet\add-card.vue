<template>
	<view class="container">
		<!-- 自定义导航栏 -->
		<view class="custom-navbar">
			<view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
			<view class="navbar-content">
				<view class="navbar-left" @click="goBack">
					<Icon name="arrow-left-line" size="36rpx" color="#333"></Icon>
					<text class="back-text">返回</text>
				</view>
				<view class="navbar-center">
					<text class="navbar-title">添加银行卡</text>
				</view>
				<view class="navbar-right"></view>
			</view>
		</view>

		<!-- 银行卡信息输入 -->
		<view class="card-form">
			<view class="form-section">
				<view class="section-title">银行卡信息</view>
				
				<view class="input-group">
					<view class="input-item">
						<text class="input-label">持卡人姓名</text>
						<input class="input-field" 
							placeholder="请输入持卡人姓名" 
							v-model="cardInfo.holderName" />
					</view>
					
					<view class="input-item">
						<text class="input-label">银行卡号</text>
						<input class="input-field" 
							type="number" 
							placeholder="请输入银行卡号" 
							v-model="cardInfo.cardNumber"
							@input="onCardNumberInput" />
					</view>
					
					<view class="input-item">
						<text class="input-label">手机号</text>
						<input class="input-field" 
							type="number" 
							placeholder="请输入预留手机号" 
							v-model="cardInfo.phoneNumber" />
					</view>
				</view>
			</view>

			<!-- 银行选择 -->
			<view class="bank-section">
				<view class="section-title">选择银行</view>
				<view class="bank-grid">
					<view class="bank-item" 
						:class="{ active: selectedBank === bank.code }"
						v-for="bank in bankList" 
						:key="bank.code"
						@click="selectBank(bank.code)">
						<view class="bank-logo">
							<Icon :name="bank.icon" size="40rpx" :color="bank.color"></Icon>
						</view>
						<text class="bank-name">{{bank.name}}</text>
					</view>
				</view>
			</view>

			<!-- 卡片类型选择 -->
			<view class="type-section">
				<view class="section-title">卡片类型</view>
				<view class="type-options">
					<view class="type-item" 
						:class="{ active: cardInfo.cardType === type.key }"
						v-for="type in cardTypes" 
						:key="type.key"
						@click="selectCardType(type.key)">
						<view class="type-radio">
							<Icon :name="cardInfo.cardType === type.key ? 'radio-button-line' : 'checkbox-blank-circle-line'" 
								size="24rpx" 
								:color="cardInfo.cardType === type.key ? '#ff8a00' : '#ccc'"></Icon>
						</view>
						<text class="type-text">{{type.name}}</text>
						<text class="type-desc">{{type.desc}}</text>
					</view>
				</view>
			</view>

			<!-- 安全提示 -->
			<view class="security-section">
				<view class="security-title">
					<Icon name="shield-check-line" size="24rpx" color="#4caf50"></Icon>
					<text>安全保障</text>
				</view>
				<view class="security-content">
					<text class="security-item">• 您的银行卡信息将通过SSL加密传输</text>
					<text class="security-item">• 我们不会存储您的完整卡号信息</text>
					<text class="security-item">• 所有交易均需要您的确认</text>
				</view>
			</view>

			<!-- 添加按钮 -->
			<view class="action-section">
				<button class="add-btn" 
					:disabled="!canAddCard" 
					@click="confirmAddCard">
					添加银行卡
				</button>
			</view>
		</view>

		<!-- 功能开发中提示 -->
		<view class="dev-notice">
			<Icon name="tools-line" size="32rpx" color="#ff8a00"></Icon>
			<text class="dev-text">此功能正在开发中，敬请期待</text>
		</view>
	</view>
</template>

<script>
import Icon from '@/components/Icon/Icon.vue'

export default {
	components: {
		Icon
	},
	data() {
		return {
			statusBarHeight: 0,
			selectedBank: '',
			cardInfo: {
				holderName: '',
				cardNumber: '',
				phoneNumber: '',
				cardType: 'debit'
			},
			bankList: [
				{
					code: 'icbc',
					name: '工商银行',
					icon: 'bank-line',
					color: '#c41e3a'
				},
				{
					code: 'ccb',
					name: '建设银行',
					icon: 'bank-line',
					color: '#0052cc'
				},
				{
					code: 'abc',
					name: '农业银行',
					icon: 'bank-line',
					color: '#00a651'
				},
				{
					code: 'boc',
					name: '中国银行',
					icon: 'bank-line',
					color: '#b8860b'
				},
				{
					code: 'cmb',
					name: '招商银行',
					icon: 'bank-line',
					color: '#dc143c'
				},
				{
					code: 'other',
					name: '其他银行',
					icon: 'bank-line',
					color: '#666'
				}
			],
			cardTypes: [
				{
					key: 'debit',
					name: '储蓄卡',
					desc: '用于日常消费和转账'
				},
				{
					key: 'credit',
					name: '信用卡',
					desc: '支持透支消费'
				}
			]
		}
	},
	computed: {
		canAddCard() {
			return this.cardInfo.holderName.trim() && 
				   this.cardInfo.cardNumber.trim().length >= 16 && 
				   this.cardInfo.phoneNumber.trim().length === 11 && 
				   this.selectedBank && 
				   this.cardInfo.cardType;
		}
	},
	onLoad() {
		const systemInfo = uni.getSystemInfoSync();
		this.statusBarHeight = systemInfo.statusBarHeight || 44;
	},
	methods: {
		onCardNumberInput() {
			// 格式化银行卡号，每4位加一个空格
			let value = this.cardInfo.cardNumber.replace(/\s/g, '').replace(/[^\d]/g, '');
			value = value.replace(/(\d{4})(?=\d)/g, '$1 ');
			this.cardInfo.cardNumber = value;
		},
		selectBank(bankCode) {
			this.selectedBank = bankCode;
		},
		selectCardType(typeKey) {
			this.cardInfo.cardType = typeKey;
		},
		confirmAddCard() {
			if (!this.canAddCard) return;
			
			const selectedBankInfo = this.bankList.find(bank => bank.code === this.selectedBank);
			const selectedTypeInfo = this.cardTypes.find(type => type.key === this.cardInfo.cardType);
			
			uni.showModal({
				title: '确认添加',
				content: `确定要添加 ${selectedBankInfo.name} ${selectedTypeInfo.name} 吗？`,
				success: (res) => {
					if (res.confirm) {
						this.processAddCard();
					}
				}
			});
		},
		processAddCard() {
			uni.showLoading({
				title: '正在添加...',
				mask: true
			});
			
			// 模拟添加银行卡流程
			setTimeout(() => {
				uni.hideLoading();
				uni.showToast({
					title: '功能开发中，敬请期待',
					icon: 'none',
					duration: 2000
				});
			}, 2000);
		},
		goBack() {
			uni.navigateBack({
				fail: () => {
					uni.navigateTo({
						url: '/pages/wallet/wallet'
					});
				}
			});
		}
	}
}
</script>

<style scoped>
.container {
	background: #f8f9fa;
	min-height: 100vh;
}

/* 导航栏样式 */
.custom-navbar {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 1000;
	background: rgba(255, 255, 255, 0.95);
	backdrop-filter: blur(20rpx);
	border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);
}

.navbar-content {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 20rpx 32rpx;
	min-height: 88rpx;
}

.navbar-left {
	display: flex;
	align-items: center;
	gap: 12rpx;
	padding: 8rpx 16rpx 8rpx 0;
	border-radius: 20rpx;
	transition: all 0.2s ease;
}

.navbar-left:active {
	background-color: rgba(0, 0, 0, 0.04);
	transform: scale(0.96);
}

.back-text {
	font-size: 32rpx;
	color: #333;
	font-weight: 500;
}

.navbar-center {
	position: absolute;
	left: 50%;
	transform: translateX(-50%);
}

.navbar-title {
	font-size: 34rpx;
	font-weight: 600;
	color: #333;
}

.navbar-right {
	display: flex;
	align-items: center;
}

/* 表单区域 */
.card-form {
	padding: 0 20rpx 40rpx;
	margin-top: 220rpx;
}

.form-section, .bank-section, .type-section, .security-section {
	background: white;
	border-radius: 30rpx;
	padding: 40rpx;
	margin-bottom: 20rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 30rpx;
}

.input-group {
	display: flex;
	flex-direction: column;
	gap: 30rpx;
}

.input-item {
	display: flex;
	flex-direction: column;
	gap: 15rpx;
}

.input-label {
	font-size: 28rpx;
	color: #333;
	font-weight: 500;
}

.input-field {
	padding: 25rpx;
	background: #f8f9fa;
	border-radius: 15rpx;
	font-size: 28rpx;
	border: 2rpx solid transparent;
}

.input-field:focus {
	border-color: #ff8a00;
	background: white;
}

/* 银行选择 */
.bank-grid {
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	gap: 20rpx;
}

.bank-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 30rpx 20rpx;
	background: #f8f9fa;
	border-radius: 20rpx;
	border: 2rpx solid transparent;
	transition: all 0.2s ease;
}

.bank-item.active {
	background: rgba(255, 138, 0, 0.1);
	border-color: #ff8a00;
}

.bank-logo {
	width: 60rpx;
	height: 60rpx;
	border-radius: 50%;
	background: white;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-bottom: 15rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.bank-name {
	font-size: 24rpx;
	color: #333;
	text-align: center;
}

/* 卡片类型选择 */
.type-options {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

.type-item {
	display: flex;
	align-items: center;
	padding: 25rpx;
	background: #f8f9fa;
	border-radius: 20rpx;
	border: 2rpx solid transparent;
	transition: all 0.2s ease;
}

.type-item.active {
	background: rgba(255, 138, 0, 0.1);
	border-color: #ff8a00;
}

.type-radio {
	margin-right: 20rpx;
}

.type-text {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
	margin-right: 15rpx;
}

.type-desc {
	font-size: 24rpx;
	color: #666;
	flex: 1;
}

/* 安全提示 */
.security-title {
	display: flex;
	align-items: center;
	gap: 10rpx;
	font-size: 28rpx;
	font-weight: bold;
	color: #4caf50;
	margin-bottom: 20rpx;
}

.security-content {
	display: flex;
	flex-direction: column;
	gap: 15rpx;
}

.security-item {
	font-size: 26rpx;
	color: #666;
	line-height: 1.5;
}

/* 操作区域 */
.action-section {
	padding: 40rpx;
}

.add-btn {
	width: 100%;
	height: 100rpx;
	background: #ff8a00;
	color: white;
	border: none;
	border-radius: 25rpx;
	font-size: 32rpx;
	font-weight: bold;
}

.add-btn:disabled {
	background: #ccc;
	color: #999;
}

.dev-notice {
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 15rpx;
	padding: 30rpx;
	margin: 20rpx;
	background: rgba(255, 138, 0, 0.1);
	border-radius: 20rpx;
}

.dev-text {
	font-size: 26rpx;
	color: #ff8a00;
}
</style>
